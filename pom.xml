<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.1.5.RELEASE</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.fangcloud</groupId>
	<artifactId>platform</artifactId>
	<version>1.0.4</version>
	<name>platform</name>
	<description>third-part-platform</description>

	<properties>
		<service.baseDirectory>${project.build.directory}/platform</service.baseDirectory>
		<java.version>1.8</java.version>
		<productId>platform</productId> <!-- 不同企业需要修改 这个值 -->
		<sync-common.version>1.2.58</sync-common.version>
		<jackson.verdion.databind>********</jackson.verdion.databind>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.alibaba.xxpt</groupId>
			<artifactId>zwdd-sdk</artifactId>
			<version>1.2.0</version>
		</dependency>
		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
			<version>2.10</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-thymeleaf</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-redis</artifactId>
			<version>2.0.10.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.0</version>
		</dependency>
		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>20160810</version>
		</dependency>
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>sqljdbc4</artifactId>
			<version>4.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.8</version>
		</dependency>
		<dependency>
			<groupId>com.unboundid</groupId>
			<artifactId>unboundid-ldapsdk</artifactId>
			<version>6.0.7</version> <!-- 检查最新版本 -->
		</dependency>
		<dependency>
			<groupId>cn.easyproject</groupId>
			<artifactId>ojdbc7</artifactId>
			<version>12.1.0.2.0</version>
		</dependency>

		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter-test</artifactId>
			<version>3.5.1</version>
		</dependency>
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.6.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.squareup.okhttp3/okhttp -->
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>3.14.9</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.83</version>
		</dependency>

		<!-- taobao sdk http://open-dev.dingtalk.com/download/openSDK/java-->
		<dependency>
			<groupId>com.taobao.sdk</groupId>
			<artifactId>taobao-sdk-java-auto-20190612</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>25.0-jre</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.8.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-text</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.10.0</version>
		</dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.1.86</version>
        </dependency>

		<dependency>
			<groupId>com.fangcloud</groupId>
			<artifactId>sync-common</artifactId>
			<version>${sync-common.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>nacos-config-spring-boot-starter</artifactId>
			<version>0.2.7</version>
		</dependency>

		<dependency>
			<groupId>org.mybatis.spring.boot</groupId>
			<artifactId>mybatis-spring-boot-starter</artifactId>
			<version>2.0.1</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>1.1.10</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.38</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-jdbc</artifactId>
		</dependency>

		<dependency>
			<groupId>com.sun</groupId>
			<artifactId>ldapbp</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.ldap</groupId>
			<artifactId>spring-ldap-core</artifactId>
			<version>2.3.2.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>com.fangcloud</groupId>
			<artifactId>container</artifactId>
			<version>0.0.1</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.dubbo</groupId>
					<artifactId>dubbo</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.apache.dubbo</groupId>
			<artifactId>dubbo</artifactId>
			<version>2.7.21</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>QLExpress</artifactId>
			<version>3.2.0</version>
		</dependency>

		<dependency>
			<groupId>org.dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>2.1.3</version>
		</dependency>

		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
			<version>1.1.6</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.13</version>
		</dependency>
		<dependency>
			<groupId>com.mashape.unirest</groupId>
			<artifactId>unirest-java</artifactId>
			<version>1.4.9</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba.cloudapi.sdk</groupId>
			<artifactId>sdk-core-java</artifactId>
			<version>1.1.7</version>
		</dependency>
		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>2.7.0</version>
		</dependency>
		<dependency>
			<groupId>com.ccwork</groupId>
			<artifactId>ccwork-open-demo</artifactId>
			<version>1.0.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.burgstaller</groupId>
			<artifactId>okhttp-digest</artifactId>
			<version>2.1</version>
		</dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.66</version>
        </dependency>
		<!-- https://mvnrepository.com/artifact/net.jradius/jradius-dictionary -->
		<dependency>
			<groupId>net.jradius</groupId>
			<artifactId>jradius-dictionary</artifactId>
			<version>1.1.5</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/net.jradius/jradius-extended -->
		<dependency>
			<groupId>net.jradius</groupId>
			<artifactId>jradius-extended</artifactId>
			<version>1.1.5</version>
		</dependency>

		<dependency>
			<groupId>com.dameng</groupId>
			<artifactId>DmJdbcDriver18</artifactId>
			<version>8.1.1.193</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.19</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-ext-jdk15on</artifactId>
			<version>1.56</version>
		</dependency>
		<dependency>
			<groupId>com.rabbitmq</groupId>
			<artifactId>amqp-client</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
		</dependency>
		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.10.3</version>
		</dependency>
		<dependency>
			<groupId>com.github.plexpt</groupId>
			<artifactId>chatgpt</artifactId>
			<version>4.0.8</version>
			<exclusions>
				<exclusion>
					<artifactId>logback-core</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!--FtpClient所在的包-->
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.6</version>
		</dependency>
	</dependencies>


	<!--  环境定义 -->
	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<environment>dev</environment>
				<release-type>stable</release-type>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<environment>test</environment>
				<release-type>stable</release-type>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<properties>
				<environment>prod</environment>
				<release-type>stable</release-type>
			</properties>
		</profile>
		<profile>
			<id>private</id>
			<properties>
				<environment>private</environment>
				<release-type>stable</release-type>
			</properties>
		</profile>
	</profiles>

	<build>
		<filters>
			<filter>${basedir}/src/main/resources/application-${environment}.properties</filter>
		</filters>
		<plugins>
			<!-- copy resources -->
			<plugin>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.0.2</version>
				<configuration>
					<useDefaultDelimiters>true</useDefaultDelimiters>
				</configuration>
				<executions>
					<execution>
						<id>copy-other</id>
						<phase>package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${service.baseDirectory}/</outputDirectory>
							<resources>
								<resource>
									<directory>${basedir}/src/main/other</directory>
								</resource>
							</resources>
						</configuration>
					</execution>
					<execution>
						<id>copy-resources</id>
						<phase>package</phase>
						<goals>
							<goal>copy-resources</goal>
						</goals>
						<configuration>
							<outputDirectory>${service.baseDirectory}/resources</outputDirectory>
							<resources>
								<resource>
									<directory>src/main/resources</directory>
									<includes>
										<include>*${productId}.yml</include> <!-- 只打对应企业的配置文件 -->
										<include>application.yml</include>
										<include>application-dev.yml</include>
										<include>application-prod.yml</include>
										<include>application-pre.yml</include>
										<include>logback.xml</include>
										<include>*.txt</include>
										<include>*mybatis/**</include>
										<include>*mapper/**</include>
										<include>static/**</include>
										<include>templates/**</include>
										<include>**/*.html</include>
									</includes>
									<excludes>
										<exclude>**.properties</exclude>
									</excludes>
									<filtering>true</filtering>
								</resource>
							</resources>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<!-- copy dependency -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<executions>
					<execution>
						<id>copy-dependencies</id>
						<phase>prepare-package</phase>
						<goals>
							<goal>copy-dependencies</goal>
						</goals>
						<configuration>
							<outputDirectory>${service.baseDirectory}/lib</outputDirectory>
							<overWriteReleases>false</overWriteReleases>
							<overWriteSnapshots>true</overWriteSnapshots>
							<overWriteIfNewer>true</overWriteIfNewer>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>3.0.2</version>
				<configuration>
					<outputDirectory>${service.baseDirectory}</outputDirectory>
					<archive>
						<manifest>
							<addClasspath>true</addClasspath>
							<classpathPrefix>lib/</classpathPrefix>
							<mainClass>com.fangcloud.thirdpartplatform.ThirdPartPlatformApplication</mainClass>
						</manifest>
						<manifestEntries>
							<Class-Path>resources/</Class-Path>
						</manifestEntries>
					</archive>
					<includes>
						<include>**/*.class</include>
					</includes>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>3.1.0</version>
				<configuration>
					<!-- not append assembly id in release file name -->
					<appendAssemblyId>false</appendAssemblyId>
					<descriptors>
						<descriptor>assembly.xml</descriptor>
					</descriptors>
				</configuration>
				<executions>
					<execution>
						<id>make-assembly</id>
						<phase>package</phase>
						<goals>
							<goal>single</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
<!--			<plugin>-->
<!--				<groupId>org.mybatis.generator</groupId>-->
<!--				<artifactId>mybatis-generator-maven-plugin</artifactId>-->
<!--				<dependencies>-->
<!--					<dependency>-->
<!--						<groupId>mysql</groupId>-->
<!--						<artifactId>mysql-connector-java</artifactId>-->
<!--						<version>5.1.35</version>-->
<!--					</dependency>-->
<!--				</dependencies>-->
<!--			</plugin>-->
		</plugins>
		<finalName>${productId}-${version}</finalName>
	</build>

</project>
