package com.fangcloud.thirdpartplatform;

import com.fangcloud.thirdpartplatform.constant.sync.ApiParamWayEnum;
import com.fangcloud.thirdpartplatform.controller.PlatformLoginConfigController;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginConfigResponse;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class LoginConfigControllerTest {

    @Resource
    private PlatformLoginConfigController platformLoginConfigController;


    @Test
    public void testGetConfig(){
        Result<PlatformLoginConfigResponse> config = platformLoginConfigController.getConfig(1);
        System.out.println();
    }


    private static LoginSourceOAUTH2ConfigDto buildDefaultLoginSourceOAUTH2ConfigDto() {
        LoginSourceOAUTH2ConfigDto loginSourceOAUTH2ConfigDto = new LoginSourceOAUTH2ConfigDto();


        APIConfigValueBox.APIConfig oauth2ApiConfig = new APIConfigValueBox.APIConfig();
        oauth2ApiConfig.setApi("127.0.0.1/getToken");

        APIParamConfigDto apiParamConfigDto = new APIParamConfigDto();
        apiParamConfigDto.setDataType("int");
        apiParamConfigDto.setValue("123");
        apiParamConfigDto.setName("userName");
        apiParamConfigDto.setParamWay(ApiParamWayEnum.HEAD.getDesc());

        List<APIParamConfigDto> apiParamConfigDtoList = Lists.newArrayList(apiParamConfigDto);

        oauth2ApiConfig.setParamConfig(apiParamConfigDtoList);




        APIConfigValueBox.APIConfig apiConfig = new APIConfigValueBox.APIConfig();

        apiConfig.setApi("127.0.0.1/getUserInfo");

        APIParamConfigDto  apiParamConfigDtoNew = new APIParamConfigDto();
        apiParamConfigDtoNew.setDataType("int");
        apiParamConfigDtoNew.setValue("123");
        apiParamConfigDtoNew.setName("userName");
        apiParamConfigDtoNew.setParamWay(ApiParamWayEnum.HEAD.getDesc());
        List<APIParamConfigDto> apiParamConfigDtoListNew = Lists.newArrayList(apiParamConfigDtoNew);

        APIResultConfigDto apiResultConfigDto = new APIResultConfigDto();
        apiResultConfigDto.setDataType("int");
        apiResultConfigDto.setName("name");
        apiResultConfigDto.setMappingName("full_name");
        List<APIResultConfigDto> apiResultConfigDtoList = Lists.newArrayList(apiResultConfigDto);

        apiConfig.setParamConfig(apiParamConfigDtoListNew);
        apiConfig.setResultConfig(apiResultConfigDtoList);
        apiConfig.setResultPath("$.result.data");

        loginSourceOAUTH2ConfigDto.setOauth2Config(oauth2ApiConfig);
        loginSourceOAUTH2ConfigDto.setApiConfig(apiConfig);
        return loginSourceOAUTH2ConfigDto;
    }

    public static LoginSourceLDAPConfigDto buildDefaultLoginSourceLDAPConfigDto(){
        LoginSourceLDAPConfigDto loginSourceLDAPConfigDto = new LoginSourceLDAPConfigDto();
        loginSourceLDAPConfigDto.setUserName("userName");
        loginSourceLDAPConfigDto.setPassword("password");
        loginSourceLDAPConfigDto.setUrl("127.0.0.1");
        loginSourceLDAPConfigDto.setBaseOu("123");
        return loginSourceLDAPConfigDto;
    }




}
