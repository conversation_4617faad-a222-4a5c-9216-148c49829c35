package com.fangcloud.thirdpartplatform;

import com.fangcloud.thirdpartplatform.controller.PlatformLoginController;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest
public class PlatformLoginControllerTest {

    @Resource
    private PlatformLoginController platformLoginController;

    @Test
    public void testGetImgCaptcha() throws IOException {
        HttpServletRequest httpServletRequest = new MockHttpServletRequest();
        HttpServletResponse httpServletResponse = new MockHttpServletResponse();
        platformLoginController.getImgCaptcha(httpServletRequest, httpServletResponse );

    }
    @Test
    public void testGet(){

    }
}
