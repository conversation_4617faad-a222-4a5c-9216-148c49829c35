package com.fangcloud.thirdpartplatform;

import okhttp3.Response;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DigestAuthTest {

    @Resource
    private com.fangcloud.thirdpartplatform.helper.HttpClientHelper httpClientHelper;

    @Test
    public void DigestAuthGet() throws IOException {
        Response root = httpClientHelper.getDigestAuth("http://117.176.241.41:8083/vdi/rest/cloud/disk/yf/sync/user", "root", "root@123");

        System.out.println();
    }

    @Test
    public void DigestAuthPost() throws IOException {

//        HttpResponse root = httpClientHelper.postDigestAuth("http://117.176.241.41:8083/vdi/rest/cloud/disk/yf/sync/user", "", "root", "root@123");
        System.out.println();
    }

}
