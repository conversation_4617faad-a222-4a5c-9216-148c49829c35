package com.fangcloud.thirdpartplatform;

import com.fangcloud.thirdpartplatform.controller.SyncPublishController;
import com.fangcloud.thirdpartplatform.db.dao.PlatformDepartmentMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformDepartment;
import com.fangcloud.thirdpartplatform.entity.sync.PublicDepartmentBean;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class PublicSyncTest {

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Resource
    private SyncPublishController syncPublishController;

    @Test
    public void insertTest() {
        PlatformDepartment platformDepartment = new PlatformDepartment();
        platformDepartment.setDepartmentId("111");
        platformDepartment.setName("111");
        platformDepartment.setYfyDepartmentId(111);
        platformDepartment.setPlatformId(0);
        platformDepartmentMapper.insert(platformDepartment);
    }

    @Test
    public void createDepartmentTest() {
        PublicDepartmentBean createDepartmentBean = new PublicDepartmentBean();
        createDepartmentBean.setDepartmentName("公有云测试部门");
        createDepartmentBean.setSpaceTotal(10L);
        v2ClientHelper.createDepartment(createDepartmentBean, 1586152L);
    }


    @Test
    public void syncPublishControllerTest(){
        syncPublishController.syncOrganization(21791, "", "");
    }

}
