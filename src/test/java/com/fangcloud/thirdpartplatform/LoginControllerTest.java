package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.controller.LoginController;
import com.fangcloud.thirdpartplatform.controller.PlatformLoginController;
import com.fangcloud.thirdpartplatform.controller.QihooTuiTuiAiChatController;
import com.fangcloud.thirdpartplatform.entity.input.DingTalkInitParams;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.entity.input.WeixinWorkInitParams;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@RunWith(SpringRunner.class)
@SpringBootTest
public class LoginControllerTest {
    @Resource
    private LoginController loginController;

    @Resource
    private PlatformLoginController platformLoginController;

    @Resource
    private QihooTuiTuiAiChatController qihooController;


    @Test
    public void weixinAutoLogin(){
        loginController.weixinWork(createWeixinWorkInitParams());
    }

    @Test
    public void dingTalkAutoLogin(){

        loginController.dingTalk(createDingTalkInitParams());
    }

    public static DingTalkInitParams createDingTalkInitParams(){
        DingTalkInitParams dingTalkInitParams = new DingTalkInitParams();
        dingTalkInitParams.setAgentId("1248671981");
        dingTalkInitParams.setCode("937af95452903299bffc5217e4887668");
        dingTalkInitParams.setAppId("dingt96mf1qx6uwdxrxa");
        dingTalkInitParams.setAppSecret("N7HVGYQ2I3BvJ5vejiBhJKN_3CsDLDVYXaKfN9id3bj1MxNWMHVISd9rarXtNRYG");
        dingTalkInitParams.setCorpId("ding26f34dfa755ebe0facaaa37764f94726");
        dingTalkInitParams.setCorpSecret("4f5309c21fab3d9ab047c48ae44dccd8");
        dingTalkInitParams.setHost("");
        dingTalkInitParams.setUnionId("");
        dingTalkInitParams.setLoginType("mobile");

        dingTalkInitParams.setEnterpriseParams(createEnterpriseParams());
        return dingTalkInitParams;
    }
    public static WeixinWorkInitParams createWeixinWorkInitParams(){
        WeixinWorkInitParams weixinWorkInitParams = new WeixinWorkInitParams();
        weixinWorkInitParams.setEnterpriseParams(createEnterpriseParams());

        weixinWorkInitParams.setCorpSecret("52XBdG4n5QW2eMSu35BYmUlibcLBF1c2TK0c5izjpJ8");
        weixinWorkInitParams.setAgentId("1000002");
        weixinWorkInitParams.setCorpId("ww339a378e2384439e");
        weixinWorkInitParams.setCode("7lo5V56ddqLKTvvev6FHpH-JSoUsirYEB8bp_IRMdOU");
        weixinWorkInitParams.setLoginType("email");
        weixinWorkInitParams.setSyncUserFlag(false);
        return weixinWorkInitParams;
    }
    public static EnterpriseParams createEnterpriseParams(){
        EnterpriseParams enterpriseParams = new EnterpriseParams();
        enterpriseParams.setClientId("3dc27264-0ba6-4f47-b7de-68b5aa5abd41");
        enterpriseParams.setEnterpriseId("21791");
        enterpriseParams.setIdentifier("");
        enterpriseParams.setPlatformId("562");
        enterpriseParams.setRedirectUrl("");
        enterpriseParams.setType("");
        return enterpriseParams;
    }

    @Test
    public void aichat() throws IOException {
        String text="{\n" +
                "    \"cid\": \"****************\",\n" +
                "    \"uid\": \"****************\",\n" +
                "    \"user_account\": \"zhangsan\",\n" +
                "    \"user_name\": \"张三\",\n" +
                "    \"timestamp\": \"**********\",\n" +
                "    \"event\": \"single_chat\",\n" +
                "    \"data\": {\n" +
                "        \"msgid\": \"123****\",\n" +
                "        \"msg_type\": \"text\",\n" +
                "        \"text\": \"如何生成token\"\n" +
                "    }\n" +
                "}";
        JSONObject jsonObject= JSON.parseObject(text);
        qihooController.chatStream(null,jsonObject);
    }

}
