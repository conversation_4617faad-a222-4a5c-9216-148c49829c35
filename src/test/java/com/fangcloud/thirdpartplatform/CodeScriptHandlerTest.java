package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.login.LoginParamEnum;
import com.fangcloud.thirdpartplatform.entity.dto.CodeScriptValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.LoginSourceCodeScriptConfigDto;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.service.impl.datasource.CodeScriptSyncHandler;
import com.sync.common.entity.dto.YfyUser;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CodeScriptHandlerTest extends ThirdPartPlatformApplicationTests {

    @Resource
    private CodeScriptSyncHandler codeScriptSyncHandler;


    @Test
    public void getYfyUserTest() {
        Map<String, String> dataMap  = new HashMap<>();
        dataMap.put(LoginParamEnum.USER_NAME.getType(), "user");
        dataMap.put(LoginParamEnum.PASSWORD.getType(), "password");
        dataMap.put(LoginParamEnum.SMS_CODE.getType(), "sms_code");

        LoginSourceCodeScriptConfigDto loginSourceCodeScriptConfigDto = new LoginSourceCodeScriptConfigDto();
        loginSourceCodeScriptConfigDto.setCodeScript("package%20com.fangcloud.thirdpartplatform%3B%0A%0Aimport%20com.alibaba.fastjson.JSON%3B%0Aimport%20com.alibaba.fastjson.JSONPath%3B%0Aimport%20com.fangcloud.thirdpartplatform.constant.login.LoginParamEnum%3B%0Aimport%20com.fangcloud.thirdpartplatform.helper.HttpClientHelper%3B%0Aimport%20com.sync.common.entity.dto.YfyUser%3B%0Aimport%20okhttp3.Response%3B%0Aimport%20org.springframework.beans.factory.annotation.Autowired%3B%0A%0Aimport%20java.io.IOException%3B%0Aimport%20java.util.*%3B%0Aimport%20java.util.logging.Logger%3B%0A%0A%0Apublic%20class%20CodeScript%20%7B%0A%0A%0A%20%20%20%20%40Autowired%0A%20%20%20%20HttpClientHelper%20httpClientHelper%3B%0A%0A%20%20%20%20private%20static%20final%20Logger%20LOGGER%20%3D%20Logger.getLogger(CodeScript.class.getName())%3B%0A%0A%20%20%20%20%2F**%0A%20%20%20%20%20*%20code_script%E7%A4%BA%E4%BE%8B%E4%BB%A3%E7%A0%81%EF%BC%8Cobj%E4%B8%BA%E4%BC%A0%E5%85%A5%E7%9A%84%E5%8F%82%E6%95%B0%0A%20%20%20%20%20*%20%40param%20obj%0A%20%20%20%20%20*%20%40return%0A%20%20%20%20%20*%2F%0A%20%20%20%20public%20YfyUser%20execute(Object%20obj)%20throws%20IOException%20%7B%0A%20%20%20%20%20%20%20%20Map%3CString%2C%20String%3E%20dataMap%20%3D%20(Map%3CString%2C%20String%3E)%20obj%3B%0A%0A%20%20%20%20%20%20%20%20YfyUser%20yfyUser%20%3D%20new%20YfyUser()%3B%0A%0A%0A%20%20%20%20%20%20%20%20Response%20response%20%3D%20httpClientHelper.getResponse(%22https%3A%2F%2Fdev.usemock.com%2F641824bb71e37d4e5431a25d%2Flogin_test%22)%3B%0A%0A%20%20%20%20%20%20%20%20String%20result%20%3D%20Objects.requireNonNull(response.body().string())%3B%0A%20%20%20%20%20%20%20%20LOGGER.info(%22getResponse%20result%3A%20%22%20%2B%20result)%3B%0A%20%20%20%20%20%20%20%20String%20loginName%20%3D%20(String)%20JSONPath.extract(result%2C%20%22%5C%24.data.user.loginName%22)%3B%0A%20%20%20%20%20%20%20%20String%20userName%20%3D%20(String)%20JSONPath.extract(result%2C%20%22%5C%24.data.user.userName%22)%3B%0A%0A%20%20%20%20%20%20%20%20yfyUser.setId(loginName)%3B%0A%20%20%20%20%20%20%20%20yfyUser.setFullName(userName)%3B%0A%20%20%20%20%20%20%20%20yfyUser.setStatus(dataMap.get(LoginParamEnum.USER_NAME.getType()))%3B%0A%0A%20%20%20%20%20%20%20%20LOGGER.info(%22yfyUser%20info%3A%20%22%20%2B%20JSON.toJSONString(yfyUser))%3B%0A%20%20%20%20%20%20%20%20return%20yfyUser%3B%0A%20%20%20%20%7D%0A%7D%0A%0A");


        YfyUser yfyUser = codeScriptSyncHandler.getYfyUserByConfigValueBox(dataMap, loginSourceCodeScriptConfigDto);

        System.out.println(JSON.toJSONString(yfyUser));

    }

    @Test
    public void getYfyUserListTest() {
        List<YfyUser> users = new ArrayList<>();

        CodeScriptValueBox codeScriptValueBox = new CodeScriptValueBox();
        codeScriptValueBox.setCodeScript("");

        codeScriptSyncHandler.getYfyUsersByConfigValueBox(users, codeScriptValueBox);

        System.out.println(JSON.toJSONString(users));

    }

    @Test
    public void getYfyDeptListTest() {
        List<YfyDepartment> departments = new ArrayList<>();

        CodeScriptValueBox codeScriptValueBox = new CodeScriptValueBox();
        codeScriptValueBox.setCodeScript("package%20com.fangcloud.thirdpartplatform%3B%0A%0Aimport%20com.alibaba.fastjson.JSON%3B%0Aimport%20com.alibaba.fastjson.JSONArray%3B%0Aimport%20com.alibaba.fastjson.JSONObject%3B%0Aimport%20com.alibaba.fastjson.JSONPath%3B%0Aimport%20com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment%3B%0Aimport%20com.fangcloud.thirdpartplatform.helper.HttpClientHelper%3B%0Aimport%20com.fangcloud.thirdpartplatform.utils.Md5Utils%3B%0Aimport%20okhttp3.Response%3B%0Aimport%20org.springframework.beans.factory.annotation.Autowired%3B%0A%0Aimport%20java.util.*%3B%0Aimport%20java.util.logging.Logger%3B%0A%0A%0Apublic%20class%20CodeScript2%20%7B%0A%0A%0A%20%20%20%20%40Autowired%0A%20%20%20%20HttpClientHelper%20httpClientHelper%3B%0A%0A%20%20%20%20private%20static%20final%20Logger%20LOGGER%20%3D%20Logger.getLogger(CodeScript2.class.getName())%3B%0A%0A%20%20%20%20%2F**%0A%20%20%20%20%20*%20%E7%99%BB%E5%BD%95%0A%20%20%20%20%20*%20%40param%20obj%0A%20%20%20%20%20*%20%40return%0A%20%20%20%20%20*%2F%0A%20%20%20%20public%20List%3CYfyDepartment%3E%20execute(Object%20obj)%20throws%20Exception%20%7B%0A%0A%20%20%20%20%20%20%20%20long%20timeMillis%20%3D%20System.currentTimeMillis()%3B%0A%20%20%20%20%20%20%20%20String%20sign%20%3D%22appKey%3D%22%2B%22E14UHfqP%22%2B%22%26appSecret%3D%22%2B%22aCMP1fVtoXYwespTmU4GL3nbh97W8Zqy%22%2B%22%26timestamp%3D%22%2BtimeMillis%3B%0A%0A%20%20%20%20%20%20%20%20String%20md5Sign%20%3D%20Md5Utils.getMD5OfStr(sign)%3B%0A%0A%20%20%20%20%20%20%20%20%2F%2F%20%E8%8E%B7%E5%8F%96%E7%94%A8%E6%88%B7%E5%88%97%E8%A1%A8%E6%8E%A5%E5%8F%A3%0A%2F%2F%20%20%20%20%20%20%20%20String%20url%20%3D%20%22http%3A%2F%2F137.168.102.206%3A7000%2Fhr%2Fapi%2FproductManage%2FdeptInfo%3Ftimestamp%3D%25s%26sign%3D%25s%22%3B%0A%20%20%20%20%20%20%20%20String%20url%20%3D%20%22https%3A%2F%2Fdev.usemock.com%2F641824bb71e37d4e5431a25d%2Fdepartment%2Finfo%3Ftimestamp%3D%25s%26sign%3D%25s%22%3B%0A%20%20%20%20%20%20%20%20String%20formatUrl%20%3D%20String.format(url%2C%20timeMillis%2C%20md5Sign)%3B%0A%0A%20%20%20%20%20%20%20%20LOGGER.info(%22get%20xndl%20user_list%20url%20is%20%3A%20%22%20%2B%20formatUrl)%3B%0A%0A%20%20%20%20%20%20%20%20try%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20Response%20response%20%3D%20httpClientHelper.getResponse(formatUrl)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20if%20(response%20%3D%3D%20null%20%7C%7C%20response.body()%20%3D%3D%20null)%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20LOGGER.info(%22get%20xndl%20dept_list%20result%20is%20null!%22)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20return%20null%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%7D%0A%20%20%20%20%20%20%20%20%20%20%20%20String%20result%20%3D%20response.body().string()%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20LOGGER.info(%22get%20xndl%20dept_list%20result%20is%20%3A%20%22%2Bresult)%3B%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20List%3CYfyDepartment%3E%20departmentList%20%3D%20new%20ArrayList%3C%3E()%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%2F%2F%20%24%E7%AC%A6%E5%8F%B7%E9%9C%80%E8%A6%81%E5%8A%A0%E5%8F%8D%E6%96%9C%E6%9D%A0%EF%BC%8C%E5%90%A6%E5%88%99%E4%BB%A3%E7%A0%81%E7%BC%96%E8%AF%91%E7%9A%84%E6%97%B6%E5%80%99%E4%BC%9A%E5%87%BA%E9%97%AE%E9%A2%98%0A%20%20%20%20%20%20%20%20%20%20%20%20JSONArray%20jsonArray%20%3D%20(JSONArray)%20JSONPath.extract(result%2C%20%22%5C%24.result%22)%3B%0A%0A%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20for%20(Object%20o%20%3A%20jsonArray)%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20JSONObject%20jsonObject%20%3D%20(JSONObject)%20o%3B%0A%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20String%20orgType%20%3D%20jsonObject.getString(%22orgType%22)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20if(%221%22.equals(orgType))%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20LOGGER.info(%22top%20department%20info%20is%20%3A%22%20%2B%20JSON.toJSONString(jsonObject))%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20continue%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7D%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20String%20departId%20%3D%20jsonObject.getString(%22departId%22)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20String%20departName%20%3D%20jsonObject.getString(%22departName%22)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20String%20departOrder%20%3D%20jsonObject.getString(%22departOrder%22)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20String%20parentDepartId%20%3D%20jsonObject.getString(%22parentDepartId%22)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20YfyDepartment%20department%20%3D%20new%20YfyDepartment()%3B%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20if(!Objects.isNull(departOrder))%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setOrder(100000L%20-%20Long.parseLong(departOrder))%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7D%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%2F%2F%20%E4%B8%80%E7%BA%A7%E9%83%A8%E9%97%A8%E6%8C%82%E5%88%B0%E9%A1%B6%E7%BA%A7%E9%83%A8%E9%97%A8%E4%B8%8B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20if(%222%22.equals(orgType))%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20parentDepartId%20%3D%20null%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7D%0A%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%2F%2F%20%E8%8E%B7%E5%8F%96%E9%83%A8%E9%97%A8%E7%AE%A1%E7%90%86%E5%91%98%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20JSONArray%20departLeaderList%20%3D%20jsonObject.getJSONArray(%22departLeaderList%22)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20for%20(Object%20object%20%3A%20departLeaderList)%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20JSONObject%20jsonObjectLead%20%3D%20(JSONObject)%20object%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20Boolean%20isLeader%20%3D%20jsonObjectLead.getBoolean(%22isLeader%22)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20if(isLeader)%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setDirectorId(jsonObjectLead.getString(%22salaryNum%22))%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7D%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7D%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setId(departId)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setName(departName)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setSpaceTotal(10L)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setParentId(parentDepartId)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setCollabAutoAccepted(true)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setPublicFolder(true)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setCreateTime(new%20Date())%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20department.setUpdateTime(new%20Date())%3B%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20departmentList.add(department)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20%7D%0A%0A%20%20%20%20%20%20%20%20%20%20%20%20return%20departmentList%3B%0A%0A%20%20%20%20%20%20%20%20%7D%20catch%20(Exception%20e)%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20LOGGER.info(%22get%20xndl%20dept_list%20result%20error%2C%20error%3A%22%20%2B%20e)%3B%0A%20%20%20%20%20%20%20%20%20%20%20%20return%20null%3B%0A%20%20%20%20%20%20%20%20%7D%0A%0A%20%20%20%20%7D%0A%7D%0A%0A");


        codeScriptSyncHandler.getYfyDepartmentsByConfigValueBox(departments, codeScriptValueBox);

        System.out.println(JSON.toJSONString(departments));

    }

}
