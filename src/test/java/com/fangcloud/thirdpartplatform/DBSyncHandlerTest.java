package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.DepValueBoxDto;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.SqlTaskConfigDto;
import com.fangcloud.thirdpartplatform.entity.dto.ValueBoxDto;
import com.fangcloud.thirdpartplatform.service.impl.datasource.MysqlSyncHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DBSyncHandlerTest {

    @Resource
    private MysqlSyncHandler mysqlSyncHandler;

    @Test
    public void mysqlSyncDept(){

        DepValueBoxDto depValueBoxDto = new DepValueBoxDto();
        depValueBoxDto.setUrl("********************************_ enterprise_01");
        depValueBoxDto.setUserName("root");
        depValueBoxDto.setPassword("123456");
        depValueBoxDto.setDeptSpace(50);
        depValueBoxDto.setFilterDept("1;2;3");

        SqlTaskConfigDto sqlTaskConfigDto = new SqlTaskConfigDto();
        sqlTaskConfigDto.setCompanyType("COLLEGE");


        List<SqlTaskConfigDto.SqlConfig> sqlConfigList = new ArrayList<>();
        SqlTaskConfigDto.SqlConfig sqlConfig = new SqlTaskConfigDto.SqlConfig();
        sqlConfig.setSql("select * from department where id < 12;");
        sqlConfig.setTaskType("ADD");
        sqlConfig.setTableType("TEACHER");
        sqlConfigList.add(sqlConfig);

        sqlTaskConfigDto.setSqlConfigs(sqlConfigList);

        depValueBoxDto.setSqlTaskConfig(sqlTaskConfigDto);

        DepValueBoxDto.SyncRule syncRule = new DepValueBoxDto.SyncRule();
        syncRule.setPublicFolder(true);
        syncRule.setCollabAutoAccepted(true);

        DepValueBoxDto.RootDept rootDept = new DepValueBoxDto.RootDept();
        rootDept.setFilter(true);
        rootDept.setRootId("100");

        syncRule.setRootDept(rootDept);

        depValueBoxDto.setSyncRule(syncRule);

        String s = JSON.toJSONString(depValueBoxDto);

        PlatformSyncConfig platformSyncConfig =new PlatformSyncConfig();
        platformSyncConfig.setValueBox(s);
        platformSyncConfig.setSourceType("MYSQL");
        platformSyncConfig.setEnterpriseId(21791);
        ExecuteResult executeResult = mysqlSyncHandler.syncDepartment(platformSyncConfig, 1);

    }
}
