package com.fangcloud.thirdpartplatform;

import java.util.Hashtable;
import javax.naming.Context;
import javax.naming.NamingException;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;


public class LdapDebugger {
    public static void main(String[] args) {
        // 启用JNDI调试
        System.setProperty("com.sun.jndi.ldap.trace.ber", "true");

        // 创建环境变量
        Hashtable<String, Object> env = new Hashtable<>();

        // 基本连接设置
        env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        env.put(Context.PROVIDER_URL, "ldap://************:389/DC=office,DC=fangcloud,DC=net");

        // 身份认证设置
        env.put(Context.SECURITY_PRINCIPAL, "yifangyun");
        env.put(Context.SECURITY_CREDENTIALS, "test123!");


        // // 基本连接设置
        // env.put(Context.INITIAL_CONTEXT_FACTORY, "com.sun.jndi.ldap.LdapCtxFactory");
        // env.put(Context.PROVIDER_URL, "ldap://************:389/DC=fangyun,DC=net");
        // env.put("javax.security.sasl.digestUri", "ldap/************");
        // env.put("java.naming.security.sasl.realm", "fangyun.net");
        // // 身份认证设置
        // env.put(Context.SECURITY_AUTHENTICATION, "DIGEST-MD5");
        // env.put(Context.SECURITY_PRINCIPAL, "ima");
        // env.put(Context.SECURITY_CREDENTIALS, "Test123?");

        // //SASL特定设置
        // // env.put("javax.security.sasl.mechanism", "DIGEST-MD5");
        // env.put("javax.security.sasl.qop", "auth-int,auth-conf");
        // env.put("javax.security.sasl.server.authentication", "true");
        // env.put("javax.security.sasl.strength", "high");
        // env.put("javax.security.sasl.policy.noplain", "true");

        // 二进制属性处理
        env.put("java.naming.ldap.attributes.binary", "objectGUID");

        // 超时设置
        env.put("com.sun.jndi.ldap.connect.timeout", "20000");
        env.put("com.sun.jndi.ldap.read.timeout", "20000");

        // 对象工厂
        //env.put(Context.FACTORY_OBJECT, "org.springframework.ldap.core.support.DefaultDirObjectFactory");

        // 尝试连接并输出详细信息
        try {
            System.out.println("Attempting to connect to LDAP server with the following environment:");
            for (String key : env.keySet()) {
                System.out.println(key + " = " + env.get(key));
            }

            System.out.println("\nInitiating connection...");
            DirContext ctx = new InitialDirContext(env);

            System.out.println("\nConnection successful!");
            System.out.println("LDAP Context: " + ctx.getNameInNamespace());

            // 执行一个简单的查询以验证连接
            System.out.println("\nAttempting a simple search...");
            ctx.getAttributes("");

            ctx.close();
        } catch (NamingException e) {
            System.err.println("\nConnection failed with error:");
            System.err.println(e.getMessage());
            System.err.println("\nDetailed exception:");
            e.printStackTrace();

            // 提供可能的解决方案
            System.out.println("\nPossible solutions:");
            System.out
                    .println("1. Try using SSL/TLS: Change URL to ldaps:// and add java.naming.security.protocol=ssl");
            System.out.println("2. Verify SASL mechanism is supported by the server");
            System.out.println("3. Check user credentials and permissions");
            System.out.println("4. Confirm server configuration allows this authentication method");
        }
    }
}
