package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.controller.MessageController;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.entity.input.WeixinWorkInitParams;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;


@RunWith(SpringRunner.class)
@SpringBootTest
public class MessageControllerTest {
    @Resource
    private MessageController messageController;

    @Test
      public void weixinWorkMessageTest(){
        messageController.weixinWorkMessage(createMessageParams());
     }

     @Test
     public void dingTalkMessageTest(){
         messageController.dingTalkMessage(createMessageParams());
     }

    public static void main(String[] args) {
        System.out.println(JSON.toJSONString(createMessageParams()));

        String s = "{\n" +
                "    \"content\": \"今天天气怎么样！\",\n" +
                "    \"customerId\": \"1\",\n" +
                "    \"ding_params\": {\n" +
                "        \"agentId\": \"1248671981\",\n" +
                "        \"appId\": \"dingt96mf1qx6uwdxrxa\",\n" +
                "        \"appSecret\": \"N7HVGYQ2I3BvJ5vejiBhJKN_3CsDLDVYXaKfN9id3bj1MxNWMHVISd9rarXtNRYG\",\n" +
                "        \"code\": \"937af95452903299bffc5217e4887668\",\n" +
                "        \"corpId\": \"ding26f34dfa755ebe0facaaa37764f94726\",\n" +
                "        \"corpSecret\": \"4f5309c21fab3d9ab047c48ae44dccd8\",\n" +
                "        \"enterprise_info\": {\n" +
                "            \"clientId\": \"9cf4e993-c9fd-4a11-86cf-4b94af108f27\",\n" +
                "            \"enterpriseId\": \"21791\",\n" +
                "            \"identifier\": \"\",\n" +
                "            \"loginType\": \"mobile\",\n" +
                "            \"platformId\": \"562\",\n" +
                "            \"redirectUrl\": \"\",\n" +
                "            \"type\": \"\"\n" +
                "        },\n" +
                "        \"host\": \"\",\n" +
                "        \"unionId\": \"\"\n" +
                "    },\n" +
                "    \"enterpriseId\": \"1\",\n" +
                "    \"h5_url\": \"1\",\n" +
                "    \"pic_url\": \"1\",\n" +
                "    \"receivers\": \"LiangXiaoSheng\",\n" +
                "    \"time\": \"\",\n" +
                "    \"title\": \"1\",\n" +
                "    \"type\": \"1\",\n" +
                "    \"webUrl\": \"1\",\n" +
                "    \"weixin_params\": {\n" +
                "        \"agentId\": \"1000021\",\n" +
                "        \"code\": \"fj3LJv0ptkBeHk7w7r5m7Ad91zNjlhbbYsXHnucN9Fs\",\n" +
                "        \"corpId\": \"wx512aca88ce71c168\",\n" +
                "        \"corpSecret\": \"kICzLA_vX5Au-JPDa71cOuuONUmnEYVwiWD2ayawG1k\",\n" +
                "        \"enterprise_info\": {\n" +
                "            \"clientId\": \"9cf4e993-c9fd-4a11-86cf-4b94af108f27\",\n" +
                "            \"enterpriseId\": \"21791\",\n" +
                "            \"identifier\": \"\",\n" +
                "            \"loginType\": \"mobile\",\n" +
                "            \"platformId\": \"562\",\n" +
                "            \"redirect_url\": \"\",\n" +
                "            \"type\": \"\"\n" +
                "        }\n" +
                "    }\n" +
                "}";
        MessageParams messageParams = JSON.parseObject(s, MessageParams.class);
        System.out.println();
    }


     static MessageParams createMessageParams(){
         MessageParams messageParams = new MessageParams();
         messageParams.setTime("");
         messageParams.setReceivers("FB20010001");
         messageParams.setType("1");
         messageParams.setContent("今天天气怎么样！");
         messageParams.setTitle("1");
         messageParams.setWebUrl("1");
         messageParams.setH5Url("1");
         messageParams.setCustomerId("1");
         messageParams.setEnterpriseId("1");
         messageParams.setPicUrl("1");

         messageParams.setDingTalkInitParams(LoginControllerTest.createDingTalkInitParams());
         messageParams.setWeixinWorkInitParams(LoginControllerTest.createWeixinWorkInitParams());
         return messageParams;
     }

}
