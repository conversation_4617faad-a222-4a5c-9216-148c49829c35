package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.service.SyncPublishService;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ApiSyncHandlerTest extends ThirdPartPlatformApplicationTests {

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    PlatformSyncConfigMapper platformSyncConfigMapper;

    @Test
    public void apiSyncDept() {
        PlatformSyncConfig config = new PlatformSyncConfig();
        config.setSourceType("Api");
        APIConfigValueBox apiConfigValueBox = new APIConfigValueBox();
        apiConfigValueBox.setProtocol("https");
        apiConfigValueBox.setVerificationMode("other");
        APIConfigValueBox.APIConfig oauthApiConfig = new APIConfigValueBox.APIConfig();
        //apiConfigValueBox.setUserSpace(10);
        apiConfigValueBox.setDeptSpace(10);
        oauthApiConfig.setApi("apitest.linmara.com");
        oauthApiConfig.setRequestMethod("GET");
        DepConfigDto depConfigDto = new DepConfigDto();
        depConfigDto.setPublicFolder(true);
        depConfigDto.setCollabAutoAccepted(true);
//        UserConfigDto userConfigDto = new UserConfigDto();
//        userConfigDto.setEmailSuffix("360.cn");
//        userConfigDto.setSyncPhone(false);
//        apiConfigValueBox.setUserConfigDto(userConfigDto);
        apiConfigValueBox.setDepConfigDto(depConfigDto);
        List<APIParamConfigDto> oauthApiParamConfigDtos = new ArrayList<>();
        APIParamConfigDto header = new APIParamConfigDto();
        header.setName("Request_Type");
        header.setDataType("String");
        header.setValue("ALIGATEWAY");
        header.setParamWay("HEAD");
        APIParamConfigDto appKey = new APIParamConfigDto();
        appKey.setName("appKey");
        appKey.setDataType("String");
        appKey.setValue("204139046");
        appKey.setParamWay("PARAMS");
        APIParamConfigDto appSecret = new APIParamConfigDto();
        appSecret.setName("appSecret");
        appSecret.setDataType("String");
        appSecret.setValue("m8lyrAdE8cPmwepeRzRoi9q5A2Vtgsib");
        appSecret.setParamWay("PARAMS");
        APIParamConfigDto path = new APIParamConfigDto();
        path.setName("path");
        path.setDataType("String");
        path.setValue("/EC_Data/NDU");
        path.setParamWay("PARAMS");
        APIParamConfigDto pageNum = new APIParamConfigDto();
        pageNum.setName("pageNum");
        pageNum.setDataType("String");
        pageNum.setValue("1");
        pageNum.setParamWay("PARAMS");
        APIParamConfigDto pageSize = new APIParamConfigDto();
        pageSize.setName("pageSize");
        pageSize.setDataType("String");
        pageSize.setValue("1000");
        pageSize.setParamWay("PARAMS");
        oauthApiParamConfigDtos.add(header);
        oauthApiParamConfigDtos.add(appKey);
        oauthApiParamConfigDtos.add(appSecret);
        oauthApiParamConfigDtos.add(path);
        oauthApiParamConfigDtos.add(pageNum);
        oauthApiParamConfigDtos.add(pageSize);
        oauthApiConfig.setParamConfig(oauthApiParamConfigDtos);
        apiConfigValueBox.setApiConfig(oauthApiConfig);
        config.setValueBox(JSON.toJSONString(apiConfigValueBox));


        /*APIConfigValueBox.APIConfig apiConfig = new APIConfigValueBox.APIConfig();
        apiConfig.setApi("api.geelytech.com/pc-gateway/enterprise-wechat/internal/usercenter/org/list/all");
        apiConfig.setRequestMethod("POST");
        List<APIParamConfigDto> apiParamConfigDtos = new ArrayList<>();
        List<APIResultConfigDto> apiParams = new ArrayList<>();
        APIParamConfigDto token = new APIParamConfigDto();
        token.setName("token");
        token.setDataType("String");
        token.setValue("JbSJwQvjLwGPvvBA1whv8TXShhtWOUaM");
        token.setParamWay("HEAD");
        APIParamConfigDto pageNoParams = new APIParamConfigDto();
        pageNoParams.setName("pageNo");
        pageNoParams.setValue("1");
        pageNoParams.setDataType("int");
        pageNoParams.setParamWay("BODY");
        pageNoParams.setFormat("++");
        APIParamConfigDto pageSizeParams = new APIParamConfigDto();
        pageSizeParams.setName("pageSize");
        pageSizeParams.setValue("1000");
        pageSizeParams.setDataType("int");
        pageSizeParams.setParamWay("BODY");
        APIParamConfigDto code = new APIParamConfigDto();
        code.setName("code");
        code.setValue("tJ9yAJAv-yasyd257-WFgVSl");
        code.setDataType("String");
        code.setParamWay("BODY");
        apiParamConfigDtos.add(token);
        apiParamConfigDtos.add(pageNoParams);
        apiParamConfigDtos.add(pageSizeParams);
        apiParamConfigDtos.add(code);
        apiConfig.setParamConfig(apiParamConfigDtos);
        APIResultConfigDto gh = new APIResultConfigDto();
        gh.setName("id");
        gh.setDataType("int");
        gh.setMappingName("id");
        APIResultConfigDto dwh = new APIResultConfigDto();
        dwh.setName("parentId");
        dwh.setDataType("int");
        dwh.setMappingName("parentId");
        APIResultConfigDto xm = new APIResultConfigDto();
        xm.setName("name");
        xm.setDataType("String");
        xm.setMappingName("name");
        apiParams.add(gh);
        apiParams.add(dwh);
        apiParams.add(xm);
        apiConfig.setResultConfig(apiParams);
        APIConfigValueBox.VerifyConfig verifyConfig = new APIConfigValueBox.VerifyConfig();
        verifyConfig.setPath("$.code");
        verifyConfig.setRuleScript("code == 0");
        apiConfig.setResultPath("$.data.data");
        apiConfig.setResultVerify(verifyConfig);
        APIConfigValueBox.VerifyConfig pageVerifyConfig = new APIConfigValueBox.VerifyConfig();
        pageVerifyConfig.setPath("$.data.data");
        pageVerifyConfig.setRuleScript("data.size() <= 0");
        apiConfig.setPagedVerify(pageVerifyConfig);
        apiConfigValueBox.setApiConfig(apiConfig);
        config.setValueBox(JSON.toJSONString(apiConfigValueBox));

        System.out.println(JSON.toJSONString(apiConfigValueBox));*/
        //apiSyncHandler.syncUser(config,1213);
        PlatformSyncConfig syncConfig = platformSyncConfigMapper.selectByPrimaryKey(90);
        apiSyncHandler.syncDepartment(syncConfig,1231);
    }
}
