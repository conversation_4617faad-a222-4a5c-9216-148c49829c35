package com.fangcloud.thirdpartplatform;

import com.alibaba.cloudapi.sdk.constant.SdkConstant;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;
import org.junit.Test;

import java.io.IOException;

public class AliApiGatewayTest extends ThirdPartPlatformApplicationTests {

    static {
        //HTTPS Client init
        HttpClientBuilderParams httpsParam = new HttpClientBuilderParams();
        httpsParam.setAppKey("204139046");
        httpsParam.setAppSecret("m8lyrAdE8cPmwepeRzRoi9q5A2Vtgsib");
        HttpsApiClientGBMAX_Data.getInstance().init(httpsParam);


    }


    @Test
    public void DeptTest() {
        ApiResponse response = HttpsApiClientGBMAX_Data.getInstance().DetData(1, 2000);
        try {
            System.out.println(getResultString(response));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }


    @Test
    public void UserTest() {
        ApiResponse response = HttpsApiClientGBMAX_Data.getInstance().UserData(1,2000);
        try {
            System.out.println(getResultString(response));
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String getResultString(ApiResponse response) throws IOException {
        StringBuilder result = new StringBuilder();
        result.append("Response from backend server").append(SdkConstant.CLOUDAPI_LF).append(SdkConstant.CLOUDAPI_LF);
        result.append("ResultCode:").append(SdkConstant.CLOUDAPI_LF).append(response.getCode()).append(SdkConstant.CLOUDAPI_LF).append(SdkConstant.CLOUDAPI_LF);
        if (response.getCode() != 200) {
            result.append("Error description:").append(response.getHeaders().get("X-Ca-Error-Message")).append(SdkConstant.CLOUDAPI_LF).append(SdkConstant.CLOUDAPI_LF);
        }

        result.append("ResultBody:").append(SdkConstant.CLOUDAPI_LF).append(new String(response.getBody(), SdkConstant.CLOUDAPI_ENCODING));

        return result.toString();
    }
}
