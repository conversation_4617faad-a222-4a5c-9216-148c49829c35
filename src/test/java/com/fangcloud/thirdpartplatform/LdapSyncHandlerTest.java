package com.fangcloud.thirdpartplatform;

import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ADFilterDto;
import com.fangcloud.thirdpartplatform.entity.dto.ADValueBoxDto;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.service.impl.datasource.LdapSyncHandler;
import com.fangcloud.thirdpartplatform.source.ldap.LdapConfigProperties;
import com.fangcloud.thirdpartplatform.source.ldap.LdapTemplateFactory;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.query.LdapQuery;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.ldap.query.LdapQueryBuilder.query;

public class LdapSyncHandlerTest {

    @Test
    public void testAd() {
//
//        LdapConfigProperties properties = LdapConfigProperties.builder()
//                .userName("yifangyun")
//                .url("ldap://************:389")
//                .base("DC=office,DC=fangcloud,DC=net")
//                .password("test123!")
//                .domainName("")
//                .build();


//        LdapConfigProperties properties = LdapConfigProperties.builder()
//                .userName("ldap.app")
//                .url("ldap://10.10.1.20:389")
//                .base("OU=知识城集团,DC=kci-gz,DC=com")
//                .password("Kci&#2023")
//                .domainName("")
//                .build();


/*        LdapConfigProperties properties = LdapConfigProperties.builder()
                .userName("360yun")
                .url("ldap://10.10.1.10:389")
                .base("OU=知识城(广州)投资集团有限公司,DC=kcggz,DC=com")
                .password("Aug#1+7=8.com")
                .domainName("")
                .build();*/

        LdapConfigProperties properties = LdapConfigProperties.builder()
                .url("ldaps://JYOA.JEYOUPHARMA.COM:636")
                .base("OU=外包员工账号,OU=员工账户,OU=账户与组,OU=SH1 上海济煜医药,OU=济煜OA办公域,DC=JYOA,DC=JEYOUPHARMA,DC=COM")
                .userName("JYOA_CDSrv")
                .password("Jy202505!9")
                .isSasl(true)
                .build();

/*        LdapConfigProperties properties = LdapConfigProperties.builder()
                .url("ldap://JMOA.JEMINCARE.COM")
                .base("OU=Users,OU=ShangHai,DC=jmoa,DC=jemincare,DC=com")
                //.userName("CN=admin,OU=yifangyun,DC=fangyun,DC=net")
                .userName("JMOA_CDSrv")
                .password("Jy202505!9")
                .isSasl(true)
                .build();*/

/*        LdapConfigProperties properties = LdapConfigProperties.builder()
                .url("ldap://47.96.157.80:389")
                .base("DC=fangyun,DC=net")
                .userName("admin")
                .password("Test123?")
                .domainName("@fangyun.net")
                .isSasl(true)
                .build();*/

    LdapTemplate template = LdapTemplateFactory.getTemplate(properties);
    //template.setIgnorePartialResultException(true);

    // 设置引用处理
    //System.setProperty("java.naming.referral", "follow");

    LdapQuery ldapQuery2 = query().
            where("objectclass").is("organizationalUnit");
    List<String> organizations = new ArrayList<>();

        try {
            template.search(ldapQuery2, (AttributesMapper<String>) attributes -> {
                String attribute = attributes.get("distinguishedname").get().toString().trim() ;
                organizations.add(attribute);
                System.out.println(attribute);
                return attribute;
            });
        } catch (Exception e) {
            System.out.println(e);
        }

    System.out.println("找到 " + organizations.size() + " 个组织单位");

        LdapQuery ldapQuery3 = query()
                .where("objectclass").is("user")
                .and("objectcategory").is("person");
        List<String> persion = new ArrayList<>();

    try {
        template.search(ldapQuery3, (AttributesMapper<String>) attributes -> {
            try {
                if (attributes.get("sAMAccountName") != null &&
                    attributes.get("sAMAccountName").get().toString().trim().equals("he.zhiwei")) {
                    System.out.println(attributes.getAll());
                }

                if (attributes.get("distinguishedname") != null &&
                    attributes.get("sAMAccountName") != null &&
                    attributes.get("userprincipalname") != null) {

                    String attribute = attributes.get("distinguishedname").get().toString().trim() +
                            "|" + attributes.get("sAMAccountName").get().toString().trim() +
                            "|" + attributes.get("userprincipalname").get().toString().trim();

                    persion.add(attribute);
                    System.out.println(attribute);
                    return attribute;
                }
                return null;
            } catch (Exception e) {
                System.out.println("处理用户属性出错: " + e.getMessage());
                return null;
            }
        });
    } catch (Exception e) {
        System.out.println("查询用户出错: " + e.getMessage());
        e.printStackTrace();
    }

    System.out.println("找到 " + persion.size() + " 个用户");
    }


    @Test
    public void dingTalkAutoLogin(){
        PlatformSyncConfig config = new PlatformSyncConfig();
        config.setSourceType("AD");
        ADValueBoxDto adValueBoxDto = new ADValueBoxDto();
        adValueBoxDto.setBaseOu("DC=office,DC=fangcloud,DC=net");
        adValueBoxDto.setEmailSuffix("360.cn");
        ADValueBoxDto.ADField fieldU = new ADValueBoxDto.ADField();
        fieldU.setId("sAMAccountName");
        fieldU.setName("name");

        adValueBoxDto.setField(fieldU);
        ADFilterDto filter = new ADFilterDto();
        filter.setOu("OU=hr");
        //filter.setGroupName("CN=TEST,OU=dev,OU=一方云,DC=office,DC=fangcloud,DC=net");
        adValueBoxDto.setFilter(filter);
        adValueBoxDto.setPassword("test123!");
        adValueBoxDto.setUrl("ldap://************:389");
        adValueBoxDto.setUserName("yifangyun");
        ADValueBoxDto.SearchBaseOU search = new ADValueBoxDto.SearchBaseOU();
        search.setBaseOu("ou=一方云");
        adValueBoxDto.setSearch(search);
        adValueBoxDto.setUserSpace(5);
        adValueBoxDto.setDeptSpace(5);
        config.setValueBox("{\"userName\":\"yifangyun\",\"url\":\"ldap://************:389\",\"password\":\"test123!\",\"baseOu\":\"DC=office,DC=fangcloud,DC=net\",\"repeatHandle\":\"OVER\",\"field\":{\"name\":\"CN\",\"mail\":\"mail\",\"id\":\"objectguid\",\"deptId\":\"objectGUID\",\"phone\":\"mobile\",\"order\":\"description\"},\"userSpace\":5,\"search\":{\"baseOu\":\"ou=一方云\"},\"filter\":{\"depts\":\"\",\"groupName\":\"\",\"users\":\"\",\"topDept\":\"description\"},\"syncRule\":{\"publicFolder\":true,\"collabAutoAccepted\":true,\"positiveOrder\":false},\"sqlTaskConfig\":{\"deleteIfExist\":false}}");

        LdapSyncHandler ldapSyncHandler = new LdapSyncHandler();
        System.out.println(config.getValueBox());
        System.out.println(StringUtils.isNotBlank(""));
        ExecuteResult executeResult = ldapSyncHandler.syncDepartment(config, 1);
//        ADValueBoxDto.ADField fieldD = new ADValueBoxDto.ADField();
//        fieldD.setId("objectGUID");
//        fieldD.setName("name");
//        adValueBoxDto.setField(fieldD);
//        config.setValueBox(JSON.toJSONString(adValueBoxDto));
//        ldapSyncHandler.syncDepartment(config, 1);
//
//
//        PlatformSyncConfigParams platformSyncConfigParams = new PlatformSyncConfigParams();
//        platformSyncConfigParams.setConfigName("ad");
//        platformSyncConfigParams.setEnterpriseId(1);
//        platformSyncConfigParams.setSyncStatus(1);
//        platformSyncConfigParams.setSyncType("AD");
//        platformSyncConfigParams.setCron("0 1 1 1 1 1");
//        platformSyncConfigParams.setId(1);
//        platformSyncConfigParams.setSourceType("AD");
//        adValueBoxDto.setBaseOu("DC=office,DC=fangcloud,DC=net");
//        adValueBoxDto.setEmailSuffix("360.cn");
//        fieldU.setId("sAMAccountName");
//        fieldU.setName("name");
//
//        adValueBoxDto.setField(fieldU);
//        platformSyncConfigParams.setValueBox(JSON.toJSONString(adValueBoxDto));
//        System.out.println(JSON.toJSONString(platformSyncConfigParams));
//
//        ConnectionCheckParam connectionCheckParam = new ConnectionCheckParam();
//        connectionCheckParam.setBase("DC=office,DC=fangcloud,DC=net");
//        connectionCheckParam.setPassword("test123!");
//        connectionCheckParam.setUrl("ldap://************:389");
//        connectionCheckParam.setUserName("yifangyun");
//        connectionCheckParam.setSourceType(SourceTypeEnum.AD.getDesc());
//        System.out.println(JSON.toJSONString(connectionCheckParam));
//
//
//        PlatformSyncTaskRecordListParam platformSyncTaskRecordListParam = new PlatformSyncTaskRecordListParam();
//        platformSyncTaskRecordListParam.setSyncTaskType("DEPT");
//        platformSyncTaskRecordListParam.setEnterpriseId(1);
//        platformSyncTaskRecordListParam.setPageNo(1);
//        platformSyncTaskRecordListParam.setPageSize(10);
//
//        PlatformSyncTaskRecordResponse platformSyncTaskRecordResponse = new PlatformSyncTaskRecordResponse();
//        ExecuteResult executeResult = new ExecuteResult();
//        executeResult.setTotalRows(100);
//        executeResult.setAddRows(90);
//        executeResult.setUpdateRows(0);
//        executeResult.setErrorRows(10);
//        platformSyncTaskRecordResponse.setSyncResult("失败");
//        platformSyncTaskRecordResponse.setSyncStatus(SyncResultStatusEnum.PROGRESSING.getStatus());
//        platformSyncTaskRecordResponse.setSyncStatusStr(SyncResultStatusEnum.PROGRESSING.getDesc());
//        platformSyncTaskRecordResponse.setConfigName("AD");
//        platformSyncTaskRecordResponse.setSyncStartTime("2022-03-17 00:00:00");
//        platformSyncTaskRecordResponse.setSyncDataCount(JSON.toJSONString(executeResult));
//        platformSyncTaskRecordResponse.setSyncEndTime("2022-03-17 01:00:00");
//        platformSyncTaskRecordResponse.setSyncTypeStr("DEPT");
//
//        System.out.println(JSON.toJSONString(platformSyncTaskRecordListParam));
//        System.out.println(JSON.toJSONString(ResultUtils.getSuccessResult(Lists.newArrayList(platformSyncTaskRecordResponse))));
//
//        PlatformSyncTaskLogListParam platformSyncTaskLogListParam = new PlatformSyncTaskLogListParam();
//        platformSyncTaskLogListParam.setTaskId(1);
//        platformSyncTaskLogListParam.setPageNo(1);
//        platformSyncTaskLogListParam.setPageSize(10);
//        System.out.println(JSON.toJSONString(platformSyncTaskLogListParam));
//
//        PlatformSyncTaskLogResponse platformSyncTaskLogResponse = new PlatformSyncTaskLogResponse();
//        platformSyncTaskLogResponse.setSyncTaskId(1);
//        platformSyncTaskLogResponse.setCustomerId("1");
//        platformSyncTaskLogResponse.setReason("yuanyin");
//        platformSyncTaskLogResponse.setId(1);
//        platformSyncTaskLogResponse.setCreated("2022-03-17 01:00:00");
//        platformSyncTaskLogResponse.setEnterpriseId(1);
//        System.out.println(JSON.toJSONString(ResultUtils.getSuccessResult(Lists.newArrayList(platformSyncTaskLogResponse))));
    }
}
