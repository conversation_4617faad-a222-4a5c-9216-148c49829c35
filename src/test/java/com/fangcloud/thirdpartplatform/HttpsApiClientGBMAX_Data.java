package com.fangcloud.thirdpartplatform;

import com.alibaba.cloudapi.sdk.client.ApacheHttpClient;
import com.alibaba.cloudapi.sdk.enums.HttpMethod;
import com.alibaba.cloudapi.sdk.enums.ParamPosition;
import com.alibaba.cloudapi.sdk.enums.Scheme;
import com.alibaba.cloudapi.sdk.model.ApiRequest;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;

public class HttpsApiClientGBMAX_Data extends ApacheHttpClient {
    public final static String HOST = "apitest.linmara.com";
    static HttpsApiClientGBMAX_Data instance = new HttpsApiClientGBMAX_Data();
    public static HttpsApiClientGBMAX_Data getInstance(){return instance;}

    public void init(HttpClientBuilderParams httpClientBuilderParams){
        httpClientBuilderParams.setScheme(Scheme.HTTPS);
        httpClientBuilderParams.setHost(HOST);
        super.init(httpClientBuilderParams);
    }


    public ApiResponse  DetData(Integer pageNum, Integer pageSize) {
        String path = "/EC_Data/ND";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);
        return sendSyncRequest(request);
    }

    public ApiResponse  UserData(Integer pageNum, Integer pageSize) {
        String path = "/EC_Data/NDU";
        ApiRequest request = new ApiRequest(HttpMethod.GET , path);
        request.addParam("pageNum" , String.valueOf(pageNum) , ParamPosition.QUERY , true);
        request.addParam("pageSize" , String.valueOf(pageSize) , ParamPosition.QUERY , true);
        return sendSyncRequest(request);
    }

}