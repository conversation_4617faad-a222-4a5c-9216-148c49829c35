package com.fangcloud.thirdpartplatform;


import java.io.File;
import java.util.HashMap;
import java.util.Map;

import org.junit.Test;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;


public class AliFaruiTest {

        private static String calculateMD5(String input) {
                try {
                    java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
                    byte[] array = md.digest(input.getBytes());
                    StringBuilder sb = new StringBuilder();
                    for (byte b : array) {
                        sb.append(Integer.toHexString((b & 0xFF) | 0x100).substring(1, 3));
                    }
                    return sb.toString();
                } catch (java.security.NoSuchAlgorithmException e) {
                    e.printStackTrace();
                }
                return null;
        }

        @Test
        public void getToken() {
            String appId = "d80bf3df";
            String appKey = "a50b9d7866f5edc0";
            String randomValue = UUID.randomUUID().toString();
            String appSecret = "8c8f6ee22461e505";
            long timestamp = System.currentTimeMillis();
            String text = appId+appKey+timestamp+randomValue+appSecret;
            String signature = calculateMD5(text);
            String thirdUserId = "2521355";
            String thirdTenantId = "77";
            String thirdAppId = "d80bf3df";
            String thirdCheckCode = "00004";

            JSONObject jsonObject = JSONUtil.createObj();
            jsonObject.set("appId", appId);
            jsonObject.set("appKey", appKey);
            jsonObject.set("signature", signature);
            jsonObject.set("timestamp", timestamp);
            jsonObject.set("random", randomValue);
            jsonObject.set("thirdAppId", thirdAppId);
            jsonObject.set("thirdCheckCode", thirdCheckCode);
            jsonObject.set("thirdUserId", thirdUserId);
            jsonObject.set("thirdTenantId", thirdTenantId);
            
            System.out.println(jsonObject);
            String result = getResponse("/open/thirdLogin/getLoginTempCode", jsonObject.toString());
            JSONObject jsonResult = JSONUtil.parseObj(result);
            if (jsonResult.containsKey("data")) {
                String token = jsonResult.getByPath("data").toString();
                System.out.println(token);
            } else {
                throw new RuntimeException("No 'data' field in the response: " + result);
            }

        }
        
        /**
         * +
         * "&sideFlagHide=true&stepFlagHide=true&breadFlagHide=true&ruleHideFlag=true&logoHideFlag=true";
         */
        @Test
        public void testCreate() {
            String appId = "d80bf3df";
            String appKey = "a50b9d7866f5edc0";
            String randomValue = UUID.randomUUID().toString();
            String appSecret = "8c8f6ee22461e505";
            long timestamp = System.currentTimeMillis();
            String text = appId+appKey+timestamp+randomValue+appSecret;
            String signature = calculateMD5(text);
            String fileUrl = "https://download01.fangcloud.com/download/de079755326f464983383c4f61688057/9641dff3e3fba168bfa244cd79c4c9e8c426ddf023b0721f346b5d3ce98d37e5/%E7%A7%9F%E8%B5%81%E5%90%88%E5%90%8C%E5%AE%A1%E6%A0%B8%E6%8A%A5%E5%91%8A.docx"; // 替换为实际文件链接
            String fileName = "file-name.docx";
            String thirdUserId = "2521355";
            String thirdTenantId = "77";
            String standpoint = "0";

            JSONObject jsonObject = JSONUtil.createObj();
            jsonObject.set("appId", appId);
            jsonObject.set("appKey", appKey);
            jsonObject.set("signature", signature);
            jsonObject.set("timestamp", timestamp);
            jsonObject.set("random", randomValue);
            jsonObject.set("fileUrl", fileUrl);
            jsonObject.set("fileName", fileName);
            jsonObject.set("standpoint", standpoint);
            jsonObject.set("thirdUserId", thirdUserId);
            jsonObject.set("thirdTenantId", thirdTenantId);

            System.out.println(getResponse("/open/contract/v1/createReviewRecord", jsonObject.toString()));
        }



        @Test
        public void testCreateByLocalFile() {
            String appId = "d80bf3df";
            String appKey = "a50b9d7866f5edc0";
            String randomValue = UUID.randomUUID().toString();
            String appSecret = "8c8f6ee22461e505";
            long timestamp = System.currentTimeMillis();
            String text = appId + appKey + timestamp + randomValue + appSecret;
            String signature = calculateMD5(text);
            String file = "/Users/<USER>/Downloads/光伏电站合作共建协议.docx"; // 替换为实际文件链接
            String fileName = "file-name.docx";
            String thirdUserId = "2521355";
            String thirdTenantId = "77";
            String standpoint = "0";
            
            File fileO = FileUtil.file(file);
            Map<String, Object> parmas = new HashMap();
            parmas.put("appId", appId);
            parmas.put("appKey", appKey);
            parmas.put("signature", signature);
            parmas.put("timestamp", timestamp);
            parmas.put("random", randomValue);
            parmas.put("file", fileO);
            parmas.put("fileName", fileName);
            parmas.put("standpoint", standpoint);
            parmas.put("thirdUserId", thirdUserId);
            parmas.put("thirdTenantId", thirdTenantId);

            System.out.println(getResponseWithFormData("/open/contract/v1/createReviewRecord", parmas));
        }


        private String getResponseWithFormData(String url, Map<String, Object> postData) {
            HttpResponse response = HttpUtil.createPost("https://tyfarui.biz.aliyun.com" + url)
            .form(postData)
            .execute();

            System.out.println("url " + url + "postData " + postData);
            String result = response.body().toString();
            System.out.println(result);
            return result;
        }


        private String getResponse(String url, String postData) {
            HttpRequest post = HttpRequest.post("https://tyfarui.biz.aliyun.com" + url)
                .header("Content-Type", "application/json")
                .body(postData);

            System.out.println("url " + url + "postData " + postData);
            HttpResponse response = post.execute();
            String result = response.body().toString();
            System.out.println(result);
            return result;
        }




}
