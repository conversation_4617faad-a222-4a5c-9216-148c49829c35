package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.utils.sm4.SM4Utils;
import com.jayway.jsonpath.JsonPath;
import org.junit.Test;

import javax.crypto.NoSuchPaddingException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;

public class NomalTest {
    @Test
    public void testJson(){
        String jsonresult = "{\"is_open_wechat_share\": true,\"is_open_dingding_share\": true,\"is_open_qq_share\": true,\"is_open_weixin_work_share\": false,\"host\": \"hybrid-yfytest-ldap.fangcloud.net\",\"product_id\": \"yfytest-ldap\",\"resource_id\": \"yfytest-ldap\",\"base_url\": \"https://yfytest.fangcloud.net/\",\"static_url\": \"https://yfytest.fangcloud.net/static/\",\"host_url\": \"https://hybrid-yfytest-ldap.fangcloud.net/\",\"ios_download_link\": \"https://www.baiud.com\",\"android_download_link\": \"https://www.baidu.com\",\"mac_download_link\": \"https://www.baidu.com\",\"windows_download_link\": \"https://www.baidu.com\",\"helper_center_url\": \"http://uchat.im-cc.com/webchat_new/static/html/index.html?ht=3823\",\"android_lowest_version\": \"0\",\"ios_lowest_version\": \"0\",\"mac_sync_lowest_version\": \"5.0.00000\",\"windows_sync_lowest_version\": \"5.0.00000\",\"windows_xp_lowest_version\": \"5.0.00000\",\"websocket_host\": \"wss://yfytest.fangcloud.net/websocket_proxy/\",\"cookie_domain\": \".fangcloud.net\",\"product_name_en\": \"yfytest-ldap\",\"product_name_zh_cn\": \"混合云盘\",\"product_name_zh_tw\": \"混合雲\",\"product_name_zh_hk\": \"混合雲\",\"resource_logos\": {\"web.logo.w248\": {\"en\": \"yfytest-ldap/web.logo.w248/en/logo_6f427df97546089ad6a08ce51951d16f_b05a.png\"},\"favicon\": {\"en\": \"yfytest-ldap/favicon/en/logo_c5d11e15c56d6baffc9364e471e4d547_b27b.ico\"},\"web.logo.w174\": {\"en\": \"yfytest-ldap/web.logo.w174/en/logo_9940081606f1bafa6dd64e065d870f68_258e.png\"},\"web.logo.w232\": {\"en\": \"yfytest-ldap/web.logo.w232/en/logo_47845efff31be29ca4a5b8c9936b3542_ac0e.png\"},\"web.logo.w188\": {\"en\": \"yfytest-ldap/web.logo.w188/en/logo_1d0709ac4710988dab55e827855c5f1f_8aa3.png\"},\"mobile.logo.w464\": {\"en\": \"yfytest-ldap/mobile.logo.w464/en/logo_3bf4e73deb1f2290fe9c54be2b64c0de_d0b3.png\"}},\"nc_windows_download_link\": \"https://www.baidu.com\",\"mac_special_download_link\": \"https://www.baidu.com\"}";

        //String resutl = JSON.parseObject(jsonresult).getString("domain_config");
        try {
            String resutl = JSONPath.extract(jsonresult, "$.resource_logos['web.logo.w232'].cn").toString();
            System.out.println(resutl);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }


    @Test
    public void testSM4Encode() {

        String aaaa = SM4Utils.webEncryptText("{\"userLoginInfo\": {\"username\": \"test1\",\"password\": \"111212121\",\"enterprise_id\": \"115\",\"product_id\": \"h3cyp\",\"domain\": \"h3c\"}}");
        System.out.println(aaaa);

        String bbbb = SM4Utils.webDecryptText("kNaahYUfgTSP5W2aFSblBi1jmPMppJH8cte5EEwQcFIHuBRWIX9E8oqLNLxFDMB7QbBYbRR2Lk/e9nd+Qx/94rm/0kuUJyctn6B5g+Uwn221ohL99nXwqxXHCE8UmKDRqpXWA7s4KRHrtwgvnGbpl+pYptaRwjUo5g6XZYw42QU=");

        System.out.println("aaa"+bbbb+"bbbb");

    }

    @Test
    public void testSM4Decode()
    {

    }

    @Test
    public void testJsonEmptyMap()
    {
        String str = JSONObject.toJSONString(new HashMap<>());
        str = String.valueOf(-1).replace("-1", "");
        System.out.println(str);
    }

    @Test
    public void testJsonPath()
    {
        String json = "{\"firstName\":\"John\",\"lastName\":\"doe\",\"age\":26,\"address\":{\"streetAddress\":\"naist street\",\"city\":\"Nara\",\"postalCode\":\"630-0192\"},\"phoneNumbers\":[{\"type\":\"iPhone\",\"number\":\"0123-4567-8888\"},{\"type\":\"home\",\"number\":\"0123-4567-8910\"}]}";

        Object titles = JsonPath.read(json, "$.phoneNumbers[?(@.type=='name')].number");
        System.out.println(titles);
    }

    private String getJsonString () {
        return "{\n" +
                "    \"errcode\": 0,\n" +
                "    \"errmsg\": \"ok\",\n" +
                "    \"userid\": \"<EMAIL>\",\n" +
                "    \"name\": \"夏劲松\",\n" +
                "    \"department\": [\n" +
                "        194\n" +
                "    ],\n" +
                "    \"position\": \"IT运维经理\",\n" +
                "    \"status\": 1,\n" +
                "    \"isleader\": 1,\n" +
                "    \"extattr\": {\n" +
                "        \"attrs\": [\n" +
                "            {\n" +
                "                \"name\": \"yfyphone\",\n" +
                "                \"value\": \"15021771270\",\n" +
                "                \"type\": 0,\n" +
                "                \"text\": {\n" +
                "                    \"value\": \"15021771270\"\n" +
                "                }\n" +
                "            },\n" +
                "                      {\n" +
                "                \"name\": \"yfymail\",\n" +
                "                \"value\": \"<EMAIL>\",\n" +
                "                \"type\": 0,\n" +
                "                \"text\": {\n" +
                "                    \"value\": \"<EMAIL>\"\n" +
                "                }\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"english_name\": \"\",\n" +
                "    \"telephone\": \"\",\n" +
                "    \"enable\": 1,\n" +
                "    \"hide_mobile\": 0,\n" +
                "    \"order\": [\n" +
                "        0\n" +
                "    ],\n" +
                "    \"external_profile\": {\n" +
                "        \"external_attr\": [],\n" +
                "        \"external_corp_name\": \"\"\n" +
                "    },\n" +
                "    \"main_department\": 194,\n" +
                "    \"alias\": \"\",\n" +
                "    \"is_leader_in_dept\": [\n" +
                "        1\n" +
                "    ],\n" +
                "    \"direct_leader\": []\n" +
                "}\n";
    }
}
