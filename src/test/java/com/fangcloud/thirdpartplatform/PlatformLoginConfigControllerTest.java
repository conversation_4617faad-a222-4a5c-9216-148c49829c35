package com.fangcloud.thirdpartplatform;

import com.fangcloud.thirdpartplatform.controller.PlatformLoginConfigController;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.PlatformLoginConfigParams;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginConfigResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class PlatformLoginConfigControllerTest {

    @Resource
    private PlatformLoginConfigController platformLoginConfigController;

    @Test
    public void testSave(){
        PlatformLoginConfigResponse data = platformLoginConfigController.getConfig(1).getData();

        PlatformLoginConfigParams platformLoginConfigParams = new PlatformLoginConfigParams();
        platformLoginConfigParams.setId(data.getId());
        platformLoginConfigParams.setEnterpriseId(data.getEnterpriseId());

        platformLoginConfigParams.setParamWay(data.getParamWay());
        platformLoginConfigParams.setProtocols(data.getProtocols());
        platformLoginConfigParams.setDataTypes(data.getDataTypes());
        platformLoginConfigParams.setRequestMethods(data.getRequestMethods());

        platformLoginConfigParams.setLoginSourceType(data.getLoginSourceType());
        platformLoginConfigParams.setPlatformLoginConfigValueBox(data.getPlatformLoginConfigValueBox());

        Result<Boolean> result = platformLoginConfigController.saveConfig(platformLoginConfigParams);
        System.out.println(result);

    }
    @Test
    public void testGet(){
        Result<PlatformLoginConfigResponse> config = platformLoginConfigController.getConfig(1);
        System.out.println(config);
    }
}
