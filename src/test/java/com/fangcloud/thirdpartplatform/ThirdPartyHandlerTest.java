package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.controller.PlatformSyncConfigController;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.DepConfigDto;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.ThirdPartyValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.UserConfigDto;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncConfigParams;
import com.fangcloud.thirdpartplatform.service.impl.custom.FTPServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.datasource.DingdingSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.datasource.QiyeWeixinSyncHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest
public class ThirdPartyHandlerTest {

    @Resource
    private QiyeWeixinSyncHandler weixinSyncHandler;

    @Resource
    private DingdingSyncHandler dingdingSyncHandler;

    @Resource
    private PlatformSyncConfigController platformSyncConfigController;

    @Resource
    private FTPServiceImpl ftpSyncDataController;

    @Test
    public void weixinSyncDept(){

        ThirdPartyValueBox thirdPartyValueBox = new ThirdPartyValueBox();

        thirdPartyValueBox.setUserSpace(5);
        thirdPartyValueBox.setDeptSpace(50);

        DepConfigDto depConfigDto = new DepConfigDto();
        depConfigDto.setPublicFolder(true);
        depConfigDto.setCollabAutoAccepted(true);

        UserConfigDto userConfigDto = new UserConfigDto();
        userConfigDto.setEmailSuffix("360.cn");
        userConfigDto.setSyncPhone(false);
        userConfigDto.setActivate(true);

        thirdPartyValueBox.setUserConfigDto(userConfigDto);
        thirdPartyValueBox.setDepConfigDto(depConfigDto);



        String s = JSON.toJSONString(thirdPartyValueBox);

        PlatformSyncConfig platformSyncConfig =new PlatformSyncConfig();
        platformSyncConfig.setValueBox(s);
        platformSyncConfig.setSourceType("");
        platformSyncConfig.setEnterpriseId(22068);
        ExecuteResult executeResult = weixinSyncHandler.syncDepartment(platformSyncConfig, 1);

    }

    @Test
    public void weixinSyncUser(){

        ThirdPartyValueBox thirdPartyValueBox = new ThirdPartyValueBox();

        thirdPartyValueBox.setUserSpace(5);
        thirdPartyValueBox.setDeptSpace(50);

        DepConfigDto depConfigDto = new DepConfigDto();
        depConfigDto.setPublicFolder(true);
        depConfigDto.setCollabAutoAccepted(true);

        UserConfigDto userConfigDto = new UserConfigDto();
        userConfigDto.setEmailSuffix("360.cn");
        userConfigDto.setSyncPhone(true);
        userConfigDto.setActivate(true);

        thirdPartyValueBox.setUserConfigDto(userConfigDto);
        thirdPartyValueBox.setDepConfigDto(depConfigDto);



        String s = JSON.toJSONString(thirdPartyValueBox);

        PlatformSyncConfig platformSyncConfig =new PlatformSyncConfig();
        platformSyncConfig.setValueBox(s);
        platformSyncConfig.setSourceType("");
        platformSyncConfig.setEnterpriseId(22068);
        ExecuteResult executeResult = weixinSyncHandler.syncUser(platformSyncConfig, 2);

    }

    @Test
    public void dingdingSyncDept(){

        ThirdPartyValueBox thirdPartyValueBox = new ThirdPartyValueBox();

        thirdPartyValueBox.setUserSpace(5);
        thirdPartyValueBox.setDeptSpace(50);

        DepConfigDto depConfigDto = new DepConfigDto();
        depConfigDto.setPublicFolder(true);
        depConfigDto.setCollabAutoAccepted(true);

        UserConfigDto userConfigDto = new UserConfigDto();
        userConfigDto.setEmailSuffix("360.cn");
        userConfigDto.setSyncPhone(false);
        userConfigDto.setActivate(true);

        thirdPartyValueBox.setUserConfigDto(userConfigDto);
        thirdPartyValueBox.setDepConfigDto(depConfigDto);



        String s = JSON.toJSONString(thirdPartyValueBox);

        PlatformSyncConfig platformSyncConfig =new PlatformSyncConfig();
        platformSyncConfig.setValueBox(s);
        platformSyncConfig.setSourceType("");
        platformSyncConfig.setEnterpriseId(22068);
        ExecuteResult executeResult = dingdingSyncHandler.syncDepartment(platformSyncConfig, 1);

    }

    @Test
    public void dingdingSyncUser(){

        ThirdPartyValueBox thirdPartyValueBox = new ThirdPartyValueBox();

        thirdPartyValueBox.setUserSpace(5);
        //thirdPartyValueBox.setDeptSpace(50);

        DepConfigDto depConfigDto = new DepConfigDto();
        depConfigDto.setPublicFolder(true);
        depConfigDto.setCollabAutoAccepted(true);

        UserConfigDto userConfigDto = new UserConfigDto();
        userConfigDto.setEmailSuffix("360.cn");
        userConfigDto.setSyncPhone(true);
        userConfigDto.setActivate(true);

        thirdPartyValueBox.setUserConfigDto(userConfigDto);
        //thirdPartyValueBox.setDepConfigDto(depConfigDto);



        String s = JSON.toJSONString(thirdPartyValueBox);

        PlatformSyncConfig platformSyncConfig =new PlatformSyncConfig();
        platformSyncConfig.setValueBox(s);
        platformSyncConfig.setSourceType("");
        platformSyncConfig.setEnterpriseId(22068);
        PlatformSyncConfigParams platformSyncConfigParams = new PlatformSyncConfigParams();
        platformSyncConfigParams.setConfigName("dingtalk");
        platformSyncConfigParams.setEnterpriseId(1);
        //platformSyncConfigParams.setSyncStatus(1);
        platformSyncConfigParams.setSyncType("USER");
        platformSyncConfigParams.setCron("0 1 1 1 1 1");
        platformSyncConfigParams.setSourceType("DING_TALK");
        platformSyncConfigParams.setValueBox(s);
        platformSyncConfigController.changeConfig(platformSyncConfigParams);
        ExecuteResult executeResult = dingdingSyncHandler.syncUser(platformSyncConfig, 1);

    }
    @Test
    public void testFTP(){
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("customFtpEnv","inside");
        jsonObject.put("ftpIp","*************");
        jsonObject.put("ftpPort",21);
        jsonObject.put("ftpUserName","root");
        jsonObject.put("ftpPassword","yifangyun123");
        jsonObject.put("ftpBaseDir","/var/ftp");
        ftpSyncDataController.sync_data(null,null,jsonObject);
    }
}
