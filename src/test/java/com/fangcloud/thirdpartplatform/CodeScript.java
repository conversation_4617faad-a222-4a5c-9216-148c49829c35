package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.login.LoginParamEnum;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.sync.common.entity.dto.YfyUser;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.util.Map;
import java.util.Objects;
import java.util.logging.Logger;


public class CodeScript {


    @Autowired
    HttpClientHelper httpClientHelper;

    private static final Logger LOGGER = Logger.getLogger(CodeScript.class.getName());

    /**
     * code_script示例代码，obj为传入的参数
     * @param obj
     * @return
     */
    public YfyUser execute(Object obj) throws IOException {
        Map<String, String> dataMap = (Map<String, String>) obj;

        YfyUser yfyUser = new YfyUser();


        Response response = httpClientHelper.getResponse("https://dev.usemock.com/641824bb71e37d4e5431a25d/login_test");

        String result = Objects.requireNonNull(response.body().string());
        LOGGER.info("getResponse result: " + result);
        // $符号需要加反斜杠，否则代码编译的时候会出问题
//        String loginName = (String) JSONPath.extract(result, "\$.data.user.loginName");
//        String userName = (String) JSONPath.extract(result, "\$.data.user.userName");

//        yfyUser.setId(loginName);
//        yfyUser.setFullName(userName);
        yfyUser.setStatus(dataMap.get(LoginParamEnum.USER_NAME.getType()));

        LOGGER.info("yfyUser info: " + JSON.toJSONString(yfyUser));
        return yfyUser;
    }
}

