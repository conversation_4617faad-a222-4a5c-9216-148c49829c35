package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSONObject;
import com.ccwork.AESEncryptUtil;
import com.fangcloud.thirdpartplatform.utils.CcworkUtils;
import org.junit.Test;

import java.util.Map;
import java.util.TreeMap;

public class CcworkTest {

    @Test
    public void testCBCencode()
    {
        String text = "success";
        String secretKey = "ytL46xLxVoXZLFujE1UNXXAKF1iEg2kZ";
        String result = CcworkUtils.CBCencrypt(secretKey, text);
        System.out.println(result);
        System.out.println(CcworkUtils.CBCdecrypt(secretKey, result));

    }

    @Test
    public void testCBCdecode()
    {
        String text = "Qvt0siFRpqJev4MG8nWBRg==";
        String secretKey = "ytL46xLxVoXZLFujE1UNXXAKF1iEg2kZ";

        System.out.println(CcworkUtils.CBCdecrypt(secretKey, text));

    }

    @Test
    public void testDecrypt()
    {
        String appSecret = "ytL46xLxVoXZLFujE1UNXXAKF1iEg2kZ";
        String token = "";
        String msgSignature = "1a234c78bd42c1f6563036fd5ec84e2b81b0f40e";
        String timeStamp = "1673341059";
        String nonce = "CEPnwHbJ";
        String encrypt = "3pHM9gaPglIAOwbeBRDPXkPnBkzINiDJ0dREN1JyzTqUrWIRusCFYe+aILAfXmlwlv48fC4hBwxQeMB5Tj0OOkgb/GKfBLhS/uvoHmtjQR4=";
        String str = AESEncryptUtil.decrypt(appSecret, token, msgSignature, timeStamp, nonce, encrypt);

        System.out.println(str);
    }

    @Test
    public void testGetSingHeader()
    {
        String body = "";
        JSONObject body1 = JSONObject.parseObject(body);
        Map<String, Object> params = new TreeMap<>();
        params.put("access_token", "kwquRV6wMzIF7CrWii136IpjUv5fnkso");
        params.put("did","268436389");
        params.put("page","1");
        Map<String, String> head = CcworkUtils.getSignatureHeader(body1, params, "ytL46xLxVoXZLFujE1UNXXAKF1iEg2kZ");
        System.out.println(head);
    }

    @Test
    public void testGetSign()
    {
        String body = "";
        JSONObject body1 = JSONObject.parseObject(body);
        Map<String, Object> params = new TreeMap<>();
        params.put("access_token", "wCR2T31Wljjn4hOJCFZSXXJl8xlI3oGL");
        params.put("did","0");
        params.put("page","1");
        String sign = CcworkUtils.encrypt("1673319090653", "1673319091", params, body1, "ytL46xLxVoXZLFujE1UNXXAKF1iEg2kZ");
        System.out.println(sign);
    }
}
