package com.fangcloud.thirdpartplatform;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.logging.Logger;

public class CodeScriptdlp {

    private static final Logger LOGGER = Logger.getLogger(CodeScriptdlp.class.getName());



    /**
     * 登录
     * @param obj
     * @return
     */
    public static Object execute(Object obj) throws Exception {
        try {
            LOGGER.info("CodeScriptdlp start!");
            JSONObject jsonObject = (JSONObject) obj;

            LOGGER.info("-------"+ JSON.toJSONString(jsonObject));
            LOGGER.info("CodeScriptdlp end!");


        }catch (Exception e){
            LOGGER.info("closeBpm error:" + e.getMessage());
        }
        return true;
    }


}