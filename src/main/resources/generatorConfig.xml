<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
		PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
		"http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<!-- context 是逆向工程的主要配置信息 -->
	<!-- id：起个名字 -->
	<!-- targetRuntime：设置生成的文件适用于那个 mybatis 版本 -->
	<context id="default" targetRuntime="MyBatis3">
		<!--optional,指在创建class时，对注释进行控制-->
		<commentGenerator>
			<property name="suppressDate" value="true"/>
			<!-- 是否去除自动生成的注释 true：是 ： false:否 -->
			<property name="suppressAllComments" value="true"/>
		</commentGenerator>

		<!--jdbc的数据库连接 wg_insert 为数据库名字-->
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
						connectionURL="***************************************************************************" userId="app"
						password="Bigbang!123"></jdbcConnection>
		<!--非必须，类型处理器，在数据库类型和java类型之间的转换控制-->
		<javaTypeResolver>
			<!-- 默认情况下数据库中的 decimal，bigInt 在 Java 对应是 sql 下的 BigDecimal 类 -->
			<!-- 不是 double 和 long 类型 -->
			<!-- 使用常用的基本类型代替 sql 包下的引用类型 -->
			<property name="forceBigDecimals" value="false"/>
		</javaTypeResolver>
		<!-- targetPackage：生成的实体类所在的包 -->
		<!-- targetProject：生成的实体类所在的硬盘位置 -->
		<javaModelGenerator targetPackage="com.fangcloud.thirdpartplatform.db.model"
							targetProject="src/main/java">
			<!-- 是否允许子包 -->
			<property name="enableSubPackages" value="false"/>
			<!-- 是否对modal添加构造函数 -->
			<property name="constructorBased" value="false"/>
			<!-- 是否清理从数据库中查询出的字符串左右两边的空白字符 -->
			<property name="trimStrings" value="true"/>
			<!-- 建立modal对象是否不可改变 即生成的modal对象不会有setter方法，只有构造方法 -->
			<property name="immutable" value="false"/>
		</javaModelGenerator>
		<!-- targetPackage 和 targetProject：生成的 mapper 文件的包和位置 -->
		<sqlMapGenerator targetPackage="mapper"
						 targetProject="src/main/resources">
			<!-- 针对数据库的一个配置，是否把 schema 作为字包名 -->
			<property name="enableSubPackages" value="false"/>
		</sqlMapGenerator>
		<!-- targetPackage 和 targetProject：生成的 interface 文件的包和位置 -->
		<javaClientGenerator type="XMLMAPPER"
							 targetPackage="com.fangcloud.thirdpartplatform.db.dao" targetProject="src/main/java">
			<!-- 针对 oracle 数据库的一个配置，是否把 schema 作为字包名 -->
			<property name="enableSubPackages" value="false"/>
		</javaClientGenerator>
<!--		<table tableName="platform_sync_config" domainObjectName="PlatformSyncConfig"-->
<!--			   enableCountByExample="false" enableUpdateByExample="false"-->
<!--			   enableDeleteByExample="false" enableSelectByExample="false"-->
<!--			   selectByExampleQueryId="false"></table>-->
		<!-- tableName是数据库中的表名，domainObjectName是生成的JAVA模型名，后面的参数不用改，要生成更多的表就在下面继续加table标签 -->
<!--		<table tableName="platform_sync_task_fail_logs" domainObjectName="PlatformSyncTaskFailLogs"-->
<!--			   enableCountByExample="false" enableUpdateByExample="false"-->
<!--			   enableDeleteByExample="false" enableSelectByExample="false"-->
<!--			   selectByExampleQueryId="false"></table>-->
<!--		&lt;!&ndash; tableName是数据库中的表名，domainObjectName是生成的JAVA模型名，后面的参数不用改，要生成更多的表就在下面继续加table标签 &ndash;&gt;-->
<!--		<table tableName="platform_sync_tasks_record" domainObjectName="PlatformSyncTaskRecord"-->
<!--			   enableCountByExample="false" enableUpdateByExample="false"-->
<!--			   enableDeleteByExample="false" enableSelectByExample="false"-->
<!--			   selectByExampleQueryId="false"></table>-->
		<!-- tableName是数据库中的表名，domainObjectName是生成的JAVA模型名，后面的参数不用改，要生成更多的表就在下面继续加table标签 -->
<!--		<table tableName="platform_login_config" domainObjectName="PlatformLoginConfig"-->
<!--			   enableCountByExample="false" enableUpdateByExample="false"-->
<!--			   enableDeleteByExample="false" enableSelectByExample="false"-->
<!--			   selectByExampleQueryId="false"></table>-->
		<table tableName="platform_async_queue" domainObjectName="PlatformAsynQueue"
			   enableCountByExample="false" enableUpdateByExample="false"
			   enableDeleteByExample="false" enableSelectByExample="false"
			   selectByExampleQueryId="false"></table>
	</context>
</generatorConfiguration>