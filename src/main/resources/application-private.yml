spring:
  profiles:
    active: ${env.active}
  main:
    lazy-initialization: true
  rabbitmq:
    enable: ${env.custom.mq.enable}
    host: ${env.custom.mq.host}
    port: ${env.custom.mq.port}
    username: ${env.custom.mq.username}
    password: ${env.custom.mq.password}
    template:
      exchange: job_queues_exchange
  datasource:
    druid:
      url: 'jdbc:mysql://${env.mysql.url}/cloudoffice?useUnicode=true&characterEncoding=utf8'
      username: ${env.mysql.username}
      password: ${env.mysql.password}
      driver-class-name: com.mysql.jdbc.Driver
  redis:
    host: ${env.redis.host}
    port: ${env.redis.port}
    password: ${env.redis.password}

custom:
  public: ${env.custom.public}
  base:
    url: ${env.custom.base.url}
    domain: ${env.custom.base.domain}
  http_client_type: ${env.custom.http_client_type}
  open:
    host:
      open: ${env.custom.open.host}
      openurl: ${env.custom.open.url}
    uri:
      get_login_url: '/api/internal/super_admin/get_login_url'
      sync_users: '/api/internal/platform/%s/sync_users'
    #从哪个应用调开放平台 1 表示v2
    service_id: ${env.custom.open.service_id}
    version: ${env.custom.open.version}
    secret: ${env.custom.open.secret}
  oauth:
    host:
      url: http://oauth-server-svc:8000
    uri:
      get_clients: '/internal/%s/client_list'
    #从哪个应用调开放平台 1 表示v2
    service_id: 1
    version: 1.0.1
    secret: ${env.custom.oauth.secret}

  v2:
    host:
      v2: ${env.custom.v2.host}
      v2url: ${env.custom.v2.url}
    uri:
      url1: '/internal_api/sync_account/mapping_user'
      url2: '/internal_api/sync_account/sync_users'
      url3: '/internal_api/sync_account/sync_depts'
    service_id: ${env.custom.v2.service_id}
    version: ${env.custom.v2.version}
    secret: ${env.custom.v2.secret}
  dingding:
    msg_token: 'a3db4d5791fff49d1be42b0b20beb3e401314c9cb09178de18237407d76adba5'
    debug: true
    oauth_url: "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=%s&response_type=code&scope=snsapi_auth&redirect_uri=%s"
  weixin:
    top_dept: ${env.custom.weixin.top_dept}
  tuitui:
    host: 'https://im.live.360.cn'
    sso_appid: '****************'
    secret_key: 'ewBLYlmya4sUru8EPMFoibJG2C39OHKC'
    message_host: 'http://alarm.im.qihoo.net'
    message_appid: '**********'
    message_secret: '69d9dc1882f67f5481f82b4de75aa59337410107'
  sync_user:
    set_department_id_null_enable: ${env.custom.sync_user.set_department_id_null_enable}
    set_user_active_close: ${env.custom.sync_user.set_user_active_close}
    user_name_suffix: ${env.custom.sync_user.user_name_suffix}
    disabled_user_name_suffix: ${env.custom.disabled_user_name_suffix }
    set_disabled_user_department_id_is_null: ${env.custom.set_disabled_user_department_id_is_null}
    set_ldap_check_sasl: ${env.custom.sync_user.set_ldap_check_sasl}