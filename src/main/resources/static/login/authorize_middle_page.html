<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>登录页</title>
</head>
<body>
<script th:src="@{/login/js/mobile-require-min-v2.1.11.js}"></script>
<script th:src="@{/login/js/jquery.js}"></script>
<script th:src="@{/login/js/jquery.i18n.min.js}"></script>
<script th:src="@{/login/js/jsencrypt.min.js}"></script>
<script type="text/javascript">
    const productId = '[[${product_id}]]';
    if (productId === 'sxjcy') {
        let code = '';
        const config = '[[${config}]]';
        app.require(['happ-mobile', 'ui-api'], function (mobile) {
            window.mobile = mobile;
            mobile.init();
            mobile.generateAccessCode({
                appId: "gzwp", callback: function (data) {
                    code = data.code;
                    console.log("code==", code);
                    window.location.href = "/platform/special/autologin?config=" + config + "&code=" + code;
                }
            });
        })

    }


    function ajaxGet(url, data) {
        console.log("url:" + url);
        $.ajax({
            method: "GET",
            url: url,
            data: data,
            async: false,
            success: function (data) {
                console.log(data)
            },
            error: function () {
                console.log("失败");
            }
        });
    }
</script>
</body>
</html>