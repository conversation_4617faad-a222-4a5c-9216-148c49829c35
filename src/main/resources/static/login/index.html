<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8"/>
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <link rel="shortcut icon" href="./image/favicon.ico" type="image/x-icon" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <link rel="stylesheet" th:href="@{/login/index.css}">
    <title>第三方登录页</title>
</head>
<body ontouchstart="">
<header class="yzz-header">
    <a href="#">
        <img class="yzz-logo" src="./image/logo-v2.png" alt="logo" />
    </a>
    <div class="yzz-lang">
        <span class="yzz-curlang" i18n="lang">简体中文</span
        ><i class="yzz-triangle"></i>
        <ul class="yzz-lang-select">
            <li data-lang="cn" class="j_switch_lang">简体中文</li>
            <li data-lang="en" class="j_switch_lang">English</li>
        </ul>
    </div>
</header>
<div class="yzz-main">
    <div class="bg"></div>
    <div class="yzz-login-third yzz-login" id='yzz-login-third'>
      <div class="third-container" id="third-container"></div>
      <a class="login-switch j_login_switch_third">账户密码登录</a>
    </div>
    <div class="yzz-login" id='yzz-login'>
        <div class="yzz-lg-title" i18n="login">登录</div>
        <div class="yzz-lg-msg j_errormsg"></div>
        <div class="yzz-lg-form">
            <div class="user-name j_userid">
                <input placeholder="账号"  autocomplete="off"/>
                <span i18n="userIdTip">请输入账号</span>
            </div>
            <div class="j_password" style="position: relative">
                <input
                        placeholder="密码"
                        type="password"
                        i18n="password"
                        i18n-only="placeholder"
                        autocomplete="off"
                        id="password_input"
                />
                <label for="password_input">
                    <img src="/platform/login/image/close_eye.png" id="eye" style="width: 15px; position: absolute;right: 10px;top: 14px;">
                </label>
                <span i18n="passwordTip">请输入密码</span>
            </div>
            <div class="captcha j_captcha">
              <input
                placeholder="验证码"
                i18n="captcha"
                i18n-only="placeholder"
              />
              <img src="" alt="验证码" class="j_captcha_img" />
              <span i18n="captchaTip">请输入验证码</span>
            </div>
            <button class="yzz-lg-btn j_login" i18n="login">登录</button>
            <button id="wechatLogin" class="enterprise-wechat-login j_wLogin" i18n="qiYeWeiXin">企业微信扫码登录</button>
            <a class="login-switch j_login_switch_auth">企业微信扫码登录</a>
        </div>
    </div>
</div>

<script>
    // 利用一个变量flag 两种状态  0 表示闭眼睛，不显示密码 ； 1 表示睁开眼睛，显示密码
    // 1.获取元素
    var eye = document.getElementById("eye");
    var input = document.querySelector("#password_input");
    // 2.注册事件 3.处理程序
    var flag = 0;
    eye.onclick=function(){
        if(flag==0){
            input.type="text"//表示此时input的属性变为输入文本，密码输入显示
            eye.src="/platform/login/image/eye.png";
            flag=1;//此时经过点击小眼睛状态变为打开 就表示此时flag的值为1
        }else{
            input.type="password"//表示此时input的属性变为输入密码，密码输入隐藏
            eye.src="/platform/login/image/close_eye.png";
            flag=0;

        }
    }
</script>
<!-- <div
  id="login-url"
  th:data-url="${template.login}"
  style="display: none"
></div>
<div
  id="weixin-host-url"
  th:data-url="${template.weixinhost}"
  style="display: none"
></div>
<div
  id="is-weixin"
  th:data-url="${template.isweixin}"
  style="display: none"
></div>
<div
  id="bg-h5"
  th:data-url="${template.bgH5Url}"
  style="display: none"
></div>
<div
  id="bg-web"
  data-url="./image/bg.png"
  style="display: none"
></div>
<div
  id="check-url"
  th:data-url="${template.check}"
  style="display: none"
></div>
<div
  id="captcha-url"
  th:data-url="${template.captcha}"
  style="display: none"
></div>
<div
  id="i18n-url"
  th:data-url="${template.i18n}"
  style="display: none"
></div>
<div
  id="logo-height"
  th:data-height="${template.logoHeight}"
  style="display: none"
></div> -->

<!--script src="http://wwcdn.weixin.qq.com/node/wework/wwopen/js/wwLogin-1.2.7.js"></script>
<script src="https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js"></script-->

<script th:src="@{/login/js/wwLogin-1.2.7.js}"></script>
<script th:src="@{/login/js/wwLogin-1.0.0.js}"></script>
<script th:src="@{/login/js/ddLogin.js}"></script>
<script th:src="@{/login/js/jquery.js}"></script>
<script th:src="@{/login/js/jquery.i18n.min.js}"></script>
<script th:src="@{/login/js/jsencrypt.min.js}"></script>
<script th:src="@{/login/js/index.js}"></script>


</body>
</html>
