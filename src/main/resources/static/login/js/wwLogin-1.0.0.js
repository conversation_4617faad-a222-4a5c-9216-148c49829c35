(function(window, document, undefined) {

    function WwLoginPrivate(data) {
        var host = data.host;
        var frame = document.createElement('iframe');
        var url = host + '/wwopen/sso/qrConnect?appid=' + data.appid + '&agentid=' + data.agentid + '&redirect_uri=' + data.redirect_uri + '&state=' + data.state + '&login_type=jssdk';
        url += data.style ? ('&style=' + data.style) : '';
        url += data.href ? ('&href=' + data.href) : '';
        frame.src = url;
        frame.frameBorder = '0';
        frame.allowTransparency = 'true';
        frame.scrolling = 'no';
        frame.width = '300px';
        frame.height = '400px';
        var el = document.getElementById(data.id);
        el.innerHTML = '';
        el.appendChild(frame);

        frame.onload = function() {
            if (frame.contentWindow.postMessage && window.addEventListener) {
                window.addEventListener('message', function (event) {
                    var hostArr = host.split(':');
                    if (hostArr[1] == 80) host = hostArr[0];
                    if (event.data && event.origin.indexOf(host) > -1) {
                        window.location.href = event.data;
                    }
                });
                frame.contentWindow.postMessage('ask_usePostMessage', '*');
            }
        };
    }

    window.WwLoginPrivate = WwLoginPrivate;
})(window, document);