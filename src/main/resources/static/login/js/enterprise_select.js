var defaultLang = "cn";
var needCaptcha = false;
var loginObj = {};

var apiConfig = {
    checkLogin: '/platform/authentication/login_check',
    getEnterpriseInfo: '/platform/authentication/get_enterprise_info',
    login: '/platform/authentication/internal_login',
    needCaptcha: '/platform/authentication/need_captcha',
    getCaptcha: '/platform/authentication/get_img_captcha'
};

// var apiConfig = {
//     checkLogin: 'http://127.0.0.1:4523/mock/952839/platform/login/loginCheck',
//     getEnterpriseInfo: 'http://127.0.0.1:4523/mock/952839/platform/oauth2/login'
// };

$(document).ready(function () {
    initPage('#enterprise-select-container');
});

function initPage(selectorId) {
    var $container = $(selectorId);

    if($container.length <= 0) {
        return
    }

    var $loginBtn = $(".j_login");
    var data = $('#enterprise_info').val();
    // {"enterprises":[{"id":1115,"product_id":"h3c","name":"企业A"},{"id":1116,"product_id":"h2c","name":"企业B"}]}
    var enterpriseInfo

    try {
        enterpriseInfo = JSON.parse(data);
    } catch(e) {
        enterpriseInfo = {enterprises: []};
    }

    let template = `
        <div class="enterprise-selector">
            <label class="name">
                <input type="radio" name="enterprise" id="{{id_alias}}" value="{{product_id}}" />
                {{name}}
            </label>
        </div>
    `


    let result = enterpriseInfo.enterprises.map(enterprise => {
        let temp = template
        for(var key in enterprise) {
            temp = temp.replace(`{{${key}}}`, enterprise[key])
        }
        return temp;
    }).join('');

    $container.html(result)

    $loginBtn.click(function () {
        if(validate()) {
            var enterpriseId = $("input[name=enterprise]:checked").attr("id");
            var productId = $("input[name=enterprise]:checked").attr("value");
            handle(enterpriseId, productId);
        }
    });

    function validate() {
        var enterpriseSelected = $("input[name=enterprise]").val();
        console.info(enterpriseSelected)
        if(enterpriseSelected) {
            return true
        }

        return false;
    }

    function handle(enterpriseId, productId) {
        var searchParams = new URLSearchParams(window.location.search);
        var jumper_url = "/platform/authentication/login?enterprise_id=" + enterpriseId + "&service="
            + encodeURIComponent(searchParams.get("service") + "?product_id=" + productId);
        window.location.href = jumper_url;
    }



}