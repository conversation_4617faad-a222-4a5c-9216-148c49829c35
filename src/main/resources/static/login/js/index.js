var defaultLang = "cn";
var needCaptcha = false;
var enterpriseId = getJsonFromUrl().enterprise_id;
var service = getJsonFromUrl().service;
var errorCode = getJsonFromUrl().errorCode;
var loginObj = {};

var apiConfig = {
    checkLogin: '/platform/authentication/login_check',
    getEnterpriseInfo: '/platform/authentication/get_enterprise_info',
    login: '/platform/authentication/internal_login',
    needCaptcha: '/platform/authentication/need_captcha',
    getCaptcha: '/platform/authentication/get_img_captcha'
};

// var apiConfig = {
//     checkLogin: 'http://127.0.0.1:4523/mock/952839/platform/login/loginCheck',
//     getEnterpriseInfo: 'http://127.0.0.1:4523/mock/952839/platform/oauth2/login'
// };

$(document).ready(function () {
    document.body.addEventListener("touchstart", function () {
    });

    // 判断登录是否过期
    // $.ajax({
    //     url: apiConfig.checkLogin + '?enterprise_id=' + enterpriseId,
    //     type: "GET",
    //     success: function (data) {
    //         // 成功自动重定向
    //         // needCaptcha = data.needCaptcha;
    //     }
    // });


    // 获取企业信息
    $.ajax({
        url: apiConfig.getEnterpriseInfo + '?enterprise_id=' + enterpriseId,
        type: "GET",
        success: function (res) {
            if (res.success) {
                initPage(res.data);
            }
        }
    });


    function initPage(data) {
        var image = new Image();

        // title
        document.title = data.title;

        // 选择指定的 link 元素
        var linkElement = $("link[rel='shortcut icon']");
        if (data.iconUrl) {
            linkElement.attr("href", data.iconUrl);
        }

        // loginType
        if (data.loginSourceType) {
            var types = data.loginSourceType.split(',');
            $('.j_userid input').attr('placeholder', data.accountPrompt);
            if (types.length === 2) {
                $('#loginSwitch').css('display', 'block');
                if (['WEIXIN', 'DINGTALK'].indexOf(types[0]) !== -1) {
                    loginObj.IMLoginType = types[0];
                } else {
                    loginObj.IMLoginType = types[1];
                }
                loginObj.isLoginSwitch = true;
                loginObj.curentTypeIsIM = true;
                $('#yzz-login').css('display', 'none');
                $('#yzz-login .login-switch').css('display', 'block').text(loginObj.IMLoginType === 'WEIXIN' ? '微信扫码登录' : '钉钉扫码登录');
                $('#yzz-login-third').css('display', 'block');
            } else {
                loginObj.isLoginSwitch = false;
                if (['WEIXIN', 'DINGTALK'].indexOf(types[0]) !== -1) {
                    loginObj.curentTypeIsIM = true;
                    $('#yzz-login').css('display', 'none');
                    $('#yzz-login-third').css('display', 'block');
                } else {
                    loginObj.curentTypeIsIM = false;
                    $('#yzz-login').css('display', 'block');
                    $('#yzz-login-third').css('display', 'none');
                }
                loginObj.IMLoginType = types[0];
                $('.login-switch').css('display', 'none');
            }

            if (loginObj.IMLoginType === 'WEIXIN') {
                $('#login-switch').text('企业微信扫码登录');
                if (data.apiHost == '' || data.apiHost === null) {
                    new WwLogin({
                        id: "third-container",
                        appid: data.corpId,
                        agentid: data.agentId,
                        redirect_uri: encodeURIComponent(data.redirectUri),
                        state: "",
                        href: "data:text/css;base64,LmltcG93ZXJCb3ggLnFyY29kZSB7d2lkdGg6IDIwMHB4O30KLmltcG93ZXJCb3ggLnRpdGxlIHtkaXNwbGF5OiBub25lO30KLmltcG93ZXJCb3ggLmluZm8ge3dpZHRoOiAyMDBweDt9Ci5zdGF0dXNfaWNvbiB7ZGlzcGxheTogbm9uZSAgIWltcG9ydGFudH0KLmltcG93ZXJCb3ggLnN0YXR1cyB7dGV4dC1hbGlnbjogY2VudGVyO30g",
                        lang: "zh",
                    });
                }else {
                    window.WwLoginPrivate({
                        id: "third-container",
                        appid: data.corpId,
                        agentid: data.agentId,
                        redirect_uri: encodeURIComponent(data.redirectUri),
                        state: "",
                        host: data.apiHost,
                    });
                }
            }

            if (loginObj.IMLoginType === 'DINGTALK') {
                var redirect_url = encodeURIComponent(data.redirectUri);
                DDLogin({
                    id: "third-container",
                    goto: encodeURIComponent('https://oapi.dingtalk.com/connect/qrconnect?appid=' + data.corpId + '&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=' + redirect_url),
                    style: "border:none;background-color:#FFFFFF;",
                    width : "365",
                    height: "400"
                });
                var handleMessage = function (event) {
                    var origin = event.origin;
                    console.log("origin", event.origin);
                    if( origin == "https://login.dingtalk.com" ) { //判断是否来自ddLogin扫码事件。
                        var loginTmpCode = event.data;
                        //获取到loginTmpCode后就可以在这里构造跳转链接进行跳转了
                        console.log("loginTmpCode", loginTmpCode);
                        window.location.href = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=" + data.corpId + "&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=" + redirect_url + "&loginTmpCode=" + loginTmpCode;
                    }
                };
                if (typeof window.addEventListener != 'undefined') {
                    window.addEventListener('message', handleMessage, false);
                } else if (typeof window.attachEvent != 'undefined') {
                    window.attachEvent('onmessage', handleMessage);
                }
            }

            if (loginObj.curentTypeIsIM && errorCode) {
                var errmsg = {
                    '601': '未获取到用户信息',
                    '602': '扫码登录异常'
                };
                $('#yzz-login').append('<div class="error-msg">' + errmsg[errorCode] + '</div>');
            }
        }


        $('.j_login_switch_auth').click(function() {
            $('#yzz-login').css('display', 'none');
            $('#yzz-login-third').css('display', 'block');
        });

        $('.j_login_switch_third').click(function() {
            $('#yzz-login').css('display', 'block');
            $('#yzz-login-third').css('display', 'none');
        });

        // logo
        var $logo = $(".yzz-header .yzz-logo");
        // $logo.attr('src', data.logoUrl || './image/logo-v2.png');
        $logo.attr('src', data.logoUrl);
        $(".yzz-header .yzz-logo").css('height', '50px').css('top', '50%');

        // background
        if (document.documentElement.clientWidth <= 768) {
            image.src = data.h5BackgroundImgUrl || '/platform/login/image/bg_h5.png';
        } else {
            image.src = data.webBackgroundImgUrl || '/platform/login/image/bg.png';
        }
        $(".yzz-main .bg").css('background-image', 'url(' + image.src + ')');


        // TODO 国际化处理
        function getI18nUrl() {
            return '/platform/login/i18n';
        }

        image.onload = function () {
            $(".yzz-main .bg").fadeIn();
        };
        var $switchLangEl = $(".j_switch_lang"); // 切换语言
        var $loginBtn = $(".j_login");
        var $wechatLoginBtn = $(".j_wLogin");
        var $userId = $(".j_userid");
        var $password = $(".j_password");
        var $form = $(".yzz-lg-form");
        var $captcha = $(".j_captcha")
        var $captchaImg = $captcha.find('img');
        var isWeixinShow = $("#is-weixin").data('url');
        var weixinBtn = document.getElementById("wechatLogin");
        if (!isWeixinShow){
            weixinBtn.classList.add("hidden");
        }

        $("[i18n]").i18n({
            defaultLang: defaultLang,
            filePath: getI18nUrl() + '/',
            filePrefix: "i18n_",
            fileSuffix: "",
            forever: true,
            callback: function () {
            }
        });

        $switchLangEl.click(function (ev) {
            defaultLang = ev.currentTarget.dataset.lang;
            $("[i18n]").i18n({
                defaultLang: defaultLang,
                filePath: getI18nUrl() + '/',
                filePrefix: "i18n_",
                fileSuffix: ""
            });
            $(ev.currentTarget)
                .parent()
                .trigger("mouseout");
        });

        $(".yzz-lang-select, .yzz-lang").mouseover(function (ev) {
            $(".yzz-lang-select").css("display", "block");
            $(".yzz-triangle").addClass("hover");
        });

        $(".yzz-lang-select, .yzz-lang").mouseout(function (ev) {
            $(".yzz-lang-select").css("display", "none");
            $(".yzz-triangle").removeClass("hover");
        });

        $loginBtn.click(function () {
            validate();
        });

        // 微信登录
        $wechatLoginBtn.click(function () {
            window.location.href = $("#weixin-host-url").data('url');
        });

        function validate() {
            var userId = $.trim($userId.find("input").val());
            var password = $.trim($password.find("input").val());
            var captcha = $.trim($captcha.find('input').val());

            if (!userId) {
                $userId.addClass("error");
                return false;
            } else {
                $userId.removeClass("error");
            }

            if (!password) {
                $password.addClass("error");
                return false;
            } else {
                $password.removeClass("error");
            }

            if (needCaptcha && !captcha) {
                $captcha.addClass('error');
                return false;
            } else {
                $captcha.removeClass('error');
            }
            return true;
        }

        $form.find("input").change(function (ev) {
            if (!$.trim(ev.currentTarget.value)) {
                $(ev.currentTarget)
                    .parent()
                    .addClass("error");
            } else {
                $(ev.currentTarget)
                    .parent()
                    .removeClass("error");
            }
        });

        function getCaptchaUrl() {
            // 如果没有配置 默认用account的验证码接口
            var url = apiConfig.getCaptcha;
            return url + '?_t=' + Date.now();
        }

        $form.find('.j_userid input').change(function(ev) {
        $(".j_errormsg").css('visibility', 'hidden');
        var url = apiConfig.needCaptcha;
        var params = {
            username: $.trim($(ev.currentTarget).val()),
            enterprise_id: enterpriseId,
            _t: Date.now()
        };

        $.ajax({
            url: url += '?' + $.param(params),
            type: "GET",
            success: function(r) {
            if (r.success) {
                needCaptcha = !!r.data.needImgCaptcha;
                if (needCaptcha) {
                $('.yzz-login').addClass('need-captcha');
                $captcha.css('display', 'block');
                $captchaImg.attr('src', getCaptchaUrl());
                } else {
                    $('.yzz-login').removeClass('need-captcha');
                    $captcha.find('input').val('');
                    $captcha.css('display', 'none');
                    $captchaImg.attr('src', getCaptchaUrl());
                }
            }
            }
        });
        });

        // 点击刷新验证码
        $captchaImg.on('click', function() {
        $captcha.find('input').val('');
        $captchaImg.attr('src', getCaptchaUrl());
        })

        $loginBtn.click(function () {
            if (validate()) {
                login();
            }
        });

        $(document).on('keydown', function (event) {
            if (event.keyCode == 13) {
                if (validate()) {
                    login();
                }
            }
        });


        function login() {
            var param = {
                username: $.trim($userId.find("input").val()),
                password: $.trim($password.find("input").val()),
                service: getJsonFromUrl().service
            };
            if (needCaptcha) {
                param.pic_captcha = $.trim($captcha.find('input').val());
            }
            $errorMsg = $(".j_errormsg");
            $.ajax({
                // url: $("#login-url").data("url"),
                url: apiConfig.login + '?enterprise_id=' + enterpriseId,
                type: "POST",
                data: makeDataSafely(param),
                success: function (data) {
                    if (data.success) {
                        window.location.href = data.data.service;
                        return;
                    }
                    var i18nPrefix = getI18nUrl();
                    $.getJSON(i18nPrefix + "/i18n_" + defaultLang + ".json", function (i18n) {
                        if (!data.success) {
                            if (needCaptcha || data.type === "CaptchaError" || data.data.needImgCaptcha ) {
                            needCaptcha = true;
                            $('.yzz-login').addClass('need-captcha');
                            $captcha.css('display', 'block');
                            $captcha.find('input').val('');
                            $captcha.find('img').attr('src', getCaptchaUrl());
                            }
                            if (i18n[data.errorCode]) {
                                $errorMsg.text(i18n[data.errorCode]);
                            } else {
                                $errorMsg.text(i18n.InputError);
                            }
                        } else {
                            $errorMsg.text(i18n.LoginError);
                        }
                        $errorMsg.css("visibility", "visible");
                    });
                },
                error: function () {
                    $.getJSON(getI18nUrl() + "/i18n_" + defaultLang + ".json", function (i18n) {
                        $errorMsg.text(i18n.LoginError);
                        $errorMsg.css("visibility", "visible");
                    });
                }
            });
        }
    }
});


function getJsonFromUrl(url) {
    if (!url) url = location.search;
    var query = url.substr(1);
    var result = {};
    query.split("&").forEach(function (part) {
        var item = part.split("=");
        result[item[0]] = decodeURIComponent(item[1]);
    });
    return result;
}

function makeDataSafely (data) {
    var publicKey = '-----BEGIN PUBLIC KEY----- \
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvikNrllO99UrXzzbJ2sY \
    Cv37fFeXXVgdTY3C/enrJtzkVWPGyzNiB38Ry0yaHnWDli5A+3H0UuK1OZUdr3Yo \
    VijJTYVrtJcAu3qZa2DwpicpQasTCaPHmwPdlGv29DyS3MAlUWi7Y0ScVpHItrn/ \
    ORvqWs08cY/u8RolHwgZH4kAlpyC1JaToivWkmtcqMnmwf2STyvL/5nNujALIs4+ \
    xsy1Jb8sYCPLQXAOHxNsKKrYzCvsh97F2eIh5SjBHsbbSXAAmiNfgyPTWIlLjDaI \
    nKKbyjmjJ/6BctY00xGjIvKRKvWOH+9Xz1wt2kZbbfh2m4r1PS6B6+n6+Trn650T \
    AQIDAQAB \
    -----END PUBLIC KEY-----';
    var white_list = ['password', 'username', 'pic_captcha'];

    if (typeof data !== 'object') {
        return data;
    }
    var box = {};
    Object.keys(data).forEach(function(key) {
        if (white_list.indexOf(key) > -1) {
            box[key] = data[key];
            delete data[key];
        }
    });
    if (Object.keys(box).length > 0) {
        // 新建JSEncrypt对象
        var encryptor = new JSEncrypt();
        // 设置公钥
        encryptor.setPublicKey(publicKey);
        // 加密数据
        var blackBoxData = encryptor.encrypt(JSON.stringify(box));
        // 加密出错，极有可能是太长，导致加密失败
        if (blackBoxData === false) {
            Object.keys(box).forEach(function(key) {
                data[key] = box[key];
            });
        } else {
            data['secure_zone'] = blackBoxData;
        }
    }
    return data;
}