<!DOCTYPE html>
<html lang="en">
<head>
    <script src="https://g.alicdn.com/gdt/jsapi/1.3.2/index.js"></script>

</head>
<body>

<!--稳定性监控-->
<script src='https://wpkgate-emas.ding.zj.gov.cn/static/wpk-jssdk.1.0.2/wpkReporter.js' crossorigin='true'></script>
<script>
    try {
        const config = {
            bid: 'znzxwjgl_zzdpro',
            signkey: '1234567890abcdef',
            gateway: 'https://wpkgate-emas.ding.zj.gov.cn'
        };
        const wpk = new wpkReporter(config);
        wpk.installAll();
        window._wpk = wpk;
    } catch (err) {
        console.error('WpkReporter init fail', err);
    }
</script>

<script>
    const params = new URLSearchParams(window.location.search);
    // 获取参数值
    const enterpriseId = '115';
    const tenantId = '196729';
    const baseUrl= 'https://doc.jiashan.gov.cn:8443/v2';
    const auth_code= params.get('auth_code');
    const redirect = params.get('redirect');
    const h5Redirect = params.get('h5Redirect');

    var url = 'https://doc.jiashan.gov.cn:8443/platform/zwd/autoLogin';

    dd.ready(function() {
        //调用jsapi方法
        dd.getAuthCode({corpId:tenantId}).then((result) => {
            if (result) {
                if(result.code){
                    var code = result.code;
                    var real_redirect = h5Redirect;

                }else{
                    var code = result.auth_code;
                    var real_redirect = redirect;
                }
                var data = {"code": code, "enterpriseId":enterpriseId};
                postProcess(url, data, real_redirect);
            }else{
                window.location.href = baseUrl;
            }
        }).catch((error)=>{
            if(redirect){
                window.location.href = baseUrl + redirect;
            }else{
                window.location.href = baseUrl;
            }

        })
    })


    function postProcess(url, data, redirect) {

        // 创建XMLHttpRequest对象
        var xhr = new XMLHttpRequest();

        // 设置请求方法和URL
        xhr.open('POST', url, true);

        // 设置请求头部信息
        xhr.setRequestHeader('Content-Type', 'application/json');

        // 设置回调函数
        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) { // 检查响应状态码是否成功范围
                var responseData = JSON.parse(xhr.responseText); // 将响应文本解析为JSON数据

                var accountId = responseData.accountId;
                var nickNameCn = responseData.nickNameCn;
                // 用户信息埋点
                pushUserInfo(accountId, nickNameCn);

                if(redirect){
                    var redirectUrl = responseData.url + "&redirect=" + encodeURIComponent(redirect)
                }else {
                    var redirectUrl = responseData.url;
                }
                window.location.href = redirectUrl;
                // 处理响应数据逻辑
            } else {
                // 处理错误响应逻辑
            }
        };
        xhr.onerror = function() {
            // 处理网络错误逻辑
        };
        // 发送请求并设置请求体数据
        var map = JSON.stringify(data);
        xhr.send(map);
    }


    function pushUserInfo(accountId, nickNameCn) {

        (function (w, d, s, q, i) {
            w[q] = w[q] || [];
            var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s);
            j.async = true;
            j.id = "beacon-aplus";
            j.src = "https://alidt.alicdn.com/alilog/mlog/aplus_cloud.js";
            f.parentNode.insertBefore(j, f);
        })(window, document, "script", "aplus_queue");

        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["aplus-rhost-v", "alog-api.ding.zj.gov.cn"],
        });
        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["aplus-rhost-g", "alog-api.ding.zj.gov.cn"],
        });

        var u = navigator.userAgent;
        var isAndroid = u.indexOf("Android") > -1;
        var isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);

        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: [
                "appId",
                isAndroid ? "28302650" : isIOS ? "28328447" : "47130293",
            ],
        });

        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_hold", "BLOCK"],
        });
        //基础埋点
        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["aplus-waiting", "MAN"],
        });
        aplus_queue.push({
            action: "aplus.sendPV",
            arguments: [
                {
                    is_auto: false,
                },
                {
                    //当前应用信息
                    sapp_id: "41799",
                    sapp_name: "znzxwjgl",
                    page_id: "index", //'页面ID，与page 参数配合使用，保证唯一性',
                    page_name: "首页", //'页面中文名称'
                    page_url: "/index.html",
                },
            ],
        });
        // 用户信息埋点
        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_user_id", accountId],
        });
        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_user_nick", nickNameCn],
        });
        aplus_queue.push({
            action: "aplus.setMetaInfo",
            arguments: ["_hold", "START"],
        });
    }

</script>
</body>
</html>