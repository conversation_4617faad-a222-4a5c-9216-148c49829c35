html,
body {
    height: 100%;
    margin: 0;
    padding: 0;
}

div,
ul,
li,
input,
img {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    list-style: none;
    font-style: normal;
}

input[type='text'] {
    -webkit-appearance: none;
    box-shadow: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    outline: none;
}

.yzz-header {
    position: relative;
    height: 72px;
    background: #fff;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(37, 46, 54, 1);
}

.yzz-logo {
    position: absolute;
    height: 30px;
    top: 50%;
    left: 32px;
    -webkit-transform: translatey(-50%);
    -moz-transform: translatey(-50%);
    -ms-transform: translatey(-50%);
    transform: translatey(-50%);
}

.yzz-lang {
    position: absolute;
    height: 72px;
    line-height: 72px;
    top: 0;
    right: 0;
    padding-right: 32px;
    cursor: pointer;
}

.yzz-lang-select {
    display: none;
    position: absolute;
    top: 72px;
    right: 16px;
    width: 128px;
    padding: 6px 0;
    background: #fff;
    box-shadow: 0px 3px 8px 0px rgba(0, 0, 0, 0.19);
    border-radius: 2px;
    z-index: 100;
}

/* .yzz-lang:hover .yzz-triangle {
  transform: rotate(180deg);
} */

.yzz-triangle.hover {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    transform: rotate(180deg);
}

/* .yzz-lang:hover .yzz-lang-select,
.yzz-lang-select:hover {
  display: block;
} */

.yzz-lang-select li {
    width: 100%;
    height: 44px;
    line-height: 44px;
    text-align: center;
    cursor: pointer;
}

.yzz-lang-select li:hover {
    background: rgba(246, 247, 249, 1);
}

.yzz-lang:hover .yzz-lang-select {
    display: block;
}

.yzz-triangle {
    display: inline-block;
    width: 10px;
    height: 10px;
    background: url(data:image/png;base64,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);
    background-size: 10px;
    -webkit-transition: transform 0.1s ease;
    -moz-transition: transform 0.1s ease;
    -ms-transition: transform 0.1s ease;
    transition: transform 0.1s ease;
}

.yzz-curlang {
    padding-right: 6px;
}

.yzz-main {
    position: relative;
    height: 100%;
    overflow: hidden;
}

.yzz-main .bg {
    width: 100%;
    height: 100%;
    background: url(../login/image/bg.png);
    background-position: center center;
    background-size: cover;
}

.yzz-login,
.yzz-login-third {
    position: absolute;
    width: 400px;
    height: 420px;
    padding-top: 44px;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 10px 24px 0px rgba(0, 0, 0, 0.35);
    border-radius: 8px;
    top: -moz-calc((100% - 420px) / 2);
    top: -webkit-calc((100% - 420px) / 2);
    top: calc((100% - 420px) / 2);
    text-align: center;
}

.yzz-login-third {
    display: none;
}

.yzz-login.alpha {
    background: rgba(255, 255, 255, 0.6);
}

.yzz-lg-title {
    font-size: 32px;
    font-weight: 500;
    color: rgba(0, 0, 0, 1);
    line-height: 40px;
}

.yzz-lg-msg {
    visibility: hidden;
    padding: 15px 0;
    color: #ff0000;
    font-size: 14px;
    line-height: 22px;
    height: 52px;
}

.yzz-lg-form {
    padding: 0 48px;
}

.yzz-lg-form input[type='text'] {
    width: 100%;
    height: 44px;
    border-radius: 2px;
    border: 1px solid rgba(217, 217, 217, 1);
    text-indent: 14px;
    outline: none;
    font-size: 16px;
}

.error input[type='text'] {
    border: 1px solid #ff0000;
}

.yzz-lg-form .error span {
    visibility: visible;
}

.yzz-lg-form span {
    display: block;
    visibility: hidden;
    padding-top: 2px;
    padding-bottom: 10px;
    text-align: left;
    font-size: 14px;
    color: rgba(255, 0, 0, 1);
    line-height: 22px;
}

.yzz-lg-btn {
    width: 100%;
    outline: none;
    background-color: #017ffd;
    color: #fff;
    font-size: 16px;
    line-height: 44px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    border-radius: 4px;
}

.enterprise-wechat-login{
    outline: none;
    margin-top: 8px;
    color: #000;
    background: none;
    line-height: 34px;
    font-weight: 500;
    border: none;
    cursor: pointer;
    border-radius: 4px;
}

.hidden{
    visibility: hidden;
}

.yzz-lg-btn:hover,
.yzz-lg-btn:focus {
    background-color: #006ad4;
}

.need-captcha.yzz-login {
  padding-top: 24px;
}

.need-captcha .yzz-lg-msg {
  padding: 0;
  height: 42px;
}

.captcha {
  position: relative;
  display: none;
}

.captcha img {
  position: absolute;
  top: 2px;
  right: 2px;
  width: auto;
  height: 40px;
}


@media screen and (max-width: 1366px) and (min-width: 768px) {
    .yzz-login {
        right: 83px;
    }
}

@media screen and (min-width: 1366px) {
    .yzz-login {
        right: -webkit-calc((100% - 1366px) / 2 + 83px);
        right: -moz-calc((100% - 1366px) / 2 + 83px);
        right: calc((100% - 1366px) / 2 + 83px);
    }
}

@media screen and (max-width: 768px) {
    .yzz-main {
        position: relative;
        height: -moz-calc(100% - 50px);
        height: -webkit-calc(100% - 50px);
        height: calc(100% - 50px);
        overflow: hidden;
    }

    .yzz-logo {
        height: 20px;
        top: 15px;
    }

    .yzz-lang {
        height: 50px;
        line-height: 50px;
        font-size: 15px;
    }

    .yzz-header {
        height: 50px;
    }

    .yzz-lang-select {
        top: 50px;
    }

    .yzz-login {
        position: fixed;
        top: 50%;
        left: 30px;
        margin-top: -168px;
        height: 420px;
        padding-top: 30px;
        width: -webkit-calc(100% - 60px);
        width: -moz-calc(100% - 60px);
        width: calc(100% - 60px);
    }

    .yzz-lg-msg {
        padding: 0;
        height: 44px;
        line-height: 44px;
    }

    .yzz-lg-form {
        padding: 0 40px;
    }

    .yzz-lg-btn {
        font-size: 18px;
    }

    .yzz-lg-form input {
        font-size: 17px;
    }

    /* .yzz-main .bg {
      background-image: url(../images/bg_h5.png);
    } */

    .need-captcha.yzz-login {
      padding-top: 14px;
    }

    .need-captcha .yzz-lg-msg {
      height: 22px;
      line-height: 22px;
    }
    .need-captcha .yzz-lg-form span {
      padding-bottom: 4px;
    }

    .need-captcha .yzz-lg-form input {
      height: 40px;
    }

    .need-captcha .captcha img {
      height: 36px;
    }
}

.error-msg {
    position: absolute;
    bottom: 40px;
    width: 400px;
    text-align: center;
    font-size: 12px;
    line-height: 20px;
    color: red;
}

.login-switch {
    font-size: 14px;
    color: #017ffd;
    position: absolute;
    bottom: 20px;
    width: 100%;
    text-align: center;
    left: 0;
    z-index: 9999;
}

.third-container {
    width: 100%;
    height: 100%;
}

.login-switch:hover {
    cursor: pointer;
}

#enterprise-select-container {
    height: 200px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.enterprise-selector {
    line-height: 40px;
    text-align: left;
}

.enterprise-selector input[type="radio"] {
    margin-right: 20px;
}

.enterprise-selector label {
    display: block;
    cursor: pointer;
}