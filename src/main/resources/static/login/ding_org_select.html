<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <link rel="stylesheet" th:href="@{/login/ding_index.css}">
    <title>选择登录组织</title>
</head>
<body ontouchstart="">
<header class="yzz-header">
<!--    <a th:href="@{${template.locationHost}}">
        <img class="yzz-logo" th:src="@{${template.logo}}" alt="logo"/>
    </a>-->
    <div class="yzz-lang">
        <span class="yzz-curlang" i18n="lang">简体中文</span>
        <i class="yzz-triangle"></i>
        <ul class="yzz-lang-select">
            <li data-lang="cn" class="j_switch_lang">简体中文</li>
            <li data-lang="en" class="j_switch_lang">English</li>
        </ul>
    </div>
</header>
<div class="yzz-main">
    <div class="bg"></div>
    <div class="yzz-login">
        <h1 style="display: inline; margin-right: 10px; font-size: 14px;">请选择登录的组织</h1>
        <select id="enterprise-select" style="font-size: 10px; height: 22px;">
            <option value="">请选择企业</option>
            <option th:each="enterprise, iterStat : ${enterprises.enterprises}"
                    th:value="${enterprise.id}"
                    th:data-appid="${enterprise.appId}"
                    th:data-redirect-uri="${enterprise.redirectUri}"
                    th:data-product-id="${enterprise.productId}"
                    th:selected="${iterStat.index == 0}"> <!-- 默认选择第一个企业 -->
                [[${enterprise.name}]]
            </option>
        </select>
        <div id="login_container"></div>
    </div>
</div>

<script th:src="@{/login/js/jquery.js}"></script>
<script src="https://g.alicdn.com/dingding/dinglogin/0.0.5/ddLogin.js"></script>
<script>
    $(document).ready(function () {
        // 初始化钉钉登录
        var loginContainer = document.getElementById('login_container');
        var selectElement = document.getElementById('enterprise-select');

        // 监听选择框变化
        selectElement.addEventListener('change', function () {
            var selectedOption = selectElement.options[selectElement.selectedIndex];
            var appId = selectedOption.getAttribute('data-appid');
            var productId = selectedOption.getAttribute('data-product-id')
            var redirectUri = selectedOption.getAttribute('data-redirect-uri') + '?product_id=' + productId; // 拼接字符串
            if (appId && productId) {
                // 清空之前的二维码
                loginContainer.innerHTML = '';

                // 初始化新的钉钉登录二维码
                var obj = DDLogin({
                    id: "login_container",
                    goto: encodeURIComponent('https://oapi.dingtalk.com/connect/qrconnect?appid=' + appId + '&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=' + encodeURIComponent(redirectUri)),
                    style: "border:none;background-color:#FFFFFF;",
                    width: "365",
                    height: "380"
                });

                // 监听扫码事件
                var handleMessage = function (event) {
                    var origin = event.origin;
                    if (origin === "https://login.dingtalk.com") {
                        var loginTmpCode = event.data;
                        console.log("loginTmpCode", loginTmpCode);
                        // 跳转到授权页面
                        window.location.href = "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=" + appId + "&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=" + encodeURIComponent(redirectUri) + "&loginTmpCode=" + loginTmpCode;
                    }
                };

                if (typeof window.addEventListener !== 'undefined') {
                    window.addEventListener('message', handleMessage, false);
                } else if (typeof window.attachEvent !== 'undefined') {
                    window.attachEvent('onmessage', handleMessage);
                }
            }
        });
        // 添加以下代码以在页面加载时生成二维码
        var defaultSelectedOption = selectElement.options[selectElement.selectedIndex];
        if (defaultSelectedOption) {
            selectElement.dispatchEvent(new Event('change')); // 触发选择框变化事件
        }
    });
</script>
</body>
</html>