<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.DepartmentsUsersMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.DepartmentsUsers">
        <result column="id" property="id"/>
        <result column="created" property="created"/>
        <result column="deleted" property="deleted"/>
        <result column="user_id" property="userId"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="department_id" property="departmentId"/>
        <result column="position" property="position"/>
        <result column="settings" property="settings"/>
    </resultMap>

    <!-- enterprise table all fields -->
    <sql id="BaseColumnList">
        id, created, deleted, user_id, enterprise_id, department_id, position, settings
    </sql>

    <select id="queryById" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments_users
        WHERE id = #{id} AND deleted = 0
    </select>

    <select id="queryByIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments_users
        WHERE 1 != 1 AND deleted = 0
        <if test="ids != null and ids.size() > 0">
            OR id in
            <foreach item="id" collection="ids"
                     open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="queryByUserId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments_users
        WHERE user_id = #{userId}
        AND deleted = 0
    </select>

    <select id="queryByDepartmentId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments_users
        WHERE department_id = #{departmentId}
        AND deleted = 0
    </select>

    <select id="queryByUserIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments_users
        WHERE 1 != 1 AND deleted = 0
        <if test="userIds != null and userIds.size() > 0">
            OR user_id in
            <foreach item="userId" collection="userIds"
                     open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>

</mapper>
