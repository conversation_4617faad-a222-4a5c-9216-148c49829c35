<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformAsynQueueMapper">
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sync_type" jdbcType="INTEGER" property="syncType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="created" jdbcType="BIGINT" property="created"/>
        <result column="updated" jdbcType="BIGINT" property="updated"/>
        <result column="sync_dated" jdbcType="BIGINT" property="syncDated"/>
        <result column="enterprise_id" jdbcType="INTEGER" property="enterpriseId"/>
        <result column="task_id" jdbcType="INTEGER" property="taskId"/>
        <result column="count" jdbcType="INTEGER" property="count"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs"
               type="com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue">
        <result column="value_box" jdbcType="LONGVARCHAR" property="valueBox"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , sync_type, status, created, updated, sync_dated, enterprise_id, task_id, count
    </sql>
    <sql id="Blob_Column_List">
        value_box
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from platform_async_queue
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from platform_async_queue
        where id = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue">
        insert into platform_async_queue (sync_type, status,
                                         created, updated, sync_dated,
                                         enterprise_id, task_id, count,
                                         value_box)
        values (#{syncType,jdbcType=INTEGER}, #{status,jdbcType=INTEGER},
                #{created,jdbcType=BIGINT}, #{updated,jdbcType=BIGINT}, #{syncDated,jdbcType=BIGINT},
                #{enterpriseId,jdbcType=INTEGER}, #{taskId,jdbcType=INTEGER}, #{count,jdbcType=INTEGER},
                #{valueBox,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue">
        insert into platform_async_queue
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="syncType != null">
                sync_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="created != null">
                created,
            </if>
            <if test="updated != null">
                updated,
            </if>
            <if test="syncDated != null">
                sync_dated,
            </if>
            <if test="enterpriseId != null">
                enterprise_id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="count != null">
                count,
            </if>
            <if test="valueBox != null">
                value_box,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="syncType != null">
                #{syncType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="created != null">
                #{created,jdbcType=BIGINT},
            </if>
            <if test="updated != null">
                #{updated,jdbcType=BIGINT},
            </if>
            <if test="syncDated != null">
                #{syncDated,jdbcType=BIGINT},
            </if>
            <if test="enterpriseId != null">
                #{enterpriseId,jdbcType=INTEGER},
            </if>
            <if test="taskId != null">
                #{taskId,jdbcType=INTEGER},
            </if>
            <if test="count != null">
                #{count,jdbcType=INTEGER},
            </if>
            <if test="valueBox != null">
                #{valueBox,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue">
        update platform_async_queue
        <set>
            <if test="syncType != null">
                sync_type = #{syncType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="created != null">
                created = #{created,jdbcType=BIGINT},
            </if>
            <if test="updated != null">
                updated = #{updated,jdbcType=BIGINT},
            </if>
            <if test="syncDated != null">
                sync_dated = #{syncDated,jdbcType=BIGINT},
            </if>
            <if test="enterpriseId != null">
                enterprise_id = #{enterpriseId,jdbcType=INTEGER},
            </if>
            <if test="taskId != null">
                task_id = #{taskId,jdbcType=INTEGER},
            </if>
            <if test="count != null">
                count = #{count,jdbcType=INTEGER},
            </if>
            <if test="valueBox != null">
                value_box = #{valueBox,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue">
        update platform_async_queue
        set sync_type     = #{syncType,jdbcType=INTEGER},
            status        = #{status,jdbcType=INTEGER},
            created       = #{created,jdbcType=BIGINT},
            updated       = #{updated,jdbcType=BIGINT},
            sync_dated    = #{syncDated,jdbcType=BIGINT},
            enterprise_id = #{enterpriseId,jdbcType=INTEGER},
            task_id       = #{taskId,jdbcType=INTEGER},
            count         = #{count,jdbcType=INTEGER},
            value_box     = #{valueBox,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue">
        update platform_async_queue
        set sync_type     = #{syncType,jdbcType=INTEGER},
            status        = #{status,jdbcType=INTEGER},
            created       = #{created,jdbcType=BIGINT},
            updated       = #{updated,jdbcType=BIGINT},
            sync_dated    = #{syncDated,jdbcType=BIGINT},
            enterprise_id = #{enterpriseId,jdbcType=INTEGER},
            task_id       = #{taskId,jdbcType=INTEGER},
            count         = #{count,jdbcType=INTEGER}
        where id = #{id,jdbcType=INTEGER}
    </update>

    <select id="selectByEnterpriseIdAndStatus" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from platform_async_queue
        where enterprise_id = #{enterpriseId,jdbcType=INTEGER} and status = #{status,jdbcType=INTEGER}
    </select>

    <insert id="batchInsertQueue" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into
        platform_async_queue
        (sync_type, status, created, updated, sync_dated, enterprise_id, task_id, coun,value_box)
        values
        <foreach collection="list" item="queue" index="index" separator=",">
            (
            #{queue.syncType,jdbcType=INTEGER},
            #{queue.status,jdbcType=INTEGER},
            #{queue.created,jdbcType=BIGINT},
            #{queue.updated,jdbcType=BIGINT},
            #{queue.syncDated,jdbcType=BIGINT},
            #{queue.enterpriseId,jdbcType=INTEGER}),
            #{queue.taskId,jdbcType=INTEGER}),
            #{queue.count,jdbcType=INTEGER}),
            #{queue.valueBox,jdbcType=LONGVARCHAR})
        </foreach>
    </insert>

    <select id="selectByTaskId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
        select
        <include refid="Base_Column_List"/>
        ,
        <include refid="Blob_Column_List"/>
        from platform_async_queue
        where task_id = #{taskId,jdbcType=INTEGER}
    </select>
</mapper>