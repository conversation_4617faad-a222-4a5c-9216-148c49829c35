<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.EnterpriseMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.Enterprise">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="platform_id" property="platformId"/>
        <result column="file_version_limit" property="fileVersionLimit"/>
        <result column="settings" property="settings"/>
        <result column="settings2" property="settings2"/>
        <result column="additional_info" property="additionalInfo"/>
        <result column="admin_user_id" property="adminUserId"/>
        <result column="space_amount" property="spaceAmount"/>
        <result column="trial_expires_at" property="trialExpiresAt"/>
        <result column="plan_id" property="planId"/>
        <result column="expires_at" property="expiresAt"/>
    </resultMap>

    <!-- enterprise table all fields -->
    <sql id="BaseColumnList">
        id, name, platform_id, file_version_limit, additional_info, settings, admin_user_id, space_amount, trial_expires_at, plan_id, expires_at, settings2
    </sql>

    <select id="queryById" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM enterprises
        WHERE id = #{id}
    </select>

    <select id="queryTrashPeriod" resultType="long" parameterType="Object">
        SELECT in_trash_period
        FROM enterprises
        WHERE id = #{enterpriseId}
    </select>

    <select id="queryFileVersionLimit" resultType="java.lang.Integer" parameterType="long">
        SELECT file_version_limit
        FROM enterprises
        WHERE id = #{enterpriseId}
    </select>

    <select id="query" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumnList" />
        FROM enterprises
        LIMIT 100
    </select>

    <select id="queryByAdditionalInfo" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumnList" />
        FROM enterprises
        WHERE additional_info LIKE CONCAT('%', #{searchText}, '%')
    </select>

    <update id="updateAdditionalById" parameterType="com.fangcloud.thirdpartplatform.db.model.Enterprise">
        update enterprises
        set
            additional_info = #{additionalInfo,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>
