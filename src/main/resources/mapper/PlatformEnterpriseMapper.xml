<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformEnterpriseMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises">
        <result column="id" property="id"/>
        <result column="alias" property="alias"/>
        <result column="enterprise_ticket" property="enterpriseTicket"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="platform_id" property="platformId"/>
        <result column="platform_enterprise_logo" property="platformEnterpriseLogo"/>
        <result column="settings" property="settings"/>
        <result column="value_box" property="valueBox"/>
    </resultMap>

    <!-- enterprise table all fields -->
    <sql id="BaseColumnList">
        id, alias, enterprise_ticket, enterprise_id, platform_id, platform_enterprise_logo, settings, value_box
    </sql>

    <select id="queryByEnterpriseTicket" resultMap="BaseResultMap" parameterType="java.lang.String">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_enterprises
        WHERE enterprise_ticket = #{enterpriseTicket}
    </select>

    <select id="queryByEnterpriseId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_enterprises
        WHERE enterprise_id = #{enterpriseId}
    </select>

</mapper>

