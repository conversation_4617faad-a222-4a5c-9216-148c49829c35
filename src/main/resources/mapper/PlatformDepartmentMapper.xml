<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformDepartmentMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformDepartment">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="department_id" property="departmentId"/>
        <result column="yfy_department_id" property="yfyDepartmentId"/>
        <result column="platform_id" property="platformId"/>
        <result column="enterprise_ticket" property="enterpriseTicket"/>
    </resultMap>

    <!-- user table all fields -->
    <sql id="BaseColumnList">
      id, name, department_id, yfy_department_id, platform_id, enterprise_ticket
    </sql>

    <delete id="deleteByIds">
        DELETE FROM platform_departments
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="insert" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformDepartment" useGeneratedKeys="true" keyProperty="id">
        insert into platform_departments ( name, department_id,yfy_department_id, platform_id, enterprise_ticket)
        values ( #{name}, #{departmentId}, #{yfyDepartmentId}, #{platformId}, #{enterpriseTicket})
    </insert>

    <update id="updateNameById" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformDepartment">
        update platform_departments
        set name =#{name}
        where id = #{id}
    </update>



    <select id="queryById" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_departments
        WHERE id = #{id}
    </select>

    <select id="getByPlatformIdAndDepartmentId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_departments
        WHERE platform_id = #{platformId} AND department_id = #{departmentId}
    </select>

    <select id="getByPlatformIdAndYfyDepartmentId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_departments
        WHERE platform_id = #{platformId} AND yfy_department_id = #{yfyDepartmentId}
    </select>

    <select id="getByEnterpriseTicket" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_departments
        WHERE enterprise_ticket = #{enterpriseTicket}
    </select>

    <select id="getByPlatformIdAndDepartmentIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_departments
        WHERE platform_id = #{platformId} AND department_id IN
        <foreach item="departmentId" collection="departmentIds"
                 open="(" separator="," close=")">
            #{departmentId}
        </foreach>
    </select>


    <select id="getByPlatformIdAndYfyDepartmentIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_departments
        WHERE platform_id = #{platformId} AND yfy_department_id IN
        <foreach item="yfyDepartmentId" collection="yfyDepartmentIds"
                 open="(" separator="," close=")">
            #{yfyDepartmentId}
        </foreach>
    </select>

    <select id="queryCountByPlatformId" resultType="java.lang.Integer">
        SELECT count(1) FROM platform_departments WHERE platform_id = #{platformId}
    </select>


    <select id="queryByPlatformIdWithPage" parameterType="Map" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_departments
        WHERE platform_id = #{platformId}
        ORDER BY id
        LIMIT #{start}, #{pageSize}
    </select>


</mapper>
