<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.UserMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.User">
        <result column="id" property="id"/>
        <result column="full_name" property="fullName"/>
        <result column="email" property="email"/>
        <result column="user_group" property="userGroup"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="space_used" property="spaceUsed"/>
        <result column="space_total" property="spaceTotal"/>
        <result column="deleted" property="deleted"/>
        <result column="profile_pic_path" property="profilePicPath"/>
        <result column="active" property="active"/>
        <result column="phone" property="phone"/>
        <result column="country_code" property="countryCode"/>
        <result column="full_name_pinyin" property="fullNamePinyin"/>
        <result column="pinyin_first_letters" property="pinyinFirstLetters"/>
        <result column="value_box" property="valueBox"/>
        <result column="user_guide_settings2" property="userGuideSettings"/>
    </resultMap>

    <!-- user table all fields -->
    <sql id="BaseColumnList">
        id, full_name, email, user_group, enterprise_id, space_used, space_total, settings, deleted,profile_pic_path,active, phone, country_code, full_name_pinyin, pinyin_first_letters, value_box, user_guide_settings2
    </sql>

    <select id="queryById" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE id = #{id}
    </select>

    <select id="queryByEmail" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE email = #{email} AND enterprise_id = #{enterpriseId}
    </select>

    <select id="queryByPhone" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE phone = #{phone} AND enterprise_id = #{enterpriseId}
    </select>

    <select id="queryByVerifiedEmail" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE email = #{email}
        <choose>
            <when test="isLoginIdentifier == true">
                <!-- 1048576 = 2 ^ 20, bitmask of identity_type_email_available -->
                <![CDATA[ AND settings & 1048576 != 0 ]]>
            </when>
            <otherwise>
                <!-- 16 = 2 ^ 4, bitmask of email_verified -->
                <![CDATA[ AND settings & 16 != 0 ]]>
            </otherwise>
        </choose>
    </select>

    <select id="queryByVerifiedPhone" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE phone = #{phone}
        <choose>
            <when test="isLoginIdentifier == true">
                <!-- 524288 = 2 ^ 19, bitmask of identity_type_email_available -->
                <![CDATA[ AND settings & 524288 != 0 ]]>
            </when>
            <otherwise>
                <!-- 8 = 2 ^ 3, bitmask of phone_verified -->
                <![CDATA[ AND settings & 8 != 0 ]]>
            </otherwise>
        </choose>
    </select>

    <select id="queryByIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE id IN
        <foreach item="id" collection="ids"
                 open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryUsersCountByUserIds" resultType="java.lang.Long">
        select count(1) from users where id in
        <foreach item="userId" collection="list"
                 open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="searchUsers" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE enterprise_id = #{enterpriseId}
        AND deleted = 0
        AND
        (full_name LIKE CONCAT('%', #{queryWords}, '%')
        OR full_name_pinyin LIKE CONCAT('%', #{queryWords}, '%')
        OR pinyin_first_letters LIKE CONCAT('%', #{queryWords}, '%')
        OR email LIKE CONCAT('%', #{queryWords}, '%'))
    </select>


    <select id="queryUsersByEnterpriseId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE enterprise_id = #{enterpriseId}
        AND deleted = 0
    </select>

    <select id="queryDelUsersByEnterpriseId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM users
        WHERE enterprise_id = #{enterpriseId}
        AND deleted > 0
    </select>

    <select id="searchUsersCount" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM users
        WHERE enterprise_id = #{enterpriseId}
        AND deleted = 0
        AND
        (full_name LIKE CONCAT('%', #{queryWords}, '%')
        OR full_name_pinyin LIKE CONCAT('%', #{queryWords}, '%')
        OR pinyin_first_letters LIKE CONCAT('%', #{queryWords}, '%')
        OR email LIKE CONCAT('%', #{queryWords}, '%'))
    </select>

    <!-- 查询条件 -->
    <sql id="Example_Where_Clause">
        where 1=1
        <trim suffixOverrides=",">
            <if test="id != null">
                and id = #{id}
            </if>
        </trim>
    </sql>

    <!-- 更新 space_used 增量信息 -->
    <update id="updateSpaceUsed">
        update users
        <set>
            <![CDATA[
            space_used=(case when space_used<-#{deltaSize} then 0 else space_used+#{deltaSize} end)
            ]]>
        </set>
        where id=#{userId}
    </update>
    <!-- 将用户改为未激活 -->
    <update id="updateUserActive">
        update users
        <set>
            active = 0
        </set>
        where id=#{userId}
    </update>
</mapper>
