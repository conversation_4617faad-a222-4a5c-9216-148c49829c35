<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformLoginConfigMapper">
  <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enterprise_id" jdbcType="INTEGER" property="enterpriseId" />
    <result column="login_source_type" jdbcType="VARCHAR" property="loginSourceType" />
    <result column="created" jdbcType="BIGINT" property="created" />
    <result column="updated" jdbcType="BIGINT" property="updated" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig">
    <result column="value_box" jdbcType="LONGVARCHAR" property="valueBox" />
  </resultMap>
  <sql id="Base_Column_List">
    id, enterprise_id, login_source_type, created, updated, deleted
  </sql>
  <sql id="Blob_Column_List">
    value_box
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_login_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectByEnterpriseId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_login_config
    where enterprise_id = #{enterpriseId,jdbcType=INTEGER} and deleted = 0
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from platform_login_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig" keyProperty="id" useGeneratedKeys="true">
    insert into platform_login_config (enterprise_id,
      login_source_type, created, updated,
      deleted, value_box)
    values (#{enterpriseId,jdbcType=INTEGER},
      #{loginSourceType,jdbcType=VARCHAR}, #{created,jdbcType=BIGINT}, #{updated,jdbcType=BIGINT}, 
      #{deleted,jdbcType=INTEGER}, #{valueBox,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig">
    insert into platform_login_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="loginSourceType != null">
        login_source_type,
      </if>
      <if test="created != null">
        created,
      </if>
      <if test="updated != null">
        updated,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="valueBox != null">
        value_box,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="loginSourceType != null">
        #{loginSourceType,jdbcType=VARCHAR},
      </if>
      <if test="created != null">
        #{created,jdbcType=BIGINT},
      </if>
      <if test="updated != null">
        #{updated,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="valueBox != null">
        #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig">
    update platform_login_config
    <set>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="loginSourceType != null">
        login_source_type = #{loginSourceType,jdbcType=VARCHAR},
      </if>
      <if test="created != null">
        created = #{created,jdbcType=BIGINT},
      </if>
      <if test="updated != null">
        updated = #{updated,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="valueBox != null">
        value_box = #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig">
    update platform_login_config
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      login_source_type = #{loginSourceType,jdbcType=VARCHAR},
      created = #{created,jdbcType=BIGINT},
      updated = #{updated,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      value_box = #{valueBox,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig">
    update platform_login_config
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      login_source_type = #{loginSourceType,jdbcType=VARCHAR},
      created = #{created,jdbcType=BIGINT},
      updated = #{updated,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>