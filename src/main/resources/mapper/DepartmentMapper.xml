<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.DepartmentMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.Department">
        <result column="id" property="id"/>
        <result column="created" property="created"/>
        <result column="parent_id" property="parentId"/>
        <result column="path_ids" property="pathIds"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="name" property="name"/>
        <result column="name_sort" property="nameSort"/>
        <result column="director_id" property="directorId"/>
        <result column="folder_id" property="folderId"/>
        <result column="settings" property="settings"/>
        <result column="space_total" property="spaceTotal"/>
        <result column="storage_id" property="storageId"/>
        <result column="order" property="order"/>
    </resultMap>

    <!-- enterprise table all fields -->
    <sql id="BaseColumnList">
        `id`, `created`, `parent_id`, `path_ids`, `enterprise_id`, `name`, `name_sort`, `director_id`, `folder_id`, `settings`, `space_total`, `storage_id` ,`order`
    </sql>

    <select id="queryById" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments
        WHERE id = #{id}
    </select>

    <select id="queryByIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments
        WHERE 1 != 1
        <if test="ids != null and ids.size() > 0">
            OR id in
            <foreach item="id" collection="ids"
                     open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>


    <select id="queryByEnterpriseId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments
        WHERE enterprise_id = #{enterpriseId}
        AND deleted = 0
    </select>


    <select id="queryRootDepartment" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments
        WHERE enterprise_id = #{enterpriseId}
        AND parent_id = 0
    </select>

    <select id="queryByName" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM departments
        WHERE enterprise_id = #{enterpriseId}
        AND name = #{name}
    </select>

</mapper>
