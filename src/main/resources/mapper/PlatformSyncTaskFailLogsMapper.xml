<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformSyncTaskFailLogsMapper">
  <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enterprise_id" jdbcType="INTEGER" property="enterpriseId" />
    <result column="sync_task_id" jdbcType="INTEGER" property="syncTaskId" />
    <result column="custome_id" jdbcType="VARCHAR" property="customeId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="created" jdbcType="BIGINT" property="created" />
    <result column="value_box" jdbcType="LONGVARCHAR" property="valueBox" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs">
    <result column="value_box" jdbcType="LONGVARCHAR" property="valueBox" />
  </resultMap>
  <sql id="Base_Column_List">
    id, enterprise_id, sync_task_id, custome_id, reason, created
  </sql>
  <sql id="Blob_Column_List">
    value_box
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_task_fail_logs
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from platform_sync_task_fail_logs
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs" useGeneratedKeys="true" keyProperty="id">
    insert into platform_sync_task_fail_logs (enterprise_id, sync_task_id,
      custome_id, reason, created,
      value_box)
    values (#{enterpriseId,jdbcType=INTEGER}, #{syncTaskId,jdbcType=INTEGER},
      #{customeId,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{created,jdbcType=BIGINT},
      #{valueBox,jdbcType=LONGVARCHAR})
  </insert>

  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    insert into
    platform_sync_task_fail_logs
    (enterprise_id, sync_task_id, custome_id, reason, created, value_box)
    values
    <foreach collection="list" item="log" index="index" separator=",">
      (
      #{log.enterpriseId,jdbcType=INTEGER},
      #{log.syncTaskId,jdbcType=INTEGER},
      #{log.customeId,jdbcType=VARCHAR},
      #{log.reason,jdbcType=VARCHAR},
      #{log.created,jdbcType=BIGINT},
      #{log.valueBox,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs" useGeneratedKeys="true" keyProperty="id">
    insert into platform_sync_task_fail_logs
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="syncTaskId != null">
        sync_task_id,
      </if>
      <if test="customeId != null">
        custome_id,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="created != null">
        created,
      </if>
      <if test="valueBox != null">
        value_box,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="syncTaskId != null">
        #{syncTaskId,jdbcType=INTEGER},
      </if>
      <if test="customeId != null">
        #{customeId,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="created != null">
        #{created,jdbcType=BIGINT},
      </if>
      <if test="valueBox != null">
        #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs">
    update platform_sync_task_fail_logs
    <set>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="syncTaskId != null">
        sync_task_id = #{syncTaskId,jdbcType=INTEGER},
      </if>
      <if test="customeId != null">
        custome_id = #{customeId,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="created != null">
        created = #{created,jdbcType=BIGINT},
      </if>
      <if test="valueBox != null">
        value_box = #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs">
    update platform_sync_task_fail_logs
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      sync_task_id = #{syncTaskId,jdbcType=INTEGER},
      custome_id = #{customeId,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      created = #{created,jdbcType=BIGINT},
      value_box = #{valueBox,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs">
    update platform_sync_task_fail_logs
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      sync_task_id = #{syncTaskId,jdbcType=INTEGER},
      custome_id = #{customeId,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      created = #{created,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="queryByTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_task_fail_logs
    where sync_task_id = #{syncTaskId,jdbcType=INTEGER}
    <if test="customId != null and customId != ''">
      and custome_id like concat('%', #{customId}, '%')
    </if>
    order by created desc
    limit #{limit}, #{pageSize}
  </select>

  <select id="countByTaskId" resultType="integer">
    select
    count(id)
    from platform_sync_task_fail_logs
    where sync_task_id = #{syncTaskId,jdbcType=INTEGER}
    <if test="customId != null and customId != ''">
      and custome_id like concat('%', #{customId}, '%')
    </if>
  </select>

  <select id="selectByTaskId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_task_fail_logs
    where sync_task_id = #{syncTaskId,jdbcType=INTEGER}
    <if test="customeId != null">
      and custome_id = #{customeId}
    </if>
    order by created desc
  </select>

  <delete id="deleteByTimestamp">
    DELETE FROM platform_sync_task_fail_logs
    WHERE created &lt; #{fifteenDaysAgoTimestamp} and enterprise_id = #{enterpriseId}
    limit #{batchSize}
  </delete>

  <select id="countDataBeforeFifteenDays" resultType="integer">
    select
    count(*)
    from platform_sync_task_fail_logs
    where enterprise_id = #{enterpriseId} and created &lt; #{fifteenDaysAgoTimestamp}
  </select>
</mapper>