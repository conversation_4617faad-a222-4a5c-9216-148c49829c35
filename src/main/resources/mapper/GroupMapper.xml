<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.GroupMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.Group">
        <result column="id" property="id"/>
        <result column="created" property="created"/>
        <result column="updated" property="updated"/>
        <result column="enterprise_id" property="enterpriseId"/>
        <result column="name" property="name"/>
        <result column="name_sort" property="nameSort"/>
        <result column="description" property="description"/>
        <result column="admin_user_id" property="adminUserId"/>
        <result column="settings" property="settings"/>
        <result column="department_id" property="departmentId"/>
        <result column="visiable" property="visiable"/>
        <result column="user_count" property="userCount"/>
        <result column="item_count" property="itemCount"/>
    </resultMap>

    <!-- enterprise table all fields -->
    <sql id="BaseColumnList">
        id, created, updated, enterprise_id, name, name_sort, description, admin_user_id, settings, department_id, visiable, user_count, item_count
    </sql>

    <select id="queryById" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM groups
        WHERE id = #{id}
    </select>

    <select id="queryByDepartmentId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM groups
        WHERE department_id = #{departmentId}
    </select>

    <select id="queryByIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM groups
        WHERE 1 != 1
        <if test="ids != null and ids.size() > 0">
            OR id IN
            <foreach item="id" collection="ids"
                     open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
