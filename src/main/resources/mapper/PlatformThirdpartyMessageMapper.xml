<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformThirdpartyMessageMapper">
  <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enterprise_id" jdbcType="INTEGER" property="enterpriseId" />
    <result column="message_type" jdbcType="VARCHAR" property="messageType" />
    <result column="unique_id" jdbcType="VARCHAR" property="uniqueId" />
    <result column="created" jdbcType="BIGINT" property="created" />
    <result column="updated" jdbcType="BIGINT" property="updated" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage">
    <result column="value_box" jdbcType="LONGVARCHAR" property="valueBox" />
  </resultMap>
  <sql id="Base_Column_List">
    id, enterprise_id, message_type, unique_id, created, updated, deleted
  </sql>
  <sql id="Blob_Column_List">
    value_box
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_thirdparty_messages
    where id = #{id,jdbcType=INTEGER}
  </select>


  <select id="selectByEnterpriseId" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_thirdparty_messages
    where enterprise_id = #{enterpriseId,jdbcType=INTEGER} and deleted = 0
  </select>

  <select id="selectByEnterpriseIdAndMessageTypeAndUniqueId" parameterType="Object" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_thirdparty_messages
    where
        enterprise_id = #{enterpriseId,jdbcType=INTEGER}
        and
        message_type = #{messageType,jdbcType=VARCHAR}
        and
        unique_id = #{uniqueId,jdbcType=VARCHAR}
        and
        deleted = 0
  </select>

  <select id="selectByEnterpriseIdAndUniqueId" parameterType="Object" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_thirdparty_messages
    where
    enterprise_id = #{enterpriseId,jdbcType=INTEGER}
    and
    unique_id = #{uniqueId,jdbcType=VARCHAR}
    and
    deleted = 0
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from platform_thirdparty_messages
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage" keyProperty="id" useGeneratedKeys="true">
    insert into
      platform_thirdparty_messages (
            enterprise_id,
            message_type,
            unique_id,
            created,
            updated,
            deleted,
            value_box)
    values (
            #{enterpriseId,jdbcType=INTEGER},
            #{messageType,jdbcType=VARCHAR},
            #{uniqueId,jdbcType=VARCHAR},
            #{created,jdbcType=BIGINT},
            #{updated,jdbcType=BIGINT},
            #{deleted,jdbcType=INTEGER},
            #{valueBox,jdbcType=LONGVARCHAR})
  </insert>

  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage">
    update platform_thirdparty_messages
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      message_type = #{messageType,jdbcType=VARCHAR},
      unique_id = #{uniqueId,jdbcType=VARCHAR},
      created = #{created,jdbcType=BIGINT},
      updated = #{updated,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      value_box = #{valueBox,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKeySelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage">
    update platform_thirdparty_messages
    <set>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="uniqueId != null">
        unique_id = #{uniqueId,jdbcType=VARCHAR},
      </if>
      <if test="created != null">
        created = #{created,jdbcType=BIGINT},
      </if>
      <if test="updated != null">
        updated = #{updated,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="valueBox != null">
        value_box = #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

</mapper>