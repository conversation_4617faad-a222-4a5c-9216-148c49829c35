<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.GroupsUsersMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.GroupsUser">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="group_id" property="groupId"/>
        <result column="accepted" property="accepted"/>
        <result column="created" property="created"/>
    </resultMap>

    <!-- enterprise table all fields -->
    <sql id="BaseColumnList">
        `id`, `user_id`, `group_id`, `accepted`, `created`
    </sql>

    <select id="queryByGroupId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM groups_users
        WHERE group_id = #{groupId}
    </select>


</mapper>
