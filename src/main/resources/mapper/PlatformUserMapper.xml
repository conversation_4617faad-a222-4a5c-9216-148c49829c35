<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper">
    <!-- Result Map -->
    <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformUser">
        <result column="id" property="id"/>
        <result column="ticket_type" property="ticketType"/>
        <result column="user_ticket" property="userTicket"/>
        <result column="user_id" property="userId"/>
        <result column="platform_id" property="platformId"/>
        <result column="platform_user_avatar" property="platformUserAvatar"/>
        <result column="enterprise_ticket" property="enterpriseTicket"/>
        <result column="user_inner_id" property="userInnerId"/>
        <result column="settings" property="settings"/>
        <result column="value_box" property="valueBox"/>
    </resultMap>

    <!-- user table all fields -->
    <sql id="BaseColumnList">
      id, ticket_type, user_ticket, user_id, platform_id, platform_user_avatar, enterprise_ticket, user_inner_id, settings, value_box
    </sql>

    <insert id="insert" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformUser" useGeneratedKeys="true" keyProperty="id">
        insert into platform_users
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ticketType != null">
                ticket_type,
            </if>
            <if test="userTicket != null">
                user_ticket,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="platformId != null">
                platform_id,
            </if>
            <if test="platformUserAvatar != null">
                platform_user_avatar,
            </if>
            <if test="enterpriseTicket != null">
                enterprise_ticket,
            </if>
            <if test="userInnerId != null">
                user_inner_id,
            </if>
            <if test="settings != null">
                settings,
            </if>
            <if test="valueBox != null">
                value_box,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ticketType != null">
                #{ticketType},
            </if>
            <if test="userTicket != null">
                #{userTicket},
            </if>
            <if test="userId != null">
                #{userId},
            </if>
            <if test="platformId != null">
                #{platformId},
            </if>
            <if test="platformUserAvatar != null">
                #{platformUserAvatar},
            </if>
            <if test="enterpriseTicket != null">
                #{enterpriseTicket},
            </if>
            <if test="userInnerId != null">
                #{userInnerId},
            </if>
            <if test="settings != null">
                #{settings},
            </if>
            <if test="valueBox != null">
                #{valueBox},
            </if>
        </trim>
    </insert>

    <select id="queryById" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_users
        WHERE id = #{id}
    </select>

    <select id="queryByPlatformIdUserTicket" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_users
        WHERE platform_id = #{platformId} AND user_ticket = #{userTicket}
    </select>

    <select id="queryByPlatformIdUserId" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_users
        WHERE platform_id = #{platformId} AND user_id = #{userId}
    </select>

    <select id="queryByPlatformIdUserIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_users
        WHERE platform_id = #{platformId} AND user_id IN
        <foreach item="userId" collection="userIds"
                 open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="queryByPlatformIdUserTicketIds" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_users
        WHERE platform_id = #{platformId} AND user_ticket IN
        <foreach item="userTicket" collection="userTicketIds"
                 open="(" separator="," close=")">
            #{userTicket}
        </foreach>
    </select>

    <select id="queryByUserTicketIdsEnterpriseTicket" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_users
        WHERE enterprise_ticket = #{enterpriseTicket} AND user_ticket IN
        <foreach item="userTicket" collection="userTicketIds"
                 open="(" separator="," close=")">
            #{userTicket}
        </foreach>
    </select>

    <select id="queryByEnterpriseTicket" resultMap="BaseResultMap" parameterType="Object">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_users
        WHERE enterprise_ticket = #{enterpriseTicket}
    </select>

    <select id="countByPlatformId" resultType="java.lang.Long">
        SELECT count(1) FROM platform_users WHERE platform_id = #{platformId}
    </select>


    <select id="queryByPlatformIdWithPage" parameterType="Map" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumnList"/>
        FROM platform_users
        WHERE platform_id = #{platformId}
        ORDER BY id
        LIMIT #{start}, #{pageSize}
    </select>



</mapper>
