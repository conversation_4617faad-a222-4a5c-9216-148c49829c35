<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformSyncTaskRecordMapper">
  <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enterprise_id" jdbcType="INTEGER" property="enterpriseId" />
    <result column="sync_config_id" jdbcType="INTEGER" property="syncConfigId" />
    <result column="sync_task_type" jdbcType="INTEGER" property="syncTaskType" />
    <result column="sync_task_status" jdbcType="INTEGER" property="syncTaskStatus" />
    <result column="sync_startat" jdbcType="BIGINT" property="syncStartat" />
    <result column="sync_endat" jdbcType="BIGINT" property="syncEndat" />
    <result column="sync_result" jdbcType="INTEGER" property="syncResult" />
    <result column="created" jdbcType="BIGINT" property="created" />
    <result column="updated" jdbcType="BIGINT" property="updated" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord">
    <result column="value_box" jdbcType="LONGVARCHAR" property="valueBox" />
  </resultMap>
  <sql id="Base_Column_List">
    id, enterprise_id, sync_config_id, sync_task_type, sync_task_status, sync_startat,
    sync_endat, sync_result, created, updated
  </sql>
  <sql id="Blob_Column_List">
    value_box
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_tasks_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from platform_sync_tasks_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord" useGeneratedKeys="true" keyProperty="id">
    insert into platform_sync_tasks_record (enterprise_id, sync_config_id,
      sync_task_type, sync_task_status, sync_startat,
      sync_endat, sync_result, created,
      updated, value_box)
    values (#{enterpriseId,jdbcType=INTEGER}, #{syncConfigId,jdbcType=INTEGER},
      #{syncTaskType,jdbcType=INTEGER}, #{syncTaskStatus,jdbcType=INTEGER}, #{syncStartat,jdbcType=BIGINT},
      #{syncEndat,jdbcType=BIGINT}, #{syncResult,jdbcType=INTEGER}, #{created,jdbcType=BIGINT},
      #{updated,jdbcType=BIGINT}, #{valueBox,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord" useGeneratedKeys="true" keyProperty="id">
    insert into platform_sync_tasks_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="syncConfigId != null">
        sync_config_id,
      </if>
      <if test="syncTaskType != null">
        sync_task_type,
      </if>
      <if test="syncTaskStatus != null">
        sync_task_status,
      </if>
      <if test="syncStartat != null">
        sync_startat,
      </if>
      <if test="syncEndat != null">
        sync_endat,
      </if>
      <if test="syncResult != null">
        sync_result,
      </if>
      <if test="created != null">
        created,
      </if>
      <if test="updated != null">
        updated,
      </if>
      <if test="valueBox != null">
        value_box,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="syncConfigId != null">
        #{syncConfigId,jdbcType=INTEGER},
      </if>
      <if test="syncTaskType != null">
        #{syncTaskType,jdbcType=INTEGER},
      </if>
      <if test="syncTaskStatus != null">
        #{syncTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="syncStartat != null">
        #{syncStartat,jdbcType=BIGINT},
      </if>
      <if test="syncEndat != null">
        #{syncEndat,jdbcType=BIGINT},
      </if>
      <if test="syncResult != null">
        #{syncResult,jdbcType=INTEGER},
      </if>
      <if test="created != null">
        #{created,jdbcType=BIGINT},
      </if>
      <if test="updated != null">
        #{updated,jdbcType=BIGINT},
      </if>
      <if test="valueBox != null">
        #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord">
    update platform_sync_tasks_record
    <set>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="syncConfigId != null">
        sync_config_id = #{syncConfigId,jdbcType=INTEGER},
      </if>
      <if test="syncTaskType != null">
        sync_task_type = #{syncTaskType,jdbcType=INTEGER},
      </if>
      <if test="syncTaskStatus != null">
        sync_task_status = #{syncTaskStatus,jdbcType=INTEGER},
      </if>
      <if test="syncStartat != null">
        sync_startat = #{syncStartat,jdbcType=BIGINT},
      </if>
      <if test="syncEndat != null">
        sync_endat = #{syncEndat,jdbcType=BIGINT},
      </if>
      <if test="syncResult != null">
        sync_result = #{syncResult,jdbcType=INTEGER},
      </if>
      <if test="created != null">
        created = #{created,jdbcType=BIGINT},
      </if>
      <if test="updated != null">
        updated = #{updated,jdbcType=BIGINT},
      </if>
      <if test="valueBox != null">
        value_box = #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord">
    update platform_sync_tasks_record
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      sync_config_id = #{syncConfigId,jdbcType=INTEGER},
      sync_task_type = #{syncTaskType,jdbcType=INTEGER},
      sync_task_status = #{syncTaskStatus,jdbcType=INTEGER},
      sync_startat = #{syncStartat,jdbcType=BIGINT},
      sync_endat = #{syncEndat,jdbcType=BIGINT},
      sync_result = #{syncResult,jdbcType=INTEGER},
      created = #{created,jdbcType=BIGINT},
      updated = #{updated,jdbcType=BIGINT},
      value_box = #{valueBox,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord">
    update platform_sync_tasks_record
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      sync_config_id = #{syncConfigId,jdbcType=INTEGER},
      sync_task_type = #{syncTaskType,jdbcType=INTEGER},
      sync_task_status = #{syncTaskStatus,jdbcType=INTEGER},
      sync_startat = #{syncStartat,jdbcType=BIGINT},
      sync_endat = #{syncEndat,jdbcType=BIGINT},
      sync_result = #{syncResult,jdbcType=INTEGER},
      created = #{created,jdbcType=BIGINT},
      updated = #{updated,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="pageByEnterpriseId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_tasks_record
    where 1=1
    <if test="enterpriseId != null">
      and enterprise_id = #{enterpriseId,jdbcType=INTEGER}
    </if>
    and sync_task_type = #{syncTaskType,jdbcType=INTEGER}
    order by updated desc
    limit #{limit}, #{pageSize}
  </select>

  <select id="countByEnterpriseId" resultType="integer">
    select
    count(id)
    from platform_sync_tasks_record
    where 1=1
    <if test="enterpriseId != null">
      and enterprise_id = #{enterpriseId,jdbcType=INTEGER}
    </if>
      and sync_task_type = #{syncTaskType,jdbcType=INTEGER}
  </select>

  <select id="selectByEnterpriseId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_tasks_record
    where enterprise_id = #{enterpriseId,jdbcType=INTEGER}
    order by updated desc
  </select>

</mapper>