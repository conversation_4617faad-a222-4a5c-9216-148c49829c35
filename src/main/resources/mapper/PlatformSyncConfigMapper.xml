<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper">
  <resultMap id="BaseResultMap" type="com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="enterprise_id" jdbcType="INTEGER" property="enterpriseId" />
    <result column="config_name" jdbcType="VARCHAR" property="configName" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="sync_type" jdbcType="INTEGER" property="syncType" />
    <result column="sync_status" jdbcType="INTEGER" property="syncStatus" />
    <result column="cron" jdbcType="VARCHAR" property="cron" />
    <result column="next_execute_time" jdbcType="BIGINT" property="nextExecuteTime" />
    <result column="created" jdbcType="BIGINT" property="created" />
    <result column="updated" jdbcType="BIGINT" property="updated" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig">
    <result column="value_box" jdbcType="LONGVARCHAR" property="valueBox" />
  </resultMap>
  <sql id="Base_Column_List">
    id, enterprise_id, config_name, source_type, sync_type, sync_status, cron, next_execute_time, 
    created, updated, deleted
  </sql>
  <sql id="Blob_Column_List">
    value_box
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from platform_sync_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig" useGeneratedKeys="true" keyProperty="id">
    insert into platform_sync_config (enterprise_id, config_name,
      source_type, sync_type, sync_status, 
      cron, next_execute_time, created, 
      updated, deleted, value_box
      )
    values (#{enterpriseId,jdbcType=INTEGER}, #{configName,jdbcType=VARCHAR},
      #{sourceType,jdbcType=VARCHAR}, #{syncType,jdbcType=INTEGER}, #{syncStatus,jdbcType=INTEGER}, 
      #{cron,jdbcType=VARCHAR}, #{nextExecuteTime,jdbcType=BIGINT}, #{created,jdbcType=BIGINT}, 
      #{updated,jdbcType=BIGINT}, #{deleted,jdbcType=INTEGER}, #{valueBox,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig" useGeneratedKeys="true" keyProperty="id">
    insert into platform_sync_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="configName != null">
        config_name,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="syncType != null">
        sync_type,
      </if>
      <if test="syncStatus != null">
        sync_status,
      </if>
      <if test="cron != null">
        cron,
      </if>
      <if test="nextExecuteTime != null">
        next_execute_time,
      </if>
      <if test="created != null">
        created,
      </if>
      <if test="updated != null">
        updated,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="valueBox != null">
        value_box,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="configName != null">
        #{configName,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="syncType != null">
        #{syncType,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="cron != null">
        #{cron,jdbcType=VARCHAR},
      </if>
      <if test="nextExecuteTime != null">
        #{nextExecuteTime,jdbcType=BIGINT},
      </if>
      <if test="created != null">
        #{created,jdbcType=BIGINT},
      </if>
      <if test="updated != null">
        #{updated,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=INTEGER},
      </if>
      <if test="valueBox != null">
        #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig">
    update platform_sync_config
    <set>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      </if>
      <if test="configName != null">
        config_name = #{configName,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="syncType != null">
        sync_type = #{syncType,jdbcType=INTEGER},
      </if>
      <if test="syncStatus != null">
        sync_status = #{syncStatus,jdbcType=INTEGER},
      </if>
      <if test="cron != null">
        cron = #{cron,jdbcType=VARCHAR},
      </if>
      <if test="nextExecuteTime != null">
        next_execute_time = #{nextExecuteTime,jdbcType=BIGINT},
      </if>
      <if test="created != null">
        created = #{created,jdbcType=BIGINT},
      </if>
      <if test="updated != null">
        updated = #{updated,jdbcType=BIGINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=INTEGER},
      </if>
      <if test="valueBox != null">
        value_box = #{valueBox,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig">
    update platform_sync_config
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      config_name = #{configName,jdbcType=VARCHAR},
      source_type = #{sourceType,jdbcType=VARCHAR},
      sync_type = #{syncType,jdbcType=INTEGER},
      sync_status = #{syncStatus,jdbcType=INTEGER},
      cron = #{cron,jdbcType=VARCHAR},
      next_execute_time = #{nextExecuteTime,jdbcType=BIGINT},
      created = #{created,jdbcType=BIGINT},
      updated = #{updated,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER},
      value_box = #{valueBox,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig">
    update platform_sync_config
    set enterprise_id = #{enterpriseId,jdbcType=INTEGER},
      config_name = #{configName,jdbcType=VARCHAR},
      source_type = #{sourceType,jdbcType=VARCHAR},
      sync_type = #{syncType,jdbcType=INTEGER},
      sync_status = #{syncStatus,jdbcType=INTEGER},
      cron = #{cron,jdbcType=VARCHAR},
      next_execute_time = #{nextExecuteTime,jdbcType=BIGINT},
      created = #{created,jdbcType=BIGINT},
      updated = #{updated,jdbcType=BIGINT},
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateDelete">
    update platform_sync_config
    set
      deleted = #{deleted,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateStatus">
    update platform_sync_config
    set
      sync_status = #{syncStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <update id="updateNextExecuteTime">
    update platform_sync_config
    set
      next_execute_time = #{nextExecuteTime,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER} and next_execute_time = #{oldNextExecuteTime,jdbcType=BIGINT}
  </update>

  <select id="queryByEnterpriseId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_config
    where enterprise_id = #{enterpriseId,jdbcType=INTEGER} and sync_type = #{syncType,jdbcType=INTEGER} and deleted = 0
    order by updated desc
  </select>

  <select id="query" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_config
    where deleted = 0
  </select>

  <select id="queryByIds" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_config
    where id in
    <foreach collection="ids" item="item" open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <select id="queryByEnterpriseIdAndSourceType" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from platform_sync_config
    where 1=1
    <if test="enterpriseId != null">
      and enterprise_id = #{enterpriseId,jdbcType=INTEGER}
    </if>
    <if test="sourceType != null">
      and source_type = #{sourceType,jdbcType=VARCHAR}
    </if>
     and deleted = 0
  </select>
</mapper>