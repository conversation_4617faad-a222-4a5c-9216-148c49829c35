spring:
  profiles:
    active: ${env.active}
  main:
    lazy-initialization: true
  thymeleaf:
    prefix: 'classpath:/static/login/'
    suffix: '.html'
    check-template-location: true
  rabbitmq:
    enable: ${env.custom.mq.enable}
    host: ${env.custom.mq.host}
    port: ${env.custom.mq.port}
    username: ${env.custom.mq.username}
    password: ${env.custom.mq.password}
    template:
      exchange: job_queues_exchange
  datasource:
    druid:
      url: 'jdbc:mysql://${env.mysql.url}:${env.mysql.port}/${env.mysql.database}?useUnicode=true&characterEncoding=utf8'
      username: ${env.mysql.username}
      password: ${env.mysql.password}
      driver-class-name: com.mysql.jdbc.Driver
      stat-view-servlet:
        enabled: false
  http:
    encoding:
      force: true
      charset: UTF-8
      enabled: true
  redis:
    host: ${env.redis.host}
    port: ${env.redis.port}
    password: ${env.redis.password}
mybatis:
  type-aliases-package: 'com.fangcloud.thirdpartplatform.db.model'
  mapper-locations: 'classpath:mapper/*Mapper.xml'

custom:
  autologin:
    base:
      ip: ${env.custom.autologin.base.ip}
      host: ${env.custom.autologin.base.host}
  public: ${env.custom.public}
  base:
    url: ${env.custom.base.url}
    domain: ${env.custom.base.domain}
  http_client_type: ${env.custom.http_client_type}
  aiapi:
    host:
      url: http://aiapi-svc
    uri:
      knowledge_chat_streamV1: /zsh/knowledgeChat/chatStreamV1
      get_knowledge_chat_token: /zsh/knowledgeChat/getToken
      assistant_chat_chatStream: /assistant/chat/chatStream
      ai_search: /gpt/aiSearch/webSearchChatStream
      ai_file_chat: /gpt/aiFile/openFileExtract
      ai_file_extract: /gpt/aiFile/fileExtract
    knowledgeGptIds: ${env.custom.aiapi.gptids}
  open:
    host:
      open: ${env.custom.open.host}
      openurl: ${env.custom.open.url}
    uri:
      get_login_url: '/api/internal/super_admin/get_login_url'
      sync_users: '/api/internal/platform/%s/sync_users'
      get_user_info: '/api/v2/user/info'
    #从哪个应用调开放平台 1 表示v2
    service_id: ${env.custom.open.service_id}
    version: ${env.custom.open.version}
    secret: ${env.custom.open.secret}
  oauth:
    host:
      url: http://oauth-server-svc:8000
    uri:
      get_clients: '/internal/%s/client_list'
    #从哪个应用调开放平台 1 表示v2
    service_id: 1
    version: 1.0.1
    secret: ${env.custom.oauth.secret}
  simba:
    host:
      url: "http://simba-svc:8080"
  ark:
    host:
      url: "http://ark-svc:8081"
    uri:
      url1: '/app/workflow/approval'
  v2:
    host:
      v2: ${env.custom.v2.host}
      v2url: ${env.custom.v2.url}
    default_password: '48P1e2PBs138XViH'
    uri:
      url1: '/internal_api/sync_account/mapping_user'
      url2: '/internal_api/sync_account/sync_users'
      url3: '/internal_api/sync_account/sync_depts'
      # 创建部门
      url4: '/internal_api/internal_enterprises/department_create?operator_id=%s'
      # 修改部门
      url5: '/internal_api/internal_enterprises/department_edit?operator_id=%s&department_id=%s'
      # 删除部门
      url6: '/internal_api/internal_enterprises/department_delete?operator_id=%s&department_id=%s'
      # 创建用户
      url7: '/internal_api/internal_enterprises/user_create?operator_id=%s'
      # 修改用户
      url8: '/internal_api/internal_enterprises/user_edit?operator_id=%s&user_id=%s'
      # 部门添加用户
      url9: '/internal_api/internal_enterprises/department_edit_users?operator_id=%s&department_id=%s'
      # 删除用户
      url10: '/internal_api/internal_enterprises/user_delete?operator_id=%s&user_id=%s'
      # 修改用户激活状态
      url11: '/internal_api/internal_enterprises/active_users_batch?operator_id=%s'
      # 同步群组
      url12: '/internal_api/sync_account/sync_groups'
      # 获取用户信息通过user_id
      url13: '/internal_api/account/get_user_info'
      # 邀请协作
      url14: '/openapi/vopen/collab/invite'
      # 修改协作
      url15: '/openapi/v1/collab/edit'
      # 创建评论
      url16: '/openapi/v1/comment/create'
      # 创建分享
      url17: '/openapi/v1/share_link/create'
      # 接收消息
      url18: '/third_party/support_platform/receive_message'
      # 通过token 获取用户信息
      url19: '/third_party/oauth/get_userinfo_by_token?token='
      #获取下载文件地址
      url20: '/openapi/v1/file/download'
    #从哪个应用调开放平台 1 表示Fangcloud upload
    service_id: ${env.custom.v2.service_id}
    version: ${env.custom.v2.version}
    secret: ${env.custom.v2.secret}
  dingding:
    msg_token: 'a3db4d5791fff49d1be42b0b20beb3e401314c9cb09178de18237407d76adba5'
    debug: true
    oauth_url: "https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=%s&response_type=code&scope=snsapi_auth&redirect_uri=%s"
  weixin:
    #企业微信默认顶级部门id为1
    top_dept: ${env.custom.weixin.top_dept}
  tuitui:
    host: ${env.custom.tuitui.host}
    sso_appid: ${env.custom.tuitui.appid}
    secret_key: ${env.custom.tuitui.secret}
    message_host: 'http://alarm.im.qihoo.net'
    message_appid: '2563455457'
    message_secret: '69d9dc1882f67f5481f82b4de75aa59337410107'
  sync_user:
    set_department_id_null_enable: ${env.custom.sync_user.set_department_id_null_enable}
    set_user_active_close: ${env.custom.sync_user.set_user_active_close}
    user_name_suffix: ${env.custom.sync_user.user_name_suffix}
    disabled_user_name_suffix: ${env.custom.sync_user.disabled_user_name_suffix}
    set_disabled_user_department_id_is_null: ${env.custom.sync_user.set_disabled_user_department_id_is_null}
    #是否开启ldap校验 2025版本的 ldap默认需要开启
    set_ldap_check_sasl: ${env.custom.sync_user.set_ldap_check_sasl}
task:
  pool:
    core-pool-size: 1
    max-pool-size: 1
    keep-alive-seconds: 100
    queue-capacity: 300

info:
  app:
    name: 'platform'
    version: 'v1.0.0'

management:
  endpoints:
    web:
      exposure:
        include: '*'
      base-path: '/monitor'
  endpoint:
    shutdown:
      enabled: true
    health:
      show-details: always

nacos:
  config:
    bootstrap:
      enable: ${env.nacos.enable}
      log-enable: ${env.nacos.enable}
    server-addr: ${env.nacos.host}
    remote-first: ${env.nacos.enable}
    data-id: application-platform.yml
    namespace: ${env.nacos.namespace}
    group: platform
    type: yaml
    auto-refresh: true
    max-retry: 10
    config-retry-time: 2333
    config-long-poll-timeout: 46000
    enable-remote-sync-config: true
    username: ${env.nacos.username}
    password: ${env.nacos.password}

server:
  servlet:
    context-path: '/platform'
  port: 8080
  tomcat:
    uri-encoding: UTF-8

dubbo:
  app:
    name: 'third-part-platform'
    owner: 'jiema'
  register:
    address: '**************:2181'
    timeout: 3000
  protocol:
    port: 28889


