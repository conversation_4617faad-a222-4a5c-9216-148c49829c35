<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%d [%thread] %-5level [%logger{35}] - [%X{uuid}] %msg%n----%n%ex{full}</pattern>
        </layout>
    </appender>

    <appender name="FILE"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>/var/log/platform/platform.log</File>
        <rollingPolicy
                class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>/var/log/platform/platform.%i.log</FileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>4</maxIndex>
        </rollingPolicy>
        <triggeringPolicy
                class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>500MB</maxFileSize>
        </triggeringPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d [%thread] %-5level [%logger{35}] - [%X{uuid}] %msg%n----%n%ex{full}</Pattern>
        </layout>
    </appender>

    <appender name="execution-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>/var/log/platform/execution.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.FixedWindowRollingPolicy">
            <FileNamePattern>/var/log/platform/execution.%i.log</FileNamePattern>
            <minIndex>1</minIndex>
            <maxIndex>2</maxIndex>
        </rollingPolicy>
        <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">
            <maxFileSize>100MB</maxFileSize>
        </triggeringPolicy>
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>%d [%thread] %-5level [%logger{35}] - [%X{uuid}] %msg%n----%n%ex{full}</Pattern>
        </layout>
    </appender>

    <logger name="executorLogger" level="INFO" additivity="false">
        <appender-ref ref="execution-appender"/>
    </logger>

    <root>
        <level value="info" />
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </root>

    <root>
        <level value="info" />
        <appender-ref ref="STDOUT" />
        <appender-ref ref="FILE" />
    </root>
</configuration>