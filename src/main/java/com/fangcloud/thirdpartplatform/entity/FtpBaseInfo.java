package com.fangcloud.thirdpartplatform.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FtpBaseInfo {

    @JSONField(name = "ip")
    private String ip;

    @JSONField(name = "port")
    private int port;

    @JSONField(name = "username")
    private String username;

    @J<PERSON><PERSON>ield(name = "password")
    private String password;

}
