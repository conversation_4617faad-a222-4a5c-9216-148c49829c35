package com.fangcloud.thirdpartplatform.entity.response;

import groovy.transform.builder.Builder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <p>Title: LoginResponse</p>
* <p>Description: </p>
* <p>Copyright: Copyright (c) 2019</p>
* <p>Company: www.fangcloud.com</p>
* <AUTHOR>
* Data 2019-06-24
* @version 1.0
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class NomalResponse {

    private String status;

    private boolean success;

    private Object data;

}
