package com.fangcloud.thirdpartplatform.entity.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PlatformSyncConfigResponse implements Serializable {

    private static final long serialVersionUID = -7171146263894382895L;

    private Integer id;

    private Integer enterpriseId;

    private String configName;


    private String syncType;

    private Integer syncStatus;

    private String syncStatusStr;

    private String sourceType;

    private String cron;

    private String valueBox;

    /**
     * 是否激活
     */
    private boolean activate;

    /**
     * 数据类型
     */
    private List<String> dataTypes;

    /**
     * 入参方式
     */
    private List<String> paramWay;

    /**
     * 请求方式
     */
    private List<String> requestMethods;

    /**
     * 验证方式
     */
    private List<String> verifyWay;

    /**
     * 协议
     */
    private List<String> protocols;

    /**
     * 部门需要使用的映射key
     */
    private List<String> deptKeys;

    /**
     * 用户需要使用的映射key
     */
    private List<String> userKeys;
}
