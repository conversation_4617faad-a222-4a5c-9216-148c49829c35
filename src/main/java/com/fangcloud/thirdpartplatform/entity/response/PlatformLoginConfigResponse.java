package com.fangcloud.thirdpartplatform.entity.response;

import com.fangcloud.thirdpartplatform.entity.dto.PlatformLoginConfigValueBox;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PlatformLoginConfigResponse implements Serializable {
    private static final long serialVersionUID = -6879313696162508590L;

    private Integer id;

    private Integer enterpriseId;

    private String loginSourceType;

    private PlatformLoginConfigValueBox platformLoginConfigValueBox;

    /**
     * 登陆数据源类型
     */
    private List<String> loginSourceTypes;

    /**
     * 数据类型
     */
    private List<String> dataTypes;

    /**
     * 入参方式
     */
    private List<String> paramWay;

    /**
     * 请求方式
     */
    private List<String> requestMethods;

    /**
     * 协议
     */
    private List<String> protocols;

    /**
     * 登陆的固定参数信息
     */
    private List<String> loginParamList;

    /**
     * 用户需要使用的映射key
     */
    private List<String> userKeyList;
}
