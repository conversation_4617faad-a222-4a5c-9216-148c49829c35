package com.fangcloud.thirdpartplatform.entity.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
* <p>Title: LoginResponse</p>
* <p>Description: </p>
* <p>Copyright: Copyright (c) 2019</p>
* <p>Company: www.fangcloud.com</p>
* <AUTHOR>
* Data 2019-06-24
* @version 1.0
*/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginResponse {

    private String status;

    private String error;

    private String type;

    private String url;

    private String message;

    // 浙政钉免密登陆需要：账号id
    private String accountId;

    // 浙政钉免密登陆需要：昵称
    private String nickNameCn;
    
}
