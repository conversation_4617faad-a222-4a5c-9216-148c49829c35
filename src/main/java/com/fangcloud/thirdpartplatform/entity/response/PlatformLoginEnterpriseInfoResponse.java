package com.fangcloud.thirdpartplatform.entity.response;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class PlatformLoginEnterpriseInfoResponse implements Serializable {


    private static final long serialVersionUID = 2594954214405164038L;
    private String title;
    private String logoUrl;
    private String webBackgroundImgUrl;
    private String h5BackgroundImgUrl;
    private String loginUrl;
    private String corpId;
    private String agentId;
    private String redirectUri;
    private String loginSourceType;
    private String accountPrompt; // 输入账号提示语
    private String apiHost;
    private String iconUrl;
}
