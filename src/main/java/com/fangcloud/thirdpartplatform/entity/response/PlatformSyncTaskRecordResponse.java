package com.fangcloud.thirdpartplatform.entity.response;

import lombok.Data;

import java.io.Serializable;

@Data
public class PlatformSyncTaskRecordResponse implements Serializable {
    private static final long serialVersionUID = -2412398729953672805L;

    private Integer id;

    private Integer enterpriseId;

    private String configName;

    private String syncTypeStr;

    private String syncStatusStr;

    private Integer syncStatus;

    private String syncStartTime;

    private String syncEndTime;

    private String syncDataCount;

    private String syncResult;
}
