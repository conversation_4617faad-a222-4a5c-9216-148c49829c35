package com.fangcloud.thirdpartplatform.entity.response;


import com.fangcloud.thirdpartplatform.entity.dto.GlobalConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class PlatformGlobalConfigResponse implements Serializable {
    private static final long serialVersionUID = 1405830051241668501L;

    private List<GlobalConfig> configList;

    private Integer enterpriseId;
}
