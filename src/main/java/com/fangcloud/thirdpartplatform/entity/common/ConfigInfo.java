package com.fangcloud.thirdpartplatform.entity.common;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 接口通用配置参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConfigInfo {


    private String productId;

    private String clientId;

    private String enterpriseId;

    private String platformId;

    private String redirectUrl;

    private String deptConfigId;

    private String userConfigId;

    private String userName;

    private String password;

    /**
     * ccwork,  ccwork_saas
     */
    private String platform;


}