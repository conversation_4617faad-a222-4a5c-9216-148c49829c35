package com.fangcloud.thirdpartplatform.entity.common;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户企业信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserEnterpriseInfo {

    /**
     * user_ticket
     */
    @JSONField(name = "user_id")
    private String userId;

    @JSONField(name = "yfy_user_id")
    private String yfyUserId;

    @JSONField(name = "enterprise_id")
    private String enterpriseId;

    @JSONField(name = "platform_id")
    private String platformId;

}