package com.fangcloud.thirdpartplatform.entity.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一装载数据的结果
 *
 * <AUTHOR>
 */
@Data
public class Result<T> implements Serializable{

    private static final long serialVersionUID = 4707530569488174223L;
    /**
     * 调用是否成功标志
     */
    private boolean success;

    /**
     * 请求id
     */
    private String requestId;

    /**
     * 错误码，在success为false的时候有用
     */
    private String errorCode;

    /**
     * 错误描述信息
     */
    private String message;

    /**
     * 实际数据
     */
    private T data;

}
