package com.fangcloud.thirdpartplatform.entity.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一装载数据的分页结果
 *
 * <AUTHOR>
 */
@Data
public class PageResult<T> extends Result<T> implements Serializable{

    private static final long serialVersionUID = 8290436847199744557L;

    private Integer pageNo;

    private Integer pageSize;

    private Integer totalPage;

    private Integer totalCount;
}
