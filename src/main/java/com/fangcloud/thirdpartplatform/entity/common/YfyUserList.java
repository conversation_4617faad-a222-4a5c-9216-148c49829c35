package com.fangcloud.thirdpartplatform.entity.common;
import com.alibaba.fastjson.annotation.JSONField;
import com.sync.common.entity.dto.YfyUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class YfyUserList {

    @JSONField(name = "users")
    private List<YfyUser> users;

    @JSONField(name="enterprise_id")
    private Long enterpriseId;
}

