package com.fangcloud.thirdpartplatform.entity.output;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QiyeWeixinUser {
    @JSONField(name = "userid")
    private String userid;
    private String name;

    private String mobile;

    private List<Long> department;
    private String position;
    private String email;
    private int status;
    @JSONField(name = "extattr")
    private Extattr extattr;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Extattr{
        // 扩展属性集合
        private JSONArray attrs;
    }

    public JSONArray getExtAttributes(){
        if (Objects.isNull(extattr)) {
            return new JSONArray();
        }
        return extattr.getAttrs();
    }

}
