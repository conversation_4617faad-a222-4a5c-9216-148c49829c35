package com.fangcloud.thirdpartplatform.entity.output;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QiyeWeixinDepartment {
    /**
     * 部门id
     */
    public int id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 父部门id
     */
    @JSONField(name = "parentid")
    private int parentId;

    /**
     * 在父部门中的次序值。order值大的排序靠前。值范围是[0, 2^32)
     */
    private int order;

    /**
     * 部门主管
     */
    @JSONField(name = "department_leader")
    private List<String> directorIds;
}
