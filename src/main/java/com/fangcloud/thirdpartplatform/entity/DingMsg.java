package com.fangcloud.thirdpartplatform.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DingMsg {

    /**
     * 标题 在linkMsg时有效
     */
    private String title;
    /**
     * 内容，可以是铺通文本，markdown 文本
     */
    private String content;
    /**
     * 在linkMsg时有效文本链接
     */
    private String msgLink;
    /**
     * 在linkMsg时有效文本中的图片
     */
    private String imgLink;
}
