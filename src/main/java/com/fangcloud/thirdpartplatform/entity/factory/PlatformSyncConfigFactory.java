package com.fangcloud.thirdpartplatform.entity.factory;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.*;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Date;
import java.util.List;

public class PlatformSyncConfigFactory {

    public static List<PlatformSyncConfigResponse> build(List<PlatformSyncConfig> list) {
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<PlatformSyncConfigResponse> result = Lists.newArrayList();
        list.forEach(s -> {
            result.add(build(s));
        });
        return result;
    }

    public static PlatformSyncConfigResponse build(PlatformSyncConfig platformSyncConfig) {
        if (platformSyncConfig == null) {
            return null;
        }
        PlatformSyncConfigResponse platformSyncConfigResponse = new PlatformSyncConfigResponse();
        platformSyncConfigResponse.setConfigName(platformSyncConfig.getConfigName());
        Integer syncStatus = platformSyncConfig.getSyncStatus();
        platformSyncConfigResponse.setSyncStatus(syncStatus);
        SyncStatusEnum syncResultStatusEnum = SyncStatusEnum.get(syncStatus);
        platformSyncConfigResponse.setSyncStatusStr(syncResultStatusEnum == null ? "" : syncResultStatusEnum.getDesc());
        Integer syncType = platformSyncConfig.getSyncType();
        SyncTypeEnum bySyncType = SyncTypeEnum.getBySyncType(syncType);

        platformSyncConfigResponse.setSyncType(bySyncType == null ? "" : bySyncType.getDesc());
        platformSyncConfigResponse.setCron(platformSyncConfig.getCron());
        platformSyncConfigResponse.setId(platformSyncConfig.getId());
        String sourceType = platformSyncConfig.getSourceType();
        platformSyncConfigResponse.setSourceType(sourceType);
        platformSyncConfigResponse.setEnterpriseId(platformSyncConfig.getEnterpriseId());
        platformSyncConfigResponse.setValueBox(platformSyncConfig.getValueBox());
        platformSyncConfigResponse.setActivate(getActivateState(platformSyncConfig));
        if (SourceTypeEnum.API.getDesc().equals(sourceType) || SourceTypeEnum.PUSH.getDesc().equals(sourceType)) {
            platformSyncConfigResponse.setDataTypes(DataTypeConstants.getDataTypes());
            platformSyncConfigResponse.setParamWay(ApiParamWayEnum.names());
            platformSyncConfigResponse.setVerifyWay(ApiVerificationModeEnum.names());
            platformSyncConfigResponse.setRequestMethods(Lists.newArrayList(RequestMethod.GET.name(), RequestMethod.POST.name()));
            platformSyncConfigResponse.setProtocols(Lists.newArrayList(APIHelper.HTTP, APIHelper.HTTPS));
            platformSyncConfigResponse.setDeptKeys(SyncTaskConstants.DEPT_KEYS);
            platformSyncConfigResponse.setUserKeys(SyncTaskConstants.USER_KEYS);
        }
        return platformSyncConfigResponse;
    }

    private static boolean getActivateState(PlatformSyncConfig platformSyncConfig) {
        if (platformSyncConfig.getSyncType() == null ||
                !platformSyncConfig.getSyncType().equals(SyncTypeEnum.USERS.getSyncType())) {
            return false;
        }
        if (SourceTypeEnum.AD.getDesc().equals(platformSyncConfig.getSourceType())) {
            ADValueBoxDto adValueBoxDto = JSON.parseObject(platformSyncConfig.getValueBox(), ADValueBoxDto.class);
            return adValueBoxDto.getSyncRule().isActivate();
        }
        if (SourceTypeEnum.CODE_SCRIPT.getDesc().equals(platformSyncConfig.getSourceType())) {
            return true;
        }

        if (SourceTypeEnum.API.getDesc().equals(platformSyncConfig.getSourceType())
                || SourceTypeEnum.PUSH.getDesc().equals(platformSyncConfig.getSourceType())) {
            APIConfigValueBox apiConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
            return apiConfigValueBox.getUserConfigDto().isActivate();
        }

        if (SourceTypeEnum.DING_TALK.getDesc().equals(platformSyncConfig.getSourceType())
                || SourceTypeEnum.WEIXIN.getDesc().equals(platformSyncConfig.getSourceType())
                || SourceTypeEnum.ZWD.getDesc().equals(platformSyncConfig.getSourceType())) {
            ThirdPartyValueBox apiConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), ThirdPartyValueBox.class);
            return apiConfigValueBox.getUserConfigDto().isActivate();
        }

        UserValueBoxDto userValueBoxDto = JSON.parseObject(platformSyncConfig.getValueBox(), UserValueBoxDto.class);
        return userValueBoxDto.getSyncRule().isActivate();
    }

    public static PlatformSyncConfig build(PlatformSyncConfigResponse platformSyncConfigResponse) {
        if (platformSyncConfigResponse == null) {
            return null;
        }
        PlatformSyncConfig platformSyncConfig = new PlatformSyncConfig();
        String desc = platformSyncConfigResponse.getSyncType();
        SyncTypeEnum syncTypeEnum = SyncTypeEnum.getByDesc(desc);
        if (syncTypeEnum == null) {
            throw new ParamException("不支持该同步类型");
        }
        // 若为第三方类型，不做数据源校验
        if(syncTypeEnum == SyncTypeEnum.THIRDPARTY_INVOKE){
            return buildThirdpartyPlatformSyncConfig(syncTypeEnum, platformSyncConfigResponse);
        }
        platformSyncConfig.setSyncType(syncTypeEnum.getSyncType());
        String cron = platformSyncConfigResponse.getCron();
        boolean validExpression = CronSequenceGenerator.isValidExpression(cron);
        if (!validExpression) {
            throw new ParamException("同步时间设置异常");
        }
        platformSyncConfig.setCron(cron);
        platformSyncConfig.setId(platformSyncConfigResponse.getId());
        platformSyncConfig.setSourceType(platformSyncConfigResponse.getSourceType());
        Integer syncStatus = platformSyncConfigResponse.getSyncStatus();
        syncStatus = syncStatus == null ? SyncConfigStatusEnum.STOP.getStatus() : syncStatus;
        platformSyncConfig.setSyncStatus(syncStatus);
        platformSyncConfig.setEnterpriseId(platformSyncConfigResponse.getEnterpriseId());
        String valueBox = platformSyncConfigResponse.getValueBox();
        if (StringUtils.isEmpty(valueBox)) {
            throw new ParamException("规则不能为空");
        }

        String configName = platformSyncConfigResponse.getConfigName();
        if (StringUtils.isEmpty(configName)) {
            throw new ParamException("配置名称不能为空");
        }
        platformSyncConfig.setConfigName(configName);
        CronSequenceGenerator generator = new CronSequenceGenerator(platformSyncConfig.getCron());
        try {
            long time = generator.next(new Date()).getTime();
            platformSyncConfig.setNextExecuteTime(time);
        } catch (Exception e) {
            throw new ParamException("同步时间设置格式异常");
        }
        SourceTypeEnum sourceTypeEnum = SourceTypeEnum.getByDesc(platformSyncConfig.getSourceType());
        if (sourceTypeEnum == null) {
            throw new ParamException("数据源类型不支持");
        }
        if (sourceTypeEnum == SourceTypeEnum.AD) {
            ADValueBoxDto adValueBoxDto = JSON.parseObject(valueBox, ADValueBoxDto.class);
            adValueBoxDto.check();
            if (syncTypeEnum == SyncTypeEnum.USERS) {
                ADValueBoxDto.ADField field = adValueBoxDto.getField();
                String id = field.getId();
                String name = field.getName();
                if (org.apache.commons.lang3.StringUtils.isBlank(id)) {
                    throw new ParamException("用户ID字段不能为空");
                }
                if (org.apache.commons.lang3.StringUtils.isBlank(name)) {
                    throw new ParamException("用户名子字段不能为空");
                }
            }
        } else if (sourceTypeEnum == SourceTypeEnum.API) {
            APIConfigValueBox apiConfigValueBox = JSON.parseObject(valueBox, APIConfigValueBox.class);
            apiConfigValueBox.check();
            APIConfigValueBox.APIConfig apiConfig = apiConfigValueBox.getApiConfig();
            apiConfigValueBox.setUrl(apiConfig == null ? "" : apiConfig.getApi());
            valueBox = JSON.toJSONString(apiConfigValueBox);
        } else if (sourceTypeEnum == SourceTypeEnum.WEIXIN || sourceTypeEnum == sourceTypeEnum.DING_TALK || sourceTypeEnum == SourceTypeEnum.ZWD){
            ThirdPartyValueBox thirdPartyValueBox = JSON.parseObject(valueBox, ThirdPartyValueBox.class);
            thirdPartyValueBox.check(syncTypeEnum);
        } else if (sourceTypeEnum == SourceTypeEnum.MESSAGE) {
            if (org.apache.commons.lang3.StringUtils.isBlank(valueBox)){
                throw new ParamException("message config not null!");
            }
        }else if (sourceTypeEnum == SourceTypeEnum.PUSH) {
            PushConfigValueBox pushConfigValueBox = JSON.parseObject(valueBox, PushConfigValueBox.class);
            pushConfigValueBox.check();
        }else if (sourceTypeEnum == SourceTypeEnum.CODE_SCRIPT) {
            CodeScriptValueBox codeScriptValueBox = JSON.parseObject(valueBox, CodeScriptValueBox.class);
            codeScriptValueBox.check();
        } else if (sourceTypeEnum == SourceTypeEnum.FTP) {
            FTPValueBox ftpValueBox = JSON.parseObject(valueBox,FTPValueBox.class);
            ftpValueBox.check();
        } else {
            ValueBoxDto valueBoxDto = JSON.parseObject(valueBox, ValueBoxDto.class);
            valueBoxDto.check();
        }
        platformSyncConfig.setValueBox(valueBox);
        ValueBoxDto valueBoxDto = JSON.parseObject(valueBox, ValueBoxDto.class);
        // 代码脚本数据源类型不需要校验个人和部门空间，写在代码中
        if(sourceTypeEnum != SourceTypeEnum.CODE_SCRIPT){
            if (syncTypeEnum == SyncTypeEnum.USERS) {
                Integer userSpace = valueBoxDto.getUserSpace();
                if (userSpace == null) {
                    throw new ParamException("默认个人空间不能为空");
                }
            } else if (syncTypeEnum == SyncTypeEnum.DEPARTMENT) {
                Integer deptSpace = valueBoxDto.getDeptSpace();
                if (deptSpace == null) {
                    throw new ParamException("默认部门空间不能为空");
                }
            }
        }
        return platformSyncConfig;
    }

    private static PlatformSyncConfig buildThirdpartyPlatformSyncConfig(SyncTypeEnum syncTypeEnum, PlatformSyncConfigResponse platformSyncConfigResponse) {
        PlatformSyncConfig platformSyncConfig = new PlatformSyncConfig();
        platformSyncConfig.setSyncType(syncTypeEnum.getSyncType());
        platformSyncConfig.setCron(platformSyncConfigResponse.getCron());
        platformSyncConfig.setId(platformSyncConfigResponse.getId());
        platformSyncConfig.setSourceType(platformSyncConfigResponse.getSourceType());
        platformSyncConfig.setSyncStatus(SyncConfigStatusEnum.STOP.getStatus());
        platformSyncConfig.setEnterpriseId(platformSyncConfigResponse.getEnterpriseId());
        String configName = platformSyncConfigResponse.getConfigName();
        if (StringUtils.isEmpty(configName)) {
            throw new ParamException("配置名称不能为空");
        }
        platformSyncConfig.setConfigName(configName);
        platformSyncConfig.setNextExecuteTime(0L);
        String valueBox = platformSyncConfigResponse.getValueBox();
        if (StringUtils.isEmpty(valueBox)) {
            throw new ParamException("规则不能为空");
        }
        platformSyncConfig.setValueBox(valueBox);

        return platformSyncConfig;
    }

    public static void main(String[] args) {
        String cron = "* * 1 1 * ? ";
        System.out.println(CronSequenceGenerator.isValidExpression(cron));
        CronSequenceGenerator generator = new CronSequenceGenerator(cron);


        System.out.println(generator.next(new Date()).getTime());
    }
}
