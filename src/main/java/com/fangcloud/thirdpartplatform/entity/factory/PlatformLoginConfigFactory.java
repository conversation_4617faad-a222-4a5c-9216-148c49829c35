package com.fangcloud.thirdpartplatform.entity.factory;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.login.LoginConfigConstants;
import com.fangcloud.thirdpartplatform.constant.login.LoginParamEnum;
import com.fangcloud.thirdpartplatform.constant.login.LoginSourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.ApiParamWayEnum;
import com.fangcloud.thirdpartplatform.constant.sync.DataTypeConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.input.PlatformLoginConfigParams;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginConfigResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginEnterpriseInfoResponse;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.google.common.collect.Lists;
import com.sync.common.utils.Md5Utils;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Date;
import java.util.Map;
import java.util.UUID;

public class PlatformLoginConfigFactory {

    public static PlatformLoginConfig build(PlatformLoginConfigParams platformLoginConfigParams) {
        PlatformLoginConfig platformLoginConfig = new PlatformLoginConfig();
        platformLoginConfig.setId(platformLoginConfigParams.getId());
        platformLoginConfig.setEnterpriseId(platformLoginConfigParams.getEnterpriseId());
        platformLoginConfig.setLoginSourceType(platformLoginConfigParams.getLoginSourceType());
        platformLoginConfig.setUpdated(new Date().getTime());
        platformLoginConfig.setValueBox(JSON.toJSONString(platformLoginConfigParams.getPlatformLoginConfigValueBox()));
        return platformLoginConfig;
    }

    public static LoginPageConfigDto buildDefaultLoginPageConfigDto() {
        LoginPageConfigDto loginPageConfigDto = new LoginPageConfigDto();
        loginPageConfigDto.setTitle(LoginConfigConstants.DEFAULT_LOGIN_PAGE_TITLE);
        loginPageConfigDto.setLogoUrl(LoginConfigConstants.DEFAULT_LOGIN_PAGE_LOGO_URL);
        loginPageConfigDto.setWebBackgroundUrl(LoginConfigConstants.DEFAULT_LOGIN_PAGE_WEB_BACKGROUND_URL);
        loginPageConfigDto.setH5BackgroundUrl(LoginConfigConstants.DEFAULT_LOGIN_PAGE_H5_BACKGROUND_URL);
        loginPageConfigDto.setClientBaseUrl(LoginConfigConstants.DEFAULT_LOGIN_PAGE_CLIENT_BASE_URL);
        loginPageConfigDto.setAccountPrompt(LoginConfigConstants.DEFAULT_LOGIN_PAGE_ACCOUNT_PROMPT);
        loginPageConfigDto.setIconUrl(LoginConfigConstants.DEFAULT_LOGIN_PAGE_ICON_URL);
        return loginPageConfigDto;
    }

    public static LoginValidateConfigDto buildDefaultLoginValidateConfigDto() {
        LoginValidateConfigDto loginValidateConfigDto = new LoginValidateConfigDto();
        loginValidateConfigDto.setNeedImgCaptcha(LoginConfigConstants.DEFAULT_LOGIN_VALIDATE_NEED_PICTURE_CAPTCHA);
        return loginValidateConfigDto;
    }

    public static LoginAuthOAUTH2ConfigDto buildDefaultLoginAuthOAUTH2ConfigDto(String baseUrl, String enterpriseId, LoginSecretConfigDto loginSecretConfigDto) {
        LoginAuthOAUTH2ConfigDto loginAuthOAUTH2ConfigDto = new LoginAuthOAUTH2ConfigDto();
        loginAuthOAUTH2ConfigDto.setLoginUrl(buildLoginUrl(baseUrl, enterpriseId));
        loginAuthOAUTH2ConfigDto.setLogoutUrl(buildLogoutUrl(baseUrl, enterpriseId));
        loginAuthOAUTH2ConfigDto.setGetTokenUrl(String.format(LoginConfigConstants.DEFAULT_AUTH_OAUTH2_GET_TOKEN_URL
                , baseUrl, enterpriseId, loginSecretConfigDto.getClientId(), loginSecretConfigDto.getClientSecret()));
        loginAuthOAUTH2ConfigDto.setGetUserInfoUrl(String.format(LoginConfigConstants.DEFAULT_AUTH_OAUTH2_GET_USER_INFO_URL, baseUrl));
        return loginAuthOAUTH2ConfigDto;
    }

    public static LoginAuthCASConfigDto buildDefaultLoginAuthCASConfigDto(String baseUrl, String enterpriseId) {
        LoginAuthCASConfigDto loginAuthCASConfigDto = new LoginAuthCASConfigDto();
        loginAuthCASConfigDto.setLoginUrl(buildLoginUrl(baseUrl, enterpriseId));
        loginAuthCASConfigDto.setLogoutUrl(buildLogoutUrl(baseUrl, enterpriseId));
        loginAuthCASConfigDto.setServiceValidateUrl(String.format(LoginConfigConstants.DEFAULT_AUTH_CAS_SERVICE_VALIDATE, baseUrl));
        return loginAuthCASConfigDto;
    }

    public static String buildLoginUrl(String baseUrl, String enterpriseId) {
        return String.format(LoginConfigConstants.DEFAULT_AUTH_LOGIN_URL, baseUrl, enterpriseId);
    }

    public static String buildLogoutUrl(String baseUrl, String enterpriseId) {
        return String.format(LoginConfigConstants.DEFAULT_AUTH_LOGOUT_URL, baseUrl, enterpriseId);
    }

    public static PlatformLoginConfigResponse buildPlatformLoginConfigResponse(PlatformLoginConfig platformLoginConfig, String baseUrl) {
        PlatformLoginConfigResponse platformLoginConfigResponse = new PlatformLoginConfigResponse();
        platformLoginConfigResponse.setId(platformLoginConfig.getId());
        platformLoginConfigResponse.setEnterpriseId(platformLoginConfig.getEnterpriseId());
        platformLoginConfigResponse.setLoginSourceType(platformLoginConfig.getLoginSourceType());
        platformLoginConfigResponse.setDataTypes(DataTypeConstants.getDataTypes());
        platformLoginConfigResponse.setParamWay(ApiParamWayEnum.names());
        platformLoginConfigResponse.setRequestMethods(Lists.newArrayList(RequestMethod.GET.name(), RequestMethod.POST.name()));
        platformLoginConfigResponse.setProtocols(Lists.newArrayList(APIHelper.HTTP, APIHelper.HTTPS));
        platformLoginConfigResponse.setLoginParamList(LoginParamEnum.names());
        platformLoginConfigResponse.setUserKeyList(SyncTaskConstants.USER_KEYS);
        platformLoginConfigResponse.setLoginSourceTypes(LoginSourceTypeEnum.names());

        PlatformLoginConfigValueBox platformLoginConfigValueBox = JSONObject.parseObject(platformLoginConfig.getValueBox(), PlatformLoginConfigValueBox.class);
        String enterpriseIdStr = Base64Utils.encode(String.valueOf(platformLoginConfig.getEnterpriseId()));

        platformLoginConfigValueBox.setLoginAuthOAUTH2ConfigDto(PlatformLoginConfigFactory.buildDefaultLoginAuthOAUTH2ConfigDto(baseUrl, enterpriseIdStr, platformLoginConfigValueBox.getLoginSecretConfigDto()));
        platformLoginConfigValueBox.setLoginAuthCASConfigDto(PlatformLoginConfigFactory.buildDefaultLoginAuthCASConfigDto(baseUrl, enterpriseIdStr));
        platformLoginConfigResponse.setPlatformLoginConfigValueBox(platformLoginConfigValueBox);

        return platformLoginConfigResponse;
    }

    public static PlatformLoginConfig buildDefaultPlatformLoginConfig(Integer enterpriseId) {
        PlatformLoginConfig platformLoginConfig = new PlatformLoginConfig();
        platformLoginConfig.setCreated(new Date().getTime());
        platformLoginConfig.setUpdated(new Date().getTime());
        platformLoginConfig.setDeleted(0);
        platformLoginConfig.setEnterpriseId(enterpriseId);
        platformLoginConfig.setLoginSourceType(LoginSourceTypeEnum.OAUTH2.getDesc());

        PlatformLoginConfigValueBox platformLoginConfigValueBox = new PlatformLoginConfigValueBox();

        LoginSecretConfigDto loginSecretConfigDto = PlatformLoginConfigFactory.buildDefaultLoginSecretConfigDto();
        platformLoginConfigValueBox.setLoginSecretConfigDto(loginSecretConfigDto);

        platformLoginConfigValueBox.setLoginPageConfigDto(PlatformLoginConfigFactory.buildDefaultLoginPageConfigDto());
        platformLoginConfigValueBox.setLoginValidateConfigDto(PlatformLoginConfigFactory.buildDefaultLoginValidateConfigDto());

        platformLoginConfig.setValueBox(JSON.toJSONString(platformLoginConfigValueBox));
        return platformLoginConfig;
    }

    private static LoginSecretConfigDto buildDefaultLoginSecretConfigDto() {
        LoginSecretConfigDto loginSecretConfigDto = new LoginSecretConfigDto();
        loginSecretConfigDto.setClientSecret(UUID.randomUUID().toString());
        loginSecretConfigDto.setClientId(UUID.randomUUID().toString());
        return loginSecretConfigDto;
    }


    public static PlatformLoginEnterpriseInfoResponse buildPlatformLoginEnterpriseInfoResponse(PlatformLoginConfigValueBox platformLoginConfigValueBox,
                                                                                               String platformConfig, String loginSourceType, String additionalInfo, String domainString) {
        String corpId = null;
        String agentId = null;
        String redirectUri = null;
        String apiHost = null;
        LoginPageConfigDto loginPageConfigDto = platformLoginConfigValueBox.getLoginPageConfigDto();
        if (loginSourceType != null && loginSourceType.contains(LoginSourceTypeEnum.WEIXIN.getDesc())){
            corpId = JSONPath.extract(platformConfig, LoginConfigConstants.CORP_ID).toString();
            agentId = JSONPath.extract(platformConfig, LoginConfigConstants.AGENT_ID).toString();
            redirectUri = platformLoginConfigValueBox.getLoginThirdWeiXinConfigDto().getSsoUrl();
            apiHost = JSONPath.extract(additionalInfo, LoginConfigConstants.WEIXIN_HOST_PATH).toString();
        } else if (loginSourceType != null && loginSourceType.contains(LoginSourceTypeEnum.DINGTALK.getDesc())){
            corpId = JSONPath.extract(platformConfig, LoginConfigConstants.APP_KEY).toString();
            redirectUri = platformLoginConfigValueBox.getLoginThirdDingTalkConfigDto().getSsoUrl();
        }

        String logoUrl = null;
        String iconUrl = null;
        if (domainString != null) {
            String baseUrl = JSONPath.extract(domainString, "$.base_url").toString();
            try {
                String logoPath = JSONPath.extract(domainString, "$.resource_logos['web.logo.w232'].zh\\-\\CN").toString();
                logoUrl = String.format("%s/apps/logos/download?logo_type=web.logo.w232&lang=zh-CN&logo_key=%s", baseUrl, Md5Utils.getMD5Str(logoPath));
            } catch (Exception e) {

            }

            try {
                String iconPath = JSONPath.extract(domainString, "$.resource_logos.favicon.zh\\-\\CN").toString();
                iconUrl = String.format("%s/apps/logos/download?logo_type=favicon&lang=zh-CN&logo_key=%s", baseUrl, Md5Utils.getMD5Str(iconPath));
            } catch (Exception e) {

            }
        }

        logoUrl = loginPageConfigDto.getLogoUrl() != null ? loginPageConfigDto.getLogoUrl() : logoUrl;
        iconUrl = loginPageConfigDto.getIconUrl() != null ? loginPageConfigDto.getIconUrl() : iconUrl;

        PlatformLoginEnterpriseInfoResponse response = PlatformLoginEnterpriseInfoResponse
                .builder()
                .title(loginPageConfigDto.getTitle())
                .logoUrl(logoUrl)
                .webBackgroundImgUrl(loginPageConfigDto.getWebBackgroundUrl())
                .h5BackgroundImgUrl(loginPageConfigDto.getH5BackgroundUrl())
                .corpId(corpId)
                .agentId(agentId)
                .redirectUri(redirectUri)
                .loginSourceType(loginSourceType)
                .loginUrl("http://localhost:8080/platform/login/internalLogin?enterpriseId=xxx")
                .accountPrompt(loginPageConfigDto.getAccountPrompt())
                .apiHost(apiHost)
                .iconUrl(iconUrl)
                .build();
        return response;
    }

    private Map<String, String> getLoginMaps(Integer platformId) {
        return null;
    }
}
