package com.fangcloud.thirdpartplatform.entity.custom.shjy;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShjyResultData {
    private Long mdId;

    private String mdCode;

    private String mdDescription;

    private String status;

    private String message;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String admPrimaryKey;
}
