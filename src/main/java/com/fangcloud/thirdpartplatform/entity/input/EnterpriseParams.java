package com.fangcloud.thirdpartplatform.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EnterpriseParams {

    @J<PERSON><PERSON>ield(name = "client_id")
    private String clientId;

    @JSONField(name = "enterprise_id")
    private String enterpriseId;

    @JSONField(name = "platform_id")
    private String platformId;

    /**
     * email phone  user_ticket  simple_phone_or_email
     */
    private String type;

    /**
     * type=email, identifier = <EMAIL>
     * type=phone, identifier = 1xxxxxxx99
     * type=user_ticket identifier = 外部用户id
     * type=simple_phone_or_email identifier=可以手机号，也可以邮箱
     */
    private String identifier;

    @JSONField(name = "redirect_url")
    private String redirectUrl;

    @JSONField(name = "admin_user_id")
    private long adminUserId;
}
