package com.fangcloud.thirdpartplatform.entity.input;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class ConnectionCheckParam implements Serializable {
    private static final long serialVersionUID = -345837402449747057L;

    private String sourceType;

    private String userName;

    private String password;

    private String base;

    private String url;

    private String valueBox;

    /**
     * 域名
     * 例如 @yifangyun.cn
     * 只需要加上邮箱@之后的名称就好
     */
    private String domainName;

    public void check() {
        if (!SourceTypeEnum.API.getDesc().equals(sourceType) && !SourceTypeEnum.CODE_SCRIPT.getDesc().equals(sourceType)) {
            if (StringUtils.isBlank(url)) {
                throw new ParamException("地址不能为空");
            }
            if (StringUtils.isBlank(userName)) {
                throw new ParamException("账号不能为空");
            }
            if (StringUtils.isBlank(password)) {
                throw new ParamException("密码不能为空");
            }
        } else {
            if (StringUtils.isBlank(valueBox)) {
                throw new ParamException("api参数不能为空");
            }
        }
    }
}
