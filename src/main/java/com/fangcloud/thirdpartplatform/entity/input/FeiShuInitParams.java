package com.fangcloud.thirdpartplatform.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业微信初始化参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FeiShuInitParams {

    @JSONField(name = "code")
    private String code;

    @JSONField(name = "app_id")
    private String appId;

    @JSONField(name = "app_secret")
    private String appSecret;

    @JSONField(name = "base_url")
    private String baseUrl;

    @JSONField(name = "host")
    private String host;

    /**
     * user_id, mobile
     */
    @JSONField(name = "login_type")
    private String loginType;

    /**
     * 根据飞书用户信息同步亿方云用户开关
     * false：不自动同步
     * true：自动同步
     */
    @JSONField(name = "sync_user_flag")
    private Boolean syncUserFlag;

    @JSONField(name = "enterprise_info")
    private EnterpriseParams enterpriseParams;
}
