package com.fangcloud.thirdpartplatform.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 钉钉初始化参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DingTalkInitParams {

    @JSONField(name = "code")
    private String code;

    @JSONField(name = "auth_code")
    private String authCode;

    @JSONField(name = "app_id")
    private String appId;

    @JSONField(name = "app_secret")
    private String appSecret;

    @JSONField(name = "agent_id")
    private String agentId;

    @JSONField(name = "corp_id")
    private String corpId;

    @JSONField(name = "corp_secret")
    private String corpSecret;

    @JSONField(name = "host")
    private String host;

    @JSONField(name = "base_url")
    private String baseUrl;

    @JSONField(name = "union_id")
    private String unionId;

    /**
     * 根据钉钉用户信息同步亿方云用户开关
     * false：不自动同步
     * true：自动同步
     */
    @JSONField(name = "sync_user_flag")
    private Boolean syncUserFlag;

    /**
     * user_id, mobile
     */
    @JSONField(name = "login_type")
    private String loginType;

    @JSONField(name = "enterprise_info")
    private EnterpriseParams enterpriseParams;

    /**
     * 消息机器人的编码
     */
    @JSONField(name = "robot_code")
    private String robotCode;
}
