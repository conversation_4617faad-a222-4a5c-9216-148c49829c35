package com.fangcloud.thirdpartplatform.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MessageParams {

    @JSONField(name = "time")
    private String time;

    @JSONField(name = "receivers")
    private String receivers;

    @JSONField(name = "type")
    private String type;

    @JSONField(name = "content")
    private String content;

    @JSONField(name = "title")
    private String title;

    @JSONField(name = "web_url")
    private String webUrl;

    @J<PERSON>NField(name = "h5_url")
    private String h5Url;

    @JSONField(name = "real_h5_url")
    private String realH5Url;

    @JSONField(name = "customer_id")
    private String customerId;

    @JSONField(name = "enterprise_id")
    private String enterpriseId;

    @JSONField(name = "pic_url")
    private String picUrl;

    @JSONField(name = "weixin_params")
    private WeixinWorkInitParams weixinWorkInitParams;

    @JSONField(name = "ding_params")
    private DingTalkInitParams dingTalkInitParams;

    @JSONField(name = "ccwork_params")
    private CcWorkInitParam ccWorkInitParams;

    @JSONField(name = "feishu_params")
    private FeiShuInitParams feiShuInitParams;

    @JSONField(name = "message_push_title")
    private String messagePushTitle;
}
