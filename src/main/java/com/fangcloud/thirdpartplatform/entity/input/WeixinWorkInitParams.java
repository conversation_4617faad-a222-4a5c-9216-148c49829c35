package com.fangcloud.thirdpartplatform.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 企业微信初始化参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeixinWorkInitParams {

    @JSONField(name = "code")
    private String code;

    @JSONField(name = "corp_id")
    private String corpId;

    @JSONField(name = "corp_secret")
    private String corpSecret;

    @JSONField(name = "agent_id")
    private String agentId;

    @JSONField(name = "host")
    private String host;

    @JSONField(name = "base_url")
    private String baseUrl;

    /**
     * user_id, mobile
     */
    @JSONField(name = "login_type")
    private String loginType;

    /**
     * 根据钉钉用户信息同步亿方云用户开关
     * false：不自动同步
     * true：自动同步
     */
    @JSONField(name = "sync_user_flag")
    private Boolean syncUserFlag;

    /**
     * 免密登陆授权方式
     * snsapi_base:静默授权
     * snsapi_privateinfo：手动授权
     */
    @JSONField(name = "authorization_method")
    private String authorizationMethod;

    @JSONField(name = "enterprise_info")
    private EnterpriseParams enterpriseParams;
}
