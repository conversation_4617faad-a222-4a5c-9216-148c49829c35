package com.fangcloud.thirdpartplatform.entity.input;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CcWorkInitParam {

    @JSO<PERSON>ield(name = "corp_id")
    private String corpId;

    @JSONField(name = "enterprise_info")
    private EnterpriseParams enterpriseParams;

    @JSONField(name = "msg_signature")
    private String msgSignature;

    @JSONField(name = "time_stamp")
    private String timeStamp;

    @JSONField(name = "nonce")
    private String nonce;

    @JSONField(name = "encrypt")
    private String encrypt;

    /**
     * user_ticket, email, phone
     */
    @JSONField(name = "account_type")
    private String accountType;
}
