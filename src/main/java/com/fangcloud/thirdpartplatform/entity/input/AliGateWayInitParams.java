package com.fangcloud.thirdpartplatform.entity.input;

import com.alibaba.cloudapi.sdk.enums.HttpMethod;
import com.alibaba.cloudapi.sdk.enums.Scheme;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 阿里网关初始化参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AliGateWayInitParams {

    String appKey;
    String appSecret;
    String host;
    Scheme scheme;
    String path;
    HttpMethod method;
    String pageNum;
    String pageSize;
}
