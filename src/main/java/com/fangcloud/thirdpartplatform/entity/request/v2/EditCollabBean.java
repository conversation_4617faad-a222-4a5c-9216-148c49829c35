package com.fangcloud.thirdpartplatform.entity.request.v2;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class EditCollabBean  {
    @JSONField(name = "collabs_roles")
    private List<CollabRole> collabRoles;

    @JSONField(name = "invite_user_collabs_roles")
    private List<CollabRole> invite_user_collabs_roles;

    @JSONField(name = "invite_group_collabs_roles")
    private List<CollabRole> invite_group_collabs_roles;

    @JSONField(name = "item_id")
    private Long itemId;

    public EditCollabBean (Long collabId, String role){
        List<CollabRole> collabRoles = new ArrayList<>();
        CollabRole collabRole = new CollabRole(collabId, role);
        collabRoles.add(collabRole);
        this.collabRoles = collabRoles;
    }

    public EditCollabBean (){
    }

    public List<CollabRole> getCollabRoles() {
        return collabRoles;
    }

    public void setCollabRoles(List<CollabRole> collabRoles) {
        this.collabRoles = collabRoles;
    }

    public static class CollabRole {
        @JSONField(name = "collab_id")
        private Long collabId;
        private String role;
        @JSONField(name = "user_id")
        private Long userId;

        public CollabRole(Long collabId, String role) {
            this.collabId = collabId;
            this.role = role;
        }

        public CollabRole(Long collabId, String role, Long userId) {
            this.collabId = collabId;
            this.role = role;
            this.userId = userId;
        }

        public Long getCollabId() {
            return collabId;
        }

        public void setCollabId(Long collabId) {
            this.collabId = collabId;
        }

        public String getRole() {
            return role;
        }

        public void setRole(String role) {
            this.role = role;
        }
    }
}

