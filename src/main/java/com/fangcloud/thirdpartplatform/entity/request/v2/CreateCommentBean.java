package com.fangcloud.thirdpartplatform.entity.request.v2;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class CreateCommentBean {

    @JSONField(name = "item_id")
    private String itemId;

    @JSONField(name = "item_type")
    private String itemType;

    @JSONField(name = "content")
    private String content;


    public CreateCommentBean(String itemType, String content, String itemId){
        this.itemId = itemId;
        this.itemType = itemType;
        this.content = content;
    }



}

