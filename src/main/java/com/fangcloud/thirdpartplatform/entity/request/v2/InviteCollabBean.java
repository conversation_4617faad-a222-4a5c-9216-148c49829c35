package com.fangcloud.thirdpartplatform.entity.request.v2;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class InviteCollabBean {

    @JSONField(name = "folder_id")
    private Long folderId;
    @JSONField(name = "invitation_message")
    private String invitationMessage;
    @JSONField(name = "invited_users")
    private String invitedUsers;
    @JSONField(name = "invited_groups")
    private String invitedGroups;
}
