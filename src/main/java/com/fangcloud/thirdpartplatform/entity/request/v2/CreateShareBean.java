package com.fangcloud.thirdpartplatform.entity.request.v2;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class CreateShareBean {

    @J<PERSON>NField(name = "access")
    private String access;

    @J<PERSON><PERSON>ield(name = "current_version")
    private Boolean currentVersion;

    @JSONField(name = "disable_download")
    private Boolean disableDownload;

    @J<PERSON>NField(name = "due_time")
    private Long dueTime;

    @JSONField(name = "password_protected")
    private Boolean passwordProtected;

    @JSONField(name = "password")
    private String password;

    @J<PERSON><PERSON>ield(name = "is_preview_limit")
    private Boolean enablePreviewLimit;

    @J<PERSON><PERSON>ield(name = "is_download_limit_v2")
    private Boolean enableDownloadLimitV2;

    @JSONField(name = "preview_limit")
    private Long previewLimit;

    @JSONField(name = "download_limit_v2")
    private Long downloadLimitV2;

    @JSONField(name = "description")
    private String description;

    @JSONField(name = "is_share_wps_edit")
    private Boolean enableShareWpsEdit;

    @JSO<PERSON>ield(name = "invited_user_ids")
    private List<Long> invitedUserIds;

    @JSONField(name = "group_ids")
    private List<Long> groupIds;

    @JSONField(name = "item_typed_id")
    private String itemTypedId;

    @JSONField(name = "watermark_template_info")
    private JSONObject watermarkTemplateInfo;

    @JSONField(name = "is_from_behavior_review")
    private Boolean fromBehaviorReview;

}

