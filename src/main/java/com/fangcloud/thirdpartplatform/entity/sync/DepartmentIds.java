package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;

public class DepartmentIds {
    @JSONField(name = "department_id")
    private Long departmentId;
    @JSONField(name = "custom_department_id")
    private String customDepartmentId;
    @JSONField(name = "name")
    private String name;

    public Long getDepartmentId() {
        return departmentId;
    }

    public String getCustomDepartmentId() {
        return customDepartmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public void setCustomDepartmentId(String customDepartmentId) {
        this.customDepartmentId = customDepartmentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
