package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.J<PERSON><PERSON>ield;

import java.util.List;

public class PublicUserBean {

    @J<PERSON><PERSON>ield(name = "name")
    private String name;
    private List<String> identifiers;
    @<PERSON><PERSON><PERSON>ield(name = "storage_id")
    private Long storageId;
    @JSO<PERSON>ield(name = "space_total")
    private Long spaceTotal;
    @JSO<PERSON>ield(name = "hide_phone")
    private Boolean hidePhone;
    @JSO<PERSON>ield(name = "disable_download")
    private Boolean disableDownload;
    private String password;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getIdentifiers() {
        return identifiers;
    }

    public void setIdentifiers(List<String> identifiers) {
        this.identifiers = identifiers;
    }

    public Long getStorageId() {
        return storageId;
    }

    public void setStorageId(Long storageId) {
        this.storageId = storageId;
    }

    public Long getSpaceTotal() {
        return spaceTotal;
    }

    public void setSpaceTotal(Long spaceTotal) {
        this.spaceTotal = spaceTotal;
    }

    public Boolean getHidePhone() {
        return hidePhone;
    }

    public void setHidePhone(Boolean hidePhone) {
        this.hidePhone = hidePhone;
    }

    public Boolean getDisableDownload() {
        return disableDownload;
    }

    public void setDisableDownload(Boolean disableDownload) {
        this.disableDownload = disableDownload;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

}
