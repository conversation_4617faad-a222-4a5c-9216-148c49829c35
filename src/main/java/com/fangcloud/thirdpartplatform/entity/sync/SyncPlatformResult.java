package com.fangcloud.thirdpartplatform.entity.sync;
import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class SyncPlatformResult {
    @JSONField(name = "success_items_count")
    private long successItemsCount;
    private List<ErrorInfo> errors;
    @JSONField(name = "failed_items")
    private List<FailedItemInfo> failedItems;
    @JSONField(name = "request_id")
    private String requestId;

    public long getSuccessItemsCount() {
        return successItemsCount;
    }

    public void setSuccessItemsCount(long successItemsCount) {
        this.successItemsCount = successItemsCount;
    }

    public List<ErrorInfo> getErrors() {
        return errors;
    }

    public void setErrors(List<ErrorInfo> errors) {
        this.errors = errors;
    }

    public List<FailedItemInfo> getFailedItems() {
        return failedItems;
    }

    public void setFailedItems(List<FailedItemInfo> failedItems) {
        this.failedItems = failedItems;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public class ErrorInfo {
        @JSONField(name = "code")
        private String code;
        @JSONField(name = "msg")
        private String msg;

        public ErrorInfo(String code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
    }

    public class FailedItemInfo {
        @JSONField(name = "reason")
        private String reason;

        @JSONField(name = "ids")
        private List<String> ids;

        public String getReason() {
            return reason;
        }

        public void setReason(String reason) {
            this.reason = reason;
        }

        public List<String> getIds() {
            return ids;
        }

        public void setIds(List<String> ids) {
            this.ids = ids;
        }
    }
}
