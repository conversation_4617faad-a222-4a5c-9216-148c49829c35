package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

import java.util.List;

public class CreateUserResult {
    @J<PERSON>NField(name = "identifiers_invalid")
    private List<String> identifiersInvalid;
    @JSO<PERSON>ield(name = "identifiers_already_invited")
    private List<String> identifiersAlreadyInvited;
    @JSONField(name = "identifiers_already_registered")
    private List<String> identifiersAlreadyRegistered;
    private List<UserResult> users;

    private String errorMessage;

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public List<String> getIdentifiersInvalid() {
        return identifiersInvalid;
    }

    public void setIdentifiersInvalid(List<String> identifiersInvalid) {
        this.identifiersInvalid = identifiersInvalid;
    }

    public List<String> getIdentifiersAlreadyInvited() {
        return identifiersAlreadyInvited;
    }

    public void setIdentifiersAlreadyInvited(List<String> identifiersAlreadyInvited) {
        this.identifiersAlreadyInvited = identifiersAlreadyInvited;
    }

    public List<String> getIdentifiersAlreadyRegistered() {
        return identifiersAlreadyRegistered;
    }

    public void setIdentifiersAlreadyRegistered(List<String> identifiersAlreadyRegistered) {
        this.identifiersAlreadyRegistered = identifiersAlreadyRegistered;
    }

    public List<UserResult> getUsers() {
        return users;
    }

    public void setUsers(List<UserResult> users) {
        this.users = users;
    }
}
