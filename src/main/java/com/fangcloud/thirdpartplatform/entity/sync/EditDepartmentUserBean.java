package com.fangcloud.thirdpartplatform.entity.sync;


import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class EditDepartmentUserBean  {
    @JSONField(name = "add_user_ids")
    private List<Long> addUserIds;
    @JSO<PERSON>ield(name = "delete_user_ids")
    private List<Long> deleteUserIds;

    public List<Long> getAddUserIds() {
        return addUserIds;
    }

    public void setAddUserIds(List<Long> addUserIds) {
        this.addUserIds = addUserIds;
    }

    public List<Long> getDeleteUserIds() {
        return deleteUserIds;
    }

    public void setDeleteUserIds(List<Long> deleteUserIds) {
        this.deleteUserIds = deleteUserIds;
    }
}
