package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;

public class PublicDepartmentResult {

    @JSONField(name = "id")
    private Long id;

    @J<PERSON>NField(name = "name")
    private String name;

    @J<PERSON><PERSON>ield(name = "path")
    private String path;

    @J<PERSON><PERSON>ield(name = "parent_id")
    private Long parentId;

    private MiniUser director;

    @JSO<PERSON>ield(name = "space_total")
    private Long spaceTotal;

    @JSONField(name = "space_used")
    private Long spaceUsed;

    @JSONField(name = "hide_phone")
    private Boolean hidePhone;

    @JSONField(name = "disable_share")
    private Boolean disableShare;

    @JSONField(name = "enable_watermark")
    private Boolean enableWatermark;

    @JSO<PERSON>ield(name = "collab_auto_accepted")
    private Boolean collabAutoAccepted;

    @JSO<PERSON>ield(name = "hide_email")
    private Boolean hideEmail;

    @JSONField(name = "order")
    private Long order;

    private String errorMessage;

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public MiniUser getDirector() {
        return director;
    }

    public void setDirector(MiniUser director) {
        this.director = director;
    }

    public Long getSpaceTotal() {
        return spaceTotal;
    }

    public void setSpaceTotal(Long spaceTotal) {
        this.spaceTotal = spaceTotal;
    }

    public Long getSpaceUsed() {
        return spaceUsed;
    }

    public void setSpaceUsed(Long spaceUsed) {
        this.spaceUsed = spaceUsed;
    }

    public Boolean getHidePhone() {
        return hidePhone;
    }

    public void setHidePhone(Boolean hidePhone) {
        this.hidePhone = hidePhone;
    }

    public Boolean getDisableShare() {
        return disableShare;
    }

    public void setDisableShare(Boolean disableShare) {
        this.disableShare = disableShare;
    }

    public Boolean getEnableWatermark() {
        return enableWatermark;
    }

    public void setEnableWatermark(Boolean enableWatermark) {
        this.enableWatermark = enableWatermark;
    }

    public Boolean getCollabAutoAccepted() {
        return collabAutoAccepted;
    }

    public void setCollabAutoAccepted(Boolean collabAutoAccepted) {
        this.collabAutoAccepted = collabAutoAccepted;
    }

    public Boolean getHideEmail() {
        return hideEmail;
    }

    public void setHideEmail(Boolean hideEmail) {
        this.hideEmail = hideEmail;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }
}
