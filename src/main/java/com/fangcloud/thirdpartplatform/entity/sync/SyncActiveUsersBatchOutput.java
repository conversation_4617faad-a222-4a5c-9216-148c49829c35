package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;


@Data
public class SyncActiveUsersBatchOutput {

    @J<PERSON>NField(name = "status")
    private String status;

    @J<PERSON>NField(name = "fail_user_ids")
    private List<SyncActiveFailUserId> failUserIds;

    @JSONField(name = "success_user_ids")
    private List<Long>  successUserIds;

}
