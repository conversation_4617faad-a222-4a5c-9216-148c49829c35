package com.fangcloud.thirdpartplatform.entity.sync;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
@Builder
public class DataBaseConfig {

    /**
     * 数据库地址
     */
    @NotNull
    private String url;

    /**
     * 数据库用户名
     */
    @NotNull
    private String userName;

    /**
     * 数据库密码
     */
    @NotNull
    private String password;

    /**
     * 数据库类型
     *  mysql
     *  oracle
     *  sqlServer
     *  dm
     */
    @NotNull
    private String type;

    /**
     * 驱动
     */
    private String driver;

    private String sql;
}
