package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;
import com.sync.common.entity.dto.YfyUser;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncUserBean extends BaseSyncBean{

    private List<YfyUser> users;

    @J<PERSON><PERSON>ield(name = "default_department_id")
    private String defaultDepartmentId;

    @J<PERSON><PERSON>ield(name = "default_group_id")
    private String defaultGroupId;

    @J<PERSON><PERSON>ield(name = "create_default_password")
    private Boolean createDefaultPassword;

    @J<PERSON><PERSON>ield(name = "custom_password")
    private String customPassword;
}
