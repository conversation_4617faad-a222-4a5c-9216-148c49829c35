package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;

public class DeleteUserBean {

    @JSONField(name = "user_received_items")
    private Long userReceivedItems;

    public Long getUserReceivedItems() {
        return userReceivedItems;
    }

    public void setUserReceivedItems(Long userReceivedItems) {
        this.userReceivedItems = userReceivedItems;
    }
}
