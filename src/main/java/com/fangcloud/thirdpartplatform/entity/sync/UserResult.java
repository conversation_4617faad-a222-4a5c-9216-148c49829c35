package com.fangcloud.thirdpartplatform.entity.sync;


import com.alibaba.fastjson.annotation.JSONField;

public class UserResult {
    private Long id;
    private String name;
    private String login;
    private String phone;
    private String email;
    @<PERSON><PERSON><PERSON><PERSON>(name = "profile_pic_key")
    private String profilePicKey;
    private Boolean active;
    @JSONField(name = "full_name_pinyin")
    private String fullNamePinyin;
    @JSONField(name = "pinyin_first_letters")
    private String pinyinFirstLetters;
    @JSO<PERSON>ield(name = "hide_phone")
    private Boolean hidePhone;
    @<PERSON><PERSON><PERSON>ield(name = "disable_download")
    private Boolean disableDownload;
    @<PERSON><PERSON><PERSON>ield(name = "space_used")
    private Long spaceUsed;
    @JSONField(name = "space_total")
    private Long spaceTotal;

    private String errorMessage;

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getProfilePicKey() {
        return profilePicKey;
    }

    public void setProfilePicKey(String profilePicKey) {
        this.profilePicKey = profilePicKey;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public String getFullNamePinyin() {
        return fullNamePinyin;
    }

    public void setFullNamePinyin(String fullNamePinyin) {
        this.fullNamePinyin = fullNamePinyin;
    }

    public String getPinyinFirstLetters() {
        return pinyinFirstLetters;
    }

    public void setPinyinFirstLetters(String pinyinFirstLetters) {
        this.pinyinFirstLetters = pinyinFirstLetters;
    }

    public Boolean getHidePhone() {
        return hidePhone;
    }

    public void setHidePhone(Boolean hidePhone) {
        this.hidePhone = hidePhone;
    }

    public Boolean getDisableDownload() {
        return disableDownload;
    }

    public void setDisableDownload(Boolean disableDownload) {
        this.disableDownload = disableDownload;
    }

    public Long getSpaceUsed() {
        return spaceUsed;
    }

    public void setSpaceUsed(Long spaceUsed) {
        this.spaceUsed = spaceUsed;
    }

    public Long getSpaceTotal() {
        return spaceTotal;
    }

    public void setSpaceTotal(Long spaceTotal) {
        this.spaceTotal = spaceTotal;
    }
}
