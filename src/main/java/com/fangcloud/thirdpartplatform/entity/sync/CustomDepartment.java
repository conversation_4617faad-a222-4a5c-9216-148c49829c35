package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

public class CustomDepartment {
    private Long id;
    @<PERSON><PERSON><PERSON>ield(name = "custom_id")
    private String customId;
    @<PERSON><PERSON><PERSON><PERSON>(name = "parent_id")
    private Long parentId;
    @<PERSON><PERSON><PERSON><PERSON>(name = "custom_parent_id")
    private String customParentId;
    private String name;
    @JSONField(name = "total_space")
    private Long totalSpace;
    @JSONField(name = "director_id")
    private Long directorId;
    @JSONField(name = "custom_director_id")
    private String customDirectorId;
    private Long order;

    public String getName() {
        return name;
    }

    public Long getId() {
        return id;
    }

    public Long getParentId() {
        return parentId;
    }

    public String getCustomId() {
        return customId;
    }

    public String getCustomParentId() {
        return customParentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setCustomId(String customId) {
        this.customId = customId;
    }

    public void setCustomParentId(String customParentId) {
        this.customParentId = customParentId;
    }

    public Long getTotalSpace() {
        return totalSpace;
    }

    public void setTotalSpace(Long totalSpace) {
        this.totalSpace = totalSpace;
    }

    public Long getDirectorId() {
        return directorId;
    }

    public void setDirectorId(Long directorId) {
        this.directorId = directorId;
    }

    public String getCustomDirectorId() {
        return customDirectorId;
    }

    public void setCustomDirectorId(String customDirectorId) {
        this.customDirectorId = customDirectorId;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }
}
