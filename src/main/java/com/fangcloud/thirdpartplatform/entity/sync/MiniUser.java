package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class MiniUser {
    private Long id;
    private String name;
    private String login;
    @J<PERSON><PERSON>ield(name = "enterprise_id")
    private Long enterpriseId;
    private List<MiniDepartment> miniDepartmentList;

    public MiniUser() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLogin() {
        return login;
    }

    public void setLogin(String login) {
        this.login = login;
    }

    public Long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public List<MiniDepartment> getMiniDepartmentList() {
        return miniDepartmentList;
    }

    public void setMiniDepartmentList(List<MiniDepartment> miniDepartmentList) {
        this.miniDepartmentList = miniDepartmentList;
    }
}
