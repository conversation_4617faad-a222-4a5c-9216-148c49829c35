package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;

import javax.validation.constraints.Min;

public class PublicDepartmentBean {
    @JSONField(name = "department_name")
    private String departmentName;

    @J<PERSON>NField(name = "parent_id")
    private Long parentId;

    @JSO<PERSON>ield(name = "director_id")
    @Min(1)
    private Long directorId;

    @JSONField(name = "space_total")
    @Min(1)
    private Long spaceTotal;

    @JSO<PERSON>ield(name = "hide_phone")
    private Boolean hidePhone;

    @J<PERSON><PERSON>ield(name = "disable_share")
    private Boolean disableShare;

    @JSONField(name = "enable_watermark")
    private Boolean enableWatermark;

    @JSONField(name = "create_common_folder")
    private Boolean createCommonFolder;

    @JSONField(name = "collab_auto_accepted")
    private Boolean collabAutoAccepted;

    @JSONField(name = "order")
    private Long order;

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getDirectorId() {
        return directorId;
    }

    public void setDirectorId(Long directorId) {
        this.directorId = directorId;
    }

    public Long getSpaceTotal() {
        return spaceTotal;
    }

    public void setSpaceTotal(Long spaceTotal) {
        this.spaceTotal = spaceTotal;
    }

    public Boolean getHidePhone() {
        return hidePhone;
    }

    public void setHidePhone(Boolean hidePhone) {
        this.hidePhone = hidePhone;
    }

    public Boolean getDisableShare() {
        return disableShare;
    }

    public void setDisableShare(Boolean disableShare) {
        this.disableShare = disableShare;
    }

    public Boolean getEnableWatermark() {
        return enableWatermark;
    }

    public void setEnableWatermark(Boolean enableWatermark) {
        this.enableWatermark = enableWatermark;
    }

    public Boolean getCreateCommonFolder() {
        return createCommonFolder;
    }

    public void setCreateCommonFolder(Boolean createCommonFolder) {
        this.createCommonFolder = createCommonFolder;
    }

    public Boolean getCollabAutoAccepted() {
        return collabAutoAccepted;
    }

    public void setCollabAutoAccepted(Boolean collabAutoAccepted) {
        this.collabAutoAccepted = collabAutoAccepted;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }
}
