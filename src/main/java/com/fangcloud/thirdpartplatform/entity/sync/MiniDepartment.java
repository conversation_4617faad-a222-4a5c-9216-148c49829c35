package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class MiniDepartment {
    private Long id;
    private String name;
    private MiniUser director;
    @<PERSON><PERSON><PERSON>ield(name = "user_count")
    private Long userCount;
    @JSONField(name = "children_departments_count")
    private Long childrenDepartmentsCount;
    @JSONField(name = "direct_item_count")
    private Long directItemCount;
    private Long order;
    private Integer level;
    private List<MiniDepartment> parentDepartments;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getUserCount() {
        return userCount;
    }

    public void setUserCount(Long userCount) {
        this.userCount = userCount;
    }

    public Long getChildrenDepartmentsCount() {
        return childrenDepartmentsCount;
    }

    public void setChildrenDepartmentsCount(Long childrenDepartmentsCount) {
        this.childrenDepartmentsCount = childrenDepartmentsCount;
    }

    public Long getDirectItemCount() {
        return directItemCount;
    }

    public void setDirectItemCount(Long directItemCount) {
        this.directItemCount = directItemCount;
    }

    public MiniUser getDirector() {
        return director;
    }

    public void setDirector(MiniUser director) {
        this.director = director;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public List<MiniDepartment> getParentDepartments() {
        return parentDepartments;
    }

    public void setParentDepartments(List<MiniDepartment> parentDepartments) {
        this.parentDepartments = parentDepartments;
    }
}
