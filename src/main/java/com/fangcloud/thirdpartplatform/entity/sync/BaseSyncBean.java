package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseSyncBean {

    @JSONField(name = "enterprise_id")
    private Long enterpriseId;

    @JSONField(name = "platform_id")
    private Long platformId;

    @JSONField(name = "request_id")
    private String requestId;
}
