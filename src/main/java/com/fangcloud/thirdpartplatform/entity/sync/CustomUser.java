package com.fangcloud.thirdpartplatform.entity.sync;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

public class CustomUser {
    private Long id;
    private String name;
    @J<PERSON><PERSON>ield(name = "custom_id")
    private String customId;

    private String email;

    private String phone;
    @J<PERSON><PERSON>ield(name = "department_ids")
    private List<DepartmentIds> departmentIds;
    /**
     * 是否激活 0未激活 1激活
     * */
    private boolean active;

    private Long order;

    private String valueBox;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCustomId() {
        return customId;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getEmail() {
        return email;
    }

    public List<DepartmentIds> getDepartmentIds() {
        return departmentIds;
    }

    public void setCustomId(String customId) {
        this.customId = customId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public void setDepartmentIds(List<DepartmentIds> departmentIds) {
        this.departmentIds = departmentIds;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getValueBox() {
        return valueBox;
    }

    public void setValueBox(String valueBox) {
        this.valueBox = valueBox;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }
}
