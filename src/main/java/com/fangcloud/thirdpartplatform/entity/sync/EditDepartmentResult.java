package com.fangcloud.thirdpartplatform.entity.sync;


import com.alibaba.fastjson.annotation.JSO<PERSON>ield;

import java.util.List;

public class EditDepartmentResult {
    @J<PERSON><PERSON>ield(name = "add_users")
    private List<MiniUser> addUsers;
    @<PERSON>SO<PERSON><PERSON>(name = "users_already_in_department")
    private List<MiniUser> usersAlreadyInDepartment;
    @JSONField(name = "users_exceed_department_limit")
    private List<MiniUser> usersExceedDepartmentLimit;
    @JSONField(name = "users_not_deleted")
    private List<MiniUser> usersNotDeleted;

    private String errorMessage;

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public List<MiniUser> getAddUsers() {
        return addUsers;
    }

    public void setAddUsers(List<MiniUser> addUsers) {
        this.addUsers = addUsers;
    }

    public List<MiniUser> getUsersAlreadyInDepartment() {
        return usersAlreadyInDepartment;
    }

    public void setUsersAlreadyInDepartment(List<MiniUser> usersAlreadyInDepartment) {
        this.usersAlreadyInDepartment = usersAlreadyInDepartment;
    }

    public List<MiniUser> getUsersExceedDepartmentLimit() {
        return usersExceedDepartmentLimit;
    }

    public void setUsersExceedDepartmentLimit(List<MiniUser> usersExceedDepartmentLimit) {
        this.usersExceedDepartmentLimit = usersExceedDepartmentLimit;
    }

    public List<MiniUser> getUsersNotDeleted() {
        return usersNotDeleted;
    }

    public void setUsersNotDeleted(List<MiniUser> usersNotDeleted) {
        this.usersNotDeleted = usersNotDeleted;
    }
}
