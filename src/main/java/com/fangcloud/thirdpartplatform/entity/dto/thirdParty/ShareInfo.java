package com.fangcloud.thirdpartplatform.entity.dto.thirdParty;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Setter
@Getter
public class ShareInfo implements Serializable {
    private static final long serialVersionUID = -1467569440762290523L;

    /**
     * public链接匿名,company链接公司成员,collaborators指定成员
     */
    @JSONField(name = "access")
    private String access;

    /**
     * 如果是文件，指定当前版本分享
     */
    @JSONField(name = "current_version")
    private boolean currentVersion;

    /**
     * 描述
     */
    @JSONField(name = "description")
    private String description;

    /**
     * 禁止下载
     */
    @JSONField(name = "disable_download")
    private boolean disableDownload;

    /**
     * 可以在线编辑
     */
    @JSONField(name = "is_share_wps_edit")
    private boolean enableShareWpsEdit;

    /**
     * 是否设置下载次数
     */
    @JSONField(name = "is_download_limit_v2")
    private boolean enableDownloadLimitV2;

    /**
     * 下载次数
     */
    @JSONField(name = "download_limit_v2")
    private long downloadLimitV2;

    /**
     * 是否设置预览次数
     */
    @JSONField(name = "is_preview_limit")
    private boolean enablePreviewLimit;

    /**
     * 预览次数
     */
    @JSONField(name = "preview_limit")
    private long previewLimit;

    /**
     * 过期时间戳
     */
    @JSONField(name = "due_time")
    private long dueTime;

    /**
     * 分享群组id
     */
    @JSONField(name = "group_ids")
    private List<Long> groupIds;

    /**
     * 分享用户id
     */
    @JSONField(name = "invited_user_ids")
    private List<Long> invitedUserIds;

    /**
     * 分享文件
     */
    @JSONField(name = "item_typed_id")
    private String itemTypedId;

    /**
     * 是否开启密码保护
     */
    @JSONField(name = "password_protected")
    private boolean passwordProtected;

    /**
     * 密码
     */
    @JSONField(name = "password")
    private String password;

    /**
     * 名称
     */
    @JSONField(name = "item_name")
    private String itemName;

    /**
     * 水印信息
     */
    @JSONField(name = "watermark_template_info")
    private JSONObject watermarkTemplateInfo;
}

