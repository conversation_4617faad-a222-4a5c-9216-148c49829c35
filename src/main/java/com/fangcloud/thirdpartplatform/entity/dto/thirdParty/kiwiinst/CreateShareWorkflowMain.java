package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CreateShareWorkflowMain extends CreateWorkflowMain implements Serializable {

    private static final long serialVersionUID = -6338915254334450801L;

    /**
     * 数据类型：file（文件），folder（文件夹）
     */
    private CreateWorkflowValue dataType;

    /**
     * 文件或者文件夹名称
     */
    private CreateWorkflowValue dataName;

    /**
     * 文件或者文件夹id
     */
    private CreateWorkflowValue dataId;

    /**
     * 分享文件或者文件夹所属部门id
     */
    private CreateWorkflowValue dataDeptId;

    /**
     * 企业id
     */
    @JSONField(name = "enterprise_id")
    private CreateWorkflowValue enterpriseId;

    /**
     * 分享留言
     */
    private CreateWorkflowValue description;

    /**
     * public公共链接,company链接公司成员,collaborators指定成员（这个类型时需要解析dt1数据）
     */
    private CreateWorkflowValue fxlx;

    /**
     * 如果是文件，指定当前版本分享，false
     */
    @JSONField(name = "current_version")
    private CreateWorkflowValue currentVersion;

    /**
     * 禁止下载，false
     */
    @JSONField(name = "disable_download")
    private CreateWorkflowValue disableDownload;

    /**
     * 可以在线编辑，false
     */
    @JSONField(name = "is_share_wps_edit")
    private CreateWorkflowValue isShareWpsEdit;

    /**
     * 是否设置下载次数，false
     */
    @JSONField(name = "is_download_limit_v2")
    private CreateWorkflowValue isDownloadLimitV2;

    /**
     * 下载次数，10
     */
    @JSONField(name = "download_limit_v2")
    private CreateWorkflowValue downloadLimitV2;

    /**
     * 是否设置预览次数，false
     */
    @JSONField(name = "is_preview_limit")
    private CreateWorkflowValue isPreviewLimit;

    /**
     * 预览次数，10
     */
    @JSONField(name = "preview_limit")
    private CreateWorkflowValue previewLimit;

    /**
     * 过期时间戳
     */
    @JSONField(name = "due_time")
    private CreateWorkflowValue dueTime;

    /**
     * 是否开启密码保护
     */
    @JSONField(name = "password_protected")
    private CreateWorkflowValue passwordProtected;

    /**
     * 密码
     */
    private CreateWorkflowValue password;

    /**
     * 水印信息，里面是json字符串，需要解析
     */
    @JSONField(name = "watermark_template_info")
    private CreateWorkflowValue watermarkTemplateInfo;


    public CreateShareWorkflowMain() {
    }

}
