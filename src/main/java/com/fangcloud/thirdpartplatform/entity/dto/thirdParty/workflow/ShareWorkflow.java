package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow;

import com.alibaba.fastjson.annotation.JSONField;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ShareInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ShareWorkflow extends ShareInfo implements Serializable {

    private static final long serialVersionUID = -7509686014067951632L;

    @JSONField(name = "enterprise_id")
    private Long enterpriseId;

    @JSONField(name = "unique_id")
    private String uniqueId;

    @J<PERSON>NField(name = "task_id")
    private String taskId;

    @JSONField(name = "receiver_id")
    private Long receiverId;

    @JSONField(name = "author_id")
    private Long authorId;
}
