package com.fangcloud.thirdpartplatform.entity.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class LoginSourceLDAPConfigDto implements Serializable {
    private static final long serialVersionUID = -4104901778249038722L;

    private String url;

    private String userName;

    private String password;

    // 根域
    private String baseOu;

    /**
     * 选填，域名
     * 例如 @yifangyun.cn
     * 只需要加上邮箱@之后的名称就好
     */
    private String domainName;

}
