package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow;

import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.MoveInfoCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Data
public class MoveWorkflowCall extends MoveInfoCall implements Serializable {

    private static final long serialVersionUID = -850243191629485937L;
    private long enterpriseId;

    private String uniqueId;

    private String taskId;

    private long receiverId;

    private ThirdpartyCustom createUser;

    private String reviewType;
}
