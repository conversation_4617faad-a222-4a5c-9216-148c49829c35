package com.fangcloud.thirdpartplatform.entity.dto;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

@Data
public class ValueBoxDto implements Serializable {
        private static final long serialVersionUID = 6621325238522703125L;

        private String url;

        private String userName;

        private String password;

        //重复数据处理 OVER 覆盖 SKIP 跳过
        private String repeatHandle;

        // 部门空间大小
        private Integer deptSpace;

        // 个人空间大小
        private Integer userSpace;

        public void check() {
                if (StringUtils.isBlank(url)) {
                        throw new ParamException("地址不能为空");
                }
                if (StringUtils.isBlank(userName)) {
                        throw new ParamException("账号不能为空");
                }
                if (StringUtils.isBlank(password)) {
                        throw new ParamException("密码不能为空");
                }
        }
}