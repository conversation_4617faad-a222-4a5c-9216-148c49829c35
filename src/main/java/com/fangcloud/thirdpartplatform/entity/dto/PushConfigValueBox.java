package com.fangcloud.thirdpartplatform.entity.dto;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-05-20
 **/
@Data
public class PushConfigValueBox extends ValueBoxDto {
    private static final long serialVersionUID = 4255233590973503086L;

    /**
     * 推送路径
     */
    private String pushUri;

    /**
     * 数据处理是否异步
     */
    private Boolean isAsync;

    /**
     * 部门同步规则
     */
    private DepConfigDto depConfigDto;

    /**
     * 用户同步规则
     */
    private UserConfigDto userConfigDto;

    /**
     * 入参数据类型
     */
    private String paramDataType;

    /**
     * 返回数据类型
     */
    private String returnDataType;

    /**
     * head取值
     */
    private String headValuePath;

    /**
     * 入参取值path
     */
    private String paramValuePath;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 参数配置
     */
    private List<PushParamConfigDto> paramConfig;

    /**
     * 出参配置
     */
    private List<PushResultConfigDto> resultConfig;


    public void check() {
        if (StringUtils.isBlank(pushUri)) {
            throw new ParamException("推送path不能为空");
        }
        if (StringUtils.isBlank(operationType)) {
            throw new ParamException("操作类型不能为空");
        }
        if (CollectionUtils.isEmpty(paramConfig)) {
            throw new ParamException("参数配置不能为空");
        }
        if (CollectionUtils.isEmpty(resultConfig)) {
            throw new ParamException("出参配置不能为空");
        }
    }
}
