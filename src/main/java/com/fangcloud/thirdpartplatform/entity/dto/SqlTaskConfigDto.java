package com.fangcloud.thirdpartplatform.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SqlTaskConfigDto implements Serializable {
    private static final long serialVersionUID = -8481797915554118207L;

    // COMPANY 非高校 COLLEGE 高校
    private String companyType;
    // 是否立即删除
    private boolean deletedNow;
    // 延后多少天删除
    private Integer delayDeletedDays;

    // 校验数据处理，true 删除 false 保留
    private boolean deleteIfExist;

    private List<SqlConfig> sqlConfigs;

    @Data
    public static class SqlConfig implements Serializable {

        private static final long serialVersionUID = -5003422355167876198L;

        private String tableType;

        private String taskType;

        private String sql;
    }
}
