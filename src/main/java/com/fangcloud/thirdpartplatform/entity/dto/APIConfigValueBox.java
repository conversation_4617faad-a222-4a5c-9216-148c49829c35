package com.fangcloud.thirdpartplatform.entity.dto;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.ApiVerificationModeEnum;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.factory.PlatformSyncConfigFactory;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class APIConfigValueBox extends ValueBoxDto{
    private static final long serialVersionUID = -1822285015048288185L;

    /**
     * 协议
     */
    private String protocol;

    /**
     * 验证方式
     */
    private String verificationMode;

    /**
     * 部门同步规则
     */
    private DepConfigDto depConfigDto;

    /**
     * 用户同步规则
     */
    private UserConfigDto userConfigDto;

    /**
     * oauth2 接口配置 如果验证方式是oauth2 方式/类型方式 会用到，如果不是则为null
     */
    private APIConfig oauth2Config;

    /**
     * 接口配置
     */
    private APIConfig apiConfig;


    public void check() {

        if (ApiVerificationModeEnum.OAUTH2.equals(verificationMode)) {
            if (oauth2Config == null)  throw new ParamException("OAuth2接口配置不能为空");
            oauth2Config.check();
        }
        if (apiConfig == null) {
            throw new ParamException("接口配置不能为空");
        }
        apiConfig.check();
    }

    @Data
    public static class APIConfig implements Serializable {
        /**
         * 接口
         * 格式：/api/user/get
         * 不需要带域名和协议
         */
        private String api;

        /**
         * 请求方式
         * @see RequestMethod
         */
        private String requestMethod;

        /**
         * 参数配置
         */
        private List<APIParamConfigDto> paramConfig;

        /**
         * 出参配置
         */
        private List<APIResultConfigDto> resultConfig;

        /**
         * 结果值校验
         */
        private VerifyConfig resultVerify;

        /**
         * 分页校验
         */
        private VerifyConfig pagedVerify;

        /**
         * 结果值路径
         */
        private String resultPath;

        public void check() {
            if (StringUtils.isBlank(api)) {
                throw new ParamException("接口url不能为空");
            }
            if (!RequestMethod.POST.name().equals(requestMethod) && !RequestMethod.GET.name().equals(requestMethod)) {
                throw new ParamException("请求方式不支持");
            }
            if (CollectionUtils.isEmpty(paramConfig)) {
                throw new ParamException("参数配置不能为空");
            }
        }
    }

    @Data
    public static class VerifyConfig implements Serializable {
        private String path;

        private String ruleScript;
    }

    public static void main(String[] args) throws Exception {

        Object parse = JSON.parse("{\"apiConfig\":{\"api\":\"/dept/query\",\"paramConfig\":[{\"dataType\":\"java.lang.Integer\",\"defaultValue\":\"1\",\"name\":\"page\",\"paramWay\":\"PARAM\",\"remark\":\"页码\"},{\"dataType\":\"java.lang.Integer\",\"name\":\"pageSize\",\"paramWay\":\"PARAM\",\"remark\":\"页面大小\",\"value\":\"100\"},{\"dataType\":\"java.lang.String\",\"defaultValue\":\"131231231231\",\"name\":\"accessToken\",\"paramWay\":\"HEAD\",\"remark\":\"token\"}],\"requestMethod\":\"POST\",\"resultConfig\":[{\"dataType\":\"java.lang.String\",\"mappingName\":\"name\",\"name\":\"deptName\",\"remark\":\"部门名称\",\"ruleScript\":\"name.equals(\\\"hr\\\")\"},{\"dataType\":\"java.lang.String\",\"name\":\"id\",\"remark\":\"部门id\"},{\"dataType\":\"java.lang.String\",\"name\":\"parentId\",\"remark\":\"父部门id\"}]},\"depConfigDto\":{\"collabAutoAccepted\":true,\"publicFolder\":true},\"deptSpace\":50,\"oauth2Config\":{\"api\":\"/accessToken/get\",\"paramConfig\":[{\"dataType\":\"java.lang.String\",\"name\":\"appkey\",\"paramWay\":\"HEAD\",\"remark\":\"appkey\",\"value\":\"appkey\"},{\"dataType\":\"java.lang.String\",\"name\":\"secret\",\"paramWay\":\"HEAD\",\"remark\":\"secret\",\"value\":\"ASFTDGDG=SADAFWT=SFAGA\"}],\"requestMethod\":\"POST\"},\"protocol\":\"HTTPS\",\"repeatHandle\":\"SKIP\",\"url\":\"fangcloud.com\"}\n");
        JSONPath compile = JSONPath.compile( "$.apiConfig.paramConfig");
        Object eval = compile.eval(parse);
        System.out.println(eval);


        ExpressRunner runner = new ExpressRunner();
        DefaultContext<String, Object> context = new DefaultContext<String, Object>();
        context.put("nextPage", 100);
        String express = "nextPage != nulll && nextPage > 1 ";
        Object r = runner.execute(express, context, null, true, false);
        System.out.println(r);


        APIConfigValueBox apiConfigValueBox = new APIConfigValueBox();
        apiConfigValueBox.setUrl("fangcloud.com");
        apiConfigValueBox.setProtocol("HTTPS");
        apiConfigValueBox.setDeptSpace(50);
        apiConfigValueBox.setRepeatHandle("SKIP");
        DepConfigDto depConfigDto = new DepConfigDto();
        depConfigDto.setCollabAutoAccepted(true);
        depConfigDto.setPublicFolder(true);
        apiConfigValueBox.setDepConfigDto(depConfigDto);
        APIConfig apiConfig = new APIConfig();
        apiConfig.setApi("/dept/query");
        apiConfig.setRequestMethod(RequestMethod.POST.name());
        ArrayList<APIParamConfigDto> paramConfig = new ArrayList<>();
        APIParamConfigDto page = new APIParamConfigDto();
        page.setParamWay("PARAM");
        page.setDefaultValue("1");
        page.setDataType("java.lang.Integer");
        page.setName("page");
        page.setRemark("页码");
        paramConfig.add(page);
        APIParamConfigDto pageSize = new APIParamConfigDto();
        pageSize.setParamWay("PARAM");
        pageSize.setValue("100");
        pageSize.setDataType("java.lang.Integer");
        pageSize.setName("pageSize");
        pageSize.setRemark("页面大小");
        paramConfig.add(pageSize);

        APIParamConfigDto token = new APIParamConfigDto();
        token.setParamWay("HEAD");
        token.setDefaultValue("131231231231");
        token.setDataType("java.lang.String");
        token.setName("accessToken");
        token.setRemark("token");
        paramConfig.add(token);
        apiConfig.setParamConfig(paramConfig);

        ArrayList<APIResultConfigDto> resultConfig = new ArrayList<>();
        APIResultConfigDto name = new APIResultConfigDto();
        name.setName("deptName");
        name.setDataType("java.lang.String");
        name.setMappingName("name");
        name.setRuleScript("name.equals(\"hr\")");
        name.setRemark("部门名称");
        resultConfig.add(name);
        APIResultConfigDto id = new APIResultConfigDto();
        id.setName("id");
        id.setDataType("java.lang.String");
        id.setRemark("部门id");
        resultConfig.add(id);

        APIResultConfigDto parentId = new APIResultConfigDto();
        parentId.setName("parentId");
        parentId.setDataType("java.lang.String");
        parentId.setRemark("父部门id");
        resultConfig.add(parentId);
        apiConfig.setResultConfig(resultConfig);
        apiConfigValueBox.setApiConfig(apiConfig);

        APIConfig oauth2Config = new APIConfig();
        oauth2Config.setApi("/accessToken/get");
        oauth2Config.setRequestMethod(RequestMethod.POST.name());
        ArrayList<APIParamConfigDto> paramConfig1 = new ArrayList<>();
        APIParamConfigDto appkey = new APIParamConfigDto();
        appkey.setParamWay("HEAD");
        appkey.setValue("appkey");
        appkey.setDataType("java.lang.String");
        appkey.setName("appkey");
        appkey.setRemark("appkey");
        paramConfig1.add(appkey);
        APIParamConfigDto secret = new APIParamConfigDto();
        secret.setParamWay("HEAD");
        secret.setValue("ASFTDGDG=SADAFWT=SFAGA");
        secret.setDataType("java.lang.String");
        secret.setName("secret");
        secret.setRemark("secret");
        paramConfig1.add(secret);
        oauth2Config.setParamConfig(paramConfig1);
        apiConfigValueBox.setOauth2Config(oauth2Config);
        VerifyConfig resultVerify = new VerifyConfig();
        resultVerify.setPath("$.result");
        resultVerify.setRuleScript("result!=null");
        apiConfig.setResultVerify(resultVerify);
        VerifyConfig pagedVerify = new VerifyConfig();
        pagedVerify.setPath("$.page");
        pagedVerify.setRuleScript("page > 0");
        apiConfig.setPagedVerify(pagedVerify);
        apiConfig.setResultPath("$.data");
        String s = JSON.toJSONString(apiConfigValueBox);
        System.out.println(s);
        PlatformSyncConfig platformSyncConfig = new PlatformSyncConfig();
        platformSyncConfig.setSourceType("API");
        PlatformSyncConfigResponse build = PlatformSyncConfigFactory.build(platformSyncConfig);
        System.out.println(JSON.toJSONString(build));
    }
}
