package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow;

import com.alibaba.fastjson.annotation.JSONField;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.DownloadInfo;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Data
public class DownloadWorkflow extends DownloadInfo implements Serializable {
    private static final long serialVersionUID = -4471011300319789392L;

    @JSONField(name = "enterprise_id")
    private Long enterpriseId;

    @JSONField(name = "unique_id")
    private String uniqueId;

    @JSONField(name = "task_id")
    private String taskId;

    @J<PERSON><PERSON>ield(name = "receiver_id")
    private Long receiverId;

    @JSONField(name = "author_id")
    private Long authorId;

}
