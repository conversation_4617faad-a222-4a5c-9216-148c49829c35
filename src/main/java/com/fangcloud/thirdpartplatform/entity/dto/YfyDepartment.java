package com.fangcloud.thirdpartplatform.entity.dto;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class YfyDepartment {

    @J<PERSON><PERSON>ield(name = "id")
    private String id;

    @JSONField(name = "name")
    private String name;

    @JSONField(name = "parent_id")
    private String parentId;

    @JSONField(name = "director_id")
    private String directorId;

    /**
     * 开放平台单位是G
     */
    @JSONField(name = "space_total")
    private Long spaceTotal;

    @J<PERSON>NField(name = "public_folder")
    private Boolean publicFolder;

    @JSONField(name = "collab_auto_accepted")
    private Boolean collabAutoAccepted;

    @JSONField(name = "create_time")
    private Date createTime;

    @JSONField(name = "update_time")
    private Date updateTime;

    /**
     * 1 同步， 0 删除
     */
    @J<PERSON><PERSON>ield(name = "status")
    private String status;

    @JSO<PERSON>ield(name = "distinguished_name")
    private String distinguishedName;

    @JSONField(name = "order")
    private Long order;
}
