package com.fangcloud.thirdpartplatform.entity.dto;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class FTPValueBox extends ValueBoxDto{

    private String ftpIp;
    private int ftpPort;
    private String ftpUserName;
    private String ftpPassword;
    private String ftpBaseDir;

    public void check() {
        if(StringUtils.isEmpty(ftpIp)||StringUtils.isEmpty(ftpUserName)||StringUtils.isEmpty(ftpPassword)||StringUtils.isEmpty(ftpBaseDir)){
            throw new ParamException("ftp 连接信息不可为空");
        }
    }

}
