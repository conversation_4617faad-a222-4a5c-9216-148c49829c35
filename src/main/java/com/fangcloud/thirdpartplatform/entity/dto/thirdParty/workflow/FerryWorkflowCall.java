package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow;

import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.FerryInfoCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class FerryWorkflowCall extends FerryInfoCall implements Serializable {

    private static final long serialVersionUID = -7113794294849635578L;

    private long enterpriseId;

    private String uniqueId;

    private String taskId;

    private long receiverId;

    private String reviewType;

    private ThirdpartyCustom createUser;
}

