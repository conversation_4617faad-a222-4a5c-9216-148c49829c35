package com.fangcloud.thirdpartplatform.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

// 用户同步数据结构配置
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserValueBoxDto extends ValueBoxDto {
    private static final long serialVersionUID = -8403201574592781027L;

    private SyncRule syncRule;

    private SqlTaskConfigDto sqlTaskConfig;

    @Data
    public static class SyncRule implements Serializable {

        private static final long serialVersionUID = -4763843784089587423L;

        private UserSyncRuleEmail email;

        private String emailSuffix;

        private boolean syncPhone;

        //是否同步激活账号
        private boolean activate;
    }

    @Data
    public static class UserSyncRuleEmail implements Serializable {
        private static final long serialVersionUID = -1013618280765133368L;
        private boolean isMakeEmail;

        private String model;
    }
}
