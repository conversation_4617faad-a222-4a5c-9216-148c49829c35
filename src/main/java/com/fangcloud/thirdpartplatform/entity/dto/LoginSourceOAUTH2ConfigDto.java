package com.fangcloud.thirdpartplatform.entity.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class LoginSourceOAUTH2ConfigDto implements Serializable {
    private static final long serialVersionUID = -6530386548409135427L;

    /**
     * oauth2 接口配置 如果验证方式是oauth2 方式/类型方式 会用到，如果不是则为null
     */
    private APIConfigValueBox.APIConfig oauth2Config;

    /**
     * 接口配置
     */
    private APIConfigValueBox.APIConfig apiConfig;
}
