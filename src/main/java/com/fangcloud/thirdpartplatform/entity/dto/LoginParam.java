package com.fangcloud.thirdpartplatform.entity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <p>Title: LoginResponse</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2019</p>
 * <p>Company: www.fangcloud.com</p>
 *
 * <AUTHOR>
 * date 2019-06-24
 * @version 1.0
 */
@Data
@AllArgsConstructor
@Builder
public class LoginParam {

    private String userName;

    private String password;

    private String clientId;

    private String clientKey;

    private String appId;

    private String appSecret;

    private String msgSignature;

    private String timeStamp;

    private String nonce;

    private String encrypt;

    private String service;

    private String code;

    private String redirectUri;

    private String userId;

    private String smsCode;

    private String phone;

}
