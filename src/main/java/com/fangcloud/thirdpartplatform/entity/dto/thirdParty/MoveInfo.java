package com.fangcloud.thirdpartplatform.entity.dto.thirdParty;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Data
public class MoveInfo implements Serializable {

    private static final long serialVersionUID = 9161303944285135525L;

    @JSONField(name = "items")
    private List<Item> items;

    @JSONField(name = "target_folder_id")
    private Long targetFolderId;

    @JSONField(name = "target_folder_name")
    private String targetFolderName;

    @J<PERSON>NField(name = "department_id")
    private String departmentId;


    @Getter
    @Setter
    @Data
    static public class Item {

        @JSONField(name = "item_id")
        private Long itemId;

        @JSONField(name = "item_name")
        private String itemName;

        @JSONField(name = "item_type")
        private String itemType;

    }

}
