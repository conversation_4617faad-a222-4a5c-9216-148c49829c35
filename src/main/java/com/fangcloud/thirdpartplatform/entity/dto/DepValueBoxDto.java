package com.fangcloud.thirdpartplatform.entity.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class DepValueBoxDto extends ValueBoxDto {
    private static final long serialVersionUID = 4039081413887209602L;

    private String filterDept;

    private SqlTaskConfigDto sqlTaskConfig;

    private SyncRule syncRule;

    @Data
    public static class SyncRule implements Serializable {

        private static final long serialVersionUID = -1561536053233765714L;

        private RootDept rootDept;

        // 是否创建部门公共资料库
        private boolean publicFolder;

        //部门是否自动接受协作邀请
        private boolean collabAutoAccepted;
    }

    @Data
    public static class RootDept implements Serializable {

        private static final long serialVersionUID = -2467671515825829906L;

        private boolean isFilter;

        private String rootId;
    }
}
