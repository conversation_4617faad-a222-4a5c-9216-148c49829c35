package com.fangcloud.thirdpartplatform.entity.dto;

import lombok.Data;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class MessageConfigValueBox implements Serializable {
    private static final long serialVersionUID = 3185065804445749126L;
    /**
     * 接口
     * 需要带域名和协议
     */
    private String api;
    /**
     * 请求方式
     * @see RequestMethod
     */
    private String requestMethod;
    /**
     * 请求接口入参
     */
    private List<MessageParamConfigDto> messageParamConfigDtos;
    /**
     * faas自定义脚本
     */
    private String code;
}
