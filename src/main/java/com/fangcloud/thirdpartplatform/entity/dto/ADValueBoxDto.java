package com.fangcloud.thirdpartplatform.entity.dto;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ADValueBoxDto extends ValueBoxDto {
    private static final long serialVersionUID = -5147377185756286562L;
    // 根域
    private String baseOu;
    // ou过滤
    private SearchBaseOU search;

    private ADField field;

    private ADFilterDto filter;

    /**
     * 邮箱后缀
     */
    private String emailSuffix;

    private SyncRule syncRule;

    @Override
    public void check() {
        super.check();
        if (field == null) {
            throw new ParamException("数据配置异常");
        }
        field.check();
    }

    @Data
    public static class SyncRule implements Serializable {

        private static final long serialVersionUID = -1561536053233765714L;

        // 是否创建部门公共资料库
        private boolean publicFolder;

        //部门是否自动接受协作邀请
        private boolean collabAutoAccepted;

        //true为正序，即order越小越前
        private boolean positiveOrder;

        private String emailSuffix;

        private boolean syncPhone;

        //是否同步激活账号
        private boolean activate;

        //是否为临时员工
        private boolean isDmz;
    }

    @Data
    public static class SearchBaseOU implements Serializable {

        private static final long serialVersionUID = 8067874489231679470L;

        private String baseOu;
        private int syncParentStep;
    }

    @Data
    public static class ADField implements Serializable {

        private static final long serialVersionUID = -1491743643290907990L;
        // 部门id/用户id
        private String id;
        // 部门名称/用户名称
        private String name;
        // 邮箱 同步用户使用
        private String mail;
        // 手机号 同步用户使用
        private String phone;
        // 部门id 同步用户使用
        private String deptId;
        // 父部门id字段
        private String parentId;
        // 部门主管id
        private String directorId;
        // 部门/用户排序字段
        private String order;
        // 状态 过滤规则
        private String filterRules;
        // 状态 删除规则
        private String isDisableRules;
        // 过期时间，到期设置成未激活
        private String expireTime;

        public void check() {

        }
    }
}
