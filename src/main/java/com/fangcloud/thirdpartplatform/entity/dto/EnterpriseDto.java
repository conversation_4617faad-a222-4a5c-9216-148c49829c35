package com.fangcloud.thirdpartplatform.entity.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class EnterpriseDto {

    private List<EnterpriseInfo> enterprises;

    @Data
    public static class EnterpriseInfo {
        private String name;
        private Long id;
        @JSONField(name = "product_id")
        private String productId;
        @JSONField(name = "id_alias")
        private String idAlias;
        private String appId;
        private String redirectUri;
    }
}
