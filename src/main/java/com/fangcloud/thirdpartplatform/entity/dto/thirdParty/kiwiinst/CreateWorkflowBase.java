package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst;

import lombok.Data;

import java.io.Serializable;

@Data
public class CreateWorkflowBase implements Serializable {

    private static final long serialVersionUID = 7993756524258921612L;
    
    private CreateWorkflowValue creator;
    private String workflowflag = "";
    private String requestlevel = "";
    private String isnextflow = "";
    private String requestid = "";
    private String fpkid = "";
    private String requestname = "";

    public CreateWorkflowBase() {
    }
}
