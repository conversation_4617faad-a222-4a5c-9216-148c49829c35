package com.fangcloud.thirdpartplatform.entity.dto;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import lombok.Data;

@Data
public class ThirdPartyValueBox extends ValueBoxDto{
    /**
     * 部门同步规则
     */
    private DepConfigDto depConfigDto;

    /**
     * 用户同步规则
     */
    private UserConfigDto userConfigDto;

    public void check(SyncTypeEnum type) {

        if (type == SyncTypeEnum.USERS) {
            if (userConfigDto == null)  {
                throw new ParamException("用户同步规则配置不能为空");
            }
        }else if (type == SyncTypeEnum.DEPARTMENT){
            if (depConfigDto == null){
                throw new ParamException("部门同步规则配置不能为空");
            }
        }
    }

}
