
package com.fangcloud.thirdpartplatform.entity.dto.thirdParty;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Data
public class MoveInfoCall implements Serializable {

    private static final long serialVersionUID = -1926500603861234612L;

    private List<Item> itemList;

    private Long targetFolderId;

    private String targetFolderName;


    @Getter
    @Setter
    @Data
    static public class Item {

        private Long itemId;

        private String itemName;

        private String itemType;

    }
}
