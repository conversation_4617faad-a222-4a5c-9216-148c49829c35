package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class KiwiinstEditCollabCallValueBox implements Serializable {

    private static final long serialVersionUID = 8509649639617579723L;


    /**
     * 协作id
     */
    @JSONField(name = "collab_id")
    private long collabId;

    /**
     * 申请协作权限
     */
    @JSONField(name = "role")
    private String role;

    /**
     * 协作关系对应的数据类型：1、user；2、group
     */
    @JSONField(name = "data_type")
    private String dataType;

    /**
     * 协作关系对应的数据id
     */
    @JSONField(name = "data_id")
    private long dataId;

    /**
     * 文件夹id
     */
    @JSONField(name = "folder_id")
    private String folderId;

    /**
     * 协作文件夹名称
     */
    @JSONField(name = "folder_name")
    private String folderName;

    /**
     * 协作文件夹所属部门id
     */
    @JSONField(name = "folder_department_id")
    private long folderDepartmentId;

    /**
     * 原本的协作类型
     */
    @JSONField(name = "original_role")
    private String originalRole;

    /**
     * 文件夹所有者用户id
     */
    @JSONField(name = "folder_owner_id")
    private long folderOwnerId;

    /**
     * 申请修改协作类型用户id
     */
    @JSONField(name = "edit_user_id")
    private long editUserId;

    /**
     * 是否是当前文件夹
     */
    @JSONField(name = "current_folder")
    private boolean currentFolder;



}
