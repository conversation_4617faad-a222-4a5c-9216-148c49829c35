package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow;

import com.alibaba.fastjson.annotation.JSONField;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.MoveInfo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class MoveWorkflow extends MoveInfo implements Serializable {


    private static final long serialVersionUID = 5735696845727100472L;
    @JSONField(name = "enterprise_id")
    private Long enterpriseId;

    @JSONField(name = "unique_id")
    private String uniqueId;

    @JSONField(name = "task_id")
    private String taskId;

    @JSONField(name = "receiver_id")
    private Long receiverId;

    @J<PERSON><PERSON>ield(name = "author_id")
    private Long authorId;
}
