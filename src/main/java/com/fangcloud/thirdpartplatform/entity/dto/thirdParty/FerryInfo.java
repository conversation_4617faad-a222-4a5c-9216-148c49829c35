package com.fangcloud.thirdpartplatform.entity.dto.thirdParty;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Data
public class FerryInfo implements Serializable {

    private static final long serialVersionUID = -7592262936812847622L;

    @JSONField(name = "item_id")
    private Long itemId;

    @JSONField(name = "item_name")
    private String itemName;

    @JSONField(name = "item_type")
    private String itemType;

    @JSONField(name = "ferry_id")
    private Long ferryId;

    @JSONField(name = "source_path")
    private String sourcePath;

    @JSONField(name = "target_path")
    private String targetPath;

}
