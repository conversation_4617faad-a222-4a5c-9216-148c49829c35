package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

@Data
public class CreateCollabWorkflowMain extends CreateWorkflowMain implements Serializable  {

    private static final long serialVersionUID = 2887790434475369244L;

    private CreateWorkflowValue sqr;

    private CreateWorkflowValue startdate;

    private CreateWorkflowValue folderId;

    private CreateWorkflowValue folderName;

    private CreateWorkflowValue folderDeptId;

    private CreateWorkflowValue invitationMessage;

    @JSONField(name = "owner_id")
    private CreateWorkflowValue ownerId;

    @JSO<PERSON>ield(name = "collab_id")
    private CreateWorkflowValue collabId;

    @JSONField(name = "enterprise_id")
    private CreateWorkflowValue enterpriseId;

    @J<PERSON><PERSON>ield(name = "current_folder")
    private CreateWorkflowValue currentFolder;

    public CreateCollabWorkflowMain() {
    }
}
