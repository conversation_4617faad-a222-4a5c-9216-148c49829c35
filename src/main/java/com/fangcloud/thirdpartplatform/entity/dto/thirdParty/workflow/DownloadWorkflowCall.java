package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow;

import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.DownloadInfoCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Data
public class DownloadWorkflowCall extends DownloadInfoCall implements Serializable {
    private static final long serialVersionUID = -4471011300319789392L;

    private long enterpriseId;

    private String uniqueId;

    private String taskId;

    private long receiverId;

    private ThirdpartyCustom createUser;

    private String reviewType;
}
