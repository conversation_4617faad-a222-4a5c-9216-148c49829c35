package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class CreateWorkflow implements Serializable {


    private static final long serialVersionUID = -7903866652482154782L;

    private CreateWorkflowBase base;

    private CreateWorkflowMain main;

    private List<CreateWorkflowDt1> dt1;


}
