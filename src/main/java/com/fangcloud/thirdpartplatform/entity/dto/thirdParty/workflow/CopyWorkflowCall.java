package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow;

import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.CopyInfoCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Data
public class CopyWorkflowCall extends CopyInfoCall implements Serializable {

    private static final long serialVersionUID = -170347840928645123L;
    private long enterpriseId;

    private String uniqueId;

    private String taskId;

    private long receiverId;

    private ThirdpartyCustom createUser;

    private String reviewType;
}
