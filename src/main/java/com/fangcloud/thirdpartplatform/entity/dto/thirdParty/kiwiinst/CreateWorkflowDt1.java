package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst;

import lombok.Data;

import java.io.Serializable;

@Data
public class CreateWorkflowDt1 implements Serializable {

    private static final long serialVersionUID = 6483130829840791529L;
    private CreateWorkflowValue bdlx;
    private CreateWorkflowValue bdr;
    private CreateWorkflowValue bm;
    private CreateWorkflowValue qz;
    private CreateWorkflowValue xzlx;
    private CreateWorkflowValue ysxzlx;

    public CreateWorkflowDt1() {
    }
}
