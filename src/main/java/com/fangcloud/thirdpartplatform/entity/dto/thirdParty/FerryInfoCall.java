package com.fangcloud.thirdpartplatform.entity.dto.thirdParty;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Data
public class FerryInfoCall implements Serializable {


    private static final long serialVersionUID = -9153877412847512754L;

    private Long itemId;

    private String itemName;

    private String itemType;

    private Long ferryId;

    private String sourcePath;

    private String targetPath;

}
