package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class KiwiinstInviteCollabCallValueBox implements Serializable {
    private static final long serialVersionUID = -2667940480876776777L;


    /**
     * 文件夹id
     */
    @JSONField(name = "folder_id")
    private String folderId;

    /**
     * 协作文件夹名称
     */
    @JSONField(name = "folder_name")
    private String folderName;

    /**
     * 协作文件夹所属部门id
     */
    @JSONField(name = "folder_department_id")
    private long folderDepartmentId;

    /**
     * 邀请协作留言
     */
    @JSONField(name = "invitation_message")
    private String invitationMessage;


    /**
     * 邀请协作用户列表
     */
    @JSONField(name = "invited_users")
    private String invitedUsers;


    /**
     * 邀请协作群组列表
     */
    @JSONField(name = "invited_groups")
    private String invitedGroups;


    /**
     * 申请协作用户id
     */
    @JSONField(name = "invite_user_id")
    private long inviteUserId;


    /**
     * 文件夹所有者用户id
     */
    @JSONField(name = "folder_owner_id")
    private long folderOwnerId;



}
