package com.fangcloud.thirdpartplatform.entity.dto.thirdParty;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
public class ShareInfoCall implements Serializable {

    private static final long serialVersionUID = 6420219214423117732L;

    private String access;

    private boolean currentVersion;

    private String description;

    private boolean disableDownload;

    private boolean enableShareWpsEdit;

    private boolean enableDownloadLimitV2;

    private long downloadLimitV2;

    private boolean enablePreviewLimit;

    private long previewLimit;

    private long dueTime;

    private List<ThirdpartyCustom> groupList;

    private List<ThirdpartyCustom> deptList;

    private List<ThirdpartyCustom> invitedUserList;

    private String itemId;

    private String itemType;

    private String itemName;

    private boolean passwordProtected;

    private String password;

    private JSONObject watermarkTemplateInfo;
}

