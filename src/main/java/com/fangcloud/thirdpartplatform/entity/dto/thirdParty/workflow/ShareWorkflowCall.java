package com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow;

import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ShareInfoCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class ShareWorkflowCall extends ShareInfoCall implements Serializable {
    private static final long serialVersionUID = -8212795460659012585L;

    private long enterpriseId;

    private String uniqueId;

    private String taskId;

    private long receiverId;

    private ThirdpartyCustom createUser;

    private String reviewType;

}
