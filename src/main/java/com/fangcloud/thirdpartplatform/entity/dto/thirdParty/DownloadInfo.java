package com.fangcloud.thirdpartplatform.entity.dto.thirdParty;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Data
public class DownloadInfo implements Serializable {
    private static final long serialVersionUID = -4471011300319789392L;

    @JSONField(name = "item_typed_ids")
    private List<String> itemTypedIds;

    @JSONField(name = "file_id")
    private String fileId;

    @JSONField(name = "item_name")
    private String itemName;

}
