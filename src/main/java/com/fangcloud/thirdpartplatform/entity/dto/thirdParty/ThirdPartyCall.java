package com.fangcloud.thirdpartplatform.entity.dto.thirdParty;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@Data
@Builder
public class ThirdPartyCall implements Serializable {
    private static final long serialVersionUID = -3864719781893481730L;

    private String type;

    private String callbackUrl;

    private Object data;
}
