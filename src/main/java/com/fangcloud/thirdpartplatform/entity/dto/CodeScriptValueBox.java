package com.fangcloud.thirdpartplatform.entity.dto;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class CodeScriptValueBox extends ValueBoxDto{

    private String codeScript;

    public void check() {
        if(StringUtils.isEmpty(codeScript)){
            throw new ParamException("代码脚本不能为空");
        }
    }

}
