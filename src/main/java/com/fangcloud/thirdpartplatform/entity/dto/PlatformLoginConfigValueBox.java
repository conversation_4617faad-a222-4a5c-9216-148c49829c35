package com.fangcloud.thirdpartplatform.entity.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PlatformLoginConfigValueBox implements Serializable {
    private static final long serialVersionUID = -8432575246118301076L;

    private LoginSecretConfigDto loginSecretConfigDto;

    private LoginPageConfigDto loginPageConfigDto;

    private LoginValidateConfigDto loginValidateConfigDto;

    private LoginAuthCASConfigDto loginAuthCASConfigDto;

    private LoginAuthOAUTH2ConfigDto loginAuthOAUTH2ConfigDto;

    private LoginSourceLDAPConfigDto loginSourceLDAPConfigDto;

    private LoginSourceOAUTH2ConfigDto loginSourceOAUTH2ConfigDto;

    private LoginThirdWeiXinConfigDto loginThirdWeiXinConfigDto;

    private LoginThirdDingTalkConfigDto loginThirdDingTalkConfigDto;

    private LoginSourceCodeScriptConfigDto loginSourceCodeScriptConfigDto;




}
