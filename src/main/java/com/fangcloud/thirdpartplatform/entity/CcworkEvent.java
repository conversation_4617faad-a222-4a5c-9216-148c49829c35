package com.fangcloud.thirdpartplatform.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @version 1.0
 * @author: MKX
 * create: 2021-08-06
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CcworkEvent {

    private String corp_id;

    private String appid;

    private String event_type;

    private String timestamp;

    private String ids_type;

    private List<String> ids;

    private List<String> accounts;

    /**
     * 部门配置id
     */
    private String deptConfigId;

    /**
     * 用户配置id
     */
    private String userConfigId;

    /**
     * 企业租户id
     */
    private String tenantId;
}
