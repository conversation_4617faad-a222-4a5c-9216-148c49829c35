package com.fangcloud.thirdpartplatform.aop;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * 记录参数信息
 */
@Component
@Slf4j
@Aspect
@Order(1)
public class LogInfoAspect {

    @Pointcut("@annotation(com.fangcloud.thirdpartplatform.annotation.LogInfo)")
    public void logInfo(){}

    @Around("logInfo()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint){
        Object result = null;
        long startTime = System.currentTimeMillis();
        log.info("request start at {}", startTime);
        List<Object> parameters = new ArrayList<>();
        String name = StringUtils.EMPTY;
        try {
            Signature signature = proceedingJoinPoint.getSignature();

            MethodSignature methodSignature = (MethodSignature) signature;
            Method method = methodSignature.getMethod();
            name = method.getName();
            Object[] args = proceedingJoinPoint.getArgs();

            for (Object arg : args) {
                if (arg instanceof ServletRequest || arg instanceof ServletResponse) {
                    continue;
                }
                parameters.add(arg);
            }
            result = proceedingJoinPoint.proceed();
            log.info("LogInfoAspect-methodName {}, parameters {}, response {}, cost {} ms", name, JSON.toJSONString(parameters) ,JSON.toJSONString(result), System.currentTimeMillis() - startTime);
        } catch (Throwable e) {
            log.info("LogInfoAspect-methodName {}, parameters {}", name, JSON.toJSONString(parameters), e);
            result = ResultUtils.getFailedResult(ResponseCodeEnum.DEFAULT, e.getMessage());
        }
        return result;
    }

}
