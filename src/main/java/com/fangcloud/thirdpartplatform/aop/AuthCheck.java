package com.fangcloud.thirdpartplatform.aop;

import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.utils.Md5Utils;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 接口鉴权
 */
@Component
@Slf4j
@Aspect
@Order(1)
public class AuthCheck {


    static long FIVE_MINUTE_MILLIS = 5 * 60 * 1000L;

    @Pointcut("@annotation(com.fangcloud.thirdpartplatform.annotation.AuthCheck)")
    public void authInfo(){}

    @Around("authInfo()")
    public Object around(ProceedingJoinPoint proceedingJoinPoint){
        Object result = null;

        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String sign = request.getHeader("PLATFORM-SIGN");
            String time = request.getHeader("PLATFORM-TIME");


            log.info("PLATFORM-SIGN is :{}, PLATFORM-TIME is :{}", sign, time);
            String md5OfStr = Md5Utils.getMD5OfStr(time + "YFY");

            if(!md5OfStr.equals(sign)){
                log.info("sign is wrong !");
                return ResultUtils.getFailedResult(ResponseCodeEnum.DEFAULT, "sign is wrong !");
            }

            long differenceMillis = System.currentTimeMillis() - Long.parseLong(time);
            if(differenceMillis < -FIVE_MINUTE_MILLIS || differenceMillis > FIVE_MINUTE_MILLIS){
                log.info("timestamp is wrong !");
                return ResultUtils.getFailedResult(ResponseCodeEnum.DEFAULT, "timestamp is wrong !");
            }

            result = proceedingJoinPoint.proceed();
        }catch (Throwable e){
            result = ResultUtils.getFailedResult(ResponseCodeEnum.DEFAULT, e.getMessage());
        }
        return result;
    }

}
