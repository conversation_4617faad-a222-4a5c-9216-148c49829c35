package com.fangcloud.thirdpartplatform.runner;

import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncConfigStatusEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.service.PlatformSyncConfigExecutor;
import com.fangcloud.thirdpartplatform.utils.NamedThreadPools;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SyncConfigRunner implements CommandLineRunner {
    ExecutorService executorService = NamedThreadPools.newFixedThreadPool(1, "platform-syncConfig-runner");

    private final static Logger executorLogger = LoggerFactory.getLogger("executorLogger");

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;
    @Resource
    private PlatformSyncConfigExecutor platformSyncConfigExecutor;
    @Resource
    private CustomNacosConfig nacosConfig;
    @Override
    public void run(String... args) throws Exception {

        if (nacosConfig.getCustomPublic()){
            return;
        }
        executorService.submit(() -> {
            while (true) {
                try {
                    long currentTimeMillis = System.currentTimeMillis();
                    List<PlatformSyncConfig> syncConfigs = platformSyncConfigMapper.query();
                    syncConfigs = syncConfigs.stream().filter(s -> SyncConfigStatusEnum.isStart(s.getSyncStatus())).collect(Collectors.toList());
                    int total = syncConfigs.size();
                    int executeCount = 0;
                    for (PlatformSyncConfig config : syncConfigs) {
                        if (config == null || config.getSourceType().equals(SourceTypeEnum.PUSH.getDesc())) {
                            continue;
                        }
                        Long nextExecuteTime = config.getNextExecuteTime();
                        Integer syncStatus = config.getSyncStatus();
                        if (SyncConfigStatusEnum.isStart(syncStatus) &&
                                currentTimeMillis >= nextExecuteTime) {
                            CronSequenceGenerator generator = new CronSequenceGenerator(config.getCron());
                            long time = generator.next(new Date()).getTime();
                            // 利用数据库乐观锁对任务进行加锁，如果没有更新成功就默认有其他服务器已经执行
                            int i = platformSyncConfigMapper.updateNextExecuteTime(config.getId(), time, nextExecuteTime);
                            if (i <= 0) {
                                continue;
                            }
                            platformSyncConfigExecutor.execute(config, false);
                            executeCount ++;
                        }
                    }
                    Thread.sleep(3000L);
                    executorLogger.info("sync-conf-task-end, total:{}, executed:{}", total, executeCount);
                } catch (Throwable e) {
                    log.error("SyncConfigRunner-error", e);
                }
            }
        });
    }
}
