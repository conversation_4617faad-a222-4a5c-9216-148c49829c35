package com.fangcloud.thirdpartplatform.controller;

import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.check.LoginControllerCheck;
import com.fangcloud.thirdpartplatform.constant.PlatformTypeEnum;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.input.*;
import com.fangcloud.thirdpartplatform.entity.response.LoginResponse;
import com.fangcloud.thirdpartplatform.service.AutoLoginService;
import com.fangcloud.thirdpartplatform.service.CcWorkSassService;
import com.fangcloud.thirdpartplatform.service.DingTalkService;
import com.fangcloud.thirdpartplatform.service.WeixinWorkService;
import com.fangcloud.thirdpartplatform.service.ZWDService;
import com.fangcloud.thirdpartplatform.service.impl.custom.FeiShuServiceImpl;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;

@Controller
@Slf4j
public class LoginController extends BaseController{

    @Resource
    private DingTalkService dingTalkService;

    @Resource
    private WeixinWorkService weixinWorkService;

    @Resource
    private ZWDService zWDService;

    @Resource
    private LoginControllerCheck loginControllerCheck;

    @Resource
    private AutoLoginService autoLoginService;

    @Resource
    private CcWorkSassService ccWorkSassService;

    @Resource
    private FeiShuServiceImpl feiShuServiceImpl;

    @RequestMapping(value = "/weixinwork/autologin", method = RequestMethod.POST)
    @ResponseBody
    public LoginResponse weixinWork(@RequestBody WeixinWorkInitParams params)
    {
        log.info("WeixinWorkInitParams {}", JSONObject.toJSONString(params));
        // 参数校验
        loginControllerCheck.checkWeixinWorkInitParams(params);

        String url = weixinWorkService.getAutoLoginUrl(params);
        LoginResponse loginResponse = null;
        if (url.contains("account_not_open")) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.WEIXIN_WORK.getType(), "账号异常");
            loginResponse.setUrl(url);
        }
        loginResponse = getLoginSuccessResponse(PlatformTypeEnum.WEIXIN_WORK.getType());
        loginResponse.setUrl(url);

        return loginResponse;
    }

    @RequestMapping(value = "/dingtalk/autologin", method = RequestMethod.POST)
    @ResponseBody
    public LoginResponse dingTalk(@RequestBody DingTalkInitParams params)
    {
        log.info("DingTalkInitParams {}", JSONObject.toJSONString(params));

        // 参数校验
        loginControllerCheck.checkDingTalkInitParams(params);
        String url = dingTalkService.getAutoLoginUrl(params);

        LoginResponse loginResponse = null;
        if (url.contains("account_not_open")) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.DING_TALK.getType(), "账号异常");
            loginResponse.setUrl(url);
        }
        loginResponse = getLoginSuccessResponse(PlatformTypeEnum.DING_TALK.getType());
        loginResponse.setUrl(url);

        return loginResponse;
    }


    @RequestMapping(value = "/special/autologin")
    @ResponseBody
    public LoginResponse spLogin(HttpServletRequest request, HttpServletResponse response, String config) throws IOException {
        ConfigInfo configInfo = ConfigInfo.builder().build();
        if (StringUtils.isNotEmpty(config)) {
            configInfo = JSONObject.parseObject(Base64Utils.decode(config), ConfigInfo.class);
        } else {
            configInfo.setProductId(request.getParameter("product_id"));
            configInfo.setClientId(request.getParameter("client_id"));
            configInfo.setEnterpriseId(request.getParameter("enterprise_id"));
            configInfo.setPlatformId(request.getParameter("platform_id"));
            configInfo.setRedirectUrl(URLDecoder.decode(request.getParameter("redirect_url")));
        }
        LoginResponse loginResponse = null;
        if (StringUtils.isEmpty(configInfo.getProductId())) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.DEFAULT.getType(), "product_id 不能为空");
            return loginResponse;
        }
        if (StringUtils.isEmpty(configInfo.getClientId())) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.DEFAULT.getType(), "client_id 不能为空");
            return loginResponse;
        }
        if (StringUtils.isEmpty(configInfo.getEnterpriseId())) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.DEFAULT.getType(), "enterprise_id 不能为空");
            return loginResponse;
        }
        if (StringUtils.isEmpty(configInfo.getPlatformId())) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.DEFAULT.getType(), "platform_id 不能为空");
            return loginResponse;
        }

        // 通过product_id 处理不同的实现
        String url = autoLoginService.getAutoLoginUrl(request, configInfo);
        if (url == null) {
            if (StringUtils.isEmpty(configInfo.getRedirectUrl())) {
                loginResponse = getLoginErrorResponse(PlatformTypeEnum.DEFAULT.getType(), "redirect_url 不能为空");
                return loginResponse;
            }
            response.sendRedirect(configInfo.getRedirectUrl());
            return null;
        }
        response.sendRedirect(url);
        return null;
    }

    @RequestMapping(path = "/authorize/middle", method = RequestMethod.GET)
    public String authorizeMiddle(Model model, String config) {
        autoLoginService.getParams(model, config);
        return "authorize_middle_page";
    }

    @RequestMapping(value = "/ccwork/autologin", method = RequestMethod.POST)
    @ResponseBody
    public LoginResponse ccwork(@RequestBody CcWorkInitParam params)
    {
        log.info("CcWorkInitParam {}", JSONObject.toJSONString(params));

        // 参数校验
        loginControllerCheck.checkCcWorkAutoLoginParams(params);
        String url = ccWorkSassService.getAutoLoginUrl(params);

        LoginResponse loginResponse = null;
        if (url.contains("account_not_open")) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.CCWORK.getType(), "账号异常");
            loginResponse.setUrl(url);
        }
        loginResponse = getLoginSuccessResponse(PlatformTypeEnum.CCWORK.getType());
        loginResponse.setUrl(url);

        return loginResponse;
    }

    @RequestMapping(value = "/zwd/autoLogin", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    public LoginResponse ZWDAutoLogin(@RequestBody AutoLoginParams params) throws IOException {
        log.info("zwd AutoLoginParams: {}", JSONObject.toJSONString(params));
        // 参数校验
        loginControllerCheck.checkAutoLoginParams(params);

        JSONObject jsonObject = zWDService.getAutoLoginUrl(params);
        String url = jsonObject.getString("url");
        String accountId = String.valueOf(jsonObject.get("accountId"));
        String nickNameCn = jsonObject.getString("nickNameCn");
        LoginResponse loginResponse = null;
        if (url.contains("account_not_open")) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.ZWD.getType(), "账号异常");
            loginResponse.setUrl(url);
        }else {
            loginResponse = getLoginSuccessResponse(PlatformTypeEnum.ZWD.getType());
            loginResponse.setUrl(url);
        }
        loginResponse.setAccountId(accountId);
        loginResponse.setNickNameCn(nickNameCn);

        return loginResponse;
    }

    @RequestMapping(value = "/zwd/autoLoginByEmployeeCode", method = RequestMethod.GET)
    @ResponseBody
    public void ZWDAutoLoginByEmployeeCode(HttpServletResponse response,
                                           @RequestParam(value = "employeeCode")String employeeCode,
                                           @RequestParam(value = "enterpriseId") String enterpriseId) throws IOException {

        // 参数校验
        loginControllerCheck.checkAutoLoginByEmployeeCodeParams(employeeCode, enterpriseId);
        log.info("zwd autoLoginByEmployeeCode enterpriseId:{}, employeeCode:{}", enterpriseId, employeeCode);
        zWDService.getAutoLoginUrlByEmployeeCode(employeeCode, enterpriseId, response);
    }

    @RequestMapping(value = "/feishu/autologin", method = RequestMethod.POST)
    @ResponseBody
    public LoginResponse feiShu(@RequestBody FeiShuInitParams params)
    {
        log.info("FeiShuInitParams {}", JSONObject.toJSONString(params));

        // 参数校验
        loginControllerCheck.checkFeiShuAutoLoginParams(params);
        String url = feiShuServiceImpl.getAutoLoginUrl(params);

        LoginResponse loginResponse = null;
        if (url.contains("account_not_open")) {
            loginResponse = getLoginErrorResponse(PlatformTypeEnum.FEI_SHU.getType(), "账号异常");
            loginResponse.setUrl(url);
        }
        loginResponse = getLoginSuccessResponse(PlatformTypeEnum.FEI_SHU.getType());
        loginResponse.setUrl(url);

        return loginResponse;
    }
}
