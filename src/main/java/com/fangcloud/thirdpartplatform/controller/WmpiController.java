package com.fangcloud.thirdpartplatform.controller;


import com.fangcloud.thirdpartplatform.entity.dto.EnterpriseDto;
import com.fangcloud.thirdpartplatform.service.impl.custom.WmpiServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;


@Controller
@Slf4j
public class WmpiController {

    @Resource
    private WmpiServiceImpl wmpiService;

    @RequestMapping(path = "/wmpi/login", method = RequestMethod.GET)
    public String wmpiLogin(Model model, HttpServletRequest request, HttpServletResponse response) throws IOException {
        EnterpriseDto enterpriseDto = wmpiService.getEnterprises();
        log.info("enterprises list {}", enterpriseDto);
        model.addAttribute("enterprises", enterpriseDto);
        return "ding_org_select";
    }
}
