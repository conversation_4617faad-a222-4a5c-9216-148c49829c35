package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.service.SyncPublishService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;

@Controller
@Slf4j
public class SyncPublishController {

    @Resource
    private SyncPublishService syncPublishService;

    /**
     * 企业微信同步公有云组织架构
     * @return
     */
    @RequestMapping(value = "/sync_public/sync_organization", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<Boolean> syncOrganization(@RequestParam(value = "enterprise_id") Integer enterpriseId, @RequestParam(value = "sync_type")String syncType, @RequestParam(value = "request_id", required = false, defaultValue = "")String requestId)  {
        // 同步组织架构数据
        syncPublishService.syncOrganization(enterpriseId, syncType, requestId);
        return ResultUtils.getSuccessResult(true);
    }

    /**
     * 批量同步推推saas
     * @return
     */
    @RequestMapping(value = "/sync_public/sync_batch_ccwork", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<Boolean> syncOrganization()  {
        // 同步组织架构数据
        syncPublishService.syncBatchyCcworkOrganization();
        return ResultUtils.getSuccessResult(true);
    }

    /**
     * 标准化同步公有云组织架构
     * @return
     */
    @RequestMapping(value = "/sync_public/sync", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<ExecuteResult> syncOrganization(String productId,
                                                  @RequestParam(value = "config_id") List<Integer> configIds,
                                                  @RequestParam(value = "enterprise_id") Integer enterpriseId,
                                                  @RequestParam(value = "task_id") Integer taskId,
                                                  @RequestParam(value = "request_id", required = false, defaultValue = "") String requestId,
                                                  String password)  {
        // 同步组织架构数据
        ExecuteResult executeResult = syncPublishService.syncPublic(productId, configIds, enterpriseId, taskId, password, requestId);
        return ResultUtils.getSuccessResult(executeResult);
    }

    /**
     * 查询公有云组织架构同步任务状态
     * @return
     */
    @RequestMapping(value = "/sync_public/status", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public String syncStatus(@RequestParam(value = "enterprise_id") Integer enterpriseId, @RequestParam(value = "sync_type")String syncType, @RequestParam(value = "request_id")String requestId)  {
        return syncPublishService.syncStatus(enterpriseId, syncType, requestId);
    }

}
