package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.check.DataQueryControllerCheck;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.fangcloud.thirdpartplatform.service.DataQueryService;
import com.fangcloud.thirdpartplatform.service.PlatFormSyncConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.Objects;

@Controller
@Slf4j
public class DataQueryController extends BaseController{

    @Resource
    private PlatFormSyncConfigService platFormSyncConfigService;

    @Resource
    private DataQueryService dataQueryService;

    @Resource
    private DataQueryControllerCheck dataQueryControllerCheck;

    /**
     * 通用查询接口，用于特殊业务需求
     *
     */
    @RequestMapping(path = "/data/process/query", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Object customQuery(@RequestParam(value = "product_id") String productId,
                              @RequestParam(value = "config_id") Integer configId,
                              @RequestParam(value = "query_params") String params) throws Exception {
        dataQueryControllerCheck.checkCustomQuery(productId, configId, params);
        PlatformSyncConfigResponse platformSyncConfig = platFormSyncConfigService.get(configId);
        Assert.isTrue(!Objects.isNull(platformSyncConfig),"platformSyncConfig is not null !");
        return dataQueryService.customQuery(productId, platformSyncConfig, params);
    }


}
