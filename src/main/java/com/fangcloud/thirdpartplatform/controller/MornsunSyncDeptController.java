package com.fangcloud.thirdpartplatform.controller;


import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.service.AiChatService;
import com.fangcloud.thirdpartplatform.service.impl.custom.MornSunServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@Slf4j
public class MornsunSyncDeptController {


    @Resource
    MornSunServiceImpl mornSunService;

    @ResponseBody
    @RequestMapping("/mornsun/sync_dept")
    public JSONObject syncdept(HttpServletRequest request, HttpServletResponse response,@RequestBody JSONObject jSONObject) throws IOException {

        log.info("sync_dept param:{}", JSONObject.toJSONString(jSONObject));

        return mornSunService.sync_dept(request, response, jSONObject);

    }
}
