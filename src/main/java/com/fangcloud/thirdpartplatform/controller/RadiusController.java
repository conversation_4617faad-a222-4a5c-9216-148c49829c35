package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.RadiusCheckLoginParams;
import com.fangcloud.thirdpartplatform.service.RadiusService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;


@Controller
@Slf4j
public class RadiusController {


    @Autowired
    private RadiusService radiusService;

    /**
     *  校验radius认证
     * @return
     */
    @RequestMapping(value = "/radius/check_login", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    public Result<Boolean> checkLogin (@RequestBody RadiusCheckLoginParams radiusCheckLoginParams)  {

        Boolean checkLogin = radiusService.checkLogin(radiusCheckLoginParams);
        if(checkLogin){
            return ResultUtils.getSuccessResult(true);
        }else {
            return ResultUtils.getFailedPageResult(ResponseCodeEnum.ERROR, "radius认证失败！");
        }
    }



}
