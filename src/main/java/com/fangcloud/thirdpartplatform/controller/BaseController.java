package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.entity.response.LoginResponse;
import com.fangcloud.thirdpartplatform.entity.response.MessageResponse;
import org.springframework.util.StringUtils;

public class BaseController {

    protected LoginResponse getLoginSuccessResponse(String type) {
        LoginResponse loginResponse = new LoginResponse();
        loginResponse.setStatus(ResponseCodeEnum.SUCCESS.getType());
        loginResponse.setMessage(ResponseCodeEnum.SUCCESS.getDesc());
        loginResponse.setType(type);

        return loginResponse;
    }

    protected LoginResponse getLoginErrorResponse(String type, String msg) {
        LoginResponse loginResponse = new LoginResponse();

        msg = StringUtils.isEmpty(msg) ? ResponseCodeEnum.ERROR.getDesc() : msg;
        loginResponse.setMessage(msg);
        loginResponse.setStatus(ResponseCodeEnum.ERROR.getType());
        if (org.apache.commons.lang3.StringUtils.isEmpty(msg)) {
            loginResponse.setMessage(ResponseCodeEnum.ERROR.getDesc());
        }
        loginResponse.setType(type);

        return loginResponse;
    }

    protected MessageResponse getMsgSuccessResponse(String type) {
        MessageResponse messageResponse = new MessageResponse();
        messageResponse.setStatus(ResponseCodeEnum.SUCCESS.getType());
        messageResponse.setMessage(ResponseCodeEnum.SUCCESS.getDesc());
        messageResponse.setType(type);

        return messageResponse;
    }

    protected MessageResponse getMsgErrorResponse(String type, String msg) {
        MessageResponse messageResponse = new MessageResponse();

        msg = StringUtils.isEmpty(msg) ? ResponseCodeEnum.ERROR.getDesc() : msg;
        messageResponse.setStatus(ResponseCodeEnum.ERROR.getType());
        messageResponse.setMessage(msg);
        messageResponse.setType(type);

        return messageResponse;
    }
}
