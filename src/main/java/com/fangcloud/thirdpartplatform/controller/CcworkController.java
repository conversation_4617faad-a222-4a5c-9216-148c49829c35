package com.fangcloud.thirdpartplatform.controller;


import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.service.impl.CcworkServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.thirdPart.CcWorkSassServiceImpl;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.sync.common.entity.dto.YfyMessage;
import com.sync.common.utils.YfyMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@Slf4j
public class CcworkController {

    @Resource
    private CcworkServiceImpl ccworkService;

    @Resource
    private CcWorkSassServiceImpl ccWorkSassService;

    /**
     * 消息推送
     * @return
     */
    @ResponseBody
    @RequestMapping(path = "/ccwork/messagepush", method = RequestMethod.POST)
    @LogInfo
    public String receiveMessage(@RequestBody String postData, String config) throws Exception {
        log.info("消息内容为:{}", postData);
        if (StringUtils.isEmpty(postData)) {
            log.error("消息内容为空");
            return "{\"success\":false}";
        }

        YfyMessage yfyMessage = YfyMessageUtil.parser(postData);

        try {
            // base64_encode({"deptConfigId":1, "userConfigId":2, "redirectHost": "http://www.baidu.com"});
            ConfigInfo configInfo = JSONObject.parseObject(Base64Utils.decode(config), ConfigInfo.class);
            String deptConfigId = configInfo.getDeptConfigId();
            String userConfigId = configInfo.getUserConfigId();
            String redirectHost = configInfo.getRedirectUrl();
            boolean isSend = ccworkService.messageSend(yfyMessage, userConfigId, redirectHost);
            if (isSend) {
                return "{\"success\":true}";
            }
        } catch (Exception e) {
            log.error("消息推送失败 yfyMessage {}", yfyMessage, e);
        }
        return "{\"success\":false}";
    }


    /**
     * 事件回调
     * @return
     */
    @ResponseBody
    @RequestMapping(path = "/ccwork/eventcallback", method = RequestMethod.POST)
    @LogInfo
    public String eventCallBack(@RequestBody String message, String config)
    {
        String encrypt = "";
        Map<String, Object> map = new HashMap<>();
        try {
            ConfigInfo configInfo = JSONObject.parseObject(Base64Utils.decode(config), ConfigInfo.class);
            String platform = configInfo.getPlatform();
            Object result = null;
            if (platform != null && platform.equals("ccwork_saas")) {
                result = ccWorkSassService.syncCallBack(message);
            } else {
                String deptConfigId = configInfo.getDeptConfigId();
                String userConfigId = configInfo.getUserConfigId();
                result = ccworkService.eventCallback(message, userConfigId, deptConfigId);
            }

            if (result instanceof String) {
                encrypt = (String)result;
            }
        }catch (Exception e){
            log.error("事件回调失败 event {}",message,e);
        }
        map.put("encrypt",encrypt);
        return JSONObject.toJSONString(map);
    }
}
