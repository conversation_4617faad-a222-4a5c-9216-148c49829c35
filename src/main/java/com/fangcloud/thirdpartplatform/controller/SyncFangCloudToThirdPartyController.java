package com.fangcloud.thirdpartplatform.controller;


import cn.hutool.core.codec.Base64;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.service.impl.SyncFangCloudToThirdPartyServiceImpl;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@RestController
@Slf4j
public class SyncFangCloudToThirdPartyController {

    @Resource
    private SyncFangCloudToThirdPartyServiceImpl syncFangCloudToThirdPartyService;

    /**
     *  同步云盘部门到第三方接口
     * @return
     */
    @ResponseBody
    @RequestMapping(path = "/fangcloudtothird/syncdept", method = RequestMethod.GET)
    @LogInfo
    public String syncdept(@RequestParam(value = "config") String config) throws Exception {
        log.info("配置信息:{}",config);
        if (StringUtils.isEmpty(config)) {
            log.error("消息内容为空");
            return "{\"success\":false}";
        }

        try {

            boolean isDone = syncFangCloudToThirdPartyService.syncDept(config);
            if (isDone) {
                return "{\"success\":true}";
            }
        } catch (Exception e) {
            log.error("处理云盘部门同步到第三方失败  ", e);
        }
        return "{\"success\":false}";
    }

    /**
     *  同步云盘部门到第三方接口
     * @return
     */
    @ResponseBody
    @RequestMapping(path = "/fangcloudtothird/syncuser", method = RequestMethod.GET)
    @LogInfo
    public String syncuser(@RequestParam(value = "config") String config) throws Exception {
        log.info("配置信息:{}",config);
        if (StringUtils.isEmpty(config)) {
            log.error("消息内容为空");
            return "{\"success\":false}";
        }

        try {

            boolean isSend = syncFangCloudToThirdPartyService.syncUser(config);
            if (isSend) {
                return "{\"success\":true}";
            }
        } catch (Exception e) {
            log.error("处理云盘部门同步到第三方失败  ", e);
        }
        return "{\"success\":false}";
    }

    @ResponseBody
    @RequestMapping(path = "/receivemsg", method = RequestMethod.POST)
    @LogInfo
    public String receiveMsg(@RequestBody String msgData) throws Exception {
        log.info("消息内容:{}", msgData);
        if (StringUtils.isEmpty(msgData)) {
            log.error("消息内容为空");
            return "{\"success\":false}";
        }

        try {

            boolean isSend = syncFangCloudToThirdPartyService.receiveMsg(msgData);
            if (isSend) {
                return "{\"success\":true}";
            }
        } catch (Exception e) {
            log.error("处理第三方消息推送到云盘失败  ", e);
        }
        return "{\"success\":false}";
    }

    @ResponseBody
    @RequestMapping(path = "/fangcloudtothird/dxurlinfo", method = RequestMethod.GET)
    @LogInfo
    public Object getDXCloudDLPUrlInfo(@RequestParam(value = "config") String config) {
        log.info("配置信息:{}",config);
        if (StringUtils.isEmpty(config)) {
            log.error("消息内容为空");
            return "{\"success\":false}";
        }

        config = Base64.decodeStr(config);
        log.info("dxurlInfo config: {}", config);

        /**
         * {
         *     "host": "http://*************:3311",
         *     "urls": {
         *         "admin": "/servlet/DlpSysSSO?type=sso&unencrypted=true&token={\"username\":\"processadmin\",\"datetime\":\"%s\"}",
         *         "process": "/servlet/DlpSysSSO?type=sso&unencrypted=true&token={\"username\":\"admin1\",\"datetime\":\"%s\"}"
         *     }
         * }
         */
        JSONObject configObject = JSONUtil.parseObj(config);

        String host = configObject.getByPath("$.host").toString();
        String adminUrl = host + String.format(configObject.getByPath("$.urls.admin").toString(), System.currentTimeMillis());
        String processUrl = host + String.format(configObject.getByPath("$.urls.process").toString(), System.currentTimeMillis());
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("admin_url", adminUrl);
        result.put("process_url", processUrl);
        return JSONUtil.toJsonStr(result);
    }

}
