package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.PlatformGlobalConfigParam;
import com.fangcloud.thirdpartplatform.entity.response.PlatformGlobalConfigResponse;
import com.fangcloud.thirdpartplatform.service.GlobalConfigService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 *
 */
@Controller
@Slf4j
public class GlobalConfigController {
    @Resource
    private GlobalConfigService globalConfigService;

    @RequestMapping(value = "/global_config/get_config", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<PlatformGlobalConfigResponse> getConfig(@RequestParam(value = "enterpriseId") Integer enterpriseId) {
        return ResultUtils.getSuccessResult(globalConfigService.get(enterpriseId));
    }

    @RequestMapping(value = "/global_config/save_config", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    public Result<Boolean> saveConfig(@RequestBody   PlatformGlobalConfigParam platformGlobalConfigParam) {
        globalConfigService.save(platformGlobalConfigParam);
        return ResultUtils.getSuccessResult(true);
    }

}
