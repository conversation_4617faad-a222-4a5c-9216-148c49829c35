package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.check.PlatformLoginConfigControllerCheck;
import com.fangcloud.thirdpartplatform.check.PlatformLoginControllerCheck;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginEnterpriseInfoResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginTokenResponse;
import com.fangcloud.thirdpartplatform.service.PlatformLoginService;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.service.CaptchaCheckImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 标准化登陆服务
 */
@Controller
@Slf4j
public class PlatformLoginController {

    @Resource
    private PlatformLoginService platformLoginService;

    @Resource
    private CaptchaCheckImpl captchaCheckImpl;

    @Resource
    private PlatformLoginControllerCheck platformLoginControllerCheck;

    private PlatformLoginConfigControllerCheck platformLoginConfigControllerCheck;

    @RequestMapping(path = "/authentication/login", method = RequestMethod.GET)
    public String login(Model model, HttpServletRequest request, HttpServletResponse response)throws IOException{
        String url = platformLoginService.loginCheck(request, response);
        if(StringUtils.isNotEmpty(url)){
            response.sendRedirect(url);
            return null;
        }
        return "index";
    }

    /**
     * 是否需要验证码
     * @param username
     * @return
     * @throws IOException
     */
    @RequestMapping(path = "/authentication/need_captcha", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<PlatformLoginResponse> needCaptcha(@RequestParam(value = "username")String username,
                                       @RequestParam(value = "enterprise_id") String enterpriseId){

        platformLoginControllerCheck.checkNeedCaptcha(username, enterpriseId);

        return platformLoginService.needCaptcha(username, enterpriseId);
    }

    /**
     * 获取企业配置信息
     * @param enterpriseId
     * @return
     * @throws IOException
     */
    @RequestMapping(path = "/authentication/get_enterprise_info", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<PlatformLoginEnterpriseInfoResponse> getEnterpriseInfo (@RequestParam(value = "enterprise_id") String enterpriseId, HttpServletRequest request) throws IOException {
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                log.info("cookie name :{}", cookie.getName());
                if (cookie.getName().equals("lang")) {
                    log.info("lang :{}", cookie.getValue());
                }
            }
        }
        platformLoginControllerCheck.checkGetEnterpriseInfo(enterpriseId);
        return platformLoginService.getEnterpriseInfo(enterpriseId);
    }


    /**
     * 内部登陆
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(path = "/authentication/internal_login", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    public Result<PlatformLoginResponse> internalLogin(HttpServletRequest request, HttpServletResponse response) throws Exception {

        platformLoginControllerCheck.checkInternalLogin(request);

        return platformLoginService.internalLogin(request, response);
    }

    /**
     * 获取验证码
     */
    @RequestMapping(path = "/authentication/get_img_captcha", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public void getImgCaptcha(HttpServletRequest request, HttpServletResponse response) throws IOException {
        captchaCheckImpl.buildCaptcha(request, response);
    }

    /**
     * 登出
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(path = "/authentication/logout", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<String> logout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        platformLoginControllerCheck.checkLogout(request);
        return platformLoginService.logout(request, response);
    }

    /**
     * oauth2获取token
     * @param enterpriseId
     * @param clientId
     * @param clientSecret
     * @param ticket
     * @return
     */
    @RequestMapping(path = "/authentication/oauth2/get_token", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<PlatformLoginTokenResponse> getToken(@RequestParam(value = "enterprise_id") String enterpriseId,
                                                       @RequestParam(value = "client_id")String clientId,
                                                       @RequestParam(value = "client_secret")String clientSecret,
                                                       @RequestParam(value = "ticket")String ticket) {

        platformLoginControllerCheck.checkGetToken(enterpriseId, clientId, clientSecret, ticket);
        return platformLoginService.getToken(enterpriseId, clientId, clientSecret, ticket);
    }

    /**
     * oauth2获取用户信息
     * @param token
     * @return
     */
    @RequestMapping(path = "/authentication/oauth2/get_user_info", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<YfyUser> getUserInfo(@RequestParam(value = "token") String token) {
        return platformLoginService.getUserInfo(token);
    }

    /**
     * cas校验信息
     * @param ticket
     * @return
     * @throws Exception
     */
    @RequestMapping(path = "/authentication/cas/service_validate", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public String serviceValidate(@RequestParam(value = "ticket") String ticket) throws Exception {
        platformLoginControllerCheck.checkServiceValidate(ticket);
        return platformLoginService.serviceValidate(ticket);
    }
}
