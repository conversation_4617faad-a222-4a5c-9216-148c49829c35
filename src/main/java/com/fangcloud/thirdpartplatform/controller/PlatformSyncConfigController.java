package com.fangcloud.thirdpartplatform.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.entity.common.PageResult;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.CodeScriptValueBox;
import com.fangcloud.thirdpartplatform.entity.input.ConnectionCheckParam;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncConfigParams;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.fangcloud.thirdpartplatform.service.PlatFormSyncConfigService;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.datasource.CodeScriptSyncHandler;
import com.fangcloud.thirdpartplatform.source.ldap.LdapConfigProperties;
import com.fangcloud.thirdpartplatform.source.ldap.LdapTemplateFactory;
import com.fangcloud.thirdpartplatform.utils.DatabaseUtils;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.query.LdapQuery;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static org.springframework.ldap.query.LdapQueryBuilder.query;

/**
 *
 */
@Controller
@Slf4j
public class PlatformSyncConfigController {
    @Resource
    private PlatFormSyncConfigService platFormSyncConfigService;
    @Resource
    private ApiSyncHandler apiSyncHandler;
    @Resource
    private CodeScriptSyncHandler codeScriptSyncHandler;

    @Resource
    private CustomNacosConfig customNacosConfig;

    @RequestMapping(value = "/sync_task/config_change", method = RequestMethod.POST, produces = "application/json; charset=utf-8")
    @ResponseBody
    @LogInfo
    public Result<Boolean> changeConfig(@RequestBody PlatformSyncConfigParams platformSyncConfigParams) {
        if (platformSyncConfigParams == null) {
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "参数不能为空");
        }
        log.info("change param:{}", JSON.toJSONString(platformSyncConfigParams));
        boolean success = false;
        try {
            Integer id = platformSyncConfigParams.getId();
            if (id == null || id <= 0) {
                success = platFormSyncConfigService.save(platformSyncConfigParams);
            } else {
                success =platFormSyncConfigService.update(platformSyncConfigParams);
            }
        } catch (ParamException e) {
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("PlatformSyncConfigController.changeConfig-error, param:{}", JSON.toJSON(platformSyncConfigParams), e);
        }
        if (!success) {
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "更新失败");
        }
        return ResultUtils.getSuccessResult(true);
    }

    @RequestMapping(value = "/sync_task/config_list", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public PageResult<List<PlatformSyncConfigResponse>> list(@RequestParam(value = "enterpriseId") Integer enterpriseId, @RequestParam(value = "syncType")String syncType) {
        SyncTypeEnum byDesc = SyncTypeEnum.getByDesc(syncType);
        if (byDesc == null) {
            return ResultUtils.getFailedPageResult(ResponseCodeEnum.ERROR, "同步类型不支持");
        }
        List<PlatformSyncConfigResponse> list = platFormSyncConfigService.list(enterpriseId, byDesc.getSyncType());
        PageResult<List<PlatformSyncConfigResponse>> successResult = ResultUtils.getSuccessPageResult(list);
        successResult.setTotalPage(1);
        successResult.setPageSize(list.size());
        successResult.setTotalCount(list.size());
        successResult.setPageNo(1);
        return successResult;
    }

    @RequestMapping(value = "/sync_task/config_get", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<PlatformSyncConfigResponse> get(@RequestParam(value = "id") Integer id) {
        PlatformSyncConfigResponse list = platFormSyncConfigService.get(id);
        return ResultUtils.getSuccessResult(list);
    }

    @RequestMapping(value = "/sync_task/delete", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<Boolean> delete(@RequestParam(value = "id") Integer id) {
        boolean deleted = platFormSyncConfigService.deleted(id);
        return ResultUtils.getSuccessResult(deleted);
    }

    @RequestMapping(value = "/sync_task/contral", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<Boolean> open(@RequestParam(value = "id") Integer id, @RequestParam(value = "open") boolean open) {
        boolean deleted = platFormSyncConfigService.open(id, open);
        return ResultUtils.getSuccessResult(deleted);
    }

    @RequestMapping(value = "/connection/check", method = RequestMethod.POST, produces = "application/json; charset=utf-8")
    @ResponseBody
    @LogInfo
    public Result<Object> checkConnection(@RequestBody ConnectionCheckParam param) {
        String sourceType = param.getSourceType();
        SourceTypeEnum byDesc = SourceTypeEnum.getByDesc(sourceType);
        if (byDesc == null) {
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "数据源不支持");
        }
        if (byDesc == SourceTypeEnum.AD) {
            Result<Object> ERROR = checkADConnection(param);
            if (ERROR != null) return ERROR;
        } else if(byDesc == SourceTypeEnum.API) {
            try {
                param.check();
                String valueBox = param.getValueBox();
                APIConfigValueBox apiConfigValueBox = JSON.parseObject(valueBox, APIConfigValueBox.class);
                apiConfigValueBox.check();
                Object o = apiSyncHandler.testConnectData(apiConfigValueBox);
                return ResultUtils.getSuccessResult(o);
            } catch (ParamException e) {
                return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, e.getMessage());
            } catch (JSONException e) {
                return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "json格式化异常");
            }
        } else if(byDesc == SourceTypeEnum.CODE_SCRIPT) {
            try {
                param.check();
                String valueBox = param.getValueBox();
                CodeScriptValueBox codeScriptValueBox = JSON.parseObject(valueBox, CodeScriptValueBox.class);
                codeScriptValueBox.check();
                Object o = codeScriptSyncHandler.testConnectData(codeScriptValueBox);
                return ResultUtils.getSuccessResult(o);
            } catch (ParamException e) {
                return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, e.getMessage());
            } catch (JSONException e) {
                return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "json格式化异常");
            }
        }else {
            Result<Object> ERROR = checkDBConnection(param);
            if (ERROR != null) return ERROR;
        }
        return ResultUtils.getSuccessResult("");
    }

    private Result<Object> checkDBConnection(ConnectionCheckParam param) {
        try {
            param.check();
            DatabaseUtils.checkConnection(param);
        } catch (ParamException e) {
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, e.getMessage());
        }
        return null;
    }

    private Result<Object> checkADConnection(ConnectionCheckParam param) {
        String base = param.getBase();
        LdapConfigProperties properties = LdapConfigProperties.builder()
                .userName(param.getUserName())
                .url(param.getUrl())
                .base(base)
                .password(param.getPassword())
                .domainName(param.getDomainName())
                .isSasl(customNacosConfig.getCustomSyncUserSetLdapCheckSasl())
                .build();
        List<String> organizations = new ArrayList<>();
        try {
            param.check();
            if (StringUtils.isBlank(base)) {
                return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "根OU不能为空");
            }
            LdapTemplateFactory.checkConnection(properties);
            checkRootOU(properties, organizations);
            if (CollectionUtils.isEmpty(organizations)) {
                return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "AD 配置异常");
            }
        } catch (ParamException e) {
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("checkConnection, param:{}", JSON.toJSONString(param), e);
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "AD 配置异常，请确认");
        }
        return null;
    }

    /**
     * 判断根OU 是否可用的，解决一种特殊场景，如果可以查到组织数据则证明根OU是可用的
     * @param properties
     * @param organizations
     */
    private void checkRootOU(LdapConfigProperties properties, List<String> organizations) {
        LdapTemplate template = LdapTemplateFactory.getTemplate(properties);
        template.setIgnorePartialResultException(true);
        LdapQuery ldapQuery2 = query().
                where("objectclass").is("organizationalUnit");
        try {
            template.search(ldapQuery2, (AttributesMapper<String>) attributes -> {
                String attribute = attributes.get("objectGUID").get().toString().trim();
                organizations.add(attribute);
                return attribute;
            });
        } catch (Exception e) {
            log.error("checkRootOU, properties:{}", JSON.toJSONString(properties), e);
        }
    }

    @RequestMapping(value = "/sync_data", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<Boolean> sync(@RequestParam(value = "id") Integer id) {
        log.info("execute sync, id:{}", id);
        Result<Boolean> syncResult = platFormSyncConfigService.sync(id);
        return syncResult;
    }

    @RequestMapping(value = "/get_host", method = RequestMethod.GET)
    @ResponseBody
    public String getHost() {
        return customNacosConfig.getBaseUrl();
    }


    @RequestMapping(value = "/test", method = RequestMethod.GET)
    @ResponseBody
    public Object test(@RequestParam(value = "pageNo") Integer pageNo) {
        try {

            String realPath = "/usr/local/services/platform/resources/";
            log.info("test-api, realpath:{}", realPath);
            if (pageNo.equals(1)) {
                String bumen1 = FileUtils.readFileToString(new File(realPath + "bumen1.txt"));
                JSONObject jsonObject = JSON.parseObject(bumen1);
                return jsonObject;
            } else if (pageNo.equals(2)) {
                String bumen1 = FileUtils.readFileToString(new File(realPath + "bumen2.txt"));
                JSONObject jsonObject = JSON.parseObject(bumen1);
                return jsonObject;
            } else if (pageNo.equals(100)) {
                String bumen1 = FileUtils.readFileToString(new File(realPath + "user.txt"));
                JSONObject jsonObject = JSON.parseObject(bumen1);
                return jsonObject;
            } else {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("code", 0);
                return jsonObject;
            }

        } catch (IOException e) {
            log.error("test-api-error",e);
        }
        return ResultUtils.getSuccessResult();
    }

    public static void main(String[] args) {
        Integer i = 2;
        System.out.println(i.equals(2));
    }

    @RequestMapping(value = "/testToken", method = RequestMethod.GET)
    @ResponseBody
    public Result<String> testToken() {
        return ResultUtils.getSuccessResult("SJFKSJLFKJOIJANJN");
    }
}
