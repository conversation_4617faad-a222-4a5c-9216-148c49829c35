package com.fangcloud.thirdpartplatform.controller;


import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.entity.response.MessageResponse;
import com.fangcloud.thirdpartplatform.entity.response.NomalResponse;
import com.fangcloud.thirdpartplatform.service.AliFaruiService;

import cn.hutool.core.lang.UUID;
import lombok.extern.slf4j.Slf4j;


import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@RestController
@Slf4j
public class AliFaruiController {


    @Resource
    private AliFaruiService AliFaruiService;


    @ResponseBody
    @RequestMapping("/alifarui/getToken")
    public Object getToken(HttpServletRequest request, HttpServletResponse response) {
        String userId = request.getHeader(CommonConstants.HEADER_USER_ID);
        String userToken = request.getParameter(CommonConstants.PARAM_USER_TOKEN);
        MessageResponse messageResponse = new MessageResponse();
        if (StringUtils.isEmpty(userId) && StringUtils.isEmpty(userToken)) {
            messageResponse.setStatus("406");
            messageResponse.setMessage("empty user info");
            return messageResponse;
        }

        log.info("Requesting token for user: {}", userId);

        String token = AliFaruiService.getToken(request, response);
        if (StringUtils.isBlank(token)) {
            messageResponse.setStatus("406");
            messageResponse.setMessage("Failed to retrieve token");
            return messageResponse;
        }

        NomalResponse normalResponse = new NomalResponse();
        normalResponse.setStatus("200");
        normalResponse.setSuccess(true);
        normalResponse.setData(token);
        return normalResponse;
    }

    @ResponseBody
    @RequestMapping("/alifarui/review")
    public Object review(HttpServletRequest request, HttpServletResponse response) throws IOException {
        //从网关获取头信息
        String userId = request.getHeader(CommonConstants.HEADER_USER_ID);
        String userToken = request.getParameter(CommonConstants.PARAM_USER_TOKEN);
        MessageResponse messageResponse = new MessageResponse();
        if (StringUtils.isEmpty(userId) && StringUtils.isEmpty(userToken)) {
            messageResponse.setStatus("406");
            messageResponse.setMessage("empty user info");
            return messageResponse;
        }

        log.info("farui review param:{}", request.getParameterMap());

        String faruiUrl = AliFaruiService.review(request, response);
        log.info("farui review url {}", faruiUrl);
        if (faruiUrl == "") {
            messageResponse.setStatus("406");
            messageResponse.setMessage("some error not do next");
            return messageResponse;
        }
        if (StringUtils.isNotBlank(request.getParameter("json"))) {
            NomalResponse normalResponse = new NomalResponse();
            normalResponse.setStatus("200");
            normalResponse.setSuccess(true);
            normalResponse.setData(getUrlInfoMap(faruiUrl));
            return normalResponse;
        }
        response.sendRedirect(faruiUrl);
        return "";

    }

    @ResponseBody
    @RequestMapping("/alifarui/homepage")
    public Object homepage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        //从网关获取头信息
        String userId = request.getHeader(CommonConstants.HEADER_USER_ID);
        String userToken = request.getParameter(CommonConstants.PARAM_USER_TOKEN);
        MessageResponse messageResponse = new MessageResponse();
        if (StringUtils.isEmpty(userId) && StringUtils.isEmpty(userToken)) {
            messageResponse.setStatus("406");
            messageResponse.setMessage("empty user info");
            return messageResponse;
        }

        log.info("farui review param:{}", request.getParameterMap());

        String faruiUrl = AliFaruiService.homepage(request, response);
        log.info("farui review url {}", faruiUrl);
        if (faruiUrl == "") {
            messageResponse.setStatus("406");
            messageResponse.setMessage("some error not do next");
            return messageResponse;
        }
        if (StringUtils.isNotBlank(request.getParameter("json"))) {
            NomalResponse normalResponse = new NomalResponse();
            normalResponse.setStatus("200");
            normalResponse.setSuccess(true);
            normalResponse.setData(getUrlInfoMap(faruiUrl));
            return normalResponse;
        }
        response.sendRedirect(faruiUrl);
        return "";

    }

    @ResponseBody
    @PostMapping("/alifarui/uploadandreview")
    public Object uploadFile(HttpServletRequest request, HttpServletResponse response, 
    @RequestParam("file") MultipartFile file) {
        MessageResponse messageResponse = new MessageResponse();
        if (file.isEmpty()) {
            messageResponse.setStatus("406");
            messageResponse.setMessage("upload file is empty");
            return messageResponse;
        }

        try {
            // 生成UUID作为文件名
            
            String fileName = UUID.randomUUID().toString() + "." + getFileExtension(file.getOriginalFilename());
            String fileLocalPath = "/tmp/" + fileName;
            Path path = Paths.get(fileLocalPath);
            Files.copy(file.getInputStream(), path);

            log.info("File uploaded successfully: {}", fileLocalPath);

            String faruiUrl = AliFaruiService.reviewWithLocalFile(request, response, fileLocalPath, file.getOriginalFilename());
            log.info("farui review url {}", faruiUrl);
            if (faruiUrl == "") {
                messageResponse.setStatus("406");
                messageResponse.setMessage("some error not do next");
                return messageResponse;
            }
            if (StringUtils.isNoneBlank(request.getParameter("json"))) {
                NomalResponse normalResponse = new NomalResponse();
                normalResponse.setStatus("200");
                normalResponse.setSuccess(true);
                normalResponse.setData(getUrlInfoMap(faruiUrl));
                return normalResponse;
            }
            response.sendRedirect(faruiUrl);
            return "";
        } catch (Exception e) {
            e.printStackTrace();
            return "Failed to upload file: " + e.getMessage();
        }
    }

    // 获取文件扩展名
    private String getFileExtension(String fileName) {
        if (fileName == null) {
            return "";
        }
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }

    private Map<String, String> getUrlInfoMap(String faruiUrl) {
        // Extract token from URL and reconstruct URL without token
        String token = "";
        String url = faruiUrl;
        String queryString = "";
        if (faruiUrl.contains("token=")) {
            String[] parts = faruiUrl.split("token=");
            url = parts[0];
            if (parts.length > 1) {
                String tokenPart = parts[1];
                if (tokenPart.contains("&")) {
                    String[] tokenAndRest = tokenPart.split("&", 2);
                    token = tokenAndRest[0];
                    queryString = tokenAndRest[1];
                } else {
                    token = tokenPart;
                }
            }
        }

        // Reconstruct URL with remaining parameters
        if (!queryString.isEmpty()) {
            if (url.contains("?")) {
                url += queryString;
            } else {
                url += "?" + queryString;
            }
            url = url.replace("?&", "?");
            
        }

        // Create a map to hold URL and token
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("url", url);
        dataMap.put("token", token);

        return dataMap;
    }
}
