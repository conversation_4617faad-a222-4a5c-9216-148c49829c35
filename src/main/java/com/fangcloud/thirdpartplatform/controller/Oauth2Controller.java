package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.entity.response.NomalResponse;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.utils.EncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;
import java.util.UUID;

@Controller
@Slf4j
public class Oauth2Controller extends BaseController{

    @Resource
    private RedisStringManager redisStringManager;


    @RequestMapping(path = "/api/oauth/token", method = RequestMethod.GET)
    @ResponseBody
    public Object getToken(@RequestParam(value = "appKey") String appKey,
                              @RequestParam(value = "appSecret") String appSecret,
                                       @RequestParam(value = "corpId") String enterpriseId) throws Exception {
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId);
        String encryptionKey = configMap.get(PlatformGlobalConfigKeyEnum.ENCRYPTION_KEY.getKey());
        if (encryptionKey != null && EncryptUtils.encrypt(appKey, encryptionKey).equals(appSecret)) {
            String token = redisStringManager.get(SyncTaskConstants.OAUTH2_TOKEN + enterpriseId);
            if (StringUtils.isEmpty(token)) {
                UUID uuid = UUID.randomUUID();
                token = uuid.toString();
                redisStringManager.set(SyncTaskConstants.OAUTH2_TOKEN + enterpriseId, token, 3*60*60L); //三小时过期
            }
            NomalResponse normalResponse = new NomalResponse();
            normalResponse.setStatus("200");
            normalResponse.setSuccess(true);
            normalResponse.setData(token);
            return normalResponse;
        }
        NomalResponse normalResponse = new NomalResponse();
        normalResponse.setStatus("500");
        normalResponse.setSuccess(false);
        normalResponse.setData("");
        return normalResponse;
    }


    public static void main(String[] args) {
        String appSecret = EncryptUtils.encrypt("jm", "a3b9c7d2e5f1g8h4");
        String appSecret1 = EncryptUtils.encrypt("jy", "7k2p9m1n4q6r8s3t");
    }

}
