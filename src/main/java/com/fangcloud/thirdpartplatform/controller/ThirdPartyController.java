package com.fangcloud.thirdpartplatform.controller;

import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.annotation.AuthCheck;
import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.service.ThirdpartyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@Controller
@Slf4j
public class ThirdPartyController {


    @Autowired
    private ThirdpartyService ThirdpartyService;



    /**
     *  第三方回调通用接口
     * @return
     */
    @RequestMapping(value = "/thirdparty/callback", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    public Result<Object> callback (@RequestBody ThirdpartyCallbackParams thirdpartyCallbackParams)  {
        return ThirdpartyService.callback(thirdpartyCallbackParams);
    }

    /**
     *  v2调用第三方中转通用接口
     * @return
     */
    @RequestMapping(value = "/thirdparty/call", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    public Result<Object> call (@RequestBody ThirdpartyParams thirdpartyParams)  {
        return ThirdpartyService.call(thirdpartyParams);
    }


    /**
     * 调用第三方通用接口
     * @return
     */
    @RequestMapping(value = "/thirdparty/invoke", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    @AuthCheck
    public Result<Object> invoke (@RequestBody ThirdpartyParams thirdpartyParams)  {
        return ThirdpartyService.invoke(thirdpartyParams);
    }

    /**
     *  第三方回调通用POST接口(无鉴权)
     * @return
     */
    @RequestMapping(value = "/thirdparty/invokePost", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    public Object invokePost (HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jsonObject)  {
        return ThirdpartyService.invokePost(request, jsonObject, response);
    }

    /**
     *  第三方回调通用GET接口(无鉴权)
     * @return
     */
    @RequestMapping(value = "/thirdparty/invokeGet", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Object invokeGet (HttpServletRequest request, HttpServletResponse response)  {
        return ThirdpartyService.invokeGet(request, response);
    }


}
