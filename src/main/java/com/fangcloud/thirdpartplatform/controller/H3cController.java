package com.fangcloud.thirdpartplatform.controller;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.entity.dto.EnterpriseDto;
import com.fangcloud.thirdpartplatform.service.impl.custom.H3cServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

@Controller
@Slf4j
public class H3cController {

    @Resource
    private H3cServiceImpl h3cService;

    @RequestMapping(path = "/h3c/login", method = RequestMethod.GET)
    public String h3cLogin(Model model, HttpServletRequest request, HttpServletResponse response) throws IOException {
        EnterpriseDto enterpriseDto = h3cService.getEnterprises();
        if (enterpriseDto.getEnterprises().size() == 1) {
            String service = request.getParameter("service");
            if (service.contains("?")) {
                service = service + "&product_id=" + enterpriseDto.getEnterprises().get(0).getProductId();
            } else {
                service = service + "?product_id=" + enterpriseDto.getEnterprises().get(0).getProductId();
            }
            String jumperUrl =  "/platform/authentication/login?enterprise_id=" + enterpriseDto.getEnterprises().get(0).getIdAlias()
                    + "&service=" + URLEncoder.encode(service , "UTF-8");
            response.sendRedirect(jumperUrl);
            return "enterprise_select";
        }
        String enterpriseJson = JSON.toJSONString(enterpriseDto);
        log.info("enterprises list {}", enterpriseJson);
        model.addAttribute("enterprises", enterpriseJson);
        return "enterprise_select";
    }
}
