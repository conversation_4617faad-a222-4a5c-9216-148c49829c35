package com.fangcloud.thirdpartplatform.controller;


import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.service.AiChatService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@Slf4j
public class AiChatController {


    @Resource
    private AiChatService aiChatService;

    @ResponseBody
    @RequestMapping("/api/v2/knowledge/chatStream")
    public SseEmitter chatStream(HttpServletRequest request, HttpServletResponse response,@RequestBody JSONObject jSONObject) throws IOException {


        log.info("chatStream param:{}", JSONObject.toJSONString(jSONObject));

        return aiChatService.chatStream(request, response, jSONObject);

    }
}
