package com.fangcloud.thirdpartplatform.controller;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.entity.common.PageResult;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncTaskLogListParam;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncTaskRecordListParam;
import com.fangcloud.thirdpartplatform.entity.input.SyncTaskBindingParam;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncTaskLogResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncTaskRecordResponse;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskService;
import com.fangcloud.thirdpartplatform.utils.PageUtils;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 任务记录controller
 */
@RestController
@Slf4j
public class PlatformSyncTaskController {
    @Resource
    private PlatformSyncTaskService platformSyncTaskRecordService;

    @Resource
    private RedisStringManager redisStringManager;

    @RequestMapping("/sync_task/list")
    @LogInfo
    public PageResult<List<PlatformSyncTaskRecordResponse>> list(PlatformSyncTaskRecordListParam param) {
        try {
            List<PlatformSyncTaskRecordResponse> list = platformSyncTaskRecordService.list(param);
            Integer count = platformSyncTaskRecordService.countPlatformSyncTaskRecord(param);
            PageResult<List<PlatformSyncTaskRecordResponse>> pageResult = ResultUtils.getSuccessPageResult(list);
            pageResult.setPageNo(param.getPageNo());
            pageResult.setPageSize(param.getPageSize());
            pageResult.setTotalCount(count);
            pageResult.setTotalPage(PageUtils.getTotalPage(param.getPageSize(), count));
            return pageResult;
        } catch (ParamException e) {
            return ResultUtils.getFailedPageResult(ResponseCodeEnum.ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("list-error, param:{}", JSON.toJSONString(param), e);
            return ResultUtils.getFailedPageResult(ResponseCodeEnum.ERROR, "系统异常");
        }
    }

    @RequestMapping("/sync_task/error_detail_list")
    @LogInfo
    public PageResult<List<PlatformSyncTaskLogResponse>> listLogs(PlatformSyncTaskLogListParam param) {
        try {
            List<PlatformSyncTaskLogResponse> list = platformSyncTaskRecordService.listLogs(param);
            Integer count = platformSyncTaskRecordService.countPlatformSyncTaskLog(param);
            PageResult<List<PlatformSyncTaskLogResponse>> pageResult = ResultUtils.getSuccessPageResult(list);
            pageResult.setPageNo(param.getPageNo());
            pageResult.setPageSize(param.getPageSize());
            pageResult.setTotalCount(count);
            pageResult.setTotalPage(PageUtils.getTotalPage(param.getPageSize(), count));
            log.info("listLogs param:{}, result:{}", JSON.toJSONString(param), JSON.toJSONString(pageResult));
            return pageResult;
        } catch (ParamException e) {
            return ResultUtils.getFailedPageResult(ResponseCodeEnum.ERROR, e.getMessage());
        } catch (Exception e) {
            log.error("listLogs-error, param:{}", JSON.toJSONString(param), e);
            return ResultUtils.getFailedPageResult(ResponseCodeEnum.ERROR, "系统异常");
        }
    }


    @RequestMapping(value = "/sync_task/update_task_binding")
    @ResponseBody
    @LogInfo
    public Result<Boolean> AddTaskBinding(@RequestBody SyncTaskBindingParam param) {
        if (StringUtils.isNotBlank(param.getSyncType()) && param.getEnterpriseId() != null) {
            redisStringManager.set(SyncTaskConstants.TASK_BINDING + param.getEnterpriseId() + param.getSyncType(),
                    param.getConfigIds(), 0L);
            return ResultUtils.getSuccessResult();
        }
        return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "param is not null!!");
    }



    @RequestMapping(value = "/sync_task/get_task_binding")
    @ResponseBody
    @LogInfo
    public Result<String> GetTaskBinding(@RequestBody SyncTaskBindingParam param) {
        String taskBinding = redisStringManager.get(SyncTaskConstants.TASK_BINDING + param.getEnterpriseId() + param.getSyncType());
        if (StringUtils.isNotBlank(taskBinding)) {
            return ResultUtils.getSuccessResult(taskBinding);
        }
        return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, "task_binding is null!!");
    }
}
