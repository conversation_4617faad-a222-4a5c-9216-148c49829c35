package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.check.PlatformLoginConfigControllerCheck;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.PlatformLoginConfigParams;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginConfigResponse;
import com.fangcloud.thirdpartplatform.service.PlatformLoginConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 标准化企业配置服务
 */
@Controller
@Slf4j
public class PlatformLoginConfigController {

    @Resource
    private PlatformLoginConfigService platformLoginConfigService;

    @Resource
    private PlatformLoginConfigControllerCheck platformLoginConfigControllerCheck;

    @RequestMapping(path = "/platform_login/save_config", method = RequestMethod.POST)
    @ResponseBody
    @LogInfo
    public Result<Boolean> saveConfig(@RequestBody PlatformLoginConfigParams platformLoginConfigParams){

        platformLoginConfigControllerCheck.checkPlatformLoginConfig(platformLoginConfigParams);
        return platformLoginConfigService.saveConfig(platformLoginConfigParams);
    }

    @RequestMapping(path = "/platform_login/get_config", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<PlatformLoginConfigResponse> getConfig(Integer enterpriseId) {
        platformLoginConfigControllerCheck.checkEnterpriseId(enterpriseId);
        return platformLoginConfigService.getConfig(enterpriseId);

    }

}
