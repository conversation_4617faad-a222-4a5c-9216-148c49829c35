package com.fangcloud.thirdpartplatform.controller;


import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.service.impl.custom.FTPServiceImpl;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;

@RestController
@Slf4j
public class FTPSyncDataController {


    @Resource
    FTPServiceImpl ftpService;

    @ResponseBody
    @RequestMapping("/ftp/sync_data")
    public JSONObject syncdept(HttpServletRequest request, HttpServletResponse response,@RequestBody JSONObject jSONObject) throws IOException {
        JSONObject object = new JSONObject();
        try {
            log.info("sync_dept param:{}", JSONObject.toJSONString(jSONObject));
            ftpService.sync_data(request, response, jSONObject);
            log.info("data covert file success");
            object.put("code",200);
            object.put("data",new ArrayList<>());
        }catch (Exception e){
            log.error("data covert file has error:{}",e.getMessage());
            object.put("code",500);
        }
        return object;

    }
}
