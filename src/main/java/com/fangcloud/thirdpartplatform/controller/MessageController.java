package com.fangcloud.thirdpartplatform.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.fangcloud.thirdpartplatform.check.LoginControllerCheck;
import com.fangcloud.thirdpartplatform.check.MessageControllerCheck;
import com.fangcloud.thirdpartplatform.constant.PlatformTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.entity.response.MessageResponse;
import com.fangcloud.thirdpartplatform.service.CcWorkSassService;
import com.fangcloud.thirdpartplatform.service.DingTalkService;
import com.fangcloud.thirdpartplatform.service.WeixinWorkService;
import com.fangcloud.thirdpartplatform.service.ZWDService;
import com.fangcloud.thirdpartplatform.service.impl.custom.FeiShuServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.thirdPart.MessageServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Controller
@Slf4j
public class MessageController extends BaseController {

    @Resource
    private DingTalkService dingTalkService;

    @Resource
    private WeixinWorkService weixinWorkService;

    @Resource
    private MessageControllerCheck messageControllerCheck;

    @Resource
    private LoginControllerCheck loginControllerCheck;

    @Resource
    private MessageServiceImpl messageServiceImpl;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @Resource
    private ZWDService zWDService;

    @Resource
    private CcWorkSassService ccWorkSassService;

    @Resource
    private FeiShuServiceImpl feiShuServiceImpl;

    @RequestMapping(value = "/weixinwork/message", method = RequestMethod.POST)
    @ResponseBody
    public MessageResponse weixinWorkMessage(@RequestBody MessageParams params) {
        log.info("MessageParams {}", JSONObject.toJSONString(params));
        // 消息参数校验
        messageControllerCheck.checkMessageParams(params);
        // 企业参数校验
        loginControllerCheck.checkWeixinWorkInitParams(params.getWeixinWorkInitParams());
        if (weixinWorkService.sendMessage(params)) {
            return getMsgSuccessResponse(PlatformTypeEnum.WEIXIN_WORK.getType());
        }

        return getMsgErrorResponse(PlatformTypeEnum.WEIXIN_WORK.getType(), "");
    }

    @RequestMapping(value = "/dingtalk/message", method = RequestMethod.POST)
    @ResponseBody
    public MessageResponse dingTalkMessage(@RequestBody MessageParams params) {
        log.info("MessageParams {}", JSONObject.toJSONString(params));
        // 消息参数校验
        messageControllerCheck.checkMessageParams(params);
        // 企业参数校验
        loginControllerCheck.checkDingTalkInitParams(params.getDingTalkInitParams());

        if (dingTalkService.sendMessage(params)) {
            return getMsgSuccessResponse(PlatformTypeEnum.DING_TALK.getType());
        }

        return getMsgErrorResponse(PlatformTypeEnum.DING_TALK.getType(), "");
    }

    @ResponseBody
    @RequestMapping(path = "/message", method = RequestMethod.POST)
    public MessageResponse receiveMessage(@RequestBody String postData) {
        log.info("postData:{}", postData);
        if (StringUtils.isEmpty(postData)) {
            return getMsgErrorResponse(PlatformTypeEnum.THIRD_PART_PLATFORM.getType(), "postData is not null!");
        }
        Map<String, String> yfyMessage = messageServiceImpl.getYfyMessageParams(postData);
        List<PlatformSyncConfig> platformSyncConfigs = platformSyncConfigMapper.queryByEnterpriseIdAndSourceType(Integer.valueOf(yfyMessage.get("enterprise_id")), SourceTypeEnum.MESSAGE.getDesc());
        if (CollectionUtils.isEmpty(platformSyncConfigs)) {
            return getMsgErrorResponse(PlatformTypeEnum.THIRD_PART_PLATFORM.getType(), "messageConfig is not null!");
        }
        if (messageServiceImpl.executeMessage(yfyMessage, platformSyncConfigs)) {
            return getMsgSuccessResponse(PlatformTypeEnum.THIRD_PART_PLATFORM.getType());
        }
        return getMsgErrorResponse(PlatformTypeEnum.THIRD_PART_PLATFORM.getType(), "");
    }

    @RequestMapping(value = "/ccwork/message", method = RequestMethod.POST)
    @ResponseBody
    public MessageResponse ccWorkMessage(@RequestBody MessageParams params) {
        log.info("MessageParams {}", JSONObject.toJSONString(params));
        // 消息参数校验
        messageControllerCheck.checkMessageParams(params);
        // 企业参数校验
        loginControllerCheck.checkCcWorkInitParams(params.getCcWorkInitParams());
        if (ccWorkSassService.sendMessage(params)) {
            return getMsgSuccessResponse(PlatformTypeEnum.CCWORK.getType());
        }

        return getMsgErrorResponse(PlatformTypeEnum.CCWORK.getType(), "");
    }

    @RequestMapping(value = "/zwd/message", method = RequestMethod.POST)
    @ResponseBody
    public MessageResponse zwdMessage(@RequestBody MessageParams params) {
        log.info("MessageParams {}", JSONObject.toJSONString(params));
        // 消息参数校验
        messageControllerCheck.checkMessageParams(params);
        // 企业参数校验
        loginControllerCheck.checkDingTalkInitParams(params.getDingTalkInitParams());

        if (zWDService.sendMessage(params)) {
            return getMsgSuccessResponse(PlatformTypeEnum.ZWD.getType());
        }
//        if (zWDService.sendTextMessage(params)) {
//            return getMsgSuccessResponse(PlatformTypeEnum.ZWD.getType());
//        }
        return getMsgErrorResponse(PlatformTypeEnum.ZWD.getType(), "");
    }


    @RequestMapping(value = "/feishu/message", method = RequestMethod.POST)
    @ResponseBody
    public MessageResponse feiShuMessage(@RequestBody String postData) {
        log.info("feishu message postData:{}", postData);
        if (StringUtils.isEmpty(postData)) {
            return getMsgErrorResponse(PlatformTypeEnum.THIRD_PART_PLATFORM.getType(), "postData is not null!");
        }
        Map<String, String> yfyMessage = messageServiceImpl.getYfyMessageParams(postData);
        log.info("yfyMessage :{}", JSONObject.toJSONString(yfyMessage));
        PlatformSyncConfig platformSyncConfig = platformSyncConfigMapper.selectByPrimaryKey(Integer.valueOf(yfyMessage.get("configId")));
        if (feiShuServiceImpl.sendMessage(yfyMessage, platformSyncConfig)) {
            return getMsgSuccessResponse(PlatformTypeEnum.THIRD_PART_PLATFORM.getType());
        }

        return getMsgErrorResponse(PlatformTypeEnum.THIRD_PART_PLATFORM.getType(), "");
    }

    @RequestMapping(value = "/feishu/message/v2", method = RequestMethod.POST)
    @ResponseBody
    public MessageResponse feiShuMessage(@RequestBody MessageParams params) {
        log.info("MessageParams {}", JSONObject.toJSONString(params));
        // 消息参数校验
        messageControllerCheck.checkMessageParams(params);
        // 企业参数校验
        loginControllerCheck.checkFeiShuInitParams(params.getFeiShuInitParams());
        if (feiShuServiceImpl.sendMessageV2(params)) {
            return getMsgSuccessResponse(PlatformTypeEnum.FEI_SHU.getType());
        }

        return getMsgErrorResponse(PlatformTypeEnum.FEI_SHU.getType(), "");
    }


}
