package com.fangcloud.thirdpartplatform.controller;


import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.service.AiChatService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;


@RestController
@Slf4j
public class QihooTuiTuiAiChatController {


    @Resource
    private AiChatService aiChatService;

    @ResponseBody
    @RequestMapping("/api/v2/knowledge/zshchat")
    public void chatStream(HttpServletRequest request,@RequestBody JSONObject jsonObject) throws IOException {

        log.info("chatStream param:{}", JSONObject.toJSONString(jsonObject));
        String aiAnswer = aiChatService.chatAiToString(request,jsonObject);
        if (StringUtils.isNotEmpty(aiAnswer)){
            log.info("AI 回答:{}",aiAnswer);
            //发送消息给推推机器人
            aiChatService.sendMsgToRobot(jsonObject,aiAnswer);
        }

    }
}
