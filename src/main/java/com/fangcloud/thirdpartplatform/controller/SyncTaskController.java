package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.sync.SyncUserBean;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

import static com.sync.common.contants.SyncConstant.GB;

@Controller
@Slf4j
public class SyncTaskController {

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Resource
    private RedisStringManager redisStringManager;

    public static Map<String,YfyUser> tokenMap= new HashMap<>();
    public static Map<String,String> userNameMap= new HashMap<>();

    static {
        userNameMap.put("harry","123456");
        userNameMap.put("donggua","donggua");
        userNameMap.put("xiaosheng","xiaosheng");
        userNameMap.put("xiaosheng1","xiaosheng1");
        userNameMap.put("xiaosheng2","xiaosheng2");
        userNameMap.put("xiaosheng3","xiaosheng3");
        userNameMap.put("xiaosheng4","xiaosheng4");
        userNameMap.put("xiaosheng5","xiaosheng5");
        userNameMap.put("xiaosheng6","xiaosheng6");
    }

    /**
     *  执行同步任务
     * @return
     */
    @RequestMapping(value = "/syncTask/syncUsers", method = RequestMethod.POST)
    @ResponseBody
    public String syncUsers ()  {
        List<YfyUser> users = new ArrayList<>();
        YfyUser yfyUser = YfyUser.builder()
                .fullName("liangxiaosheng")
                .id("test123456")
                .spaceTotal(20* GB)
                .phone("13312344567")
                .build();


        yfyUser.setCreateTime(new Date());
        yfyUser.setStatus("1");
        users.add(yfyUser);
        SyncUserBean syncUserBean = new SyncUserBean();
        syncUserBean.setUsers(users);
        syncUserBean.setPlatformId(562L);
        syncUserBean.setEnterpriseId(21791L);
        syncUserBean.setRequestId("SecurityHelper.randomUUIDString()");
        v2ClientHelper.syncUser(syncUserBean);


        return null;
    }

    /**
     *  执行同步任务
     * @return
     */
    @RequestMapping(value = "/syncTask/testRedisGet", method = RequestMethod.POST)
    @ResponseBody
    public String testRedisGet (String key)  {
        log.info("key {}",key);
        String s = redisStringManager.get(key);
        log.info("value {}",s);
        return s;
    }
    /**
     *  执行同步任务
     * @return
     */
    @RequestMapping(value = "/syncTask/testRedisSet", method = RequestMethod.POST)
    @ResponseBody
    public Boolean testRedisSet (String key, String value)  {

        log.info("key {}, value {}",key ,value);
        redisStringManager.set(key,value,100L);
        return true;
    }
    /**
     *  执行同步任务
     * @return
     */
    @RequestMapping(value = "/syncTask/testRedisDelete", method = RequestMethod.POST)
    @ResponseBody
    public Boolean testRedisDelete (String key)  {
        redisStringManager.delete(key);
        return true;
    }

    /**
     *  执行同步任务
     * @return
     */
    @RequestMapping(value = "/syncTask/testRedisIncrement", method = RequestMethod.POST)
    @ResponseBody
    public Boolean testRedisIncrement (String key)  {
        redisStringManager.increment(key,1);
        return true;
    }


    /**
     *  测试获取token
     * @return
     */
    @RequestMapping(value = "/test/getToken", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<String> getToken (String username, String password)  {
        String value = userNameMap.get(username);
        if(StringUtils.isEmpty(value) || !value.equals(password)){
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR,"登陆失败");
        }
        String token = UUID.randomUUID().toString();
        YfyUser build = YfyUser.builder()
                .fullName(username)
                .id(username)
                .email(username+"@alpha205.com")
                .build();
        tokenMap.put(token, build);
        return ResultUtils.getSuccessResult(token);
    }
    /**
     *  测试获取用户信息
     * @return
     */
    @RequestMapping(value = "/test/getUserInfo", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<YfyUser> getUserInfo (String token)  {

        YfyUser yfyUser = tokenMap.get(token);
        if(Objects.isNull(yfyUser)){
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR, null);
        }
        return ResultUtils.getSuccessResult(yfyUser);
    }

}
