package com.fangcloud.thirdpartplatform.controller;


import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class HeathController {

    @ResponseBody
    @RequestMapping("/beat")
    @LogInfo
    public String checkHealth()
    {
        log.info("checkHealth OK !");
        return "OK";
    }
}
