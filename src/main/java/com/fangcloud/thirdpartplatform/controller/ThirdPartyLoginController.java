package com.fangcloud.thirdpartplatform.controller;

import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.service.ThirdpartyLoginService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@Controller
@Slf4j
public class ThirdPartyLoginController {


    @Autowired
    private ThirdpartyLoginService thirdpartyLoginService;


    /**
     *  第三方登陆需要单独写cookie回调通用接口
     * @return
     */
    @RequestMapping(value = "/thirdparty/loginCallback", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Object loginCallback(HttpServletRequest request, HttpServletResponse response) throws Exception{
        return thirdpartyLoginService.loginCallback(request, response);
    }

    /**
     *  v第三方登陆需要单独清cookie登出
     * @return
     */
    @RequestMapping(value = "/thirdparty/logout", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Object logout (HttpServletRequest request, HttpServletResponse response) throws Exception{
        return thirdpartyLoginService.logout(request, response);
    }

}
