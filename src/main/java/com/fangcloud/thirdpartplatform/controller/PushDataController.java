package com.fangcloud.thirdpartplatform.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.annotation.LogInfo;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.PushResultTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.PushConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.service.PushSyncService;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.PlatformSyncConfigExecutorImpl;
import com.fangcloud.thirdpartplatform.service.impl.SyncServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.datasource.PushSyncHandler;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import com.fangcloud.thirdpartplatform.utils.sm4.SM4Utils;
import com.google.common.collect.Lists;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-05-17
 **/
@RestController
@Slf4j
public class PushDataController {

    @Resource
    private PushSyncHandler pushSyncHandler;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @Resource
    private PlatformSyncConfigExecutorImpl platformSyncConfigExecutor;

    @Resource
    private PushSyncService pushSyncService;

    @Resource
    private SyncServiceImpl syncService;

    @Resource
    private EnterpriseServiceImpl enterpriseService;

    @RequestMapping(value = "/pushData/**")
    public Object pushData(HttpServletRequest httpServletRequest) {
        String requestURI = httpServletRequest.getRequestURI();
        String[] split = requestURI.split("/");
        Integer enterpriseId = Integer.valueOf(split[3]);
        int i = requestURI.indexOf(enterpriseId.toString());
        String path = requestURI.substring(i + enterpriseId.toString().length());
        PlatformSyncConfig pushSyncConfig = null;
        PushConfigValueBox pushConfigValueBox = null;
        List<PlatformSyncConfig> platformSyncConfigs = platformSyncConfigMapper.queryByEnterpriseIdAndSourceType(enterpriseId, SourceTypeEnum.PUSH.getDesc());
        if (CollectionUtils.isEmpty(platformSyncConfigs)) {
            throw new ParamException("推送配置不能为空!");
        }
        for (PlatformSyncConfig platformSyncConfig : platformSyncConfigs) {
            if (platformSyncConfig.getSyncStatus() == 2) {
                continue;
            }
            pushConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), PushConfigValueBox.class);
            if (pushConfigValueBox.getPushUri().equals(path)) {
                log.info("current execution pushConfigValueBox:{} , path:{}", JSON.toJSONString(pushConfigValueBox), path);
                pushSyncConfig = platformSyncConfig;
                break;
            }
        }
        if (pushSyncConfig == null) {
            throw new ParamException("推送url配置与接口url不符!");
        }
        Map<String, Object> resultMap = new HashMap<>();
        Document document = DocumentHelper.createDocument();
        Element xml = document.addElement(PushResultTypeEnum.XML.getDesc());
        if (pushConfigValueBox.getIsAsync()) {
            Integer taskId = platformSyncConfigExecutor.execute(pushSyncConfig, false);
            pushSyncHandler.getResultData(resultMap, pushConfigValueBox, taskId, xml);
        } else {
            Integer taskId = platformSyncConfigExecutor.execute(pushSyncConfig, true);
            pushSyncHandler.getResultData(resultMap, pushConfigValueBox, taskId, xml);
        }
        if (pushConfigValueBox.getReturnDataType().equals(PushResultTypeEnum.XML.getDesc())) {
            return xml.asXML();
        }
        return resultMap;
    }

    @RequestMapping(value = "/generateDocument", method = RequestMethod.GET)
    public Object generateDocument(@RequestParam(value = "config_id") Integer configId) {
        if (configId == null) {
            throw new ParamException("config_id is not null!");
        }
        Object generateDocument = pushSyncHandler.generateDocument(configId);
        return generateDocument;
    }


    @RequestMapping(value = "/push_exception_retry", method = RequestMethod.GET)
    @ResponseBody
    @LogInfo
    public Result<Boolean> asyncExceptionRetry(@RequestParam(value = "enterprise_id") Integer enterpriseId) {
        pushSyncService.asyncExceptionRetry(enterpriseId);
        return ResultUtils.getSuccessResult(true);
    }

    @RequestMapping(value = "/data/szgd/**")
    public Object pushOrg(HttpServletRequest httpServletRequest) {
        String requestURI = httpServletRequest.getRequestURI();
        String[] split = requestURI.split("/");
        String requestPath = split[4];
        PlatformSyncConfig pushSyncConfig = null;
        List<PlatformSyncConfig> platformSyncConfigs =null;
        if (requestPath.startsWith("Org")){
            platformSyncConfigs = platformSyncConfigMapper.queryByEnterpriseId(115, SyncTypeEnum.getByDesc("DEPT").getSyncType());
        }else {
            platformSyncConfigs = platformSyncConfigMapper.queryByEnterpriseId(115, SyncTypeEnum.getByDesc("USER").getSyncType());
        }

        if (CollectionUtils.isEmpty(platformSyncConfigs)) {
            throw new ParamException("推送配置不能为空!");
        }
        PlatformSyncConfig platformSyncConfig = platformSyncConfigs.get(0);
        PushConfigValueBox pushConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), PushConfigValueBox.class);
        List<String> actions = Arrays.asList(pushConfigValueBox.getPushUri().split(","));
        if (actions.contains(requestPath)) {
            if (requestPath.equals("OrgDeleteService")||requestPath.equals("UserDeleteService")){
                pushConfigValueBox.setOperationType(CommonConstants.OPERATION_TYP_DELETE);
            }else {
                pushConfigValueBox.setOperationType(CommonConstants.OPERATION_TYP_SAVE_OR_UPDATE);
            }
            platformSyncConfig.setValueBox(JSON.toJSONString(pushConfigValueBox));
            pushSyncConfig = platformSyncConfig;
        }
        if (pushSyncConfig == null) {
            throw new ParamException("推送url配置与接口url不符!");
        }
        Integer taskId = platformSyncConfigExecutor.execute(pushSyncConfig, true);
        Map<String, Object> resultMap = new HashMap<>();
        pushSyncHandler.getResultData(resultMap,pushConfigValueBox,taskId,null);
        return SM4Utils.szgdEncreptText(JSON.toJSONString(resultMap));

    }

    /*@RequestMapping(value = "/user/szgd/**")
    public Object pushUser(HttpServletRequest httpServletRequest) {
        String requestURI = httpServletRequest.getRequestURI();
        String[] split = requestURI.split("/");
        String requestPath = split[4];
        PlatformSyncConfig pushSyncConfig = null;
        List<PlatformSyncConfig> platformSyncConfigs = platformSyncConfigMapper.queryByEnterpriseId(115, SyncTypeEnum.getByDesc("USER").getSyncType());
        if (CollectionUtils.isEmpty(platformSyncConfigs)) {
            throw new ParamException("推送配置不能为空!");
        }
        PlatformSyncConfig platformSyncConfig = platformSyncConfigs.get(0);
        PushConfigValueBox pushConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), PushConfigValueBox.class);
        List<String> actions = Arrays.asList(pushConfigValueBox.getPushUri().split(","));
        if (actions.contains(requestPath)) {
            if (requestPath.equals("UserDeleteService")){
                pushConfigValueBox.setOperationType(CommonConstants.OPERATION_TYP_DELETE);
            }else {
                pushConfigValueBox.setOperationType(CommonConstants.OPERATION_TYP_SAVE_OR_UPDATE);
            }
            platformSyncConfig.setValueBox(JSON.toJSONString(pushConfigValueBox));
            pushSyncConfig = platformSyncConfig;
        }
        if (pushSyncConfig == null) {
            throw new ParamException("推送url配置与接口url不符!");
        }
        Integer taskId = platformSyncConfigExecutor.execute(pushSyncConfig, true);
        Map<String, Object> resultMap = new HashMap<>();
        pushSyncHandler.getResultData(resultMap,pushConfigValueBox,taskId,null);
        return SM4Utils.szgdEncreptText(JSON.toJSONString(resultMap));

    }*/
    @RequestMapping(value = "/data/import")
    public Object importData(@RequestParam(value = "dataType") String dataType,@RequestParam(value = "filePath") String filePath){
        //开始处理组织文件
        List<YfyDepartment> orgDataList = new ArrayList<>();
        List<YfyUser> userDataList = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
            // 读取表头（第一行）
            String headerLine = br.readLine();
            if (headerLine == null) {
                System.err.println("CSV文件为空或格式不正确");
                return "error";
            }

            // 解析表头，建立列名到索引的映射
            String[] headers = headerLine.split(",");
            Map<String, Integer> headerMap = new HashMap<>();
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
            if ("org".equals(dataType)){
                // 检查必要的列是否存在
                if (!headerMap.containsKey("id") || !headerMap.containsKey("name") || !headerMap.containsKey("parentid")) {
                    System.err.println("CSV文件缺少必要的列(id, name或parentId)");
                    return "org-error";
                }
                int idIndex = headerMap.get("id");
                int nameIndex = headerMap.get("name");
                int parentIdIndex = headerMap.get("parentid");
                // 读取数据行
                String line;
                while ((line = br.readLine()) != null) {
                    String[] values = line.split(",");
                    String id = (idIndex < values.length) ? values[idIndex].trim() : "";
                    String name = (nameIndex < values.length) ? values[nameIndex].trim() : "";
                    String parentId = (parentIdIndex < values.length) ? values[parentIdIndex].trim() : "";
                    orgDataList.add(YfyDepartment.builder().id(id).name(name).parentId(parentId).spaceTotal(2048L).status("1").build());
                }
            }else {
                // 检查必要的列是否存在
                if (!headerMap.containsKey("id") || !headerMap.containsKey("real_name") || !headerMap.containsKey("department_id")||!headerMap.containsKey("user_status")) {
                    System.err.println("CSV文件缺少必要的列(id, name或parentId)");
                    return "user-error";
                }
                int idIndex = headerMap.get("id");
                int nameIndex = headerMap.get("real_name");
                int departmentIdIndex = headerMap.get("department_id");
                int userStatusIndex = headerMap.get("user_status");
                // 读取数据行
                String line;
                while ((line = br.readLine()) != null) {
                    String[] values = line.split(",");
                    String id = (idIndex < values.length) ? values[idIndex].trim() : "";
                    String name = (nameIndex < values.length) ? values[nameIndex].trim() : "";
                    List<String> departmentids = (departmentIdIndex < values.length) ? Lists.newArrayList(values[departmentIdIndex].trim()) : new ArrayList<>();
                    boolean userStatus = !"1".equals(values[userStatusIndex].trim())? true:false;
                    String email = id.contains("@") ? id : id + "@csg.cn";
                    if (!userStatus){
                        userDataList.add(YfyUser.builder().id(id).fullName(name).departmentIds(departmentids).spaceTotal(10*1073741824L).status("1").email(email).build());
                    }

                }
            }

        } catch (IOException e) {
            System.err.println(e.getMessage());
        }catch (Exception e){
            System.err.println(e.getMessage());
        }
        Enterprise enterprise = enterpriseService.getEnterpriseById(115);
        PlatformSyncConfig pushSyncConfig = new PlatformSyncConfig();
        pushSyncConfig.setSourceType("PUSH");
        if (CollectionUtil.isNotEmpty(orgDataList)){
            syncService.syncDepartments(new ArrayList<>(),orgDataList,1,enterprise,pushSyncConfig);
        }
        if (CollectionUtil.isNotEmpty(userDataList)){
            syncService.syncUsers(new ArrayList<>(),userDataList,2,enterprise,pushSyncConfig,false,"");
        }
        return "success";
    }
}
