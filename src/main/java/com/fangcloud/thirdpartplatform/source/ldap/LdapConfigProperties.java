package com.fangcloud.thirdpartplatform.source.ldap;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Builder
@Data
public class LdapConfigProperties implements Serializable {

    private static final long serialVersionUID = 802392078748040891L;
    private String userName;

    private String password;

    private String base;

    private String url;

    /**
     * 域名
     * 例如 @yifangyun.cn
     * 只需要加上邮箱@之后的名称就好
     */
    private String domainName;

    /**
     * 是否使用sasl
     */
    private boolean isSasl;
}
