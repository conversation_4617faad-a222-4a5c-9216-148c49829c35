package com.fangcloud.thirdpartplatform.source.ldap;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.ParamException;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ldap.CommunicationException;
import org.springframework.ldap.InvalidNameException;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;

import org.springframework.ldap.AuthenticationException;
import org.springframework.ldap.support.LdapUtils;

import javax.naming.Context;
import javax.naming.directory.DirContext;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class LdapTemplateFactory {

    public static void main(String[] args) {
        LdapConfigProperties build = LdapConfigProperties.builder().isSasl(true).base("DC=fangyun,DC=net").password("<EMAIL>").url("ldap://47.96.157.80:389").userName("<EMAIL>").build();
        checkConnection(build);
    }

    public static boolean checkConnection(LdapConfigProperties properties) {
        DirContext context = null;
        try {
            LdapTemplate template = getTemplate(properties);
            String domainName = properties.getDomainName();
            String userName = properties.getUserName();
            String domain = userName + (domainName == null ? "" : domainName);
            context = template.getContextSource().getContext(domain, properties.getPassword());
            log.info("checkConnection, param:{}, result:{}", JSON.toJSONString(properties), JSON.toJSONString(context));
        } catch (AuthenticationException e) {
            log.error("param:{}", JSON.toJSONString(properties), e);
            throw new ParamException("AD 连接用户名密码错误");
        } catch (CommunicationException e) {
            log.error("param:{}", JSON.toJSONString(properties), e);
            throw new ParamException("AD 连接地址错误，请确认后重试");
        } catch(InvalidNameException e) {
            log.error("param:{}", JSON.toJSONString(properties), e);
            throw new ParamException("AD 配置异常，请确认后重试");
        } catch (Exception e) {
            log.error("param:{}", JSON.toJSONString(properties), e);
            throw new ParamException("AD 配置异常，请确认以后重试");
        } finally {
            LdapUtils.closeContext(context);
        }
        return true;
    }

    public static LdapTemplate getTemplate(LdapConfigProperties properties) {

        LdapTemplate ldapTemplate = new LdapTemplate(getSource(properties));
        return ldapTemplate;
    }

    public static LdapContextSource getSource(LdapConfigProperties properties) {
        try {
        LdapContextSource source = new LdapContextSource();
        Map<String, Object> config = new HashMap();
        //  解决 乱码 的关键一句
        config.put("java.naming.ldap.attributes.binary", "objectGUID");
        config.put("com.sun.jndi.ldap.connect.timeout","20000");
        config.put("com.sun.jndi.ldap.read.timeout","20000");
        if (properties.isSasl()) {
            //跳过SSL证书验证
            TrustManager[] trustAllCerts = new TrustManager[] { new TrustAllTrustManager() };
            SSLContext sc = null;
            sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new java.security.SecureRandom());
            SSLContext.setDefault(sc);
            //config.put("java.naming.security.sasl.realm","" );
            config.put(Context.SECURITY_AUTHENTICATION, "SASL");
            // 指定 SASL 机制为 DIGEST-MD5
            config.put("javax.security.sasl.mechanism", "DIGEST-MD5");
            config.put("javax.security.sasl.strength", "high");
            config.put("javax.security.sasl.policy.noplain", "true");
            // 可选：防止退回到 PLAIN 机制
            config.put("javax.security.sasl.server.authentication", "true");
            config.put("javax.security.sasl.qop", "auth-int,auth-conf");
            String host = extractHostFromUrl(properties.getUrl());
            config.put("javax.security.sasl.digestURI", "ldap/" + host);
            String realm = properties.getDomainName();
            if (realm != null && !realm.isEmpty()) {
                if (realm.startsWith("@")) {
                    realm = realm.substring(1);
                }
                config.put("java.naming.security.sasl.realm", realm);
            }
            // 配置SSL相关系统属性
            System.setProperty("javax.net.ssl.trustStore", "NONE");
            System.setProperty("javax.net.ssl.trustStoreType", "JKS");
            System.setProperty("javax.net.ssl.trustStorePassword", "");
            System.setProperty("com.sun.jndi.ldap.object.disableEndpointIdentification", "true");
            System.setProperty("java.naming.referral", "follow");
        }
        source.setUserDn(properties.getUserName());
        source.setPassword(properties.getPassword());
        source.setBase(properties.getBase());
        source.setUrls(new String[]{properties.getUrl()});
        source.setBaseEnvironmentProperties(config);
        source.afterPropertiesSet();
        log.info("source: {}", JSONUtil.toJsonStr(source));
        return source;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 从URL中提取主机名
     */
    private static String extractHostFromUrl(String url) {
        if (url == null || url.isEmpty()) {
            return "";
        }

        // 移除协议部分
        String host = url;
        if (host.startsWith("ldap://")) {
            host = host.substring(7);
        } else if (host.startsWith("ldaps://")) {
            host = host.substring(8);
        }

        // 移除端口和路径部分
        int portIndex = host.indexOf(':');
        if (portIndex > 0) {
            host = host.substring(0, portIndex);
        }

        int pathIndex = host.indexOf('/');
        if (pathIndex > 0) {
            host = host.substring(0, pathIndex);
        }

        return host;
    }


    /**
     * 信任所有证书的TrustManager
     */
    private static class TrustAllTrustManager implements X509TrustManager {
        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }

        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
            // 信任所有客户端证书
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
            // 信任所有服务器证书
        }
    }
}
