package com.fangcloud.thirdpartplatform.utils;

public class PageUtils {

    public static Integer getLimit(Integer pageNo, Integer pageSize) {
        pageSize = pageSize == null ? 10 : pageSize;
        pageNo = (pageNo == null || pageNo <= 0) ? 1 : pageNo;

        return (pageNo - 1) * pageSize;
    }

    public static Integer getTotalPage(Integer pageSize, Integer totalCount) {
        if (pageSize == null || pageSize == 0) {
            pageSize = 10;
        }
        totalCount = totalCount == null ? 0 : totalCount;
        int page = (totalCount - 1) / pageSize + 1;
        return page;
    }
}
