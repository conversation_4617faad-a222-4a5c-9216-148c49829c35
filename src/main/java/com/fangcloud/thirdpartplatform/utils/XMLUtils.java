package com.fangcloud.thirdpartplatform.utils;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.entity.dto.APIResultConfigDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.*;
import org.json.JSONObject;
import org.json.XML;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public abstract class XMLUtils {

    public static boolean isXmlStr(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        if (str.startsWith("<") && str.endsWith(">")) return true;
        return false;
    }

    public static String reade(String xml, String xmlPath) {
        try {
            Document read = DocumentHelper.parseText(xml);
            Node node = read.selectSingleNode(xmlPath);
            return node == null ? null : node.getStringValue();
        } catch (DocumentException e) {
            log.error("xml-read-error, xml:{}, xmlPath:{}", xml, xmlPath, e);
        }
        return null;
    }

    public static List<Map<String, Object>> readeList(String xml, String xmlPath, List<APIResultConfigDto> resultConfigs) {
        if (resultConfigs == null) {
            return null;
        }
        List<Map<String, Object>> results = new ArrayList<>();
        try {
            Document read = DocumentHelper.parseText(xml);
            List<Node> nodes = read.selectNodes(xmlPath);
            List<String> keys = resultConfigs.stream().map(APIResultConfigDto::getName).collect(Collectors.toList());
            Map<String, Object> obj = null;
            for (Node node : nodes) {
                obj = new HashMap<>();
                for (String key : keys) {
                    Node value = node.selectSingleNode(key);
                    obj.put(key, value == null ? null : value.getStringValue());
                }
                results.add(obj);
            }
        } catch (DocumentException e) {
            log.error("xml-readeList-error, xml:{}, xmlPath:{}, resultConfigs:{}", xml, xmlPath, JSON.toJSONString(resultConfigs), e);
        }
        return results;
    }

    public static void main(String[] args) {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><DATAINFOS uuid=\"ppo123\"><DATAINFO><LASTMODIFYRECORDTIME REMARK=\"上一次变更时间\" STARTTIME=\"2019-04-12 00:00:00\"  ENDTIME=\"2019-05-17 23:59:59\" ></LASTMODIFYRECORDTIME></DATAINFO></DATAINFOS>";
        JSONObject jsonObject = XML.toJSONObject(xml);
        System.out.println(jsonObject);
        List<APIResultConfigDto> resultConfigs = new ArrayList<>();
        APIResultConfigDto name = new APIResultConfigDto();
        name.setName("name");
        resultConfigs.add(name);
        APIResultConfigDto url = new APIResultConfigDto();
        url.setName("url");
        resultConfigs.add(url);

        List<Map<String, Object>> maps = readeList(xml, "/sites/*", resultConfigs);
        System.out.println(maps);
    }
}
