package com.fangcloud.thirdpartplatform.utils;

import com.fangcloud.thirdpartplatform.entity.input.RadiusCheckLoginParams;
import lombok.extern.slf4j.Slf4j;
import net.jradius.client.RadiusClient;
import net.jradius.client.auth.CHAPAuthenticator;
import net.jradius.dictionary.Attr_ReplyMessage;
import net.jradius.dictionary.Attr_UserName;
import net.jradius.dictionary.Attr_UserPassword;
import net.jradius.packet.AccessAccept;
import net.jradius.packet.AccessRequest;
import net.jradius.packet.RadiusResponse;
import net.jradius.packet.attribute.AttributeFactory;
import net.jradius.packet.attribute.AttributeList;

import java.net.InetAddress;

@Slf4j
public class RadiusUtils {

    public static Boolean checkLogin(RadiusCheckLoginParams params) {
        String serverIp = params.getHost();
        String username = params.getUsername();
        String password = params.getPassword();
        String secret = params.getSecret();
        try {
            AttributeFactory.loadAttributeDictionary("net.jradius.dictionary.AttributeDictionaryImpl");
            InetAddress host = InetAddress.getByName(serverIp);
            RadiusClient rc = new RadiusClient(host, secret, params.getPort(), 1813, 5);
            AttributeList attrs = new AttributeList();
            attrs.add(new Attr_UserName(username));
            AccessRequest request = new AccessRequest(rc, attrs);
            request.addAttribute(new Attr_UserPassword(password));
            RadiusResponse reply = reply = rc.authenticate(request, new CHAPAuthenticator() {
                }, 3);


            boolean isAuthenticated = (reply instanceof AccessAccept);

            log.info("radius isAuthenticated:{}", isAuthenticated);
            String replyMessage = (String) reply.getAttributeValue(Attr_ReplyMessage.TYPE);
            if (replyMessage != null) {
            log.info("replyMessage:{}", replyMessage);
            }
            return isAuthenticated;
        } catch (Exception e) {
            log.info("radius checkLogin error:{}", e.getMessage());
            return false;
        }
    }

}
