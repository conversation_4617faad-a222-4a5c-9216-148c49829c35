package com.fangcloud.thirdpartplatform.utils.sm4;

public class SM4Utils {

    // 与客户端通信时使用的密钥
    private final static String SECRET_KEY_CLIENT = "*yoiH&^%56_Ha!@#";
    // 管理平台中使用的密钥
    private final static String SECRET_KEY_WEB = "*yoiH&^%56_Ha!@#";
    // 南瑞API使用的密钥
    private final static String SECRET_KEY_NANRUI = "NaNRuISKeY#20*22";
    // CBC模式下才使用的偏移量
    private final static String IV = "UISwD9fW6cFh9SNS";

    private final static String SECRET_KEY_SZGD = "9816643913193993";

    private final static SM4Instance webSm4Instance = new SM4Instance(SECRET_KEY_WEB);
    private final static SM4Instance clientSm4Instance = new SM4Instance(SECRET_KEY_CLIENT);
    private final static SM4Instance nanruiSm4Instance = new SM4Instance(SECRET_KEY_NANRUI);
    private final static SM4Instance szgdSM4Instance = new SM4Instance(SECRET_KEY_SZGD.substring(0,16));

    /**
     * 对字符串进行加密，主要用于在保存用户密码时，进行加密操作，web使用
     * @param plainText
     * @return
     */
    public static String webEncryptText(String plainText) {
            return webSm4Instance.encryptData(plainText);
            }

    /**
     * 对字符串数据进行解密操作，web使用
     * @param cipherText
     * @return
     */
    public static String webDecryptText(String cipherText) {
            return webSm4Instance.decryptData(cipherText);
            }

    /**
     * 与客户端通信时，加密字符串
     * @param plainText
     * @return
     */
    public static String clientEncryptText(String plainText) {
            return clientSm4Instance.encryptData(plainText);
            }

    /**
     * 与客户端通信时，解密客户端传输的字符串
     * @param cipherText
     * @return
     */
    public static String clientDecryptText(String cipherText) {
            return clientSm4Instance.decryptData(cipherText);
            }

    /**
     * 与客户端通信时，解密客户端传输的GBK字符串
     * @param cipherText
     * @return
     */
    public static String clientDecryptGBKText(String cipherText) {
        return clientSm4Instance.decryptDataECBByGBK(cipherText);
    }

    /**
     * 对字符串进行加密，主要用于在保存用户密码时，进行加密操作，南瑞集团API使用
     * @param plainText
     * @return
     */
    public static String nanruiEncryptText(String plainText) {
        return nanruiSm4Instance.encryptData(plainText);
    }

    /**
     * 对字符串数据进行解密操作，南瑞集团API使用
     * @param cipherText
     * @return
     */
    public static String nanruiDecryptText(String cipherText) {
        return nanruiSm4Instance.decryptData(cipherText);
    }

    public static String szgdDecryptText(String cipherText){return szgdSM4Instance.decryptData(cipherText);}

    public static String szgdEncreptText(String plainText){return szgdSM4Instance.encryptData(plainText);}

    public static void main(String[] args) {
        //System.out.println(szgdDecryptText("nOhUb3uIX6U6HrUtofJFVSPRFl8wjil2GDBUZAvGwRsWj/nGEV/46ZO0hc/w8kZPjhHDWtkgjYfWuWpzu4S96GS1ErNgQvQLNcE1gQ/sFNwGBwKhzEEsyXdHNdaCFXScpPS0D95ZjtmaTF8mqB1KfA=="));
        //System.out.println("部门删除报文加密"+szgdEncreptText("{\"serialNum\":\"************-DSFIFASCC\",\"baseOrgId\":\"1065781\"}"));
        //System.out.println("用户删除报文加密"+szgdEncreptText("{\"serialNum\":\"************-DSFIFAS00C\",\"uid\":\"2FEA83F790B2432CA92C2AC6D2DFA522\"}"));
        //System.out.println("部门添加报文加密"+szgdEncreptText("{\"serialNum\":\"************-DSFIFASCC\",\"baseOrgId\":\"1065781\",\"baseOrgCode\":\"\",\"baseOrgName\":\"测试的部门010\",\"parentBaseOrgId\":\"\"}"));
        //System.out.println("用户添加报文加密"+szgdEncreptText("{\"loginName\":\"test19889303@csgcn\",\"realName\":\"马冬梅004\",\"mobile\":\"13110261633\",\"userStatus\":\"1\",\"userId\":\"2FEA83F790B2432CA92C2AC6D2DFA522\",\"email\":\"<EMAIL>\",\"serialNum\":\"************-DSFIFAS00C\",\"baseOrgId\":\"1065781\"}"));
        }
}
