package com.fangcloud.thirdpartplatform.utils;

import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.entity.common.PageResult;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import org.slf4j.MDC;

/**
 * 返回结果包装工具类
 *
 * <AUTHOR> create date: 15/9/12
 */
public class ResultUtils {
    private ResultUtils() {
    }

    public static Result<Boolean> getSuccessResult() {
        Result<Boolean> result = new Result<>();
        result.setData(true);
        result.setSuccess(true);
        result.setRequestId(MDC.get("uuid"));
        return result;
    }

    public static <T> Result<T> getSuccessResult(T t) {
        Result<T> result = new Result<>();
        result.setData(t);
        result.setSuccess(true);
        result.setRequestId(MDC.get("uuid"));
        return result;
    }

    public static <T> PageResult<T> getSuccessPageResult(T t) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setData(t);
        pageResult.setSuccess(true);
        pageResult.setRequestId(MDC.get("uuid"));
        return pageResult;
    }

    public static Result<Boolean> getFailedResult(String msg, String errorCode) {
        Result<Boolean> result = new Result<>();
        result.setData(false);
        result.setErrorCode(errorCode);
        result.setMessage(msg);
        result.setRequestId(MDC.get("uuid"));
        return result;
    }


    public static Result<Boolean> getFailedResult(ResponseCodeEnum responseCodeEnum) {
        Result<Boolean> result = new Result<>();
        result.setData(false);
        result.setErrorCode(responseCodeEnum.getType());
        result.setMessage(responseCodeEnum.getDesc());
        result.setRequestId(MDC.get("uuid"));
        return result;
    }

    public static <T> Result<T> getFailedResult(ResponseCodeEnum responseCodeEnum, T data) {
        Result<T> result = new Result<>();
        result.setData(data);
        result.setErrorCode(responseCodeEnum.getType());
        result.setMessage(responseCodeEnum.getDesc());
        result.setRequestId(MDC.get("uuid"));
        return result;
    }

    public static <T> Result<T> getFailedResult(ResponseCodeEnum responseCodeEnum, String mag) {
        Result<T> result = new Result<>();
        result.setErrorCode(responseCodeEnum.getType());
        result.setMessage(mag);
        result.setRequestId(MDC.get("uuid"));
        return result;
    }

    public static <T> Result<T> getFailedResult(ResponseCodeEnum responseCodeEnum, String mag, T data) {
        Result<T> result = new Result<>();
        result.setData(data);
        result.setErrorCode(responseCodeEnum.getType());
        result.setMessage(mag);
        result.setRequestId(MDC.get("uuid"));
        return result;
    }

    public static <T> PageResult<T> getFailedPageResult(ResponseCodeEnum responseCodeEnum, String mag) {
        PageResult<T> pageResult = new PageResult<>();
        pageResult.setErrorCode(responseCodeEnum.getType());
        pageResult.setMessage(mag);
        pageResult.setRequestId(MDC.get("uuid"));
        return pageResult;
    }
}
