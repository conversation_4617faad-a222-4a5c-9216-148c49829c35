package com.fangcloud.thirdpartplatform.utils;

public class SyncStringUtils {

    public static String formatString(String name)
    {
        if (name != null) {

            return name
                    .replace("/", "／")
                    .replace("*","＊")
                    .replace("?", "？")
                    .replace(":", "：")
                    .replace(">", "＞")
                    .replace("<", "＜")
                    .replace("|", "｜")
                    .replace("$", "＄")
                    .replace(".", "．")
                    .replace("(", "（")
                    .replace(")", "）")
                    .replace("[", "［")
                    .replace("]", "］")
                    .trim();
        }

        return name;
    }
}
