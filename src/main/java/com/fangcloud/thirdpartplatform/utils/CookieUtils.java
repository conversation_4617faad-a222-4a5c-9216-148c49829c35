package com.fangcloud.thirdpartplatform.utils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class CookieUtils {
    public static String SSO_COOKIE_NAME = "iPlanetDirectoryPro";



    public static String getValueFromCookie(Cookie[] cookies, String value) {
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (value.equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        return null;
    }

    public static String getSsoCookieValue(HttpServletRequest request) {
        Cookie[] cs = request.getCookies();
        return  null == cs ? null : getSsoCookieValue(cs);
    }

    public static Cookie getSsoCookie(HttpServletRequest request){
        Cookie[] cs = request.getCookies();
        return  null == cs ? null : getSsoCookie(cs);
    }

    public static String getSsoCookieValue(Cookie[] cs) {
        Cookie cookie = getSsoCookie(cs);
        return null == cookie? null : cookie.getValue();
    }

    public static Cookie getSsoCookie(Cookie[] cs){
        for (int i = 0; i < cs.length; i++) {
            if ( CookieUtils.SSO_COOKIE_NAME.equals(cs[i].getName())) {
                return cs[i];
            }
        }
        return null;
    }

    public static void deleteAllCookie(HttpServletResponse response, Cookie[] cookies) {
        for (Cookie cookie: cookies) {
            cookie.setMaxAge(0);
            cookie.setPath("/");
            response.addCookie(cookie);
        }
    }
}
