package com.fangcloud.thirdpartplatform.utils;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.UnhandledException;
import org.apache.commons.lang3.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.io.StringWriter;
import java.io.Writer;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
public class CcworkUtils {

    /**
     * 对称解密
     * @param secretKey
     * @param base64Text
     * @return
     */
    public static String CBCdecrypt(String secretKey, String base64Text)
    {
        try {
            String iv = getMd5(secretKey).substring(0, 16);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes("UTF-8"));
            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes("UTF-8"), "AES");
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);
            byte[] original = cipher.doFinal(new BASE64Decoder().decodeBuffer(base64Text));
            return new String(original);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 对称解密
     * @param secretKey
     * @param text
     * @return
     */
    public static String CBCencrypt(String secretKey, String text)
    {
        try {
            String iv = getMd5(secretKey).substring(0, 16);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(iv.getBytes("UTF-8"));
            byte[] secretKeys = secretKey.getBytes();
            final SecretKey secret = new SecretKeySpec(secretKeys, "AES");
            final Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, secret, ivParameterSpec);
            final byte[] original = cipher.doFinal(text.getBytes("UTF-8"));
            return new BASE64Encoder().encodeBuffer(original).replace("\n", "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 签名
     * @param nonce
     * @param timestamp
     * @param urlParams
     * @param body
     * @param secretKey
     * @return
     */
    public static String encrypt(String nonce, String timestamp, Map<String, Object> urlParams, JSONObject body, String secretKey){

        try{
            Map<String, Object> map = new TreeMap<>();
            map.put("nonce", nonce);
            map.put("timestamp", timestamp);
            //使用Tree做参数字典排序
            Map<String, Object> paramTree = new TreeMap<>();
            if (body != null) {
                paramTree.putAll(body);
            }
            // 将url的一些参数合并进请求体中
            if(urlParams != null){
                paramTree.putAll(urlParams);
            }
            // 对需要加密的body字符串做中文unicode
            map.put("body", jsonEncode(paramTree));
            // pathInfo 参数
            map.put("urlParams", getUrlEncode(urlParams));
            // 密钥
            map.put("secretKey", secretKey);
            log.info("签名数据 {}", map);

            return DigestUtils.md5Hex(JSONObject.toJSONString(map).replace("/", "\\/"));
        }
        catch (Exception e){
            log.error("IM 获取加密签名失败：{}", e.getMessage());
        }
        return null;
    }


    /**
     *
     * @param body
     * @param urlParams 需要使用TreeMap 自带排序
     * @param secretKey
     * @return
     */
    public static Map<String, String> getSignatureHeader(JSONObject body, Map<String, Object> urlParams, String secretKey){
        try{
            String nonce = String.valueOf(System.currentTimeMillis() - 1000);
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
            String encryptStr = encrypt(nonce, timestamp, urlParams, body, secretKey);
            Map<String, String> headers = new HashMap<>();
            headers.put("nonce", nonce);
            headers.put("timestamp", timestamp);
            headers.put("signature", encryptStr);
            return headers;
        }catch (Exception e){
            log.error("IM 获取加密签名工具失败：{}", e.getMessage());
        }
        return null;
    }


    private static String getUrlEncode(Map<String, Object> map){
        if (map == null) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String, Object> entry : map.entrySet()) {

            try {
                sb.append(entry.getKey() + "=" + URLEncoder.encode(entry.getValue().toString(), "UTF-8"));
            } catch (Exception e) {
                sb.append(entry.getKey() + "=" + entry.getValue());
            }
            sb.append("&");
        }
        String str = sb.toString();
        if(StringUtils.isNotBlank(str)){
            return str.substring(0, str.length() - 1);
        }
        return "";

    }

    private static String jsonEncode(Object map) throws JsonProcessingException {
        String s = new ObjectMapper().writeValueAsString(map);
        s = escapeJava(s).replace("\\\"", "\"").replace("/", "\\/");
        return s;
    }

    private static String escapeJava(String str) {
        return escapeJavaStyleString(str, false, false);
    }

    private static String escapeJavaStyleString(String str, boolean escapeSingleQuotes, boolean escapeForwardSlash) {
        if (str == null) {
            return null;
        }
        try {
            StringWriter writer = new StringWriter(str.length() * 2);
            escapeJavaStyleString(writer, str, escapeSingleQuotes, escapeForwardSlash);
            return writer.toString();
        } catch (IOException ioe) {
            // this should never ever happen while writing to a StringWriter
            throw new UnhandledException(ioe);
        }
    }

    private static void escapeJavaStyleString(Writer out, String str, boolean escapeSingleQuote,
                                             boolean escapeForwardSlash) throws IOException {
        if (out == null) {
            throw new IllegalArgumentException("The Writer must not be null");
        }
        if (str == null) {
            return;
        }
        int sz;
        sz = str.length();
        for (int i = 0; i < sz; i++) {
            char ch = str.charAt(i);

            // handle unicode
            if (ch > 0xfff) {
                out.write("\\u" + hex(ch));
            } else if (ch > 0xff) {
                out.write("\\u0" + hex(ch));
            } else if (ch > 0x7f) {
                out.write("\\u00" + hex(ch));
            } else if (ch < 32) {
                switch (ch) {
                    case '\b' :
                        out.write('\\');
                        out.write('b');
                        break;
                    case '\n' :
                        out.write('\\');
                        out.write('n');
                        break;
                    case '\t' :
                        out.write('\\');
                        out.write('t');
                        break;
                    case '\f' :
                        out.write('\\');
                        out.write('f');
                        break;
                    case '\r' :
                        out.write('\\');
                        out.write('r');
                        break;
                    default :
                        if (ch > 0xf) {
                            out.write("\\u00" + hex(ch));
                        } else {
                            out.write("\\u000" + hex(ch));
                        }
                        break;
                }
            } else {
                switch (ch) {
                    case '\'' :
                        if (escapeSingleQuote) {
                            out.write('\\');
                        }
                        out.write('\'');
                        break;
                    case '"' :
                        out.write('\\');
                        out.write('"');
                        break;
                    case '\\' :
                        out.write('\\');
                        out.write('\\');
                        break;
                    case '/' :
                        if (escapeForwardSlash) {
                            out.write('\\');
                        }
                        out.write('/');
                        break;
                    default :
                        out.write(ch);
                        break;
                }
            }
        }
    }

    private static String hex(char ch) {
        return Integer.toHexString(ch);
    }

    private static String getMd5(String text) {
        try {
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            byte[] md5bytes = md5.digest(text.getBytes());
            return bytesToHex(md5bytes);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }

        return "";
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuffer md5str = new StringBuffer();
        // 把数组每一字节换成16进制连成md5字符串
        int digital;
        for (int i = 0; i < bytes.length; i++) {
            digital = bytes[i];

            if (digital < 0) {
                digital += 256;
            }
            if (digital < 16) {
                md5str.append("0");
            }
            md5str.append(Integer.toHexString(digital));
        }
        return md5str.toString();
    }

}
