package com.fangcloud.thirdpartplatform.utils;

import java.util.Base64;

public class Base64Utils {

    public static String decode(String str) {
        byte[] ok = Base64.getDecoder().decode(str);
        try {
            return new String(ok, "UTF-8");
        } catch (Exception e) {
            return null;
        }
    }
    public static String decodeGbk(String str) {
        byte[] ok = Base64.getDecoder().decode(str);
        try {
            return new String(ok, "GBK");
        } catch (Exception e) {
            return null;
        }
    }

    public static String encode(String str) {

        byte[] ok = Base64.getEncoder().encode(str.getBytes());

        try {
            return new String(ok, "UTF-8");
        } catch (Exception e) {
            return null;
        }
    }
}
