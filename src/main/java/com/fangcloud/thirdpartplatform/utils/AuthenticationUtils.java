package com.fangcloud.thirdpartplatform.utils;

import com.alibaba.fastjson.JSON;

import java.util.HashMap;
import java.util.Map;

public class AuthenticationUtils {

    public static Map<String, String> getHeaders(int serviceId, String version, String secret) {
        Map<String, String> headerParams = new HashMap<>();

        String time = String.valueOf(System.currentTimeMillis() / 1000);
        String token = Md5Utils.getMD5OfStr(String.format("%s%s%s%s", serviceId, secret, time, version));

        headerParams.put("Internal-Serviceid", String.valueOf(serviceId));
        headerParams.put("Internal-Timestamp", time);
        headerParams.put("Internal-Token", token);
        headerParams.put("Internal-Version", version);

        return headerParams;
    }

    public static void main(String[] args) {
        Map<String, String> headers = getHeaders(2,"1.0", "855bc28260168be0b7e9892398437b45ee2118eb");
        System.out.println(JSON.toJSONString(headers));
    }


}
