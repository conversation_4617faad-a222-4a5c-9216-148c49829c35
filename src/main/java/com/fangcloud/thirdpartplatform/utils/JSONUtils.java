package com.fangcloud.thirdpartplatform.utils;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;

public abstract class JSONUtils {
    public static boolean isJSONStr(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        if (str.startsWith("{") && str.endsWith("}")) return true;

        if (str.startsWith("[") && str.endsWith("}]")) return true;
        return false;
    }

    public static JSONObject stringToJson(String str) {
        JSONObject jsonObject = new JSONObject();
        String[] urlStr = str.split("\\(");
        String[] urlStr1 = urlStr[1].split("\\)");
        String[] urlStr2 = urlStr1[0].split("\\,");
        for (int i = 0; i < urlStr2.length; i++) {
            String[] urlStr3 = urlStr2[i].split("\\=");
            jsonObject.put(urlStr3[0], urlStr3[1]);
        }
        return jsonObject;
    }
}
