package com.fangcloud.thirdpartplatform.utils;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class TimeUtils {

    public static Date getNowTime()
    {
        return new Date();
    }

    public static String getFormatTime(Date time, String p) {
        DateFormat format = new SimpleDateFormat(p);
        return format.format(time);
    }

    public static Date getBeforeDate(Date time, Integer limit) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(time);
        calendar.add(Calendar.DATE, -limit);

        return calendar.getTime();
    }

    public static String getBeforeDateTime(Date time, Integer limit, String p) {

        Date timeBefore = getBeforeDate(time, limit);

        return getFormatTime(timeBefore, p);
    }
}
