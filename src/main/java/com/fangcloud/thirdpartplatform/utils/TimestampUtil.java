package com.fangcloud.thirdpartplatform.utils;

import com.fangcloud.thirdpartplatform.constant.CommonConstants;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2021-09-16
 **/
public class TimestampUtil {

    /**
     *  秒 时间戳转换日期string 类型
     * */
    public static String convertTimestampToDate(String time){
        Long longTime = Long.valueOf(time);
        Long timestamp = longTime * 1000L;
        return convertTimeToDate(timestamp);
    }

    /**
     *  毫 秒时间戳转换日期string 类型
     * */
    public static String convertTimestampMsToDate(String time){
        Long longTime = Long.valueOf(time);
        return convertTimeToDate(longTime);
    }

    /**
     * 获取便宜时间
     * @param type 偏移类型
     * @param offset 偏移量
     * @return
     */
    public static Date getOffsetDate(String type, int offset) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        switch (type){
            case CommonConstants.YEAR:
                calendar.add(Calendar.YEAR, offset);
                break;
            case CommonConstants.MONTH:
                calendar.add(Calendar.MONTH, offset);
                break;
            case CommonConstants.DAY:
                calendar.add(Calendar.DAY_OF_YEAR, offset);
                break;
            case CommonConstants.HOUR:
                calendar.add(Calendar.HOUR, offset);
                break;
            case CommonConstants.MINUTE:
                calendar.add(Calendar.MINUTE, offset);
                break;
            case CommonConstants.SECOND:
                calendar.add(Calendar.SECOND, offset);
                break;
        }
        return calendar.getTime();
    }

    /**
     * 格式化时间
     * @param date
     * @param format
     * @return
     */
    public static String formatDate(Date date, String format) {
        switch (format){
            case CommonConstants.DYNAMIC_DATE_FORMAT_SECOND:
                return  String.valueOf(date.getTime()/1000);
            case CommonConstants.DYNAMIC_DATE_FORMAT_MILLISECOND:
                return String.valueOf(date.getTime());
            default:
                DateFormat dateFormat = new SimpleDateFormat(format);
                return dateFormat.format(date);
        }
    }

    public static String convertTimeToDate(Long longTime) {
        Date date = new Date(longTime);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dates = sdf.format(date);
        return dates;
    }
    public static void main(String[] args) {
        Date date = new Date();
        String s1 = formatDate(date,"second");
        String s2 = formatDate(date,"millisecond");
        String s3 = formatDate(date,"yyyy-MM-dd hh:mm:ss");
        System.out.println();
    }
}
