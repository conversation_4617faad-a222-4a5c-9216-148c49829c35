package com.fangcloud.thirdpartplatform.utils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.entity.input.ConnectionCheckParam;
import com.fangcloud.thirdpartplatform.entity.sync.DataBaseConfig;
import com.fangcloud.thirdpartplatform.repository.database.mapper.BaseMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.datasource.pooled.PooledDataSource;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 通用数据库工具类
 */
@Slf4j
public class DatabaseUtils {

    /**
     * 查询通用sql
     * @param baseMapper
     * @param sql
     * @return
     */
    public static List<Map<String, Object>> getBaseSqlData(BaseMapper baseMapper, String sql){
        try {

            if(Objects.isNull(baseMapper) || StringUtils.isEmpty(sql)){
                log.info("baseMapper or sql is null !");
                return null;
            }
            List<Map<String, Object>> maps = baseMapper.selectData(sql);
            log.info("sql info {}, result:{}",sql, JSON.toJSONString(maps));
            return maps;
        }catch (Exception e){
            log.error("init DB error ! msg {}", e.getMessage());
            throw new ParamException(e.getMessage());
        }
    }


    /**
     * 获取通用mapper
     * @param dataBaseConfig
     * @return
     */
    public static BaseMapper getBaseMapper(DataBaseConfig dataBaseConfig){
        try {
            log.info("dataBaseConfig info {}", JSON.toJSONString(dataBaseConfig));

            if(SourceTypeEnum.MYSQL.getDesc().equals(dataBaseConfig.getType())){
                dataBaseConfig.setDriver(SyncTaskConstants.DATABASE_MYSQL_DRIVER);
            }else if(SourceTypeEnum.ORACLE.getDesc().equalsIgnoreCase(dataBaseConfig.getType())){
                dataBaseConfig.setDriver(SyncTaskConstants.DATABASE_ORACLE_DRIVER);
            }else if(SourceTypeEnum.SQLServer.getDesc().equalsIgnoreCase(dataBaseConfig.getType())){
                dataBaseConfig.setDriver(SyncTaskConstants.DATABASE_SQL_SERVER_DRIVER);
            }else if(SourceTypeEnum.DM.getDesc().equalsIgnoreCase(dataBaseConfig.getType())) {
                dataBaseConfig.setDriver(SyncTaskConstants.DATABASE_DM_DRIVER);
            } else {
                log.info("database type is not support!");
                throw new ParamException("database type is not support!");
            }

            PooledDataSource dataSource = new PooledDataSource();
            dataSource.setDriver(dataBaseConfig.getDriver());
            dataSource.setUrl(dataBaseConfig.getUrl());
            dataSource.setUsername(dataBaseConfig.getUserName());
            dataSource.setPassword(dataBaseConfig.getPassword());
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            //配置mapper路径
            org.springframework.core.io.Resource[] resources = resolver.getResources("classpath:mybatis/*.xml");
            MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
            sqlSessionFactoryBean.setMapperLocations(resources);
            sqlSessionFactoryBean.setDataSource(dataSource);
            sqlSessionFactoryBean.setConfiguration(new MybatisConfiguration());
            SqlSessionFactory sqlSessionFactory = sqlSessionFactoryBean.getObject();

            SqlSession sqlSession = sqlSessionFactory.openSession();
            BaseMapper mapper = sqlSession.getMapper(BaseMapper.class);
            log.info("mapper:{}", mapper == null);
            return mapper;
        }catch (Exception e){
            log.error("get baseMapper error, msg {}", e.getMessage());
            throw new ParamException(e.getMessage());
        }
    }


    /**
     * 测试数据库连接
     *  1.通过查询数据库的表进行验证
     * @param param
     * @return
     */
    public static boolean checkConnection(ConnectionCheckParam param ){
        DataBaseConfig dataBaseConfig = DataBaseConfig.builder()
                .userName(param.getUserName())
                .url(param.getUrl())
                .password(param.getPassword())
                .type(param.getSourceType())
                .build();
        log.info("dataBaseConfig info {}", JSON.toJSONString(dataBaseConfig));

        if(SourceTypeEnum.MYSQL.getDesc().equals(dataBaseConfig.getType())){
            dataBaseConfig.setDriver(SyncTaskConstants.DATABASE_MYSQL_DRIVER);
        }else if(SourceTypeEnum.ORACLE.getDesc().equalsIgnoreCase(dataBaseConfig.getType())){
            dataBaseConfig.setDriver(SyncTaskConstants.DATABASE_ORACLE_DRIVER);
        }else if(SourceTypeEnum.SQLServer.getDesc().equalsIgnoreCase(dataBaseConfig.getType())){
            dataBaseConfig.setDriver(SyncTaskConstants.DATABASE_SQL_SERVER_DRIVER);
        } else if(SourceTypeEnum.DM.getDesc().equalsIgnoreCase(dataBaseConfig.getType())) {
            dataBaseConfig.setDriver(SyncTaskConstants.DATABASE_DM_DRIVER);
        }
        else {
            throw new ParamException("数据库类型不支持！");
        }
        Connection dbConn = null;
        try {
            Class.forName(dataBaseConfig.getDriver());

            DriverManager.setLoginTimeout(10);
            dbConn = DriverManager.getConnection(dataBaseConfig.getUrl(), dataBaseConfig.getUserName(), dataBaseConfig.getPassword());
            return true;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ParamException("数据源配置错误，请检查账户密码及连接地址！");
        } finally {
            if(dbConn != null){
                try {
                    dbConn.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
        }
    }


}
