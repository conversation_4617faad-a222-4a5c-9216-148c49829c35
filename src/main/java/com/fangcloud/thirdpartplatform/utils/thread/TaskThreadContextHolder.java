package com.fangcloud.thirdpartplatform.utils.thread;

import java.util.Map;

/**
 * <AUTHOR>
 */
public abstract class TaskThreadContextHolder {
    private static ThreadLocal<TaskThreadContext> threadLocal = new ThreadLocal<>();

    public static void set(TaskThreadContext context) {
        threadLocal.set(context);
    }

    public static TaskThreadContext get() {
        return threadLocal.get();
    }

    public static void remove() {
        threadLocal.remove();
    }

    public static class TaskThreadContext {

        private Map<String, String> headsMap;

        private Map<String, Object> paramsMap;

        private String body;

        public TaskThreadContext(Map<String, String> headsMap, Map<String, Object> paramsMap, String body) {
            this.headsMap = headsMap;
            this.paramsMap = paramsMap;
            this.body = body;
        }

        public Map<String, String> getHeadsMap() {
            return headsMap;
        }

        public void setHeadsMap(Map<String, String> headsMap) {
            this.headsMap = headsMap;
        }

        public Map<String, Object> getParamsMap() {
            return paramsMap;
        }

        public void setParamsMap(Map<String, Object> paramsMap) {
            this.paramsMap = paramsMap;
        }

        public String getBody() {
            return body;
        }

        public void setBody(String body) {
            this.body = body;
        }
    }
}
