package com.fangcloud.thirdpartplatform.utils;

import com.sun.org.apache.xerces.internal.impl.dv.util.Base64;
import lombok.extern.slf4j.Slf4j;
import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

@Slf4j
public class EncryptUtils {
    private static KeyGenerator keyGenerator = null;

    /**
     * AES加密
     * @param data 加密字符串
     * @param key 密钥 长度为8的倍数
     */
    public static byte[] encryptMode(String data,String key){
        try {
            keyGenerator = KeyGenerator.getInstance("AES");
            keyGenerator.init(128);
            //生成key
            SecretKey secretKey = new SecretKeySpec(key.getBytes(), "AES");
            byte[] bytes = secretKey.getEncoded();
            //获取key
            Key AESKey = new SecretKeySpec(bytes, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, AESKey);
            return cipher.doFinal(data.getBytes());
        } catch (Exception e) {
            log.error("【AESUtil】加密发生异常",e);
        }
        return null;
    }

    /**
     * byte[]字符串转base64
     * @param bytes
     */
    public static String encodeBase64(byte[] bytes){
        return Base64.encode(bytes);
    }
    /**
     * base64 转byte[]
     * @param data
     */
    public static byte[] decodeBase64(String data){
        return Base64.decode(data);
    }

    /**
     * AES解密算法
     * @param data 加密数据
     * @param key 密钥 长度为16的倍数
     */
    public static byte[] decryptMode(byte[] data,String key){
        try {
            keyGenerator = KeyGenerator.getInstance("AES");
            keyGenerator.init(128);
            //生成key
            SecretKey secretKey = new SecretKeySpec(key.getBytes(), "AES");
            byte[] bytes = secretKey.getEncoded();
            //获取key
            Key AESKey = new SecretKeySpec(bytes, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, AESKey);
            return cipher.doFinal(data);
        } catch (Exception e) {
            log.error("【AESUtil】解密发生异常",e);
        }
        return null;
    }

    /**
     * 格式化为byte[] to String
     * @param bytes
     */
    public static String formatByteToString(byte[] bytes){
        return new String(bytes);
    }


    /**
     * AES 加密
     * @param data 加密的数据
     * @param key 秘钥
     */
    public static String encrypt(String data,String key){
        byte[] bytes = encryptMode(data, key);
        return Base64.encode(bytes);
    }

    /**
     * AES 解密
     * @param data 加密的数据
     * @param key 秘钥
     */
    public static String decrypt(String data,String key){
        byte[] b = decodeBase64(data);
        byte[] bytes = decryptMode(b, key);
        return formatByteToString(bytes);
    }

    public static void main(String[] args) throws InterruptedException {
        String str4 = EncryptUtils.encrypt("18800000008", "h6lr679hdru3pt6b");
        String str5 = EncryptUtils.encrypt("17747365876", "3z76rq7umvtcy2l2");
        String str6 = EncryptUtils.decrypt(str4, "h6lr679hdru3pt6b");
        String str7 = EncryptUtils.decrypt(str5, "3z76rq7umvtcy2l2");
        System.out.println(str4);
        long timeMillis = System.currentTimeMillis();
        System.out.println(timeMillis);

        for (int i = 0; i < 5; i++) {
            String str1 = EncryptUtils.encrypt("FS17020005|"+ timeMillis +"|360cloud", "h6lr677hdru3pt3b");
            System.out.println(str1);
            String str2 = EncryptUtils.decrypt(str1, "h6lr677hdru3pt3b");
            System.out.println(str2);
        }
    }

}
