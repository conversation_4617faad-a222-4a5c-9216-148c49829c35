package com.fangcloud.thirdpartplatform.event.handler;

import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.event.CreateTaskEvent;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class TaskEventHandler {

    @Resource
    private PlatformSyncTaskExecutor platformSyncTaskExecutor;
    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @EventListener
    public void handlerCreateTaskEvent(CreateTaskEvent event) {
        Integer configId = event.getConfigId();
        log.info("publishEvent id {}, running!", configId);
        PlatformSyncConfig syncConfig = platformSyncConfigMapper.selectByPrimaryKey(configId);
        platformSyncTaskExecutor.execute(syncConfig, event.getTaskId());
    }
}
