package com.fangcloud.thirdpartplatform.event;

import java.io.Serializable;

public class CreateTaskEvent implements Serializable {
    private static final long serialVersionUID = -270078487326472971L;

    private Integer configId;

    private Integer taskId;

    public CreateTaskEvent() {
    }

    public CreateTaskEvent(Integer configId, Integer taskId) {
        this.configId = configId;
        this.taskId = taskId;
    }

    public Integer getConfigId() {
        return configId;
    }

    public void setConfigId(Integer configId) {
        this.configId = configId;
    }

    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }
}
