package com.fangcloud.thirdpartplatform.service;



import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.stereotype.Service;



public interface AliFaruiService {

    String getToken(HttpServletRequest request, HttpServletResponse response);

    String review(HttpServletRequest request, HttpServletResponse response);

    String reviewWithLocalFile(HttpServletRequest request, HttpServletResponse response, String path, String fileName);

    String homepage(HttpServletRequest request, HttpServletResponse response);
}
