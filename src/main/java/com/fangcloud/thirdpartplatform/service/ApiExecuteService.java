package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;

import java.util.Map;

public interface ApiExecuteService {

    String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult);

    String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId);

    String getApiExecuteType();
}
