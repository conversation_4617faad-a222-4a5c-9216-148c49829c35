package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;

public interface ThirdpartyExecuteService {

    Result<Object> execute(ThirdpartyParams thirdpartyParams);

    Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams);

    String getThirdpartyExecuteType();
}
