package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SyncResultStatusEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncTaskFailLogsMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncTaskRecordMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncTaskLogListParam;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncTaskRecordListParam;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncTaskLogResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncTaskRecordResponse;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskService;
import com.fangcloud.thirdpartplatform.utils.PageUtils;
import com.fangcloud.thirdpartplatform.utils.TimestampUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PlatformSyncTaskServiceImpl implements PlatformSyncTaskService {
    @Resource
    private PlatformSyncTaskRecordMapper platformSyncTaskRecordMapper;
    @Resource
    private PlatformSyncTaskFailLogsMapper platformSyncTaskFailLogsMapper;
    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @Resource
    private RedisStringManager redisStringManager;

    @Override
    public List<PlatformSyncTaskRecordResponse> list(PlatformSyncTaskRecordListParam param) {
        if (param == null) {
            throw new ParamException("参数不能为空");
        }
        Integer enterpriseId = param.getEnterpriseId();
        String syncTaskType = param.getSyncTaskType();
        SyncTypeEnum byDesc = SyncTypeEnum.getByDesc(syncTaskType);
        Integer pageNo = param.getPageNo();
        Integer pageSize = param.getPageSize();
        Integer limit = PageUtils.getLimit(pageNo, pageSize);
        List<PlatformSyncTaskRecord> platformSyncTaskRecords = platformSyncTaskRecordMapper.pageByEnterpriseId(enterpriseId, byDesc.getSyncType(), limit, pageSize);
        if (CollectionUtils.isEmpty(platformSyncTaskRecords)) {
            return Lists.newArrayList();
        }
        List<Integer> ids = platformSyncTaskRecords.stream().map(PlatformSyncTaskRecord::getSyncConfigId).collect(Collectors.toList());
        List<PlatformSyncConfig> platformSyncConfigs = platformSyncConfigMapper.queryByIds(ids);
        Map<Integer, String> syncConfigNameMap = new HashMap<>();
        if (platformSyncConfigs != null) {
            syncConfigNameMap = platformSyncConfigs.stream().collect(Collectors.toMap(PlatformSyncConfig::getId, PlatformSyncConfig::getConfigName));
        }
        List<PlatformSyncTaskRecordResponse> recordResponses = new ArrayList<>();
        Map<Integer, String> finalSyncConfigNameMap = syncConfigNameMap;
        platformSyncTaskRecords.forEach(s-> {
            PlatformSyncTaskRecordResponse response = new PlatformSyncTaskRecordResponse();
            response.setSyncResult(s.getSyncResult() == 1 ? "开始" : s.getSyncResult() == 2 ? "成功" : "失败");
            response.setSyncEndTime(s.getSyncEndat() == null ? "" : TimestampUtil.convertTimeToDate(s.getSyncEndat()));
            response.setSyncDataCount(s.getValueBox());
            response.setSyncStartTime(TimestampUtil.convertTimeToDate(s.getSyncStartat()));
            SyncTypeEnum bySyncType = SyncTypeEnum.getBySyncType(s.getSyncTaskType());
            response.setSyncTypeStr(bySyncType.getDesc());
            response.setSyncStatus(s.getSyncTaskStatus());
            SyncResultStatusEnum syncResultStatusEnum = SyncResultStatusEnum.get(s.getSyncTaskStatus());
            response.setSyncStatusStr(syncResultStatusEnum.getDesc());
            response.setId(s.getId());
            String name = finalSyncConfigNameMap.get(s.getSyncConfigId());
            response.setConfigName(name);
            response.setEnterpriseId(s.getEnterpriseId());
            recordResponses.add(response);
        });
        return recordResponses;
    }

    @Override
    public Integer countPlatformSyncTaskRecord(PlatformSyncTaskRecordListParam param) {
        if (param == null) {
            throw new ParamException("参数不能为空");
        }
        Integer enterpriseId = param.getEnterpriseId();
        String syncTaskType = param.getSyncTaskType();
        SyncTypeEnum byDesc = SyncTypeEnum.getByDesc(syncTaskType);
        return platformSyncTaskRecordMapper.countByEnterpriseId(enterpriseId, byDesc.getSyncType());
    }

    @Override
    public List<PlatformSyncTaskLogResponse> listLogs(PlatformSyncTaskLogListParam param) {
        if (param == null) {
            throw new ParamException("参数不能为空");
        }
        Integer taskId = param.getTaskId();
        int pageNo = param.getPageNo();
        int pageSize = param.getPageSize();
        String customId = param.getCustomId();
        Integer limit = PageUtils.getLimit(pageNo, pageSize);
        List<PlatformSyncTaskFailLogs> logs = platformSyncTaskFailLogsMapper.queryByTaskId(taskId, customId, limit, pageSize);
        if (CollectionUtils.isEmpty(logs)) {
            return Lists.newArrayList();
        }
        List<PlatformSyncTaskLogResponse> responses = new ArrayList<>();
        logs.forEach( s -> {
            PlatformSyncTaskLogResponse logResponse = new PlatformSyncTaskLogResponse();
            logResponse.setCreated(s.getCreated() == null ? "" : TimestampUtil.convertTimeToDate(s.getCreated()));
            logResponse.setSyncTaskId(s.getSyncTaskId());
            logResponse.setCustomerId(s.getCustomeId());
            logResponse.setReason(s.getReason());
            logResponse.setEnterpriseId(s.getEnterpriseId());
            logResponse.setId(s.getId());
            logResponse.setValueBox(s.getValueBox());
            responses.add(logResponse);
        });
        return responses;
    }

    @Override
    public Integer countPlatformSyncTaskLog(PlatformSyncTaskLogListParam param) {
        if (param == null) {
            throw new ParamException("参数不能为空");
        }
        Integer taskId = param.getTaskId();
        String customId = param.getCustomId();
        return platformSyncTaskFailLogsMapper.countByTaskId(taskId, customId);
    }

    @Override
    public Integer batchInsertLogs(List<PlatformSyncTaskFailLogs> records) {
        return platformSyncTaskFailLogsMapper.batchInsert(records);
    }

    @Override
    public List<PlatformSyncTaskFailLogs> selectByTaskId(Integer syncTaskId, String customeId) {
        return platformSyncTaskFailLogsMapper.selectByTaskId(syncTaskId, customeId);
    }

    @Override
    public Integer updateByPrimaryKeySelective(PlatformSyncTaskFailLogs record) {
        return platformSyncTaskFailLogsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    @Async
    public void scheduledDeleteSyncTaskFailLogs(Integer enterpriseId) {
        String key = redisStringManager.get(SyncTaskConstants.SCHEDULED_DELETE_SYNC_ERROR_LOGS);
        if (key == null) {
            redisStringManager.set(SyncTaskConstants.SCHEDULED_DELETE_SYNC_ERROR_LOGS,"done",24 * 60 * 60L);
            log.info("delete sync_task_fail_logs  running....");
            int batchSize = 1000;
            long fifteenDaysAgoTimestamp = System.currentTimeMillis() - (15L * 24 * 60 * 60 * 1000);
            int totalRecords = platformSyncTaskFailLogsMapper.countDataBeforeFifteenDays(fifteenDaysAgoTimestamp, enterpriseId);
            if (totalRecords == 0) {
                return;
            }
            long totalBatches = (totalRecords + batchSize - 1) / batchSize;
            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                platformSyncTaskFailLogsMapper.deleteByTimestamp(fifteenDaysAgoTimestamp, enterpriseId, batchSize);
            }
            log.info("delete sync_task_fail_logs  stop....");
        }
    }


}
