package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import org.springframework.stereotype.Component;

@Component
public class SqlServerSyncHand<PERSON> extends MysqlSyncHandler{

    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return super.syncUser(platformSyncConfig, taskId);
    }
    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return super.syncDepartment(platformSyncConfig, taskId);
    }
    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.SQLServer.getDesc();
    }
}
