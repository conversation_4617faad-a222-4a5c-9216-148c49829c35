package com.fangcloud.thirdpartplatform.service.impl.thirdPart;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.input.AutoLoginParams;
import com.fangcloud.thirdpartplatform.entity.input.DingTalkInitParams;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.helper.ChameleonClientHelper;
import com.fangcloud.thirdpartplatform.helper.OauthClientHelper;
import com.fangcloud.thirdpartplatform.helper.ThirdClientHelper;
import com.fangcloud.thirdpartplatform.helper.ZWDClientHelper;
import com.fangcloud.thirdpartplatform.service.PlatformLoginConfigService;
import com.fangcloud.thirdpartplatform.service.ZWDService;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ZWDSyncHandler;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.sync.common.entity.dto.YfyMessage;
import com.sync.common.enums.YfyMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.URL;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;
import java.util.Objects;


@Service
@Slf4j
public class ZWDServiceImpl extends  BaseServiceImpl implements ZWDService {
    private ZWDClientHelper zWDClientHelper;

    @Resource
    private OauthClientHelper oauthClientHelper;

    @Resource
    private ThirdClientHelper thirdClientHelper;

    @Resource
    private ZWDSyncHandler zwdSyncHandler;

    @Resource
    private EnterpriseServiceImpl enterpriseService;

    @Resource
    private ChameleonClientHelper chameleonClientHelper;

    @Resource
    private PlatformLoginConfigService platformLoginConfigService;


    @Override
    public JSONObject getAutoLoginUrl(AutoLoginParams params) {

        // 1、获取企业信息
        Enterprise enterprise = enterpriseService.getEnterpriseById(params.getEnterpriseId());

        // 2、获取钉钉信息
        DingTalkInitParams dingTalkInitParams = buildDingTalkInitParams(enterprise, params);
        ZWDClientHelper zWDClientHelper = getZWDClient(dingTalkInitParams);
        // 3、构建亿方云对象
        JSONObject zwdUserInfoObj = zWDClientHelper.getUserInfoByCode(params.getCode());

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("accountId", zwdUserInfoObj.get("accountId"));
        jsonObject.put("nickNameCn", zwdUserInfoObj.get("nickNameCn"));
        if (Objects.isNull(zwdUserInfoObj)) {
            log.info("get zwd user info is null!");
        }else {
            EnterpriseParams enterpriseParams = buildEnterpriseParams(zwdUserInfoObj.getString("employeeCode"), enterprise);
            // 4、生成免密登录链接
            String loginUrl = getLoginUrl(false, enterpriseParams, null);
            if (loginUrl != null) {
                jsonObject.put("url", loginUrl);
                return jsonObject;
            }
        }

        dropZWDClient();
        jsonObject.put("url", CommonConstants.URL_ACCOUNT_NOT_OPEN);
        return jsonObject;
    }

    @Override
    public boolean sendMessage(MessageParams params) {

        ZWDClientHelper zWDClientHelper = getZWDClient(params.getDingTalkInitParams());

        // 设置接受者id
        setRealReceivers(params, zWDClientHelper.getZwdAccountIdList(params.getReceivers()));

        // 发送消息
        zWDClientHelper.sendOAMessage(getZWDYfyMessage(params), params);

        dropZWDClient();
        return true;
    }

    @Override
    public void getAutoLoginUrlByEmployeeCode(String employeeCode, String enterpriseId, HttpServletResponse response) throws IOException {

        String enterpriseIdDecode = Base64Utils.decode(enterpriseId);
        String employeeCodeDecode = new StringBuilder(Base64Utils.decode(employeeCode)).reverse().toString();
        // 1、获取企业信息
        Enterprise enterprise = enterpriseService.getEnterpriseById(Integer.valueOf(enterpriseIdDecode));
        // 2、构建获取免密链接参数
        EnterpriseParams enterpriseParams = buildEnterpriseParams(employeeCodeDecode, enterprise);
        // 3、生成免密登录链接
        String loginUrl = getLoginUrl(false, enterpriseParams, null);
        if(StringUtils.isEmpty(loginUrl)){
            loginUrl = CommonConstants.URL_ACCOUNT_NOT_OPEN;
        }
        response.sendRedirect(loginUrl);
    }

    @Override
    public boolean sendTextMessage(MessageParams params) {
        thirdClientHelper.sendTextMessage(getZWDYfyMessage(params), params.getEnterpriseId());
        return true;
    }

    private EnterpriseParams buildEnterpriseParams(String employeeCode, Enterprise enterprise) {
        EnterpriseParams enterpriseParams = new EnterpriseParams();
        enterpriseParams.setIdentifier(employeeCode);
        enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
        enterpriseParams.setPlatformId(String.valueOf(enterprise.getPlatformId()));
        enterpriseParams.setEnterpriseId(String.valueOf(enterprise.getId()));
        // 获取clientId
        JSONArray jSONArray = oauthClientHelper.getClientList(enterprise.getId());
        if(!CollectionUtils.isEmpty(jSONArray)){
            for (Object o : jSONArray) {
                JSONObject clientObj = (JSONObject) o;
                if(clientObj.getBoolean("enabled")){
                    enterpriseParams.setClientId(clientObj.getString("client_id"));
                    break;
                }
            }
        }
        return enterpriseParams;
    }

    private DingTalkInitParams buildDingTalkInitParams(Enterprise enterprise, AutoLoginParams params) {
        PlatformSyncConfig platformSyncConfig = new PlatformSyncConfig();
        platformSyncConfig.setEnterpriseId((int) enterprise.getId());
        DingTalkInitParams dingdingInfo = zwdSyncHandler.getDingdingInfo(platformSyncConfig);
        dingdingInfo.setCode(params.getCode());
        return dingdingInfo;
    }



    /**
     * 设置真正的接受者id
     * @param params
     * @param zwdAccountId
     */
    private void setRealReceivers(MessageParams params, String zwdAccountId) {
        if(StringUtils.isEmpty(zwdAccountId)){
            throw new ParamException("zwdAccountId is null!");
        }else {
            params.setReceivers(zwdAccountId);
        }
    }

    private ZWDClientHelper getZWDClient(DingTalkInitParams params) {
        if (zWDClientHelper == null) {
            zWDClientHelper = new ZWDClientHelper(params);
        }

        return zWDClientHelper;
    }

    private void dropZWDClient() {
        zWDClientHelper = null;
    }

    /**
     * 政务钉钉消息体构造
     * @param params
     * @return
     */
    protected YfyMessage getZWDYfyMessage(MessageParams params)
    {

        String redirect = getRedirect(params);
        YfyMessage yfyMessage = YfyMessage.builder()
                .content(params.getContent())
                .customerId(params.getCustomerId())
                .enterpriseId(params.getEnterpriseId())
                .h5Url(redirect)
                .receivers(params.getReceivers())
                .time(params.getTime())
                .title(params.getTitle())
                .type(params.getType())
                .webUrl(redirect)
                .picUrl(params.getPicUrl())
                .build();

        String title = YfyMessageTypeEnum.getEnum(params.getType()).getDesc();
        if (StringUtils.isNotBlank(params.getMessagePushTitle())) {
            title  = title.replace("云盘", params.getMessagePushTitle());
        }
        if(!YfyMessageTypeEnum.FOLLOW.getType().equals(params.getType())){
            title = title + "：" + params.getTitle();
        }

        String content = yfyMessage.getContent().replaceAll("\\[\\d+:", "[").replaceAll("\\[g-\\d+:","[");
        yfyMessage.setTitle(title);
        yfyMessage.setContent(content);

        return yfyMessage;
    }

    /**
     * 获取消息重定向地址
     * @param params
     * @return
     */
    private String getRedirect(MessageParams params)  {


        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(params.getEnterpriseId()+ "");
        String autoLoginUrl = configMap.get(PlatformGlobalConfigKeyEnum.ZWD_AUTO_LOGIN_URL.getKey());
        String autoLoginPageUrl = configMap.get(PlatformGlobalConfigKeyEnum.ZWD_AUTO_LOGIN_PAGE_URL.getKey());

        String redirectUrl = autoLoginPageUrl
                + "&platformUrl=" + URL.encode(autoLoginUrl)
                + "&redirect=" + URL.encode(params.getWebUrl())
                + "&h5Redirect=" + URL.encode(params.getRealH5Url())
                + "&baseUrl=" + URL.encode(params.getDingTalkInitParams().getBaseUrl());

        log.info("zwd redirectUrl is :{}", redirectUrl);

        return redirectUrl;
    }

}
