package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.service.AutoLoginService;
import com.fangcloud.thirdpartplatform.service.impl.custom.*;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@Component
@Slf4j
public class AutoLoginServiceImpl implements AutoLoginService {

    @Resource
    HLDServiceImpl hldService;

    @Resource
    ZjszzsServiceImpl zjszzsService;

    @Resource
    CcworkServiceImpl ccworkService;

    @Resource
    LzljServiceImpl lzljService;

    @Resource
    SxtjyServiceImpl sxtjyService;

    @Resource
    GzycServiceImpl gzycService;

    @Resource
    SeeyonServiceImpl seeyonService;

    @Resource
    JnkcyServiceImpl jnkcyService;

    @Resource
    ZscServiceImpl zscService;

    @Resource
    MornSunServiceImpl mornSunService;

    @Override
    public String getAutoLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        String productId = configInfo.getProductId();
        log.info("auto login params productId {}, request {}, configInfo {}",
                productId, request.getQueryString(), configInfo);
        if (productId.equals("HLD")) {
            return hldService.getLoginUrl(request, configInfo);
        }else if(productId.equals("zjszzs")){
            return zjszzsService.getLoginUrl(request, configInfo);
        }else if(productId.equals("ccwork")){
            return ccworkService.getLoginUrl(request, configInfo);
        } else if(productId.equals("lzlj")) {
            return lzljService.getLoginUrl(request, configInfo);
        }else if(productId.equals("sxjcy")){
            return sxtjyService.getLoginUrl(request, configInfo);
        }else if(productId.equals("gzyc")){
            return gzycService.getLoginUrl(request, configInfo);
        }else if(productId.equals("seeyon")){
            return seeyonService.getLoginUrl(request, configInfo);
        }else if(productId.equals("jnkcy")){
            return jnkcyService.getLoginUrl(request, configInfo);
        }else if(productId.equals("zsc")){
            return zscService.getLoginUrl(request, configInfo);
        }else if(productId.equals("mornsun")){
            return mornSunService.getLoginUrl(request,configInfo);
        }
        return null;
    }

    @Override
    public Model getParams(Model model, String config) {
        ConfigInfo configInfo = JSONObject.parseObject(Base64Utils.decode(config), ConfigInfo.class);
        String productId = configInfo.getProductId();
        model.addAttribute("product_id", productId);
        if (productId.equals("sxjcy")) {
            return sxtjyService.getParams(model, config);
        }
        return null;
    }
}
