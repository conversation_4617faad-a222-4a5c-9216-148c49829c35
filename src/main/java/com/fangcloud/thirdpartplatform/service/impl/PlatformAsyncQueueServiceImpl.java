package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.db.dao.PlatformAsynQueueMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue;
import com.fangcloud.thirdpartplatform.service.PlatformAsyncQueueService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-06-01
 **/
@Service
public class PlatformAsyncQueueServiceImpl implements PlatformAsyncQueueService {
    @Resource
    private PlatformAsynQueueMapper platformAsynQueueMapper;

    @Override
    public Integer batchInsertQueue(List<PlatformAsyncQueue> platformAsynQueues) {
        return platformAsynQueueMapper.batchInsertQueue(platformAsynQueues);
    }

    @Override
    public List<PlatformAsyncQueue> selectByTaskId(Integer taskId) {
        return platformAsynQueueMapper.selectByTaskId(taskId);
    }

    @Override
    public List<PlatformAsyncQueue> selectByEnterpriseIdAndStatus(Integer enterpriseId, Integer status) {
        return platformAsynQueueMapper.selectByEnterpriseIdAndStatus(enterpriseId, status);
    }

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return platformAsynQueueMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PlatformAsyncQueue platformAsynQueue) {
        return platformAsynQueueMapper.insert(platformAsynQueue);
    }

    @Override
    public int updateByPrimaryKeySelective(PlatformAsyncQueue platformAsynQueue) {
        return platformAsynQueueMapper.updateByPrimaryKeySelective(platformAsynQueue);
    }
}
