package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.User;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.utils.EncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.util.Objects;

/**
 * 知识城免密登录
 */
@Component
@Slf4j
public class ZscServiceImpl {

    @Resource
    private OpenClientHelper openClientHelper;

    @Resource
    private UserMapper userMapper;

    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        try {
            String code = request.getParameter("code");
            String type = request.getParameter("type");
            String phone = EncryptUtils.decrypt(code, "3z76rq7umvtcy2l2");
            String name = URLDecoder.decode(EncryptUtils.decrypt(request.getParameter("name"), "3z76rq7umvtcy2l2"), "UTF-8");
            log.info("zscAutologin code: {} ,encrypt name: {}, phone: {}, name: {}", code, request.getParameter("name"), phone, name);
            User user = userMapper.queryByPhone(phone, 115L);
            if (Objects.isNull(user)) {
                log.info("user not exist, phone:{}", phone);
                return null;
            }
            if (phone != null && name != null && name.equals(user.getFullName())) {
                EnterpriseParams enterpriseParams = new EnterpriseParams();
                enterpriseParams.setEnterpriseId(configInfo.getEnterpriseId());
                enterpriseParams.setIdentifier(phone);
                enterpriseParams.setPlatformId(configInfo.getPlatformId());
                enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
                enterpriseParams.setClientId(configInfo.getClientId());
                String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
                if (type != null && type.equals("mobile")) {
                    loginUrl += "&redirect=https%3A%2F%2Fpan.kci-gz.com%2Fh5%2F";
                }
                log.info("zsc getLoginUrl {}", loginUrl);
                return loginUrl;
            }
        } catch (Exception e) {
            log.error("zscAutologin 异常", e);
        }
        return null;
    }
}
