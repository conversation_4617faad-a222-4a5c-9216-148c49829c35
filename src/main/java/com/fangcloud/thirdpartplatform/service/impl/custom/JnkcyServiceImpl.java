package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.APIParamConfigDto;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.utils.Md5Utils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Component
@Slf4j
public class JnkcyServiceImpl {

    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private OpenClientHelper openClientHelper;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMap;

    private static final String SSO_URI = "%s/api/hrm/resful/getHrmUserInfoWithPage";


    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        String userName = request.getParameter("userName");
        String password = request.getParameter("password");
        String loginName = getUserLoginName(userName, password, configInfo);
        if (StringUtils.isBlank(loginName)) {
            return null;
        }
        try {
            EnterpriseParams enterpriseParams = new EnterpriseParams();
            enterpriseParams.setEnterpriseId(configInfo.getEnterpriseId());
            enterpriseParams.setIdentifier(loginName);
            enterpriseParams.setPlatformId(configInfo.getPlatformId());
            enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
            enterpriseParams.setClientId(configInfo.getClientId());
            String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
            log.info("jnkcy getLoginUrl {}", loginUrl);
            return loginUrl;
        } catch (Exception e) {
            log.error("jnkcy getLoginUrl error configInfo:{}", JSON.toJSONString(configInfo), e);
        }
        return null;
    }

    private String getUserLoginName(String userName, String password, ConfigInfo configInfo) {
        String userConfigId = configInfo.getUserConfigId();
        PlatformSyncConfig platformSyncConfig = platformSyncConfigMap.selectByPrimaryKey(Integer.valueOf(userConfigId));
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        List<APIParamConfigDto> paramConfig = apiConfigValueBox.getApiConfig().getParamConfig();
        String uri = paramConfig.stream().filter(apiParamConfigDto -> apiParamConfigDto.getName().equals("host")).findFirst().map(APIParamConfigDto::getValue).orElse(null);
        String idPath = paramConfig.stream().filter(apiParamConfigDto -> apiParamConfigDto.getName().equals("id_path")).findFirst().map(APIParamConfigDto::getValue).orElse(null);


        String md5Password = Md5Utils.getMD5OfStr(password).toUpperCase();
        log.info("jnkcy md5Password is :{}", md5Password);

        // 登录接口
        String url = String.format(SSO_URI, uri);

        String postData = String.format("{\"params\":{\"pagesize\":10,\"loginid\":\"%s\"}}", userName);

        try {
            Response response = httpClientHelper.postResponse(url, postData);
            if (response == null || response.body() == null) {
                log.info("get jnkcy login info is null!");
                return null;
            }
            String result = response.body().string();
            log.info("get jnkcy login info result:{} : ", result);

            String code = (String) JSONPath.extract(result, "$.code");
            if (!"1".equals(code)) {
                return null;
            }
            JSONObject jsonObject = (JSONObject) JSONPath.extract(result, "$.data");
            JSONArray dataList = jsonObject.getJSONArray("dataList");
            if (CollectionUtils.isEmpty(dataList)) {
                return null;
            }
            JSONObject jSONObjectUser = (JSONObject) dataList.get(0);

            String passwordResult = jSONObjectUser.getString("password");
            if (!md5Password.equals(passwordResult)) {
                log.error("password is wrong!");
                return null;
            }
            String id = jSONObjectUser.getString(idPath);
            return id;
        } catch (Exception e) {
            log.error("jnkcy getUserLoginName error ", e);
        }
        return null;
    }
}
