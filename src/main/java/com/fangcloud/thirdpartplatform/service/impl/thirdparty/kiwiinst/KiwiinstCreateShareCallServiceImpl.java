package com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.*;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.v2.CreateCommentBean;
import com.fangcloud.thirdpartplatform.helper.DateHelper;
import com.fangcloud.thirdpartplatform.helper.KiwiinstClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst.KiwiinstInviteCollabCallbackServiceImpl.*;

@Service
@Slf4j
public class KiwiinstCreateShareCallServiceImpl implements ThirdpartyExecuteService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private GroupsUsersMapper groupsUsersMapper;

    @Autowired
    private KiwiinstClientHelper kiwiinstClientHelper;

    @Resource
    private V2ClientHelper v2ClientHelper;


    static final String CJFXSP_WORK_FLOW_FLAG = "CJFXSP";
    static final List<String> NEED_BUILD_DT1 = Arrays.asList("collaborators");


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        // 请求oa，创建流程单
        sendCreateWorkflow(thirdpartyParams);

        return ResultUtils.getSuccessResult(true);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {
        return null;
    }

    /**
     * 请求OA，创建审批单
     * @param thirdpartyParams
     */
    private void sendCreateWorkflow(ThirdpartyParams thirdpartyParams) {
        KiwiinstCreateSharkCallValueBox kiwiinstCreateSharkCallValueBox = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getValueBox()), KiwiinstCreateSharkCallValueBox.class);

        long createUserId = kiwiinstCreateSharkCallValueBox.getCreateUserId();
        User user = userMapper.queryById(createUserId);

        if(Objects.isNull(user)){
            throw new ParamException("createUser is null !");
        }
        long enterpriseId = user.getEnterpriseId();
        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);

        if(Objects.isNull(platformEnterprises)){
            throw new ParamException("platformEnterprises is null !");
        }

        int platformId = platformEnterprises.getPlatformId();
        PlatformUser createPlatformUser = platformUserMapper.queryByPlatformIdUserId(platformId, createUserId);
        if(Objects.isNull(createPlatformUser)){
            throw new ParamException("createPlatformUser is null!");
        }

        CreateWorkflow createWorkflow = new CreateWorkflow();
        createWorkflow.setBase(buildCreateWorkflowBase(createPlatformUser));
        createWorkflow.setMain(buildCreateWorkflowMain(kiwiinstCreateSharkCallValueBox, createPlatformUser, enterpriseId));
        if(NEED_BUILD_DT1.contains(kiwiinstCreateSharkCallValueBox.getAccess())){
            createWorkflow.setDt1(buildCreateWorkflowDt1(kiwiinstCreateSharkCallValueBox, platformId));
        }

        List<CreateWorkflow> list = new ArrayList<>();
        list.add(createWorkflow);

        // 调用创建流程接口
        String result = kiwiinstClientHelper.createWorkflow(JSON.toJSONString(list),enterpriseId);

        String status = (String) JSONPath.extract(result, "$.resultlist[0].status");
        if(!"0".equals(status)){
            throw new ParamException("createWorkflow error!");
        }
        // 新增评论记录OA流程
        createComment(result, kiwiinstCreateSharkCallValueBox);
    }


    /**
     * 构建OA流程main信息
     * @param kiwiinstCreateSharkCallValueBox
     * @param createPlatformUser
     * @param enterpriseId
     * @return
     */
    private CreateWorkflowMain buildCreateWorkflowMain(KiwiinstCreateSharkCallValueBox kiwiinstCreateSharkCallValueBox, PlatformUser createPlatformUser, long enterpriseId) {

        CreateShareWorkflowMain createShareWorkflowMain = new CreateShareWorkflowMain();
        createShareWorkflowMain.setSqr(new CreateWorkflowValue(createPlatformUser.getUserTicket()));
        createShareWorkflowMain.setStartdate(new CreateWorkflowValue(DateHelper.transformTimeStampToDate(System.currentTimeMillis()/1000)));
        createShareWorkflowMain.setFxlx(new CreateWorkflowValue(kiwiinstCreateSharkCallValueBox.getAccess()));
        String itemTypedId = kiwiinstCreateSharkCallValueBox.getItemTypedId();
        String[] split = itemTypedId.split("_");
        createShareWorkflowMain.setDataType(new CreateWorkflowValue(split[0]));
        createShareWorkflowMain.setDataId(new CreateWorkflowValue(split[1]));
        createShareWorkflowMain.setDataName(new CreateWorkflowValue(kiwiinstCreateSharkCallValueBox.getItemName()));
        PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentId(createPlatformUser.getPlatformId(), kiwiinstCreateSharkCallValueBox.getDeptId());
        if(Objects.isNull(platformDepartment)){
            throw new ParamException("folderDepartment is null!");
        }
        createShareWorkflowMain.setDataDeptId(new CreateWorkflowValue(platformDepartment.getDepartmentId()));
        createShareWorkflowMain.setDescription(new CreateWorkflowValue(kiwiinstCreateSharkCallValueBox.getDescription()));
        createShareWorkflowMain.setEnterpriseId(new CreateWorkflowValue(String.valueOf(enterpriseId)));
        createShareWorkflowMain.setCurrentVersion(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.isCurrentVersion())));
        createShareWorkflowMain.setIsShareWpsEdit(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.isEnableShareWpsEdit())));
        createShareWorkflowMain.setDueTime(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.getDueTime())));
        createShareWorkflowMain.setPasswordProtected(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.isPasswordProtected())));
        createShareWorkflowMain.setPassword(new CreateWorkflowValue(kiwiinstCreateSharkCallValueBox.getPassword()));
        createShareWorkflowMain.setDisableDownload(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.isDisableDownload())));
        if(!NEED_BUILD_DT1.contains(kiwiinstCreateSharkCallValueBox.getAccess())){
            createShareWorkflowMain.setIsDownloadLimitV2(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.isEnableDownloadLimitV2())));
            createShareWorkflowMain.setPreviewLimit(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.getPreviewLimit())));
            createShareWorkflowMain.setIsPreviewLimit(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.isEnablePreviewLimit())));
            createShareWorkflowMain.setDownloadLimitV2(new CreateWorkflowValue(String.valueOf(kiwiinstCreateSharkCallValueBox.getDownloadLimitV2())));
        }

        return createShareWorkflowMain;
    }

    /**
     * 构建base信息
     * @param createPlatformUser
     * @return
     */
    private CreateWorkflowBase buildCreateWorkflowBase(PlatformUser createPlatformUser) {
        CreateWorkflowBase createWorkflowBase = new CreateWorkflowBase();
        createWorkflowBase.setCreator(new CreateWorkflowValue(createPlatformUser.getUserTicket()));
        createWorkflowBase.setFpkid(CJFXSP_WORK_FLOW_FLAG);
        createWorkflowBase.setWorkflowflag(CJFXSP_WORK_FLOW_FLAG);
        return createWorkflowBase;
    }

    /**
     * 构建dt1信息
     * @param kiwiinstCreateSharkCallValueBox
     * @param platformId
     * @return
     */
    private List<CreateWorkflowDt1> buildCreateWorkflowDt1(KiwiinstCreateSharkCallValueBox kiwiinstCreateSharkCallValueBox, int platformId) {
        List<CreateWorkflowDt1> dt1List = new ArrayList<>();

        List<Long> invitedUserIds = kiwiinstCreateSharkCallValueBox.getInvitedUserIds();
        List<Long> groupIds = kiwiinstCreateSharkCallValueBox.getGroupIds();

        if(!CollectionUtils.isEmpty(invitedUserIds)){

            List<PlatformUser> platformUsers = platformUserMapper.queryByPlatformIdUserIds(platformId, invitedUserIds);
            for (PlatformUser platformUser : platformUsers) {
                CreateWorkflowDt1 createWorkflowDt1 = new CreateWorkflowDt1();
                createWorkflowDt1.setBdr(new CreateWorkflowValue(platformUser.getUserTicket()));
                createWorkflowDt1.setBdlx(new CreateWorkflowValue(BDR_TYPE));
                dt1List.add(createWorkflowDt1);
            }
        }

        if(!CollectionUtils.isEmpty(groupIds)){

            List<Group> groups = groupMapper.queryByIds(groupIds);

            for (Group group : groups) {
                CreateWorkflowDt1 createWorkflowDt1 = new CreateWorkflowDt1();
                long departmentId = group.getDepartmentId();
                if(departmentId>0){ // 群组有对应的部门
                    PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentId(platformId, group.getDepartmentId());
                    if(Objects.isNull(platformDepartment)){
                        throw new ParamException("platformDepartment is null !");
                    }
                    createWorkflowDt1.setBm(new CreateWorkflowValue(platformDepartment.getDepartmentId()));
                    createWorkflowDt1.setBdlx(new CreateWorkflowValue(BM_TYPE));
                    createWorkflowDt1.setQz(new CreateWorkflowValue(String.valueOf(group.getId())));
                    dt1List.add(createWorkflowDt1);

                }else {// 群组没有对应的部门
                    List<GroupsUser> groupsUsers = groupsUsersMapper.queryByGroupId(group.getId());
                    List<Long> userIdList = new ArrayList<>();
                    for (GroupsUser groupsUser : groupsUsers) {
                        userIdList.add(groupsUser.getUserId());
                    }

                    List<PlatformUser> platformUsers = platformUserMapper.queryByPlatformIdUserIds(platformId, userIdList);
                    List<String> collect = platformUsers.stream().map(p -> p.getUserTicket()).collect(Collectors.toList());
                    String join = String.join(",", collect);

                    createWorkflowDt1.setQz(new CreateWorkflowValue(String.valueOf(group.getId())));
                    createWorkflowDt1.setBdlx(new CreateWorkflowValue(QZ_TYPE));
                    createWorkflowDt1.setBdr(new CreateWorkflowValue(join));
                    dt1List.add(createWorkflowDt1);
                }
            }
        }
        return dt1List;
    }


    /**
     * 新增评论记录OA流程
     * @param result
     * @param kiwiinstCreateSharkCallValueBox
     */
    private void createComment(String result, KiwiinstCreateSharkCallValueBox kiwiinstCreateSharkCallValueBox) {
        try {
            String msg = (String) JSONPath.extract(result, "$.resultlist[0].msg");
            if(StringUtils.isNotEmpty(msg)){
                // 新增评论
                String itemTypedId = kiwiinstCreateSharkCallValueBox.getItemTypedId();
                String[] split = itemTypedId.split("_");
                CreateCommentBean createCommentBean = new CreateCommentBean(split[0], msg, split[1]);
                v2ClientHelper.createComment(createCommentBean, kiwiinstCreateSharkCallValueBox.getCreateUserId());
            }
        }catch (Exception e){
            log.info("createWorkflow create comment error !");
        }
    }


    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.KIWIINST_CREATE_SHARE_CALL.getDesc();
    }
}
