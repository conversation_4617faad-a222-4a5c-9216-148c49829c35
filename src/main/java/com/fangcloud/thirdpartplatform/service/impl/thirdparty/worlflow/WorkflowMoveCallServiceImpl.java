package com.fangcloud.thirdpartplatform.service.impl.thirdparty.worlflow;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.DepartmentMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformEnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.Department;
import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.db.model.User;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.MoveInfo;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.MoveInfoCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdPartyCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.MoveWorkflow;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.MoveWorkflowCall;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.ark.WorkflowApprovalBean;
import com.fangcloud.thirdpartplatform.helper.ArkClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyCommonService;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class WorkflowMoveCallServiceImpl implements ThirdpartyExecuteService {

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private ArkClientHelper arkClientHelper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private ThirdpartyCommonService thirdpartyCommonService;


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        MoveWorkflow moveWorkflow = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getData()), MoveWorkflow.class);

        MoveWorkflowCall moveWorkflowCall = moveWorkflowConvertToMoveWorkflowCall(moveWorkflow);

        ThirdPartyCall thirdPartyCall = ThirdPartyCall.builder()
                .type(ThirdpartyCallbackTypeEnum.MOVE_CALLBACK.getDesc())
                .data(moveWorkflowCall)
                .build();

        return ResultUtils.getSuccessResult(thirdPartyCall);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {

        MoveWorkflowCall moveWorkflowCall = JSON.parseObject(JSON.toJSONString(thirdpartyCallbackParams.getData()), MoveWorkflowCall.class);

        // 校验回调记录
        thirdpartyCommonService.checkCallbackResult(
                moveWorkflowCall.getUniqueId(),
                moveWorkflowCall.getEnterpriseId(),
                moveWorkflowCall.getReviewType(),
                thirdpartyCallbackParams.isReviewResult());

        WorkflowApprovalBean workflowApprovalBean = new WorkflowApprovalBean();
        workflowApprovalBean.setPass(thirdpartyCallbackParams.isReviewResult());
        workflowApprovalBean.setTaskId(Long.parseLong(moveWorkflowCall.getTaskId()));

        boolean workflowApprovalResult = arkClientHelper.workflowApproval(workflowApprovalBean, moveWorkflowCall.getReceiverId());

        return ResultUtils.getSuccessResult(workflowApprovalResult);
    }

    /**
     * 转化对应字段
     * @param moveWorkflow
     * @return
     */
    private MoveWorkflowCall moveWorkflowConvertToMoveWorkflowCall(MoveWorkflow moveWorkflow) {
        MoveWorkflowCall moveWorkflowCall = new MoveWorkflowCall();

        Long enterpriseId = moveWorkflow.getEnterpriseId();

        moveWorkflowCall.setEnterpriseId(enterpriseId);
        moveWorkflowCall.setReceiverId(moveWorkflow.getReceiverId());
        moveWorkflowCall.setTaskId(moveWorkflow.getTaskId());
        moveWorkflowCall.setUniqueId(moveWorkflow.getUniqueId());
        moveWorkflowCall.setTargetFolderId(moveWorkflow.getTargetFolderId());
        if(0 == moveWorkflow.getTargetFolderId()){
            Department department = departmentMapper.queryById(moveWorkflow.getDepartmentId());
            if(!Objects.isNull(department)){
                moveWorkflowCall.setTargetFolderName(department.getName());
            }
        }else {
            moveWorkflowCall.setTargetFolderName(moveWorkflow.getTargetFolderName());
        }

        List<MoveWorkflowCall.Item> itemList = new ArrayList<>();
        List<MoveInfo.Item> items = moveWorkflow.getItems();
        for (MoveInfo.Item item : items) {
            MoveInfoCall.Item itemNew = new MoveInfoCall.Item();
            itemNew.setItemName(item.getItemName());
            itemNew.setItemId(item.getItemId());
            itemNew.setItemType(item.getItemType());
            itemList.add(itemNew);
        }
        moveWorkflowCall.setItemList(itemList);

        Long authorId = moveWorkflow.getAuthorId();
        ThirdpartyCustom thirdpartyCustom = new ThirdpartyCustom();
        thirdpartyCustom.setId(authorId);

        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);
        if(!Objects.isNull(platformEnterprises)){
            PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserId(platformEnterprises.getPlatformId(), authorId);
            if(!Objects.isNull(platformUser)){
                thirdpartyCustom.setCustomId(platformUser.getUserTicket());
            }
        }
        User authorUser = userMapper.queryById(authorId);
        if(!Objects.isNull(authorUser)){
            thirdpartyCustom.setName(authorUser.getFullName());
        }
        moveWorkflowCall.setCreateUser(thirdpartyCustom);
        moveWorkflowCall.setReviewType(ThirdpartyCallTypeEnum.WOEKFLOW_MOVE_CALL.getDesc());
        return moveWorkflowCall;
    }

    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.WOEKFLOW_MOVE_CALL.getDesc();
    }
}
