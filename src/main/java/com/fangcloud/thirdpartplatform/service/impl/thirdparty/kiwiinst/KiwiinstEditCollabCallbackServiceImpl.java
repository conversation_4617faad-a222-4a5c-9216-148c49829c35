package com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.GroupMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformEnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.Group;
import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.CreateCollabWorkflowMain;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.CreateWorkflowDt1;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.v2.EditCollabBean;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst.KiwiinstInviteCollabCallbackServiceImpl.BM_TYPE;
import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst.KiwiinstInviteCollabCallbackServiceImpl.QZ_TYPE;

@Service
@Slf4j
public class KiwiinstEditCollabCallbackServiceImpl implements ThirdpartyExecuteService {

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        JSONObject jsonObject = (JSONObject)  JSONObject.parse(JSON.toJSONString(thirdpartyParams.getValueBox()));

        CreateCollabWorkflowMain createWorkflowMain = JSON.parseObject(JSON.toJSONString(jsonObject.getJSONObject("main")), CreateCollabWorkflowMain.class);
        CreateWorkflowDt1 createWorkflowDt1 = JSON.parseObject(JSON.toJSONString(jsonObject.getJSONArray("dt1").get(0)), CreateWorkflowDt1.class);


        // 获取协作文件夹的所有者用户id
        String enterpriseId = createWorkflowMain.getEnterpriseId().getValue();
        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(Long.parseLong(enterpriseId));
        if(Objects.isNull(platformEnterprises)){
            throw new ParamException("platformEnterprises is null !");
        }
        int platformId = platformEnterprises.getPlatformId();
        PlatformUser ownerPlatformUser = platformUserMapper.queryByPlatformIdUserTicket(platformId, createWorkflowMain.getOwnerId().getValue());
        if(Objects.isNull(ownerPlatformUser)){
            throw new ParamException("ownerPlatformUser is null!");
        }

        // 获取协作id
        String collabId = createWorkflowMain.getCollabId().getValue();

        // 获取要修改的协作权限
        String role = createWorkflowDt1.getXzlx().getValue();

        EditCollabBean editCollabBean = null;
        if(Boolean.valueOf(createWorkflowMain.getCurrentFolder().getValue())){
            editCollabBean = new EditCollabBean(Long.valueOf(collabId), role);
        }else {
            editCollabBean = buildEditCollabBean(createWorkflowMain,  collabId, role, createWorkflowDt1, platformId);
        }

        // 修改协作
        v2ClientHelper.editCollab(editCollabBean, ownerPlatformUser.getUserId());

        return  ResultUtils.getSuccessResult(true);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {
        return null;
    }

    private EditCollabBean buildEditCollabBean(CreateCollabWorkflowMain createWorkflowMain, String collabId, String role, CreateWorkflowDt1 createWorkflowDt1, int platformId) {
        EditCollabBean editCollabBean = new EditCollabBean();
        editCollabBean.setItemId(Long.valueOf(createWorkflowMain.getFolderId().getValue()));
        String bdlx = Base64Utils.decodeGbk(createWorkflowDt1.getBdlx().getValue());
        log.info("buildEditCollabBean bdlx:{}", bdlx);
        if(BM_TYPE.equals(bdlx) || QZ_TYPE.equals(bdlx)){ // 群组类型或者部门类型
            Group group = groupMapper.queryById(Long.valueOf(createWorkflowDt1.getQz().getValue()));
            if(Objects.isNull(group)){
                throw new ParamException("group is null!");
            }
            EditCollabBean.CollabRole collabRole = new EditCollabBean.CollabRole(Long.valueOf(collabId),role, group.getId());
            List<EditCollabBean.CollabRole> list = new ArrayList<>();
            list.add(collabRole);
            editCollabBean.setInvite_group_collabs_roles(list);
        }else{ // 人力资源类型
            String bdr = createWorkflowDt1.getBdr().getValue();
            PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserTicket(platformId, bdr);
            if(Objects.isNull(platformUser)){
                throw new ParamException("platformDepartment is null!");
            }
            EditCollabBean.CollabRole collabRole = new EditCollabBean.CollabRole(Long.valueOf(collabId),role, platformUser.getUserId());
            List<EditCollabBean.CollabRole> list = new ArrayList<>();
            list.add(collabRole);
            editCollabBean.setInvite_user_collabs_roles(list);
        }

        return editCollabBean;
    }


    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallbackTypeEnum.KIWIINST_EDIT_COLLAB_CALLBACK.getDesc();
    }
}
