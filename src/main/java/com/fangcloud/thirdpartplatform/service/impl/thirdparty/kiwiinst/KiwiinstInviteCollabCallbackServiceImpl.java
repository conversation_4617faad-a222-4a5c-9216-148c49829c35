package com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.Group;
import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.CreateCollabWorkflowMain;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.CreateWorkflowDt1;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.v2.InviteCollabBean;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class KiwiinstInviteCollabCallbackServiceImpl implements ThirdpartyExecuteService {

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    static final String QZ_TYPE = "群组";
    static final String BM_TYPE = "部门";
    static final String BDR_TYPE = "人力资源";


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        JSONObject jsonObject = (JSONObject)  JSONObject.parse(JSON.toJSONString(thirdpartyParams.getValueBox()));

        CreateCollabWorkflowMain createWorkflowMain = JSON.parseObject(JSON.toJSONString(jsonObject.getJSONObject("main")), CreateCollabWorkflowMain.class);
        List createWorkflowDt1List = JSON.parseObject(JSON.toJSONString(jsonObject.getJSONArray("dt1")), List.class);


        // 获取协作文件夹的所有者用户id
        String enterpriseId = createWorkflowMain.getEnterpriseId().getValue();
        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(Long.parseLong(enterpriseId));
        if(Objects.isNull(platformEnterprises)){
            throw new ParamException("platformEnterprises is null !");
        }
        int platformId = platformEnterprises.getPlatformId();
        PlatformUser invitePlatformUser = platformUserMapper.queryByPlatformIdUserTicket(platformId, createWorkflowMain.getOwnerId().getValue());
        if(Objects.isNull(invitePlatformUser)){
            throw new ParamException("editPlatformUser is null!");
        }

        InviteCollabBean inviteCollabBean = buildInviteCollabBean(createWorkflowMain, createWorkflowDt1List, platformId);

        // 创建协作
        String result = v2ClientHelper.inviteCollab(inviteCollabBean, invitePlatformUser.getUserId());

        log.info(result);

        return  ResultUtils.getSuccessResult(true);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {
        return null;
    }

    private InviteCollabBean buildInviteCollabBean(CreateCollabWorkflowMain createWorkflowMain, List createWorkflowDt1List, int platformId) {

        String invited_users = "";
        String invited_groups = "";
        for (Object o : createWorkflowDt1List) {
            CreateWorkflowDt1 createWorkflowDt1 = JSON.parseObject(JSON.toJSONString(o), CreateWorkflowDt1.class);
            String bdlx = Base64Utils.decodeGbk(createWorkflowDt1.getBdlx().getValue());
            log.info("buildInviteCollabBean bdlx:{}", bdlx);

            String xzlx = createWorkflowDt1.getXzlx().getValue();
            if(BM_TYPE.equals(bdlx) || QZ_TYPE.equals(bdlx)){ // 群组类型或者部门类型
                Group group = groupMapper.queryById(Long.valueOf(createWorkflowDt1.getQz().getValue()));
                if(Objects.isNull(group)){
                    throw new ParamException("group is null!");
                }
                invited_groups = invited_groups + ";" + createWorkflowDt1.getQz().getValue() + ":" + xzlx;
            }else{ // 人力资源类型
                String bdr = createWorkflowDt1.getBdr().getValue();
                PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserTicket(platformId, bdr);
                if(Objects.isNull(platformUser)){
                    throw new ParamException("platformDepartment is null!");
                }
                invited_users =  invited_users + ";" + platformUser.getUserId() + ":" + xzlx;

            }
        }

        String folderId = createWorkflowMain.getFolderId().getValue();
        String invitationMessage = Base64Utils.decodeGbk(createWorkflowMain.getInvitationMessage().getValue());
        log.info("buildInviteCollabBean invitationMessage:{}", invitationMessage);

        if(invited_users.startsWith(";")){
            invited_users = invited_users.substring(1);
        }

        if(invited_groups.startsWith(";")){
            invited_groups = invited_groups.substring(1);
        }

        InviteCollabBean inviteCollabBean = new InviteCollabBean();
        inviteCollabBean.setFolderId(Long.valueOf(folderId));
        inviteCollabBean.setInvitationMessage(invitationMessage);
        inviteCollabBean.setInvitedUsers(invited_users);
        inviteCollabBean.setInvitedGroups(invited_groups);
        return inviteCollabBean;
    }


    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallbackTypeEnum.KIWIINST_INVITE_COLLAB_CALLBACK.getDesc();
    }
}
