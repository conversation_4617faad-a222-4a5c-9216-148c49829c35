package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 广州实验室获取部门列表
 */
@Component
@Slf4j
public class GzlabServiceImpl {

    @Resource
    private ApiSyncHandler apiSyncHandler;

    public Object query(PlatformSyncConfigResponse platformSyncConfig, String params) {

        String valueBox = platformSyncConfig.getValueBox();
        String syncType = platformSyncConfig.getSyncType();
        String sourceType = platformSyncConfig.getSourceType();
        log.info("sourceType is {}, syncType is {}, valueBox is {}", sourceType, syncType, valueBox);
        if(!SourceTypeEnum.API.getDesc().equals(sourceType) || !SyncTypeEnum.DEPARTMENT.getDesc().equals(syncType)){
            log.info("sourceType or syncType is wrong!");
            ResultUtils.getFailedResult( ResponseCodeEnum.ERROR, "sourceType or syncType is wrong!");
        }

        APIConfigValueBox apiConfigValueBox = JSON.parseObject(valueBox, APIConfigValueBox.class);

        String oauth2Result = apiSyncHandler.executeLoginApi(null, apiConfigValueBox.getOauth2Config(), apiConfigValueBox.getProtocol(), null, null);
        String apiResult = apiSyncHandler.executeLoginApi(oauth2Result, apiConfigValueBox.getApiConfig(), apiConfigValueBox.getProtocol(), null, null);

        if(StringUtils.isEmpty(apiResult)){
            log.info("dept list is empty!");
            ResultUtils.getFailedResult( ResponseCodeEnum.ERROR, "dept list is empty!");
        }

        List<YfyDepartment> yfyDepartmentList = new ArrayList<>();
        JSONObject parse = (JSONObject) JSONObject.parse(apiResult);
        JSONArray dataArrary = parse.getJSONArray("data");

        for (Object data : dataArrary) {
            JSONObject jsonObject = (JSONObject) data;
            buildDept(null, jsonObject,yfyDepartmentList);
        }
        log.info("gzlab yfyDepartmentList is {}", JSON.toJSONString(yfyDepartmentList));

        return ResultUtils.getSuccessResult(yfyDepartmentList);
    }


    private static void buildDept(String parentId, JSONObject jsonObject, List<YfyDepartment> deptList) {
        YfyDepartment dept = new YfyDepartment();
        String id = jsonObject.getString("id");
        String name = jsonObject.getString("name");
        Integer seq = jsonObject.getInteger("seq");
        dept.setId(id);
        dept.setName(name);
        dept.setParentId(parentId);
        dept.setOrder(100000L - seq);
        deptList.add(dept);

        String sub_departments = jsonObject.getString("sub_departments");
        JSONArray jsonArray = JSON.parseArray(sub_departments);
        for (Object o : jsonArray) {
            JSONObject jsonObj = (JSONObject) o;
            buildDept(id, jsonObj, deptList);
        }
    }
}
