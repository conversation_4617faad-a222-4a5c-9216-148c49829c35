package com.fangcloud.thirdpartplatform.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONNull;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.db.dao.DepartmentMapper;
import com.fangcloud.thirdpartplatform.db.dao.DepartmentsUsersMapper;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.Department;
import com.fangcloud.thirdpartplatform.db.model.DepartmentsUsers;
import com.fangcloud.thirdpartplatform.db.model.User;
import com.fangcloud.thirdpartplatform.utils.AuthenticationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class SyncFangCloudToThirdPartyServiceImpl {

    @Resource
    private DepartmentMapper departmentMapper;
    @Resource
    private DepartmentsUsersMapper departmentsUsersMapper;
    @Resource
    private UserMapper userMapper;

    @Resource
    private CustomNacosConfig customNacosConfig;

    private static final String TYPE_SYNC_DEPT = "syncdept";

    private static final String TYPE_DEL_DEPT = "deldept";

    private static final String TYPE_SYNC_USER = "syncuser";

    private static final String TYPE_DEL_USER = "deluser";

    private static final String MSG_TYPE_SYSTEM = "system";


    /**
     *
     * @param msgData
     * @return
     */
    public boolean receiveMsg(String msgData) {
        log.info("receiveMsg msgData: {}", msgData);

        JSONObject msgObject = JSONUtil.parseObj(msgData);
        if (msgObject.getByPath("$.type").equals(MSG_TYPE_SYSTEM)) {
           String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl18();
           String receiveId = msgObject.getByPath("$.data.receivers").toString();
            User user = userMapper.queryById(receiveId);
            if (user == null) {
                log.info("receive_message receiveId {} is not exist msgData {}", receiveId, msgData);
                return false;
            }
            Map<String, String> params = new HashMap<>();
            params.put("enterprise_id", String.valueOf(user.getEnterpriseId()));
            params.put("receiver_id", receiveId);
            params.put("msg_type", msgObject.getByPath("$.type").toString());
            params.put("title", msgObject.getByPath("$.data.title").toString());
            params.put("content", msgObject.getByPath("$.data.content").toString());
            params.put("url", msgObject.getByPath("$.data.url").toString());

            Map<String, String> headers = new HashMap<>();
            headers.put("content-type", "application/json");

            Map<String, String> headersV2 = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            headers.putAll(headersV2);

            String result = httpRequestPost(url, headers, JSONUtil.toJsonStr(params));
            log.info("receive_message receiveId {} result: {} ", receiveId, result);
            return true;
        } else {
            log.info("receiveMsg type: {} is not support yet", msgObject.getByPath("$.type"));
        }
        return false;
    }


    /**
     * {"http": {"method": "POST","headers": {"content-type": "application/json"},"url": "/servlet/DlpServletInterface?console=DlpServer&function=EmployeeUserMgr&act=addEmployeeDept"},"enterprise": {"id": "115"},"type": "syncdept"}
     * @param config
     * @return
     */

    public boolean syncDept(String config) {
        config = Base64.decodeStr(config);
        log.info("syncDept config: {}", config);

        JSONObject configObject = JSONUtil.parseObj(config);
        if (configObject != null) {
            if (TYPE_SYNC_DEPT.equals(configObject.getByPath("$.type"))) {
               List<Department> departmentList = departmentMapper.queryByEnterpriseId(
                       Long.parseLong(configObject.getByPath("$.enterprise.id").toString()));
               JSONObject deptObject = JSONUtil.parseObj(configObject.getByPath("$.http.data_template"));

               if (departmentList!= null && departmentList.size() > 0) {
                   departmentList.forEach(department -> {
                       String parentId = "-1";
                       if (department.getParentId() != 0) {
                           parentId = buildDeptId(String.valueOf(department.getParentId()));
                       }
                       String deptData = JSONUtil.toJsonStr(deptObject)
                               .replace("${deptId}", buildDeptId(String.valueOf(department.getId())))
                               .replace("${deptName}", department.getName())
                               .replace("${parentId}", parentId);

                       Map<String, String> headers = new HashMap<>();
                       if (configObject.getByPath("$.http.headers") != null) {
                           headers = JSONUtil.toBean(configObject.getByPath("$.http.headers").toString(), HashMap.class);
                       } else {
                           headers.put("content-type", "application/json");
                       }

                       String url = configObject.getByPath("$.http.url").toString();
                       if (configObject.getByPath("$.http.method") != null &&
                               configObject.getByPath("$.http.method").equals("POST")) {

                           String result = httpRequestPost(url, headers, deptData);
                           log.info("sync dept url {} postdata {} result: {} ", url, deptData, result);
                       } else {
                           // default is GET
                           log.info("method is GET, not support yet");
                       }
                   });
               }
            }
            return true;
        }

        return false;
    }


    public boolean syncUser(String config) {
        config = Base64.decodeStr(config);
        log.info("syncUser config: {}", config);

        JSONObject configObject = JSONUtil.parseObj(config);

        if (TYPE_SYNC_USER.equals(configObject.getByPath("$.type"))) {
            return createAndUpdateUser(configObject);
        }

        if (TYPE_DEL_USER.equals(configObject.getByPath("$.type"))) {
            return deleteUser(configObject);
        }

        return false;
    }

    private boolean createAndUpdateUser(JSONObject configObject) {
        List<User> users = userMapper.queryUsersByEnterpriseId(Long.valueOf(configObject.getByPath("$.enterprise.id").toString()));
        JSONObject userObject = JSONUtil.parseObj(configObject.getByPath("$.http.data_template"));
        if (users != null && users.size() > 0) {
            users.forEach(user -> {
                List<DepartmentsUsers> departmentsUsersList = departmentsUsersMapper.queryByUserId(user.getId());
                if (departmentsUsersList.size() == 0) {
                    return;
                }
                String userData = JSONUtil.toJsonStr(userObject)
                        .replace("${userId}", String.valueOf(user.getId()))
                        .replace("${email}", user.getEmail())
                        .replace("${fullName}", String.valueOf(user.getFullName()))
                        .replace("${deptId}", buildDeptId(String.valueOf(departmentsUsersList.get(0).getDepartmentId())));

                Map<String, String> headers = new HashMap<>();
                if (configObject.getByPath("$.http.headers") != null) {
                    headers = JSONUtil.toBean(configObject.getByPath("$.http.headers").toString(), HashMap.class);
                } else {
                    headers.put("content-type", "application/json");
                }

                String url = configObject.getByPath("$.http.url").toString();
                if (configObject.getByPath("$.http.method") != null &&
                        configObject.getByPath("$.http.method").equals("POST")) {

                    String result = httpRequestPost(url, headers, userData);
                    log.info("sync user url {} postdata {} result: {} ", url, userData, result);
                } else {
                    // default is GET
                    log.info("method is GET, not support yet");
                }

            });
            return true;
        }
        return false;
    }

    private boolean deleteUser(JSONObject configObject) {
        List<User> users = userMapper.queryDelUsersByEnterpriseId(Long.valueOf(configObject.getByPath("$.enterprise.id").toString()));
        JSONObject userObject = JSONUtil.parseObj(configObject.getByPath("$.http.data_template"));
        if (users != null && users.size() > 0) {
            users.forEach(user -> {
                String userData = JSONUtil.toJsonStr(userObject)
                        .replace("${email}", user.getEmail());

                Map<String, String> headers = new HashMap<>();
                if (configObject.getByPath("$.http.headers") != null) {
                    headers = JSONUtil.toBean(configObject.getByPath("$.http.headers").toString(), HashMap.class);
                } else {
                    headers.put("content-type", "application/json");
                }

                String url = configObject.getByPath("$.http.url").toString();
                if (configObject.getByPath("$.http.method") != null &&
                        configObject.getByPath("$.http.method").equals("POST")) {

                    String result = httpRequestPost(url, headers, userData);
                    log.info("sync dept url {} postdata {} result: {} ", url, userData, result);
                } else {
                    // default is GET
                    log.info("method is GET, not support yet");
                }

            });
            return true;
        }
        return false;
    }

    private String httpRequestPost(String url, Map<String, String> headers, String postData) {
        log.info("httpRequest url {}, headers {}, postData {}", url, headers, postData);
        HttpRequest request = HttpRequest.post(url);
        if (headers.containsKey("content-type") && headers.get("content-type").equals("application/x-www-form-urlencoded")){
            request.form(JSONUtil.toBean(postData, HashMap.class));
        } else {
            request.body(postData);
        }
        String result = request
                .addHeaders(headers)
                .execute().body();

        return result;
    }

    private String buildDeptId(String deptId) {
        StringBuilder zeros = new StringBuilder();
        for (int i = 0; i < 10 - deptId.length(); i++) {
            zeros.append('0');
        }
        return zeros.toString() + deptId;
    }

}
