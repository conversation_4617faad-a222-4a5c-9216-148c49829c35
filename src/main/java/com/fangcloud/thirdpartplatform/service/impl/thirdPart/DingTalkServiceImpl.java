package com.fangcloud.thirdpartplatform.service.impl.thirdPart;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.entity.common.UserEnterpriseInfo;
import com.fangcloud.thirdpartplatform.entity.input.DingTalkInitParams;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.helper.DingTalkClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.DingTalkService;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;


@Service
@Slf4j
public class DingTalkServiceImpl extends  BaseServiceImpl implements DingTalkService {
    private DingTalkClientHelper dingTalkClientHelper;

    @Resource
    private V2ClientHelper v2ClientHelper;


    @Resource
    private RedisStringManager redisStringManager;

    @Override
    public String getAutoLoginUrl(DingTalkInitParams params) {
        // 调钉钉接口获取用户ID
        String idInfo = null;
        // 获取钉钉i用户信息
        String userId;
        if (StringUtils.isNotBlank(params.getAuthCode())) {
            userId = new DingTalkClientHelper(params).getUserIdByAuthCode();
        } else {
            userId = new DingTalkClientHelper(params).getUserIdByCode();
        }
        // 构建亿方云user对象
        YfyUser yfyUser = new DingTalkClientHelper(params).getUserInfoByUserId(userId);

        if (CommonConstants.ENTERPRISE_LOGIN_TYPE_USER_ID.equals(params.getLoginType())) {
            idInfo = userId;
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
        } else if (CommonConstants.ENTERPRISE_LOGIN_TYPE_PHONE.equals(params.getLoginType())) {
            String phone = yfyUser.getPhone();
            if (StringUtils.isNotBlank(phone) && phone.contains("+")) {
                if (phone.contains(SyncTaskConstants.USER_PHONE_CN)) {
                    phone = phone.substring(Math.max(0, phone.length() - 11));
                } else {
                    phone = null;
                }
            }
            idInfo = phone;
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
        } else if (CommonConstants.ENTERPRISE_LOGIN_TYPE_EMAIL.equals(params.getLoginType())) {
            String jobNumbers = redisStringManager.get(SyncTaskConstants.DING_TALK_JOB_NUMBER + params.getAgentId());
            if (StringUtils.isNotBlank(jobNumbers)) {
                JSONObject cacheUser = JSONObject.parseObject(jobNumbers);
                yfyUser = JSON.parseObject(cacheUser.getString(yfyUser.getErrorCode()), YfyUser.class);
                log.info("get yfy users by job_numbers {}", yfyUser);
            }
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
            idInfo = yfyUser.getEmail();
        }else {
            log.error("未知的类型 loginType {}", params.getLoginType());
        }

        if (StringUtils.isEmpty(idInfo)) {
            log.warn("从钉钉获取用户信息为空 入参 params {}", params);
        }else {
            params.getEnterpriseParams().setIdentifier(idInfo);
            // 获取登录url
            String loginUrl = getLoginUrl(params.getSyncUserFlag(), params.getEnterpriseParams(), yfyUser);
            if (loginUrl != null) {
                return loginUrl;
            }
        }

        dropDingTalkClient();
        return CommonConstants.URL_ACCOUNT_NOT_OPEN;
    }

    @Override
    public Boolean sendMessage(MessageParams params) {
        boolean sendFlag = false;
        String dingTalkUserId = params.getReceivers();
        DingTalkClientHelper dingTalkClient = new DingTalkClientHelper(params.getDingTalkInitParams());
        EnterpriseParams enterpriseParams = params.getDingTalkInitParams().getEnterpriseParams();
        UserEnterpriseInfo userEnterpriseInfo = UserEnterpriseInfo.builder()
                .enterpriseId(enterpriseParams.getEnterpriseId())
                .platformId(enterpriseParams.getPlatformId())
                .userId(params.getReceivers()).build();
        JSONObject userInfoJSONObject = v2ClientHelper.getUserInfoFromV2(userEnterpriseInfo);
        //获取到用户信息为空，使用亿方云user_id重新获取
        if (Objects.isNull(userInfoJSONObject)) {
            userEnterpriseInfo.setUserId(null);
            userEnterpriseInfo.setYfyUserId(params.getReceivers());
            userInfoJSONObject = v2ClientHelper.getUserInfoFromV2(userEnterpriseInfo);
        }
        // 登陆类型为mobile的时候需要转化消息接受者
        if (CommonConstants.ENTERPRISE_LOGIN_TYPE_PHONE.equals(params.getDingTalkInitParams().getLoginType())) {
            if(!Objects.isNull(userInfoJSONObject)){
                String phone = userInfoJSONObject.getString("phone");
                if(StringUtils.isEmpty(phone)){
                    return sendFlag;
                }
                // 根据手机号获取获取钉钉用户id
                dingTalkUserId = dingTalkClient.getUserIdByPhone(phone);
            }
            //通过获取云盘上的邮箱实现为钉钉发送消息
        } else if (CommonConstants.ENTERPRISE_LOGIN_TYPE_EMAIL.equals(params.getDingTalkInitParams().getLoginType())) {
            if(!Objects.isNull(userInfoJSONObject)) {
                String email = userInfoJSONObject.getString("email");
                if (StringUtils.isBlank(email)) {
                    log.error("email is null, send error");
                    return sendFlag;
                }
                String jobNumber = email.split("@")[0];
                String jobNumbers = redisStringManager.get(SyncTaskConstants.DING_TALK_JOB_NUMBER + params.getDingTalkInitParams().getAgentId());
                if (StringUtils.isNotBlank(jobNumbers)) {
                    JSONObject cacheUser = JSONObject.parseObject(jobNumbers);
                    YfyUser yfyUser = JSON.parseObject(cacheUser.getString(jobNumber), YfyUser.class);
                    dingTalkUserId =  yfyUser.getId();
                } else if (StringUtils.isNotBlank(redisStringManager.get(SyncTaskConstants.DING_TALK_USER_ID + params.getDingTalkInitParams().getAgentId()))) {
                    dingTalkUserId = jobNumber;
                }
                log.info("email split dingTalkUserId:{}", dingTalkUserId);
            }
        }

        if(StringUtils.isBlank(dingTalkUserId)){
            return sendFlag;
        }
        params.setReceivers(dingTalkUserId);

        // 发送消息
        if (dingTalkClient.isSendRobotMessage()) {
            sendFlag = dingTalkClient.sendRobotLinkMessage(getYfyMessage(params));
        } else {
            sendFlag = dingTalkClient.sendLinkMessage(getYfyMessage(params));
        }

        dropDingTalkClient();
        return sendFlag;
    }

    private void dropDingTalkClient() {
        dingTalkClientHelper = null;
    }
}
