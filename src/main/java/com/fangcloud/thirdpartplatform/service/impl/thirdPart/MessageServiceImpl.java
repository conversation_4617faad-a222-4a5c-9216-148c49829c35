package com.fangcloud.thirdpartplatform.service.impl.thirdPart;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.sync.ApiParamWayEnum;
import com.fangcloud.thirdpartplatform.constant.sync.DataTypeConstants;
import com.fangcloud.thirdpartplatform.constant.sync.MessageUtilEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncTaskRecordMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.MessageConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.MessageParamConfigDto;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.sync.common.enums.YfyMessageTypeEnum;
import com.sync.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageServiceImpl extends BaseServiceImpl {

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    private PlatformSyncTaskRecordMapper platformSyncTaskRecordMapper;

    public Map<String, String> getYfyMessageParams(String postData) {
        Map<String, String> yfyMessage = new HashMap<>();
        String[] urlStr = postData.split(CommonConstants.MESSAGE_POST_DATA_SPLIT);
        for (String s : urlStr) {
            String[] params = s.split(CommonConstants.MESSAGE_URL_SPLIT);
            try {
                String value = URLDecoder.decode(params[1], CommonConstants.MESSAGE_URL_ENCODE);
                yfyMessage.put(params[0], value);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
        return yfyMessage;
    }

    public Boolean executeMessage(Map<String, String> yfyMessage, List<PlatformSyncConfig> platformSyncConfigs) {
        if (platformSyncConfigs == null || platformSyncConfigs.isEmpty()) {
            return false;
        }
        PlatformSyncConfig platformSyncConfig = platformSyncConfigs.get(0);
        try {
            String type = yfyMessage.get(MessageUtilEnum.MESSAGE_TYPE.getKey());
            String title = YfyMessageTypeEnum.getEnum(type).getDesc();
            yfyMessage.put(MessageUtilEnum.MESSAGE_TITLE.getKey(), title);
            String messagePushTitle = yfyMessage.get(MessageUtilEnum.MESSAGE_PUSH_TITLE.getKey());
            String content = yfyMessage.get(MessageUtilEnum.MESSAGE_CONTENT.getKey()).replaceAll("\\[\\d+:", "[").replaceAll("\\[g-\\d+:","[");
            String valueBox = platformSyncConfig.getValueBox();
            JSONObject jsonObject = JSON.parseObject(valueBox);
            JSONObject valueBoxJsonObject = null;
            valueBoxJsonObject = jsonObject.getJSONObject("all") != null ? jsonObject.getJSONObject("all") : jsonObject.getJSONObject(type);
            if (valueBoxJsonObject == null) {
                log.info("该消息类型并未配置 消息类型 {}", type);
                return false;
            }
            if (messagePushTitle != null) {
                yfyMessage.put(MessageUtilEnum.MESSAGE_TITLE.getKey(), title.replaceAll(CommonConstants.MESSAGE_TITLE, messagePushTitle));
            }
            if (content != null) {
                yfyMessage.put(MessageUtilEnum.MESSAGE_CONTENT.getKey(), content);
            }
            MessageConfigValueBox messageConfigValueBox = JSON.parseObject(valueBoxJsonObject.toString(), MessageConfigValueBox.class);
            List<MessageParamConfigDto> messageParamConfigDtos = messageConfigValueBox.getMessageParamConfigDtos();
            if (CollectionUtils.isEmpty(messageParamConfigDtos)) {
                return false;
            }
            Object result = null;
            Map<String, Object> codeResultMap = new HashMap<>();
            if (StringUtils.isNotBlank(messageConfigValueBox.getCode())) {
                 codeResultMap = executeContext(URLDecoder.decode(messageConfigValueBox.getCode(), "UTF-8"), yfyMessage);
                 log.info("脚本输出结果：{}",codeResultMap.toString());
            }
            if (StringUtils.isNotBlank(messageConfigValueBox.getApi())) {
                Map<String, Object> paramsMap = new HashMap<>();
                Map<String, Object> bodyMap = new HashMap<>();
                Map<String, String> headMap = new HashMap<>();
                buildParams(yfyMessage, messageParamConfigDtos, paramsMap, bodyMap, headMap, codeResultMap);
                result = sendMessage(messageConfigValueBox, headMap, bodyMap, paramsMap, platformSyncConfig.getEnterpriseId());
                log.info("send message result:{}",result.toString());
            }
            return true;
        } catch (Exception e) {
            log.error("executeMessage error", e);
            return false;
        }
    }

    private void buildParams(Map<String, String> yfyMessage, List<MessageParamConfigDto> messageParamConfigDtos, Map<String, Object> paramsMap, Map<String, Object> bodyMap, Map<String, String> headMap,  Map<String, Object> codeResultMap) {
        messageParamConfigDtos.forEach(messageParamConfigDto -> {
            String format = messageParamConfigDto.getFormat();
            String name = messageParamConfigDto.getName();
            if (codeResultMap != null && codeResultMap.size() > 0){
                codeResultMap.forEach((key, value) -> {
                    yfyMessage.put(key, value.toString());
                });
            }
            String mappingName = messageParamConfigDto.getMappingName();
            Object value = getValueByFormat(yfyMessage.get(name), format);
            String paramWay = messageParamConfigDto.getParamWay();
            if (value == null && !paramWay.equals(ApiParamWayEnum.URLENCODED.getDesc())){
                return;
            }
            String dataType = messageParamConfigDto.getDataType();
            Object convert = DataTypeConstants.convert(value, dataType);
            if (paramWay.equals(ApiParamWayEnum.BODY.getDesc())) {
                bodyMap.put(mappingName, convert);
            } else if (paramWay.equals(ApiParamWayEnum.PARAMS.getDesc())) {
                paramsMap.put(mappingName, convert);
            } else if (paramWay.equals(ApiParamWayEnum.HEAD.getDesc())) {
                headMap.put(mappingName, (String) convert);
            } else if (paramWay.equals(ApiParamWayEnum.URLENCODED.getDesc())) {
                headMap.put(ApiParamWayEnum.URLENCODED.getDesc(), ApiParamWayEnum.URLENCODED.getDesc());
            }
        });
    }

    private Object getValueByFormat(Object value, String format) {
        Object result = null;
        if (format != null && !format.trim().equals(StringUtils.EMPTY)) {
            if (format.contains(CommonConstants.STRING_FORMAT)) {
                result = String.format(format, value);
            } else if (format.startsWith(CommonConstants.DATE_FORMAT)) {
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                result = DateUtil.stringToDate(sdf.format(date), sdf);
            }
        } else {
            result = value;
        }
        return result;
    }


    private String sendMessage(MessageConfigValueBox messageConfigValueBox, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramsMap, Integer enterpriseId) {
        String result = StringUtils.EMPTY;
        try {
            if (RequestMethod.POST.name().equals(messageConfigValueBox.getRequestMethod())) {
                result = apiSyncHandler.doPost(messageConfigValueBox.getApi(), headMap, paramsMap, bodyMap, enterpriseId);
            } else if (RequestMethod.GET.name().equals(messageConfigValueBox.getRequestMethod())) {
                result = apiSyncHandler.doGet(headMap, paramsMap, messageConfigValueBox.getApi(), enterpriseId);
            }
        } catch (Exception e) {
            log.error("executeMessage-error", e);
        }
        return result;
    }
}
