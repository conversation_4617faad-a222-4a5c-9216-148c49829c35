package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskService;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Component
@Slf4j
public class MysqlSyncHandler extends AbstractDataSourceSyncHandler {

    @Resource
    private PlatformSyncTaskService PlatformSyncTaskService;

    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        // 从数据库获取用户信息
        List<YfyUser> yfyUserList = findUserDataFromDB(platformSyncConfig, taskId);
        // 若用户数据为空
        if(CollectionUtils.isEmpty(yfyUserList)){
            throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
        }
            // 同步用户信息
        log.info("database-syncUser, users:{}, taskId:{}", JSON.toJSONString(yfyUserList), taskId);
        return syncUserList(platformSyncConfig, yfyUserList, taskId);
    }

    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        // 从数据库获取用户部门信息
        List<YfyDepartment> yfyDepartmentList = findDepartmentDataFromDB(platformSyncConfig,taskId);
        // 若部门信息为空
        if(CollectionUtils.isEmpty(yfyDepartmentList)){
            throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
        }
        // 同步部门信息
        log.info("database-syncDepartment, department:{}, taskId:{}", JSON.toJSONString(yfyDepartmentList), taskId);
        return syncDepartmentList(platformSyncConfig, yfyDepartmentList, taskId);
    }

    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return null;
    }

    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.MYSQL.getDesc();
    }
}