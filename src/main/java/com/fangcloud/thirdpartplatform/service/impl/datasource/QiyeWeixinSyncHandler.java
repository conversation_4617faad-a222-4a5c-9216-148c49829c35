package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.CustomIdentifierEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.ThirdPartyValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.entity.input.WeixinWorkInitParams;
import com.fangcloud.thirdpartplatform.entity.output.QiyeWeixinDepartment;
import com.fangcloud.thirdpartplatform.entity.output.QiyeWeixinUser;
import com.fangcloud.thirdpartplatform.helper.WeixinWorkClientHelper;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.utils.StreamUtils;
import com.sync.common.utils.SyncStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.sync.common.contants.SyncConstant.GB;

@Component
@Slf4j
public class QiyeWeixinSyncHandler extends AbstractDataSourceSyncHandler {

    private static String WEIXIN_CORP_ID_PATH = "$.platform_config.weixinWork.corpId.value";
    private static String WEIXIN_CORP_SECRET_PATH = "$.platform_config.weixinWork.corpSecret.value";
    private static String WEIXIN_HOST_PATH = "$.platform_config.weixinWork.host.value";

    private static String ARR_PLATFORM_CONFIG_PATH = "$.platform_config_arr";
    private static String ARR_WEIXIN_CORP_ID_PATH = "$.platform_config_arr[0].weixinWork.corpId.value";
    private static String ARR_WEIXIN_CORP_SECRET_PATH = "$.platform_config_arr[0].weixinWork.corpSecret.value";
    private static String ARR_WEIXIN_HOST_PATH = "$.platform_config_arr[0].weixinWork.host.value";

    private static String WEIXIN_EXTATTR_PARAMETER_NAME = "name";
    private static String WEIXIN_EXTATTR_PARAMETER_VALUE = "value";

    @Autowired
    private EnterpriseServiceImpl enterpriseService;

    @Autowired
    private CustomNacosConfig nacosConfig;

    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        //获取appKey和appSecret
        WeixinWorkInitParams params = getWeixinInfo(platformSyncConfig.getEnterpriseId());
        List<QiyeWeixinUser> weixinUsers = new WeixinWorkClientHelper(params).getUsers(nacosConfig.getCustomWeiXinTopDept(), true);
        if (CollectionUtils.isEmpty(weixinUsers)) {
            throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
        }
        List<YfyUser> users = buildUsers(weixinUsers,platformSyncConfig);
        log.info("apiSyncHandler-syncUser, user:{}, taskId:{}", JSON.toJSONString(users), taskId);
        if (CollectionUtils.isEmpty(users)) {
            return executeResult;
        }
        return syncUserList(platformSyncConfig, users, taskId);
    }

    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        WeixinWorkInitParams params = getWeixinInfo(platformSyncConfig.getEnterpriseId());
        List<QiyeWeixinDepartment> weixinDepartments = new WeixinWorkClientHelper(params).getDepartments(nacosConfig.getCustomWeiXinTopDept());
        if (CollectionUtils.isEmpty(weixinDepartments)) {
            throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
        }
        List<YfyDepartment> departments = buildDepartments(weixinDepartments, platformSyncConfig);
        log.info("apiSyncHandler-syncDepartment, department:{}, taskId:{}", JSON.toJSONString(departments), taskId);
        if (CollectionUtils.isEmpty(departments)) {
            return executeResult;
        }
        return syncDepartmentList(platformSyncConfig, departments, taskId);
    }

    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return null;
    }

    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.WEIXIN.getDesc();
    }

    public WeixinWorkInitParams getWeixinInfo(Integer enterpriseId) {
        try {
            WeixinWorkInitParams params = new WeixinWorkInitParams();
//            Integer enterpriseId = platformSyncConfig.getEnterpriseId();
            log.info("enterpriseId:{}", enterpriseId);
            Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
            String additionalInfo = enterpriseInfo.getAdditionalInfo();
            if (enterpriseInfo == null || additionalInfo == null) {
                log.info("enterprise id {}, info is null ! {}", enterpriseId, enterpriseInfo);
                throw new ParamException("enterpriseInfo is null !");
            }

            String corpId = null;
            String corpSecret = null;
            String host = null;
            Object platformConfigArr = JSONPath.extract(additionalInfo, ARR_PLATFORM_CONFIG_PATH);
            if(Objects.isNull(platformConfigArr)){
                corpId = JSONPath.extract(additionalInfo, WEIXIN_CORP_ID_PATH).toString();
                corpSecret = JSONPath.extract(additionalInfo, WEIXIN_CORP_SECRET_PATH).toString();
                host = JSONPath.extract(additionalInfo, WEIXIN_HOST_PATH).toString();
            }else {
                corpId = JSONPath.extract(additionalInfo, ARR_WEIXIN_CORP_ID_PATH).toString();
                corpSecret = JSONPath.extract(additionalInfo, ARR_WEIXIN_CORP_SECRET_PATH).toString();
                host = JSONPath.extract(additionalInfo, ARR_WEIXIN_HOST_PATH).toString();
            }

            log.info("result: corpId:{} , corpSecret:{} , host:{}", corpId, corpSecret, host);
            if (corpId == null || corpSecret == null) {
                throw new ParamException("appId or appSecret is null !");
            }
            params.setCorpId(corpId);
            params.setCorpSecret(corpSecret);
            params.setHost(host);
            // 添加企业超级管理员信息
            EnterpriseParams enterpriseParams = new EnterpriseParams();
            enterpriseParams.setAdminUserId(enterpriseInfo.getAdminUserId());
            enterpriseParams.setPlatformId(enterpriseInfo.getPlatformId() + "");
            enterpriseParams.setEnterpriseId(enterpriseId + "");
            params.setEnterpriseParams(enterpriseParams);
            return params;
        } catch (Exception e) {
            log.error("获取企微信息异常 {}", e);
            throw new ParamException("get enterpriseInfo error !");
        }
    }

    private List<YfyUser> buildUsers(List<QiyeWeixinUser> weixinUsers, PlatformSyncConfig platformSyncConfig) {
        String valueBox = platformSyncConfig.getValueBox();
        ThirdPartyValueBox thirdPartyValueBox = JSON.parseObject(valueBox, ThirdPartyValueBox.class);
        List<YfyUser> yfyUsers = StreamUtils.map(weixinUsers, user -> {
            String email = null;
            String phone = getCustomIdentifierValue(user.getExtAttributes(), CustomIdentifierEnum.YFYPHONE.getDesc());
            if (phone != null){
                user.setMobile(phone);
            }
            if(StringUtils.isBlank(user.getEmail())){
                email = getCustomIdentifierValue(user.getExtAttributes(), CustomIdentifierEnum.YFYEMAIL.getDesc());
                if (email == null){
                    String emailSuffix = thirdPartyValueBox.getUserConfigDto().getEmailSuffix();
                    if(StringUtils.isNotEmpty(emailSuffix)){
                        email = user.getUserid() + "@" + emailSuffix;
                    } else {
                        log.info("email is null! user info {}", JSON.toJSONString(user));
                    }
                }
            }else {
                email = user.getEmail();
            }

            //激活状态: 1=已激活，2=已禁用，4=未激活，5=退出企业
            if (user.getStatus() != 1){
                return null;
            }

            YfyUser yfyUser = YfyUser.builder()
                    .id(user.getUserid())
                    .fullName(SyncStringUtils.formatString(user.getName()))
                    .spaceTotal(thirdPartyValueBox.getUserSpace() * GB)
                    .email(email)
                    .createTime(new Date())
                    .status(thirdPartyValueBox.getUserConfigDto().isActivate() ? "1" : "3")
                    .departmentIds(StreamUtils.map(user.getDepartment(), String::valueOf))
                    .phone(thirdPartyValueBox.getUserConfigDto().isSyncPhone() ? user.getMobile() : null)
                    .forced(true)
                    .build();

            // 5为企业微信里的退出企业状态，2为开放平台用户删除状态，连接器不做删除
            /*if (user.getStatus() == 5){
                yfyUser.setStatus("2");
            }*/

            return yfyUser;
        });
        yfyUsers.removeAll(Collections.singleton(null));
        log.info("从企业微信获取并转换的用户总数为{}, 转换后用户为 {}", yfyUsers.size(), yfyUsers);
        return yfyUsers;
    }

    private List<YfyDepartment> buildDepartments(List<QiyeWeixinDepartment> weixinDepartments, PlatformSyncConfig platformSyncConfig) {
        String valueBox = platformSyncConfig.getValueBox();
        ThirdPartyValueBox thirdPartyValueBox = JSON.parseObject(valueBox, ThirdPartyValueBox.class);
        List<YfyDepartment> YfyDepartments = StreamUtils.map(weixinDepartments, weixinDepartment -> {
            YfyDepartment yfyDepartment = YfyDepartment.builder()
                    .id(String.valueOf(weixinDepartment.getId()))
                    .name(SyncStringUtils.formatString(weixinDepartment.getName()))
                    .directorId(CollectionUtils.isEmpty(weixinDepartment.getDirectorIds()) ? null : weixinDepartment.getDirectorIds().get(0))
                    .spaceTotal(thirdPartyValueBox.getDeptSpace().longValue())
                    .publicFolder(thirdPartyValueBox.getDepConfigDto().isPublicFolder())
                    .collabAutoAccepted(thirdPartyValueBox.getDepConfigDto().isCollabAutoAccepted())
                    .order((long) weixinDepartment.getOrder())
                    .status("1")
                    .build();

            if (isRoot(weixinDepartment.getId())) {
                return null;
            }
            if (isRoot(weixinDepartment.getParentId())) {
                yfyDepartment.setParentId(null);
            } else {
                yfyDepartment.setParentId(String.valueOf(weixinDepartment.getParentId()));
            }
            log.info("从企业微信获取部门数据并且重新组装后的 yfyDepartment {} department {}", yfyDepartment, weixinDepartment);
            return yfyDepartment;
        });
        YfyDepartments.removeAll(Collections.singleton(null));
        return YfyDepartments;
    }

    private boolean isRoot(long deptId) {
        return deptId == nacosConfig.getCustomWeiXinTopDept();
    }

    public String getCustomIdentifierValue(JSONArray extAttributes, String customIdentifier){
        for (Object extAttribute : extAttributes) {
            JSONObject jsonObject = (JSONObject) extAttribute;
            String name = jsonObject.getString(WEIXIN_EXTATTR_PARAMETER_NAME);
            if(customIdentifier.equals(name)){
                 return jsonObject.getString(WEIXIN_EXTATTR_PARAMETER_VALUE);
            }
        }
        return null;
    }
}