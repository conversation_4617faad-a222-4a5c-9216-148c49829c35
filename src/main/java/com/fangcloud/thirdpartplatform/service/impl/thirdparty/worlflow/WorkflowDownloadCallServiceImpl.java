package com.fangcloud.thirdpartplatform.service.impl.thirdparty.worlflow;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformEnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformThirdpartyMessageMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.db.model.User;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdPartyCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.DownloadWorkflow;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.DownloadWorkflowCall;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.ark.WorkflowApprovalBean;
import com.fangcloud.thirdpartplatform.helper.ArkClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyCommonService;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class WorkflowDownloadCallServiceImpl implements ThirdpartyExecuteService {

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PlatformThirdpartyMessageMapper platformThirdpartyMessageMapper;

    @Autowired
    private ArkClientHelper arkClientHelper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private ThirdpartyCommonService thirdpartyCommonService;


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        DownloadWorkflow downloadWorkflow = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getData()), DownloadWorkflow.class);

        DownloadWorkflowCall downloadWorkflowCall = downloadWorkflowConvertToDownloadWorkflowCall(downloadWorkflow);

        ThirdPartyCall thirdPartyCall = ThirdPartyCall.builder()
                .type(ThirdpartyCallbackTypeEnum.DOWNLOAD_CALLBACK.getDesc())
                .data(downloadWorkflowCall)
                .build();

        return ResultUtils.getSuccessResult(thirdPartyCall);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {

        DownloadWorkflowCall downloadWorkflowCall = JSON.parseObject(JSON.toJSONString(thirdpartyCallbackParams.getData()), DownloadWorkflowCall.class);

        // 校验回调记录
        thirdpartyCommonService.checkCallbackResult(
                downloadWorkflowCall.getUniqueId(),
                downloadWorkflowCall.getEnterpriseId(),
                downloadWorkflowCall.getReviewType(),
                thirdpartyCallbackParams.isReviewResult());

        WorkflowApprovalBean workflowApprovalBean = new WorkflowApprovalBean();
        workflowApprovalBean.setPass(thirdpartyCallbackParams.isReviewResult());
        workflowApprovalBean.setTaskId(Long.parseLong(downloadWorkflowCall.getTaskId()));

        boolean workflowApprovalResult = arkClientHelper.workflowApproval(workflowApprovalBean, downloadWorkflowCall.getReceiverId());

        return ResultUtils.getSuccessResult(workflowApprovalResult);
    }

    /**
     * 转化对应字段
     * @param downloadWorkflow
     * @return
     */
    private DownloadWorkflowCall downloadWorkflowConvertToDownloadWorkflowCall(DownloadWorkflow downloadWorkflow) {
        DownloadWorkflowCall downloadWorkflowCall = new DownloadWorkflowCall();

        Long enterpriseId = downloadWorkflow.getEnterpriseId();

        downloadWorkflowCall.setEnterpriseId(enterpriseId);
        downloadWorkflowCall.setReceiverId(downloadWorkflow.getReceiverId());
        downloadWorkflowCall.setTaskId(downloadWorkflow.getTaskId());
        downloadWorkflowCall.setUniqueId(downloadWorkflow.getUniqueId());
        downloadWorkflowCall.setItemName(downloadWorkflow.getItemName());

        String fileId = downloadWorkflow.getFileId();
        if(StringUtils.isEmpty(fileId)){
            String[] itemTypedId = downloadWorkflow.getItemTypedIds().get(0).split("_");
            downloadWorkflowCall.setItemType(itemTypedId[0]);
            downloadWorkflowCall.setItemId(itemTypedId[1]);
        }else {
            downloadWorkflowCall.setItemType("file");
            downloadWorkflowCall.setItemId(fileId);
        }

        Long authorId = downloadWorkflow.getAuthorId();
        ThirdpartyCustom thirdpartyCustom = new ThirdpartyCustom();
        thirdpartyCustom.setId(authorId);

        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);
        if(!Objects.isNull(platformEnterprises)){
            PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserId(platformEnterprises.getPlatformId(), authorId);
            if(!Objects.isNull(platformUser)){
                thirdpartyCustom.setCustomId(platformUser.getUserTicket());
            }
        }
        User authorUser = userMapper.queryById(authorId);
        if(!Objects.isNull(authorUser)){
            thirdpartyCustom.setName(authorUser.getFullName());
        }
        downloadWorkflowCall.setCreateUser(thirdpartyCustom);
        downloadWorkflowCall.setReviewType(ThirdpartyCallTypeEnum.WOEKFLOW_DOWNLOAD_CALL.getDesc());
        return downloadWorkflowCall;
    }

    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.WOEKFLOW_DOWNLOAD_CALL.getDesc();
    }
}
