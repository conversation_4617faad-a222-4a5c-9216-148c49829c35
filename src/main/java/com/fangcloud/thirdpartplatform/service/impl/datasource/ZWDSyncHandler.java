package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.input.DingTalkInitParams;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.ZWDClientHelper;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;

import static com.fangcloud.thirdpartplatform.helper.ZWDClientHelper.GET_ZWD_ROOT_DEPT;
import static com.sync.common.contants.SyncConstant.GB;

@Component
@Slf4j
public class ZWDSyncHandler extends AbstractDataSourceSyncHandler {

    @Autowired
    private EnterpriseServiceImpl enterpriseService;

    private static String DINGTALK_APP_KEY_PATH = "$.platform_config.dingTalk.appKey.value";
    private static String DINGTALK_APP_SECRET_PATH = "$.platform_config.dingTalk.appSecret.value";
    private static String DINGTALK_HOST_PATH = "$.platform_config.dingTalk.host.value";
    private static String DINGTALK_CORP_ID_PATH = "$.platform_config.dingTalk.corpId.value";


    private static String ARR_PLATFORM_CONFIG_PATH = "$.platform_config_arr";
    private static String ARR_DINGTALK_APP_KEY_PATH = "$.platform_config_arr[0].dingTalk.appKey.value";
    private static String ARR_DINGTALK_APP_SECRET_PATH = "$.platform_config_arr[0].dingTalk.appSecret.value";
    private static String ARR_DINGTALK_HOST_PATH = "$.platform_config_arr[0].dingTalk.host.value";
    private static String ARR_DINGTALK_CORP_ID_PATH = "$.platform_config_arr[0].dingTalk.corpId.value";



    public static void main(String[] args) throws IOException {


        DingTalkInitParams params = new DingTalkInitParams();
        params.setCorpId("196729");
        params.setHost("https://doc.jiashan.gov.cn:8443/zjding");
        params.setAppId("znzxwjgl-U8We4Z6o97V58MZaAbYn2");
        params.setAppSecret("LYZikXNT68Aoi12wY8cw1Q42Bag8JiACv517T65S");
        params.setCode("b2ec3a2e600f4c67b6bcc5d7c7eea8000d547201");

        //-------------------------------------------------------------------------------------------------
        ThirdPartyValueBox thirdPartyValueBox = new ThirdPartyValueBox();

        thirdPartyValueBox.setUserSpace(5);
        thirdPartyValueBox.setDeptSpace(50);

        DepConfigDto depConfigDto = new DepConfigDto();
        depConfigDto.setPublicFolder(true);
        depConfigDto.setCollabAutoAccepted(true);

        UserConfigDto userConfigDto = new UserConfigDto();
        userConfigDto.setEmailSuffix("360.cn");
        userConfigDto.setSyncPhone(false);
        userConfigDto.setActivate(true);

        thirdPartyValueBox.setUserConfigDto(userConfigDto);
        thirdPartyValueBox.setDepConfigDto(depConfigDto);

        String s1 = JSON.toJSONString(thirdPartyValueBox);

        PlatformSyncConfig platformSyncConfig =new PlatformSyncConfig();
        platformSyncConfig.setValueBox(s1);
        platformSyncConfig.setSourceType("");
        platformSyncConfig.setEnterpriseId(115);
        //-------------------------------------------------------------------------------------------------

        // 初始化信息
        ZWDClientHelper zwdHelper = new ZWDClientHelper(params);
        JSONArray go_a72884292c5c4d9cb27ad3905673d3ce = zwdHelper.getDeptInfo(Arrays.asList("GO_a72884292c5c4d9cb27ad3905673d3ce"));

        // 获取全量用户信息
        JSONArray allUserJSONArray = zwdHelper.getAllUserList();

        Map<String, Object> cacheMap = zwdHelper.getAllDeptCodeSet();
        // 获取全量部门信息
        JSONArray allDeptJSONArray = zwdHelper.getAllDeptList(cacheMap);


        List<YfyUser> users = buildUser(allUserJSONArray, platformSyncConfig);

        List<YfyDepartment> departments = buildDepartments(allDeptJSONArray, platformSyncConfig, cacheMap);
        for (YfyDepartment department : departments) {
            String name = department.getName();
            if(name.length()>39){
                System.out.println(name);
            }
        }

        System.out.println();
    }
    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        //获取appKey和appSecret
        DingTalkInitParams params = getDingdingInfo(platformSyncConfig);
        ZWDClientHelper zwdHelper = new ZWDClientHelper(params);
        JSONArray allUserJSONArray = zwdHelper.getAllUserList();

        if(CollectionUtils.isEmpty(allUserJSONArray)){
            throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
        }
        List<YfyUser> users = buildUser(allUserJSONArray, platformSyncConfig);
        log.info("apiSyncHandler-syncUser, user:{}, taskId:{}", JSON.toJSONString(users), taskId);
        if (CollectionUtils.isEmpty(users)) {
            return executeResult;
        }
        return syncUserList(platformSyncConfig, users, taskId);
    }

    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        DingTalkInitParams params = getDingdingInfo(platformSyncConfig);
        ZWDClientHelper zwdHelper = new ZWDClientHelper(params);
        Map<String, Object> cacheMap = zwdHelper.getAllDeptCodeSet();
        JSONArray allDeptJSONArray = zwdHelper.getAllDeptList(cacheMap);

        if (CollectionUtils.isEmpty(allDeptJSONArray)) {
            throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
        }
        List<YfyDepartment> departments = buildDepartments(allDeptJSONArray, platformSyncConfig, cacheMap);
        log.info("apiSyncHandler-syncDepartment, department:{}, taskId:{}", JSON.toJSONString(departments), taskId);
        if (CollectionUtils.isEmpty(departments)) {
            return executeResult;
        }
        return syncDepartmentList(platformSyncConfig, departments, taskId);
    }

    private static List<YfyUser> buildUser(JSONArray allUserJSONArray, PlatformSyncConfig platformSyncConfig) {
        String valueBox = platformSyncConfig.getValueBox();
        ThirdPartyValueBox thirdPartyValueBox = JSON.parseObject(valueBox, ThirdPartyValueBox.class);
        Map<String, YfyUser> yfyUserMap = new HashMap<>();
        for (Object o : allUserJSONArray) {

            JSONObject jsonObject = (JSONObject) o;
            // id
            String employeeCode = jsonObject.getString("employeeCode");

            YfyUser yfyUser = yfyUserMap.get(employeeCode);
            Set<String> set = new HashSet<>();
            if(!Objects.isNull(yfyUser)){ // 若用户已经组装过了，继续设置新的部门信息
                set.addAll(yfyUser.getDepartmentIds());
                yfyUser.setDepartmentIds(getDeptInfo(set, jsonObject));
                yfyUserMap.put(employeeCode, yfyUser);
                continue;
            }

            // 姓名
            String employeeName = jsonObject.getString("employeeName");
            // 邮箱全部自己组装
            String email = employeeCode + "@" + platformSyncConfig.getEnterpriseId() + ".com";

            List<String> deptIds = getDeptInfo(set, jsonObject);

            YfyUser build = YfyUser.builder()
                    .id(employeeCode)
                    .fullName(employeeName)
                    .spaceTotal(thirdPartyValueBox.getUserSpace() * GB)
                    .forced(true)
                    .email(email)
                    .createTime(new Date())
                    .status(thirdPartyValueBox.getUserConfigDto().isActivate() ? "1" : "3")
                    .departmentIds(deptIds)
                    .build();
            yfyUserMap.put(build.getId(), build);
        }
        List<YfyUser> list = new ArrayList<>();
        for (Map.Entry<String, YfyUser> stringYfyUserEntry : yfyUserMap.entrySet()) {
            list.add(stringYfyUserEntry.getValue());
        }
        return list;
    }

    private static List<YfyDepartment> buildDepartments(JSONArray allDeptJSONArray, PlatformSyncConfig platformSyncConfig, Map<String, Object> params) {

        String valueBox = platformSyncConfig.getValueBox();
        ThirdPartyValueBox thirdPartyValueBox = JSON.parseObject(valueBox, ThirdPartyValueBox.class);

        String rootDeptCode = (String) params.get(GET_ZWD_ROOT_DEPT);
        List<YfyDepartment> list = new ArrayList<>();
        for (Object o : allDeptJSONArray) {
            JSONObject jsonObject = (JSONObject) o;
            YfyDepartment yfyDepartment = new YfyDepartment();
            // id
            String organizationCode = jsonObject.getString("organizationCode");
            // 名称
            String organizationName = jsonObject.getString("organizationName");
            if(organizationName.length() <= 40){
                yfyDepartment.setName(organizationName);
            }else {// organizationName可能会超过长度限制
                String shortName =  jsonObject.getString("shortName");
                yfyDepartment.setName(shortName);
            }

            // 父部门id
            String parentCode = jsonObject.getString("parentCode");
            // 负责人列表，以｜分割
            String responsibleEmployeeCodes = jsonObject.getString("responsibleEmployeeCodes");

            // 排序
            Long displayOrder = jsonObject.getLong("displayOrder");

            yfyDepartment.setId(organizationCode);


            //将次顶级部门挂到顶级部门下
            if(parentCode.equals(rootDeptCode)){
                yfyDepartment.setParentId(null);
            }else {
                yfyDepartment.setParentId(parentCode);
            }

            if(StringUtils.isNotEmpty(responsibleEmployeeCodes)){
                String[] split = responsibleEmployeeCodes.split("\\|");
                yfyDepartment.setDirectorId(split[0]);
            }

            yfyDepartment.setSpaceTotal(thirdPartyValueBox.getDeptSpace().longValue());
            yfyDepartment.setPublicFolder(thirdPartyValueBox.getDepConfigDto().isPublicFolder());
            yfyDepartment.setCollabAutoAccepted(thirdPartyValueBox.getDepConfigDto().isCollabAutoAccepted());
            yfyDepartment.setOrder(1000000000L - displayOrder);
            yfyDepartment.setStatus("1");
            log.info("从钉钉获取部门数据并且重新组装后的 yfyDepartment {} department {}", yfyDepartment, jsonObject);
            list.add(yfyDepartment);
        }
        return list;
    }

    private static List<String> getDeptInfo(Set<String> departmentIds, JSONObject jsonObject) {
        JSONArray govEmployeePositions = jsonObject.getJSONArray("govEmployeePositions");
        for (Object o : govEmployeePositions) {
            JSONObject govEmployeePosition = (JSONObject) o;

            // 部门id
            String organizationCode = govEmployeePosition.getString("organizationCode");
            if(StringUtils.isNotEmpty(organizationCode)){
                departmentIds.add(organizationCode);
            }
        }
        List<String> list = new ArrayList<>();
        list.addAll(departmentIds);
        return list;
    }


    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return null;
    }

    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.ZWD.getDesc();
    }

    public DingTalkInitParams getDingdingInfo(PlatformSyncConfig platformSyncConfig) {
        try {
            DingTalkInitParams dingTalkParams = new DingTalkInitParams();
            Integer enterpriseId = platformSyncConfig.getEnterpriseId();
            log.info("enterpriseId:{}", enterpriseId);

            EnterpriseParams enterpriseParams = new EnterpriseParams();
            enterpriseParams.setEnterpriseId(String.valueOf(enterpriseId));
            dingTalkParams.setEnterpriseParams(enterpriseParams);

            Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
            String additionalInfo = enterpriseInfo.getAdditionalInfo();
            if (enterpriseInfo == null || additionalInfo == null) {
                log.info("enterprise id {}, info is null ! {}", enterpriseId, enterpriseInfo);
                throw new ParamException("enterpriseInfo is null !");
            }

            String appId = null;
            String appSecret = null;
            String host = null;
            String corpId = null;
            Object platformConfigArr = JSONPath.extract(additionalInfo, ARR_PLATFORM_CONFIG_PATH);
            if(Objects.isNull(platformConfigArr)){
                appId = JSONPath.extract(additionalInfo, DINGTALK_APP_KEY_PATH).toString();
                appSecret = JSONPath.extract(additionalInfo, DINGTALK_APP_SECRET_PATH).toString();
                host = JSONPath.extract(additionalInfo, DINGTALK_HOST_PATH).toString();
                corpId = JSONPath.extract(additionalInfo, DINGTALK_CORP_ID_PATH).toString();
            }else {
                appId = JSONPath.extract(additionalInfo, ARR_DINGTALK_APP_KEY_PATH).toString();
                appSecret = JSONPath.extract(additionalInfo, ARR_DINGTALK_APP_SECRET_PATH).toString();
                host = JSONPath.extract(additionalInfo, ARR_DINGTALK_HOST_PATH).toString();
                corpId = JSONPath.extract(additionalInfo, ARR_DINGTALK_CORP_ID_PATH).toString();
            }
            log.info("result: appId:{} , appSecret:{} , host:{}, corpId:{}", appId, appSecret, host, corpId);
            if (appId == null || appSecret == null) {
                throw new ParamException("appId or appSecret is null !");
            }
            dingTalkParams.setAppId(appId);
            dingTalkParams.setAppSecret(appSecret);
            dingTalkParams.setHost((null == host) ?  "" : host);
            dingTalkParams.setCorpId(corpId);
            return dingTalkParams;
        } catch (Exception e) {
            log.error("获取钉钉信息异常 {}", e);
            throw new ParamException("get enterpriseInfo error !");
        }
    }

}
