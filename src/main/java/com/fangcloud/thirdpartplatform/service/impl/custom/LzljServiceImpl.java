package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.APIParamConfigDto;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.utils.HmacSHAUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 泸州老窖免密登录
 */
@Component
@Slf4j
public class LzljServiceImpl implements ApiExecuteService {

    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private OpenClientHelper openClientHelper;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    private static final String API_HOST = "https://openapi.yonyoucloud.com";
    private static final String ACCESS_TOKEN_URL = "%s/token?appid=%s&secret=%s";

    private static final String ACCESS_TOKEN_PATH = "$.data.access_token";

    private static final String GET_URL_INFO_URL = "%s/certified/userInfo/%s?access_token=%s";
    private static final String GET_URL_INFO_URL_PATH = "$.data.mobile";
    private static final String APPID = "20b486cefc83889f";
    private static final String SECRET = "4b8f340cb3b127950bfb4c632d355c5ab5330bb1bd7b4e7307ccf68217e5";

    private static final String GET_HOST_URL = "get_host_url";
    private static final String GET_TOKEN_URL = "get_token_url";
    private static final String GET_USER_ID_URL = "get_user_id_url";

    private static final String GET_USER_INFO_URL_ONE = "get_user_info_url_one";
    private static final String GET_USER_INFO_URI = "get_user_info_uri";
    private static final String TOKEN_URL = "tokenUrl";
    private static final String GATEWAY_URL = "gatewayUrl";

    private static final String GET_TENANT_ID_PATH = "tenantId";

    private static final String APP_KET_PATH = "appKey";
    private static final String APP_SECRET_PATH = "appSecret";
    private static final String USER_ID_PATH = "$.data.yhtUserId";
    private static final String ACCESS_TOKEN = "$.data.access_token";
    private static final String TOKEN_URL_PATH = "$tokenUrl";
    private static final String GATEWAY_URL_PATH = "$gatewayUrl";

    private static final String USER_INFO_PATH = "$.data.data[0].mobile";

    private static final String DATA_PATH = "$.data";

    private static final String GET_TOKEN_PATH = "get_token_path";


    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        String code = request.getParameter("code");
        String userConfigId = configInfo.getUserConfigId();
        PlatformSyncConfig platformSyncConfig = platformSyncConfigMapper.selectByPrimaryKey(Integer.valueOf(userConfigId));
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        List<APIParamConfigDto> paramConfig = apiConfigValueBox.getApiConfig().getParamConfig();
        Map<String, String> params = getParams(paramConfig);
        getHost(params);
        String accessToken = getAccessToken(params);
        String phone = getPhoneByCode(params, accessToken, code);
        if (StringUtils.isBlank(phone)) {
            return null;
        }
        if (phone.contains(SyncTaskConstants.USER_PHONE_CN)) {
            phone = phone.substring(Math.max(0, phone.length() - 11));
        }
        String clientId = configInfo.getClientId();
        String enterpriseId = configInfo.getEnterpriseId();
        String platformId = configInfo.getPlatformId();
        EnterpriseParams enterpriseParams = new EnterpriseParams();
        enterpriseParams.setEnterpriseId(enterpriseId);
        enterpriseParams.setIdentifier(phone);
        enterpriseParams.setPlatformId(platformId);
        enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
        enterpriseParams.setClientId(clientId);
        String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
        log.info("lzlj getLoginUrl {}", loginUrl);
        return loginUrl;
    }

    private String getPhoneByCode(Map<String, String> params, String accessToken, String code) {
        String userId = getUserIdByCode(params, accessToken, code);
        String url = params.get(GET_USER_INFO_URL_ONE);
        String getUserInfoUrl = String.format(url, params.get(GATEWAY_URL), accessToken);
        return httpPost(getUserInfoUrl, getPhoneByCodePostData(userId), USER_INFO_PATH);
    }

    private String getPhoneByCodePostData(String userId) {
        return "{\"userId\":[\"" + userId + "\"]}";
    }

    private String getUserIdByCode(Map<String, String> params, String accessToken, String code) {
        String getUserIdUrl = params.get(GET_USER_ID_URL);
        String url = String.format(getUserIdUrl, accessToken, code);
        return httpGet(url, USER_ID_PATH);
    }

    private String getAccessToken(Map<String, String> params) {
        String getTokenUrl = params.get(GET_TOKEN_URL);
        String appKey = params.get(APP_KET_PATH);
        String appSecret = params.get(APP_SECRET_PATH);
        String tokenHost = params.get(TOKEN_URL);
        String time = String.valueOf(System.currentTimeMillis());
        String url = String.format(getTokenUrl, tokenHost, appKey, time, getSignature(appKey, appSecret, time));
        return httpGet(url, ACCESS_TOKEN);
    }

    private void getHost(Map<String, String> params) {
        String getHostUrl = params.get(GET_HOST_URL);
        String url = String.format(getHostUrl, params.get(GET_TENANT_ID_PATH));
        String httpGet = httpGet(url, DATA_PATH);
        String tokenUrl = (String) JSONPath.extract(httpGet, TOKEN_URL_PATH);
        String gatewayUrl = (String) JSONPath.extract(httpGet, GATEWAY_URL_PATH);
        params.put(TOKEN_URL, tokenUrl);
        params.put(GATEWAY_URL, gatewayUrl);
    }

    private String getSignature(String appKey, String appSecret, String time) {
        Map<String, String> params = new HashMap<>();
        // 除签名外的其他参数
        params.put("appKey", appKey);
        params.put("timestamp", time);
        try {
            String signature = HmacSHAUtils.sign(params, appSecret);
            return signature;
        } catch (Exception e) {
            log.error("getSignature error {}", e);
        }
        return null;
    }

    private Map<String, String> getParams(List<APIParamConfigDto> paramConfig) {
        Map<String, String> params = new HashMap<>();
        paramConfig.forEach(apiParamConfigDto -> {
            String name = apiParamConfigDto.getName();
            String value = apiParamConfigDto.getValue();
            params.put(name, value);
        });
        return params;
    }

    public String getLoginUrlOld(HttpServletRequest request, ConfigInfo configInfo) {
        String code = request.getHeader("code");
        String accessToken = getAccessTokenOld();
        String mobile = getUserInfoByCode(code, accessToken);
        if (StringUtils.isBlank(mobile)) {
            return null;
        }
        String clientId = configInfo.getClientId();
        String enterpriseId = configInfo.getEnterpriseId();
        String platformId = configInfo.getPlatformId();
        EnterpriseParams enterpriseParams = new EnterpriseParams();
        enterpriseParams.setEnterpriseId(enterpriseId);
        enterpriseParams.setIdentifier(mobile);
        enterpriseParams.setPlatformId(platformId);
        enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
        enterpriseParams.setClientId(clientId);
        String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
        log.info("lzlj 免密登录url {}", loginUrl);
        return loginUrl;
    }


    private String getAccessTokenOld() {
        String url = String.format(ACCESS_TOKEN_URL, API_HOST, APPID, SECRET);
        return httpGet(url, ACCESS_TOKEN_PATH);
    }

    private String getUserInfoByCode(String code, String accessToken) {
        String url = String.format(GET_URL_INFO_URL, API_HOST, code, accessToken);
        return httpGet(url, GET_URL_INFO_URL_PATH);
    }

    private String httpGet(String url, String resultPath) {
        try {
            log.info("httpGet url {}", url);
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("result {}", result);
            return JSONPath.extract(result, resultPath).toString();
        } catch (Exception e) {
            log.error("httpGet error url {}", url, e);
        }
        return null;
    }

    private String httpPost(String url, String postData, String resultPath) {
        try {
            log.info("httpPost url {} , postData {}", url, postData);
            Response response = httpClientHelper.postResponse(url, postData);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("result {}", result);
            return JSONPath.extract(result, resultPath).toString();
        } catch (Exception e) {
            log.error("httpPost error url {}", url, e);
        }
        return null;
    }

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        getHost(headMap);
        String accessToken = getAccessToken(headMap);
        if (accessToken == null) {
            return jsonObject.toJSONString();
        }
        String userInfoUri = headMap.get(GET_USER_INFO_URI);
        int pagaNum = 1;
        while (true) {
            String userInfoUrl = String.format(userInfoUri, headMap.get(GATEWAY_URL), accessToken, pagaNum);
            String userInfo = httpGet(userInfoUrl, DATA_PATH);
            if (userInfo == null) {
                jsonArray.clear();
                break;
            }
            JSONArray extract = (JSONArray) JSONPath.extract(userInfo, "$.data.content");
            if (extract.isEmpty()) {
                break;
            }
            jsonArray.addAll(extract);
            pagaNum++;
            Integer total = (Integer) JSONPath.extract(userInfo, "$.data.totalPages");
            if (pagaNum > total) {
                break;
            }
        }
        jsonObject.put("data", jsonArray);
        return jsonObject.toJSONString();
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.LZLJ.getDesc();
    }
}
