package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.constant.login.LoginConfigConstants;
import com.fangcloud.thirdpartplatform.constant.login.LoginParamEnum;
import com.fangcloud.thirdpartplatform.constant.login.LoginSourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformLoginConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.factory.PlatformLoginConfigFactory;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginEnterpriseInfoResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginTokenResponse;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.fangcloud.thirdpartplatform.helper.ChameleonClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.PlatformLoginService;
import com.fangcloud.thirdpartplatform.service.impl.custom.H3cServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.datasource.CodeScriptSyncHandler;
import com.fangcloud.thirdpartplatform.source.ldap.LdapConfigProperties;
import com.fangcloud.thirdpartplatform.source.ldap.LdapTemplateFactory;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.fangcloud.thirdpartplatform.utils.CookieUtils;
import com.fangcloud.thirdpartplatform.utils.PlaceholderReplaceUtils;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import com.google.common.collect.Lists;
import com.sync.common.entity.dto.LoginUserDto;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.service.CaptchaCheckImpl;
import com.sync.common.service.LoginServiceImpl;
import com.sync.common.utils.RsaUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringSubstitutor;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PlatformLoginServiceImpl implements PlatformLoginService {

    @Resource
    private RedisStringManager redisStringManager;

    @Resource
    private LoginServiceImpl loginServiceImpl;

    @Resource
    private CaptchaCheckImpl captchaCheckImpl;

    @Resource
    private PlatformLoginConfigMapper platformLoginConfigMapper;

    @Resource
    private EnterpriseServiceImpl enterpriseService;

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    private APIHelper apiHelper;

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private H3cServiceImpl h3cService;

    @Resource
    private ChameleonClientHelper chameleonClientHelper;

    @Resource
    private CodeScriptSyncHandler codeScriptSyncHandler;


    @Override
    public String loginCheck(HttpServletRequest request, HttpServletResponse response) throws IOException {
        Cookie[] cookies = request.getCookies();
        String enterpriseIdEncode = request.getParameter("enterprise_id");
        String serviceEncode = request.getParameter("service");

        if(Objects.isNull(cookies) || cookies.length == 0 || StringUtils.isEmpty(enterpriseIdEncode) || StringUtils.isEmpty(serviceEncode)){
            return null;
        }
        String service = URLDecoder.decode(serviceEncode, "UTF-8");
        List<String> uidCollect = Arrays.stream(cookies)
                .filter(p -> LoginConfigConstants.PLATFORM_LOGIN_COOKIE_UID.equals(p.getName()))
                .map(p -> p.getValue())
                .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(uidCollect)){
            log.info("check cookie info error !");
            return null;
        }
        String uid = uidCollect.get(0);


        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        PlatformLoginConfig platformLoginConfig = platformLoginConfigMapper.selectByEnterpriseId(Integer.valueOf(enterpriseId)).get(0);

        PlatformLoginConfigValueBox platformLoginConfigValueBox = JSONObject.parseObject(platformLoginConfig.getValueBox(), PlatformLoginConfigValueBox.class);

        //校验跳转service
        checkService(service, platformLoginConfigValueBox.getLoginPageConfigDto().getClientBaseUrl());
        log.info("loginCheck enterpriseId {} uid {} service {}", enterpriseId, uid, service);

        String uidKey = LoginConfigConstants.PLATFORM_LOGIN_REDIS_PREFIX_UID + CommonConstants.SPLIT_UNDERLINE + enterpriseId + CommonConstants.SPLIT_UNDERLINE + uid;

        String value = redisStringManager.get(uidKey);

        if(StringUtils.isEmpty(value)){
            log.info("check login status error!");
            return null;
        }

        // 生成ticket
        String ticket = createUidTicket(uid, value);

        String loginUrl = service.contains("?") ? service + "&ticket=" + ticket :  service + "?ticket=" + ticket;

        log.info("loginCheck loginUrl为：{}", loginUrl);
        return loginUrl;
    }

    @Override
    public Result<PlatformLoginResponse> internalLogin(HttpServletRequest request, HttpServletResponse response) throws Exception{
        String domain = extractSubdomain(request.getServerName());
        if (StringUtils.isBlank(domain)) {
            domain = customNacosConfig.getBaseDomain();
        }
        LoginUserDto user = loginServiceImpl.getLoginUser(request);

        String service = URLDecoder.decode(user.getService(), "UTF-8");

        String enterpriseIdEncode = request.getParameter("enterprise_id");
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);

        log.info("loginUserDto info : {}, enterpriseId :{}", JSON.toJSONString(user), enterpriseId);
        PlatformLoginConfig platformLoginConfig = platformLoginConfigMapper.selectByEnterpriseId(Integer.valueOf(enterpriseId)).get(0);

        PlatformLoginConfigValueBox platformLoginConfigValueBox = JSONObject.parseObject(platformLoginConfig.getValueBox(), PlatformLoginConfigValueBox.class);

        // 是否需要图片验证码
        boolean needImgCaptcha = platformLoginConfigValueBox.getLoginValidateConfigDto().getNeedImgCaptcha();
        // 失败次数key
        String errorCountKey = LoginConfigConstants.PLATFORM_LOGIN_REDIS_PREFIX_ERROR_COUNT + CommonConstants.SPLIT_UNDERLINE + enterpriseId + CommonConstants.SPLIT_UNDERLINE + user.getUsername();

        // 校验图片验证码
        if(!checkImgCaptcha(request, user, needImgCaptcha, errorCountKey)){
            return ResultUtils.getFailedResult(ResponseCodeEnum.CAPTCHA_ERROR, ResponseCodeEnum.CAPTCHA_ERROR.getDesc());
        }

        YfyUser yfyUser = getUserInfo(buildDataMap(user), platformLoginConfig.getLoginSourceType(), platformLoginConfigValueBox, Integer.parseInt(enterpriseId));

        // 登陆失败后操作
        if(Objects.isNull(yfyUser)){
            return ResultUtils.getFailedResult(
                    ResponseCodeEnum.INPUT_ERROR,
                    PlatformLoginResponse.builder().needImgCaptcha(checkErrorCount(needImgCaptcha, errorCountKey)).build());
        }

        // 登陆成功后操作
        String ticket = cacheYfyUser(yfyUser, response, errorCountKey, service, enterpriseId, domain);

        if (service.contains("?")) {
            service = service + "&ticket=" + ticket;
        } else {
            service = service + "?ticket=" + ticket;
        }
        service = URLEncoder.encode(service, "UTF-8");
        log.info("service:{}", service);

        return ResultUtils.getSuccessResult(PlatformLoginResponse.builder().service(URLDecoder.decode(service, "UTF-8")).build());
    }

    @Override
    public Result<String> logout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        Cookie[] cookies = request.getCookies();
        // 从cookie中获取用户id
        List<String> cookieCollect = Arrays.stream(cookies)
                .filter(p -> LoginConfigConstants.PLATFORM_LOGIN_COOKIE_UID.equals(p.getName()))
                .map(p -> p.getValue())
                .collect(Collectors.toList());
        String uid = null;
        if(!CollectionUtils.isEmpty(cookieCollect)){
            uid = cookieCollect.get(0);
        }

        String serviceEncode = request.getParameter("service");
        String enterpriseIdEncode = request.getParameter("enterprise_id");
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        String service;
        if(StringUtils.isEmpty(serviceEncode)){
            PlatformLoginConfig platformLoginConfig = platformLoginConfigMapper.selectByEnterpriseId(Integer.valueOf(enterpriseId)).get(0);
            PlatformLoginConfigValueBox platformLoginConfigValueBox = JSONObject.parseObject(platformLoginConfig.getValueBox(), PlatformLoginConfigValueBox.class);
            service = platformLoginConfigValueBox.getLoginPageConfigDto().getClientBaseUrl();
        }else {
            service = URLDecoder.decode(serviceEncode,"UTF-8");
        }

        // 若cookie中存在uid，将登陆状态删除
        if(StringUtils.isNotEmpty(uid)){
            // 将缓存的用户信息清除
            String uidKey = LoginConfigConstants.PLATFORM_LOGIN_REDIS_PREFIX_UID + CommonConstants.SPLIT_UNDERLINE + enterpriseId + CommonConstants.SPLIT_UNDERLINE + uid;
            redisStringManager.delete(uidKey);
            // 清除cookie
            CookieUtils.deleteAllCookie(response, request.getCookies());
        }
        log.info("redirect url {}", service);

        response.sendRedirect(service);
        return null;
    }

    @Override
    public Result<PlatformLoginEnterpriseInfoResponse> getEnterpriseInfo(String enterpriseIdEncode) {
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        List<PlatformLoginConfig> platformLoginConfigs = platformLoginConfigMapper.selectByEnterpriseId(Integer.valueOf(enterpriseId));
        Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(Integer.valueOf(enterpriseId));
        PlatformLoginConfigValueBox platformLoginConfigValueBox = JSONObject.parseObject(platformLoginConfigs.get(0).getValueBox(), PlatformLoginConfigValueBox.class);
        String loginSourceType = platformLoginConfigs.get(0).getLoginSourceType();
        String platformConfig = getPlatformConfig(Integer.valueOf(enterpriseId));

        String domainString = chameleonClientHelper.getDomainInfo(enterpriseInfo.getPlatformId());

        PlatformLoginEnterpriseInfoResponse response = PlatformLoginConfigFactory.buildPlatformLoginEnterpriseInfoResponse(
                platformLoginConfigValueBox, platformConfig, loginSourceType, enterpriseInfo.getAdditionalInfo(), domainString);
        return ResultUtils.getSuccessResult(response);
    }

    @Override
    public Result<YfyUser> getUserInfo(String token) {
        String yfyUserJson = redisStringManager.get(token);
        if(StringUtils.isEmpty(yfyUserJson)){
            throw new ParamException("token is expired !");
        }
        return ResultUtils.getSuccessResult(JSONObject.parseObject(yfyUserJson, YfyUser.class));
    }

    @Override
    public String serviceValidate(String ticket) {
        String casTicket = LoginConfigConstants.PLATFORM_LOGIN_REDIS_PREFIX_CAS_TICKET + ticket;
        String yfyUserJson = redisStringManager.get(casTicket);
        redisStringManager.delete(casTicket);

        log.info("yfyUserJson:{}", yfyUserJson);
        if(StringUtils.isEmpty(yfyUserJson)){
            throw new ParamException("service validate error");
        }
        String casXml = createCasXml(JSONObject.parseObject(yfyUserJson, YfyUser.class));
        log.info("casXml:{}",casXml);
        return casXml;
    }

    @Override
    public Result<PlatformLoginTokenResponse> getToken(String enterpriseIdEncode, String clientId, String clientSecret, String ticket) {
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        checkClientIdAndClientSecret(enterpriseId, clientId, clientSecret);
        String yfyUserJson = null;
        if(ticket.startsWith(LoginConfigConstants.PLATFORM_LOGIN_TICKET_PREFIX)){
            String yfyUserJsonKey = LoginConfigConstants.PLATFORM_LOGIN_REDIS_PREFIX_CAS_TICKET + ticket;
            yfyUserJson = redisStringManager.get(yfyUserJsonKey);
            redisStringManager.delete(yfyUserJsonKey);
            if(StringUtils.isEmpty(yfyUserJson)){
                throw new ParamException("ticket is expired !");
            }
        }else {
            yfyUserJson = getH3CUserInfo(enterpriseId, ticket);
        }
        String token = UUID.randomUUID().toString();
        PlatformLoginTokenResponse response = PlatformLoginTokenResponse
                .builder()
                .token(token)
                .expires_in(System.currentTimeMillis() + 30 * 1000)
                .build();
        redisStringManager.set(token, yfyUserJson , 30L);
        return ResultUtils.getSuccessResult(response);
    }

    private String getH3CUserInfo(String enterpriseId, String ticket) {

        /**
         * {
         *     "Domain": "",
         *     "enable": "true",
         *     "hostname": "http://*************:8888/remote.php/webdav",
         *     "password": "123456",
         *     "username": "stu"
         * }
         */
        try {
            ticket = URLDecoder.decode(ticket, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("url decode ticket error!");
        }
        String decodeJson = Base64Utils.decode(ticket);
        String username = (String) JSONPath.extract(decodeJson, "$.username");
        String domain = (String) JSONPath.extract(decodeJson, "$.Domain");
        if(!StringUtils.isEmpty(domain)){
            username = domain + "\\" + username;
        }

        LoginUserDto user = LoginUserDto.builder().username(username).password((String) JSONPath.extract(decodeJson, "$.password")).build();
        PlatformLoginConfig platformLoginConfig = platformLoginConfigMapper.selectByEnterpriseId(Integer.valueOf(enterpriseId)).get(0);

        PlatformLoginConfigValueBox platformLoginConfigValueBox = JSONObject.parseObject(platformLoginConfig.getValueBox(), PlatformLoginConfigValueBox.class);

        YfyUser yfyUser = getUserInfo(buildDataMap(user), platformLoginConfig.getLoginSourceType(), platformLoginConfigValueBox, Integer.parseInt(enterpriseId));

        return JSON.toJSONString(yfyUser);
    }

    @Override
    public Result<PlatformLoginResponse> needCaptcha(String username, String enterpriseIdEncode) {
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        PlatformLoginConfig platformLoginConfig = platformLoginConfigMapper.selectByEnterpriseId(Integer.valueOf(enterpriseId)).get(0);
        PlatformLoginConfigValueBox platformLoginConfigValueBox = JSONObject.parseObject(platformLoginConfig.getValueBox(), PlatformLoginConfigValueBox.class);
        Boolean needImgCaptcha = platformLoginConfigValueBox.getLoginValidateConfigDto().getNeedImgCaptcha();

        if(needImgCaptcha){
            // 失败次数key
            String errorCountKey = LoginConfigConstants.PLATFORM_LOGIN_REDIS_PREFIX_ERROR_COUNT + CommonConstants.SPLIT_UNDERLINE + enterpriseId + CommonConstants.SPLIT_UNDERLINE + username;

            String errorCountStr = redisStringManager.get(errorCountKey);
            Integer errorCount = StringUtils.isEmpty(errorCountStr) ? 0 : Integer.valueOf(errorCountStr);

            log.info("login userName : {}, errorCount : {}",username, errorCount);
            if(errorCount <= 3){
                needImgCaptcha = false;
            }
        }
        return ResultUtils.getSuccessResult(PlatformLoginResponse.builder().needImgCaptcha(needImgCaptcha).build());

    }

    /**
     * 构建入参map
     * @param user
     * @return
     */
    private static Map<String, String> buildDataMap(LoginUserDto user) {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put(LoginParamEnum.USER_NAME.getType(), user.getUsername());
        dataMap.put(LoginParamEnum.PASSWORD.getType(), user.getPassword());
        dataMap.put(LoginParamEnum.SMS_CODE.getType(), user.getSmsCode());
        log.info("loginDataMapInfo {}", JSON.toJSONString(dataMap));
        return dataMap;
    }

    /**
     * 检查失败后检查失败次数
     * @param needImgCaptcha
     * @param errorCountKey
     * @return
     */
    private boolean checkErrorCount(boolean needImgCaptcha, String errorCountKey) {
        if(needImgCaptcha){
            // 记录登陆失败次数
            long increment = redisStringManager.increment(errorCountKey, 1);
            // 若失败超过3次需要验证码
            if(increment <= 3){
                needImgCaptcha = false;
            }
        }
        return needImgCaptcha;
    }

    /**
     * 缓存用户信息，生成ticket
     * @param yfyUser
     * @param response
     * @param errorCountKey
     * @param service
     * @param enterpriseId
     * @return
     */
    private String cacheYfyUser(YfyUser yfyUser, HttpServletResponse response, String errorCountKey, String service, String enterpriseId, String baseDomain) {

        // 生成放入cookie的随机值
        String cookUidValue = DigestUtils.md5DigestAsHex((UUID.randomUUID().toString() + System.currentTimeMillis()).getBytes());

        String uidKey = LoginConfigConstants.PLATFORM_LOGIN_REDIS_PREFIX_UID + CommonConstants.SPLIT_UNDERLINE + enterpriseId + CommonConstants.SPLIT_UNDERLINE + cookUidValue;

        // 1.将用户id为key，用户信息为值放入redis缓存1天时间  用户信息为yfyUser的json
        redisStringManager.set(uidKey, JSON.toJSONString(yfyUser), 60 * 60 * 24L );

        // 2.生成ticket为key，将用户id作为值放入redis缓存1分钟
        String ticket = createUidTicket(cookUidValue, JSON.toJSONString(yfyUser));
        log.info("ticket : {}, cookUidValue : {}, baseDomain : {}", ticket, cookUidValue, baseDomain);
        // 3.将用户id和service放入cookie中
        Cookie cookieUid = new Cookie(LoginConfigConstants.PLATFORM_LOGIN_COOKIE_UID, cookUidValue);
        if(StringUtils.isNotEmpty(baseDomain)){
            baseDomain = baseDomain.startsWith(".") ? baseDomain.substring(1) : baseDomain;
            cookieUid.setDomain(baseDomain);
        }
        //根据企业id查询全局配置，判断是否setCookie,默认true
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId);
        String isSetCookie = configMap.get(PlatformGlobalConfigKeyEnum.INTERNAL_LOGIN_COOKIE.getKey());
        if (Boolean.getBoolean(isSetCookie)) {
            response.addCookie(cookieUid);  
        }
        // 4.登陆成功后，清空错误次数
        redisStringManager.delete(errorCountKey);
        return ticket;
    }

    /**
     * 校验图片验证码
     * @param request
     * @param user
     * @param needImgCaptcha
     * @param errorCountKey
     */
    private boolean checkImgCaptcha(HttpServletRequest request, LoginUserDto user, boolean needImgCaptcha, String errorCountKey) {
        if(needImgCaptcha){
            String errorCountStr = redisStringManager.get(errorCountKey);
            Integer errorCount = StringUtils.isEmpty(errorCountStr) ? 0 : Integer.valueOf(errorCountStr);

            if(errorCount > 3){
                log.info("need check imgCaptcha,userVerCode : {}, sessionVerCode : {}", user.getVerCode(), request.getSession().getAttribute("captcha"));

                return captchaCheckImpl.checkCaptcha(request, user.getVerCode());
            }
        }
        return true;
    }



    private void checkClientIdAndClientSecret(String enterpriseId, String clientId, String clientSecret) {
        List<PlatformLoginConfig> platformLoginConfigs = platformLoginConfigMapper.selectByEnterpriseId(Integer.valueOf(enterpriseId));
        PlatformLoginConfigValueBox platformLoginConfigValueBox = JSONObject.parseObject(platformLoginConfigs.get(0).getValueBox(), PlatformLoginConfigValueBox.class);
        LoginSecretConfigDto loginSecretConfigDto = platformLoginConfigValueBox.getLoginSecretConfigDto();
        Assert.isTrue((clientId.equals(loginSecretConfigDto.getClientId()) && clientSecret.equals(loginSecretConfigDto.getClientSecret())),"client info error !");
    }


    /**
     * 根据企业配置获取用户信息
     * @param dataMap
     * @param loginSourceType
     * @param platformLoginConfigValueBox
     * @return
     */
    private YfyUser getUserInfo(Map<String, String> dataMap, String loginSourceType, PlatformLoginConfigValueBox platformLoginConfigValueBox, Integer enterpriseId) {
        YfyUser yfyUser = null;
        try {
            if (loginSourceType.contains(LoginSourceTypeEnum.OAUTH2.getDesc())){
                LoginSourceOAUTH2ConfigDto loginSourceOAUTH2ConfigDto = platformLoginConfigValueBox.getLoginSourceOAUTH2ConfigDto();
                if (loginSourceOAUTH2ConfigDto.getOauth2Config() != null){
                    placeholderReplace(dataMap,loginSourceOAUTH2ConfigDto.getOauth2Config().getParamConfig());
                    APIConfigValueBox.APIConfig oauth2Config = loginSourceOAUTH2ConfigDto.getOauth2Config();
                    String oauth2Result = apiSyncHandler.executeLoginApi(null,oauth2Config, null, enterpriseId, null);
                    String apiResult = apiSyncHandler.executeLoginApi(oauth2Result, loginSourceOAUTH2ConfigDto.getApiConfig(), null, enterpriseId, JSON.toJSONString(dataMap));
                    JSONObject resultDataByPath = (JSONObject) apiSyncHandler.getResultDataByPath(loginSourceOAUTH2ConfigDto.getApiConfig().getResultPath(), apiResult);
                    yfyUser = buildLoginUser(resultDataByPath, loginSourceOAUTH2ConfigDto.getApiConfig());
                }else {
                    placeholderReplace(dataMap,loginSourceOAUTH2ConfigDto.getApiConfig().getParamConfig());
                    APIConfigValueBox.APIConfig apiConfig = loginSourceOAUTH2ConfigDto.getApiConfig();
                    String apiResult = apiSyncHandler.executeLoginApi(null, loginSourceOAUTH2ConfigDto.getApiConfig(), null, enterpriseId, null);
                    JSONObject resultDataByPath = (JSONObject) apiSyncHandler.getResultDataByPath(apiConfig.getResultPath(), apiResult);
                    yfyUser = buildLoginUser(resultDataByPath, loginSourceOAUTH2ConfigDto.getApiConfig());
                }
            }else if (loginSourceType.contains(LoginSourceTypeEnum.LDAP.getDesc())){
                LoginSourceLDAPConfigDto loginSourceLDAPConfigDto = platformLoginConfigValueBox.getLoginSourceLDAPConfigDto();
                LdapConfigProperties properties = LdapConfigProperties.builder()
                        .base(loginSourceLDAPConfigDto.getBaseOu())
                        .url(loginSourceLDAPConfigDto.getUrl())
                        .userName(dataMap.get(LoginParamEnum.USER_NAME.getType()))
                        .password(dataMap.get(LoginParamEnum.PASSWORD.getType()))
                        .domainName(loginSourceLDAPConfigDto.getDomainName())
                        .isSasl(customNacosConfig.getCustomSyncUserSetLdapCheckSasl())
                        .build();
                    boolean checkConnection = LdapTemplateFactory.checkConnection(properties);
                    if (checkConnection){
                        yfyUser = YfyUser.builder()
                                .id(dataMap.get(LoginParamEnum.USER_NAME.getType()))
                                .build();
                    }
            } else if (loginSourceType.contains(LoginSourceTypeEnum.CODE_SCRIPT.getDesc())){
                yfyUser = codeScriptSyncHandler.getYfyUserByConfigValueBox(dataMap, platformLoginConfigValueBox.getLoginSourceCodeScriptConfigDto());
            }
        }catch (ParamException e){
            log.error("异常信息",e);
        }
        return yfyUser;
    }

    /**
     * 校验跳转链接
     * @param service
     * @param clientBaseUrl
     */
    public void checkService(String service, String clientBaseUrl) {
        log.info("service {} hostName {}", service, clientBaseUrl);
        String[] split = clientBaseUrl.split(",");
        for (String uri : split) {
            if (service.contains(uri)) {
                return;
            }
        }
        throw new RuntimeException("service validate hostname error");
    }


    /**
     * 生成登陆验证的ticket并放入缓存，兼容cas和oauth
     * @param cookUidValue
     * @param userJson
     * @return
     */
    private String createUidTicket(String cookUidValue, String userJson) {

        String ticket = LoginConfigConstants.PLATFORM_LOGIN_TICKET_PREFIX + DigestUtils.md5DigestAsHex((cookUidValue + UUID.randomUUID().toString() + System.currentTimeMillis()).getBytes());
        redisStringManager.set(ticket, cookUidValue, 60L);
        // 缓存cas登陆信息
        redisStringManager.set(LoginConfigConstants.PLATFORM_LOGIN_REDIS_PREFIX_CAS_TICKET + ticket, userJson, 60L);
        return ticket;
    }

    private String createCasXml(YfyUser userInfo) {
        if (userInfo == null) {
            return null;
        }
        Document document = DocumentHelper.createDocument();
        Element serviceResponse = document.addElement("cas:serviceResponse").addNamespace("cas", "http://www.yale.edu/tp/cas");
        Element authenticationSuccess = serviceResponse.addElement("cas:authenticationSuccess");
        authenticationSuccess.addElement("cas:user").setText(userInfo.getId());
        Element casAttributes = authenticationSuccess.addElement("cas:attributes");
        casAttributes.addElement("cas:uid").addText(userInfo.getId());
        if(null !=userInfo.getFullName()){
            casAttributes.addElement("cas:full_name").addText(userInfo.getFullName());
        }
        if (null != userInfo.getEmail()) {
            casAttributes.addElement("cas:mail").addText(userInfo.getEmail());
        }
        if (null != userInfo.getPhone()) {
            casAttributes.addElement("cas:phone").addText(userInfo.getPhone());
        }
        if (null != userInfo.getDeptNames()) {
            casAttributes.addElement("cas:dept").addText(userInfo.getDeptNames());
        }
        if (null != userInfo.getDeptIds()) {
            casAttributes.addElement("cas:deptId").addText(userInfo.getDeptIds());
        }

        log.info(serviceResponse.asXML());
        return serviceResponse.asXML();
    }


    /**
     * 构建接口返回的用户信息
     * @param values
     * @param apiConfig
     * @return
     */
    public YfyUser buildLoginUser(JSONObject values, APIConfigValueBox.APIConfig apiConfig) {
        if (CollectionUtils.isEmpty(values) || apiConfig == null) {
            return null;
        }
        List<APIResultConfigDto> resultConfigs = apiConfig.getResultConfig();
        Map<String, Object> mappingValue = mapping(values, resultConfigs);
        Object idObject = mappingValue.get(SyncTaskConstants.USER_PARAMETER_ID);
        String id = Objects.isNull(idObject) ? null : String.valueOf(idObject);
        if(StringUtils.isEmpty(id)){
            return null;
        }

        Object phoneObject = mappingValue.get(SyncTaskConstants.USER_PARAMETER_PHONE);
        String phone = Objects.isNull(phoneObject) ? null : String.valueOf(phoneObject);

        Object emailObject = mappingValue.get(SyncTaskConstants.USER_PARAMETER_EMAIL);
        String email = Objects.isNull(emailObject) ? null : String.valueOf(emailObject);

        Object departmentIdObject = mappingValue.get(SyncTaskConstants.USER_PARAMETER_DEPARTMENT_ID);
        String departmentId = Objects.isNull(departmentIdObject) ? null : String.valueOf(departmentIdObject);

        Object fullNameObject = mappingValue.get(SyncTaskConstants.USER_PARAMETER_FULL_NAME);
        String fullName = Objects.isNull(fullNameObject) ? null : String.valueOf(fullNameObject);

        YfyUser user =  new YfyUser();
        user.setId(id);
        user.setEmail(email);
        user.setPhone(phone);
        user.setDepartmentIds(Lists.newArrayList(departmentId));
        user.setFullName(fullName);
        return user;
    }

    public Map<String, Object> mapping(Map<String, Object> values, List<APIResultConfigDto> apiResultConfigs) {
        if (values == null || apiResultConfigs == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        for (APIResultConfigDto resultConfig : apiResultConfigs) {
            if (resultConfig == null) {
                continue;
            }
            String key = resultConfig.getName();
            if (StringUtils.isNotBlank(resultConfig.getMappingName())) {
                key = resultConfig.getMappingName();
            }
            Object value = values.get(resultConfig.getName());
            result.put(key, value);
        }
        return result;
    }

    private static void placeholderReplace(Map<String,String> valuesMap,List<APIParamConfigDto> apiParamConfigDtos){
        for (APIParamConfigDto apiParamConfigDto : apiParamConfigDtos) {
            String replace = PlaceholderReplaceUtils.replace(valuesMap, apiParamConfigDto.getValue());
            apiParamConfigDto.setValue(replace);
        }
    }

    private String getPlatformConfig(Integer enterpriseId){
        Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
        String additionalInfo = enterpriseInfo.getAdditionalInfo();
        if (additionalInfo == null || StringUtils.isEmpty(additionalInfo)) {
            return "";
        }

        Object platformConfig = null;
        Object platformConfigArr = JSONPath.extract(additionalInfo, "$.platform_config_arr");
        if(Objects.isNull(platformConfigArr)){
            platformConfig = JSONPath.extract(additionalInfo, "$.platform_config");
        }else {
            platformConfig = ((JSONArray)platformConfigArr).getJSONObject(0);
        }
        if (platformConfig == null) {
            return "";
        }

        String  platformConfigStr =  platformConfig.toString();
        if (enterpriseInfo == null || additionalInfo == null || Objects.isNull(platformConfigStr)) {
            log.info("enterprise id {}, info is null ! {}", enterpriseId, enterpriseInfo);
        }
        return platformConfigStr;
    }

    private static String extractSubdomain(String domain) {
        // 获取第一个 "." 的索引
        int dotIndex = domain.indexOf('.');

        // 判断是否找到 "." 并提取后面的内容
        if (dotIndex != -1 && dotIndex < domain.length() - 1) {
            return domain.substring(dotIndex + 1);
        } else {
            return null;
        }
    }

    public static void main(String[] args) {
        String baseDomain = ".123";
        String substring = baseDomain.substring(1);
        baseDomain = baseDomain.startsWith(".") ? baseDomain.substring(1) : baseDomain;
        System.out.println(baseDomain);
        String a ="uo9PDz5kIXOmUbuT/I27LpytJQR44g2INErbpVdoch9F58SEhyAMXtl68YcZ467sPjvltdBvf+vgOtNOyCDQlH45E4+wMGATPido2xlbImJgoSkGqWz/PKbrgwsCTSR9SPPTxzhgN+BHHyNNRLaHzGGK9IfHLmd+UCf7RKy5EDbbGVtOlNNNABFODuRZDXoyCWzxFa2RwRlTHI7AooAmiIgCLJ4aQn59qAstlZRZuuk/l9J/yJYKY4JNyy+S277foNBghRfyRxhgu5n71fmlMAsHCvq65fZBi7+c0spNv7MsKbEG2NAtEFhGBCotAMjUqCsubFb0plrjw1q3Na6sGQ==";
        String decrypt = RsaUtils.decrypt(a);
        LoginUserDto user = LoginUserDto.builder().username("donggua").password("donggua").build();
        Map<String, String> stringStringMap = buildDataMap(user);

        String replace = PlaceholderReplaceUtils.replace(stringStringMap, "${USER_NAME}111");
        Map<String,String> valuesMap = new HashMap ();
        valuesMap.put("name", "张三");
        valuesMap.put("code", "1234");
        String templateString = "您好 ,您的短信验证码是 123";
        StringSubstitutor sub = new StringSubstitutor(valuesMap);
        String resolvedString = sub.replace(templateString);
        System.out.println (resolvedString);
    }


}
