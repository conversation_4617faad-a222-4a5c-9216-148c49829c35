package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.*;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.helper.PushHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.PlatformAsyncQueueService;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskService;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.custom.ShjyServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.custom.SzgdServiceImpl;
import com.fangcloud.thirdpartplatform.utils.thread.TaskThreadContextHolder;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PushSyncHandler extends AbstractDataSourceSyncHandler {

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    private PushHelper pushHelper;

    @Resource
    private EnterpriseServiceImpl enterpriseServiceImpl;

    @Resource
    private OpenClientHelper openClientHelper;

    @Resource
    private PlatformSyncTaskService platformSyncTaskService;

    @Resource
    private PlatformAsyncQueueService platformAsynQueueService;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @Resource
    private SzgdServiceImpl szgdService;

    @Resource
    private ShjyServiceImpl shjyService;

    @Resource
    private RedisStringManager redisStringManager;

    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        TaskThreadContextHolder.TaskThreadContext taskThreadContext = TaskThreadContextHolder.get();
        String body = taskThreadContext.getBody();
        Map<String, String> headsMap = taskThreadContext.getHeadsMap();
        Map<String, Object> paramsMap = taskThreadContext.getParamsMap();
        PushConfigValueBox pushConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), PushConfigValueBox.class);
        log.info("syncUser-pushConfigValueBox:{}", pushConfigValueBox);
        Integer enterpriseId = platformSyncConfig.getEnterpriseId();
        List<PushParamConfigDto> token = pushConfigValueBox.getParamConfig().stream().filter(s -> CommonConstants.TOKEN.equals(s.getMappingName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(token)) {
            checkToken(pushConfigValueBox.getParamConfig(), enterpriseId, body, headsMap, paramsMap);
        }
        PushResultConfigDto responseType = pushConfigValueBox.getResultConfig().stream().filter(s -> "Response_Type".equals(s.getName())).collect(Collectors.toList()).get(0);
        if (responseType!=null&&responseType.getResultValue().equals("SZGD")){
            body = szgdService.processApiResult(taskThreadContext.getBody(),"USER",taskId);
            log.info("decry body is current:{}",body);
        }
        JSONArray jsonArray = buildParams(pushConfigValueBox, body, headsMap, paramsMap);
        if (responseType!=null&&responseType.getResultValue().equals("SHJY")){
            shjyService.cachaSyncInfo(jsonArray, platformSyncConfig.getSyncType(), taskId);
            log.info("succeed to cache shjy info");
        }
        if (isTest(pushConfigValueBox.getParamConfig(), jsonArray)) {
            return new ExecuteResult();
        }
        List<YfyUser> yfyUsers = pushHelper.buildUser(jsonArray, pushConfigValueBox);
        log.info("build users list:{}"+yfyUsers);
        if (CollectionUtils.isEmpty(yfyUsers)) {
            return new ExecuteResult();
        }
        return syncUserList(platformSyncConfig, yfyUsers, taskId);
    }

    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        TaskThreadContextHolder.TaskThreadContext taskThreadContext = TaskThreadContextHolder.get();
        String body = taskThreadContext.getBody();
        Map<String, String> headsMap = taskThreadContext.getHeadsMap();
        Map<String, Object> paramsMap = taskThreadContext.getParamsMap();
        PushConfigValueBox pushConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), PushConfigValueBox.class);
        log.info("syncUser-pushConfigValueBox:{}", pushConfigValueBox);
        Integer enterpriseId = platformSyncConfig.getEnterpriseId();
        List<PushParamConfigDto> token = pushConfigValueBox.getParamConfig().stream().filter(s -> CommonConstants.TOKEN.equals(s.getMappingName())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(token)) {
            checkToken(token, enterpriseId, body, headsMap, paramsMap);
        }
        PushResultConfigDto responseType = pushConfigValueBox.getResultConfig().stream().filter(s -> "Response_Type".equals(s.getName())).collect(Collectors.toList()).get(0);
        if (responseType!=null&&responseType.getResultValue().equals("SZGD")){
            body = szgdService.processApiResult(taskThreadContext.getBody(),"DEPT",taskId);
            log.info("decry body is current:{}",body);
        }
        JSONArray jsonArray = buildParams(pushConfigValueBox, body, headsMap, paramsMap);
        if (responseType!=null&&responseType.getResultValue().equals("SHJY")){
            shjyService.cachaSyncInfo(jsonArray, platformSyncConfig.getSyncType(), taskId);
            log.info("succeed to cache shjy info");
        }
        if (isTest(pushConfigValueBox.getParamConfig(), jsonArray)) {
            return new ExecuteResult();
        }
        List<YfyDepartment> departments = pushHelper.buildDept(jsonArray, pushConfigValueBox);
        log.info("build department list:{}"+departments);
        if (CollectionUtils.isEmpty(departments)) {
            return new ExecuteResult();
        }
        return syncDepartmentList(platformSyncConfig, departments, taskId);
    }

    /**
     * 是否测试，业务方系统操作类型的值为test，则表明此次请求是测试请求,直接返回成功
     */
    private boolean isTest(List<PushParamConfigDto> paramConfig, JSONArray jsonArray) {
        String key = StringUtils.EMPTY;
        for (PushParamConfigDto pushParamConfigDto : paramConfig) {
            String mappingName = pushParamConfigDto.getMappingName();
            if (mappingName.equals(SyncTaskConstants.DEPARTMENT_PARAMETER_IS_DELETE) || mappingName.equals(SyncTaskConstants.USER_IS_DISABLE)) {
                key = pushParamConfigDto.getName();
            }
        }
        if (StringUtils.isNotBlank(key)) {
            for (Object o : jsonArray) {
                JSONObject obj = (JSONObject) o;
                String value = obj.getString(key);
                if ("test".equals(value)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return null;
    }

    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.PUSH.getDesc();
    }

    public void checkToken(List<PushParamConfigDto> paramConfig, Integer enterpriseId, String bodysMap, Map<String, String> headsMap, Map<String, Object> paramsMap) {
        Enterprise enterprise = enterpriseServiceImpl.getEnterpriseById(enterpriseId);
        if (Objects.isNull(enterprise)) {
            throw new ParamException("企业信息为空");
        }
        int platformId = enterprise.getPlatformId();
        if (CollectionUtils.isEmpty(paramConfig)) {
            throw new ParamException("配置不能为空");
        }
        String token = StringUtils.EMPTY;
        for (PushParamConfigDto pushParamConfigDto : paramConfig) {
            String paramWay = pushParamConfigDto.getParamWay();
            String name = pushParamConfigDto.getName();
            if (ApiParamWayEnum.HEAD.getDesc().equals(paramWay)) {
                token = headsMap.get(name);
            } else if (ApiParamWayEnum.PARAMS.getDesc().equals(paramWay)) {
                token = (String) paramsMap.get(name);
            } else if (ApiParamWayEnum.BODY.getDesc().equals(paramWay)) {
                token = JSON.parseObject(bodysMap).getString(name);
            }
        }
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId + "");
        String encryptionKey = configMap.get(PlatformGlobalConfigKeyEnum.ENCRYPTION_KEY.getKey());
        //oauth2验证方式
        if (encryptionKey != null) {
            String redisToken = redisStringManager.get(SyncTaskConstants.OAUTH2_TOKEN + enterpriseId);
            if (redisToken != null && redisToken.equals(token)) {
                return;
            }
            throw new ParamException("token已过期");
        }
        JSONArray oneUserInfo = openClientHelper.getOneUserInfo(token, platformId);
        if (CollectionUtils.isEmpty(oneUserInfo)) {
            throw new ParamException("token校验失败");
        }
    }

    public String getBodyParam(HttpServletRequest request) {
        String str, wholeParams = "";
        BufferedReader reader = null;
        try {
            reader = request.getReader();
            while ((str = reader.readLine()) != null) {
                wholeParams += str;
            }
        } catch (IOException e) {
            log.info("getBodyParam-IOException:{}", e);
        } finally {
            try {
                reader.close();
            } catch (IOException e) {
            }
        }
        return wholeParams;
    }

    public JSONArray buildParams(PushConfigValueBox pushConfigValueBox, String bodysMap, Map<String, String> headsMap, Map<String, Object> paramsMap) {
        List<PushParamConfigDto> paramConfig = pushConfigValueBox.getParamConfig();
        List<PushParamConfigDto> heads = paramConfig.stream().filter(s -> ApiParamWayEnum.HEAD.getDesc().equals(s.getParamWay()) && !CommonConstants.TOKEN.equals(s.getMappingName())).collect(Collectors.toList());
        List<PushParamConfigDto> params = paramConfig.stream().filter(s -> ApiParamWayEnum.PARAMS.getDesc().equals(s.getParamWay())).collect(Collectors.toList());
        List<PushParamConfigDto> bodys = paramConfig.stream().filter(s -> ApiParamWayEnum.BODY.getDesc().equals(s.getParamWay())).collect(Collectors.toList());
        JSONObject jsonObject = new JSONObject();
        JSONArray pushConfigData = new JSONArray();
        String paramDataType = pushConfigValueBox.getParamDataType();
        String headerValue = StringUtils.EMPTY;
        String paramValuePath = pushConfigValueBox.getParamValuePath();
        String headValuePath = pushConfigValueBox.getHeadValuePath();
        if (PushParamtTypeEnum.LIST.getDesc().equals(paramDataType)) {
            if (StringUtils.isNotBlank(headValuePath)) {
                headerValue = headsMap.get(headValuePath);
                if (StringUtils.isNotBlank(paramValuePath)) {
                    pushConfigData = (JSONArray) apiSyncHandler.getResultDataByPath(paramValuePath, headerValue);
                } else {
                    pushConfigData.add(JSONObject.parse(headerValue));
                }
            }
            if (!CollectionUtils.isEmpty(bodys)) {
                pushConfigData = (JSONArray) apiSyncHandler.getResultDataByPath(paramValuePath, bodysMap);
            }

        } else {
            if (StringUtils.isNotBlank(headValuePath)) {
                headerValue = headsMap.get(headValuePath);
                if (StringUtils.isNotBlank(headerValue) && !headerValue.equals(CommonConstants.STRING_NULL)) {
                    jsonObject = JSON.parseObject(headerValue);
                }
            }
            if (!CollectionUtils.isEmpty(heads)) {
                for (PushParamConfigDto headParam : heads) {
                    String value = headsMap.get(headParam.getName());
                    if (StringUtils.isNotBlank(value) && !value.equals(CommonConstants.STRING_NULL)) {
                        jsonObject.put(headParam.getName(), value);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(params)) {
                for (PushParamConfigDto param : params) {
                    Object value = paramsMap.get(param.getName());
                    if (Objects.nonNull(value)) {
                        jsonObject.put(param.getName(), value);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(bodys)) {
                JSONObject bodyParamJson = JSON.parseObject(bodysMap);
                for (PushParamConfigDto body : bodys) {
                    String value = bodyParamJson.getString(body.getName());
                    if (StringUtils.isNotBlank(value)) {
                        jsonObject.put(body.getName(), value);
                    }
                }
            }
            pushConfigData.add(jsonObject);
        }
        return pushConfigData;
    }

    public void getResultData(Map<String, Object> resultMap, PushConfigValueBox pushConfigValueBox, Integer taskId, Element xml) {
        List<PushResultConfigDto> resultConfig = pushConfigValueBox.getResultConfig();
        PushResultConfigDto responseType = resultConfig.stream().filter(s -> "Response_Type".equals(s.getName())).collect(Collectors.toList()).get(0);
        if (responseType!=null&&responseType.getResultValue().equals("SZGD")){
            List<PlatformSyncTaskFailLogs> platformSyncTaskFailLogs = platformSyncTaskService.selectByTaskId(taskId, null);
            szgdService.buildResultData(taskId,resultMap,getErrors(platformSyncTaskFailLogs));
        } else if (responseType!=null&&responseType.getResultValue().equals("SHJY")){
            List<PlatformSyncTaskFailLogs> platformSyncTaskFailLogs = platformSyncTaskService.selectByTaskId(taskId, null);
            shjyService.buildResultData(taskId, resultMap, platformSyncTaskFailLogs);
        } else {
            List<PushResultConfigDto> successConfig = resultConfig.stream().filter(s -> s.getResultStatus().equals(PushResultTypeEnum.SUCCESS.getDesc())).collect(Collectors.toList());
            List<PushResultConfigDto> errorConfig = resultConfig.stream().filter(s -> s.getResultStatus().equals(PushResultTypeEnum.ERROR.getDesc())).collect(Collectors.toList());
            Element successXml = xml.addElement(PushResultTypeEnum.SUCCESS.getDesc());
            Element errorXml = xml.addElement(PushResultTypeEnum.ERROR.getDesc());
            if (pushConfigValueBox.getIsAsync()) {
                buildResultData(resultMap, successConfig, null, pushConfigValueBox.getReturnDataType(), successXml, taskId);
            } else {
                List<PlatformSyncTaskFailLogs> platformSyncTaskFailLogs = platformSyncTaskService.selectByTaskId(taskId, null);
                if (!CollectionUtils.isEmpty(platformSyncTaskFailLogs)) {
                    buildResultData(resultMap, errorConfig, getErrors(platformSyncTaskFailLogs), pushConfigValueBox.getReturnDataType(), errorXml, taskId);
                } else {
                    buildResultData(resultMap, successConfig, null, pushConfigValueBox.getReturnDataType(), successXml, taskId);
                }
            }
        }
    }

    private void buildResultData(Map<String, Object> resultMap, List<PushResultConfigDto> config, String value, String returnDataType, Element xml, Integer taskId) {

        for (PushResultConfigDto pushResultConfigDto : config) {
            if (pushResultConfigDto.getResultStatus().equals(PushResultTypeEnum.ERROR.getDesc())) {
                //value不为空和resultValue为固定值,说明有同步失败信息
                if (StringUtils.isNotBlank(value) && pushResultConfigDto.getResultValue().equals(PushResultTypeEnum.ERROR.getDesc())) {
                    pushResultConfigDto.setResultValue(value);
                }
            }
            if (returnDataType.equals(PushResultTypeEnum.JSON.getDesc())) {
                Object convert = DataTypeConstants.convert(pushResultConfigDto.getResultValue(), pushResultConfigDto.getDataType());
                resultMap.put(pushResultConfigDto.getName(), convert);
            }
            if (returnDataType.equals(PushResultTypeEnum.XML.getDesc())) {
                xml.addElement(pushResultConfigDto.getName()).setText(pushResultConfigDto.getResultValue());
            }
        }
        if (taskId != null) {
            resultMap.put("task_id", taskId);
        } else {
            resultMap.put("task_id", "任务id方便查询错误日志");
        }
    }

    public void asyncExceptionRetry(Integer enterpriseId) {
        List<PlatformSyncConfig> platformSyncConfigs = platformSyncConfigMapper.queryByEnterpriseIdAndSourceType(enterpriseId, SourceTypeEnum.PUSH.getDesc());
        platformSyncConfigs.forEach(platformSyncConfig -> {
            log.info("asyncExceptionRetry config {}", JSON.toJSONString(platformSyncConfig));
            //只查询未同步成功的数据
            List<PlatformAsyncQueue> platformAsyncQueues = platformAsynQueueService.selectByEnterpriseIdAndStatus(enterpriseId, 2);
            if (!CollectionUtils.isEmpty(platformAsyncQueues)) {
                platformAsyncQueues.forEach(platformAsyncQueue -> {
                    String valueBox = platformAsyncQueue.getValueBox();
                    Object parse = JSON.parse(valueBox);
                    if (platformAsyncQueue.getCount() >= 3) {
                        platformAsyncQueue.setStatus(1);
                        platformAsyncQueue.setUpdated(System.currentTimeMillis());
                        //如果同步次数大于等于三次，修改状态为同步成功
                        platformAsynQueueService.updateByPrimaryKeySelective(platformAsyncQueue);
                        return;
                    }
                    if (platformAsyncQueue.getSyncType().equals(SyncTypeEnum.USERS.getSyncType())) {
                        List<YfyUser> yfyUsers = new ArrayList<>();
                        if (parse instanceof List) {
                            List<YfyUser> yfyUserList = JSON.parseArray(valueBox, YfyUser.class);
                            yfyUsers.addAll(yfyUserList);
                        } else {
                            yfyUsers.add(JSON.parseObject(valueBox, YfyUser.class));
                        }
                        ExecuteResult executeResult = syncUserList(platformSyncConfig, yfyUsers, platformAsyncQueue.getTaskId());
                        checkAsyncResult(platformAsyncQueue, executeResult);
                    } else {
                        List<YfyDepartment> yfyDepartments = new ArrayList<>();
                        if (parse instanceof List) {
                            List<YfyDepartment> yfyDepartmentList = JSON.parseArray(valueBox, YfyDepartment.class);
                            yfyDepartments.addAll(yfyDepartmentList);
                        } else {
                            yfyDepartments.add(JSON.parseObject(valueBox, YfyDepartment.class));
                        }
                        ExecuteResult executeResult = syncDepartmentList(platformSyncConfig, yfyDepartments, platformAsyncQueue.getTaskId());
                        checkAsyncResult(platformAsyncQueue, executeResult);
                    }
                });
            }
        });

    }


    private void checkAsyncResult(PlatformAsyncQueue platformAsynQueue, ExecuteResult executeResult) {
        if (executeResult.getErrorRows() != 0) {
            platformAsynQueue.setUpdated(System.currentTimeMillis());
            platformAsynQueue.setCount(platformAsynQueue.getCount() + 1);
            platformAsynQueueService.updateByPrimaryKeySelective(platformAsynQueue);
        } else {
            //同步成功就在队列删除该条数据
            platformAsynQueueService.deleteByPrimaryKey(platformAsynQueue.getId());
        }
    }

    private String getErrors(List<PlatformSyncTaskFailLogs> platformSyncTaskFailLogs) {
        String error = null;
        for (PlatformSyncTaskFailLogs platformSyncTaskFailLog : platformSyncTaskFailLogs) {
            if (error != null) {
                error += String.format("\n错误id: %s,错误原因: %s", platformSyncTaskFailLog.getCustomeId(), platformSyncTaskFailLog.getReason());
            } else {
                error = String.format("错误id: %s,错误原因: %s", platformSyncTaskFailLog.getCustomeId(), platformSyncTaskFailLog.getReason());
            }
        }
        return error;
    }

    public Map<String, String> getHeadsByRequest(HttpServletRequest request) {
        Map<String, String> headsMap = new HashMap<>();
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String key = headerNames.nextElement();
            String value = request.getHeader(key);
            headsMap.put(key, value);
        }
        return headsMap;
    }

    public Map<String, Object> getParamsByRequest(HttpServletRequest request) {
        Map<String, Object> paramsMap = new HashMap<>();
        request.getParameterMap().forEach((key, value) -> paramsMap.put(key, value[0]));
        return paramsMap;
    }

    public Object generateDocument(Integer id) {
        JSONObject paramsJson = new JSONObject();
        JSONObject headJson = new JSONObject();
        JSONObject resultJson = new JSONObject();
        Map<String, String> paramsMap = new HashMap<>();
        Map<String, String> bodyMap = new HashMap<>();
        Map<String, String> headMap = new HashMap<>();
        Map<String, Object> successMap = new HashMap<>();
        Map<String, Object> errorMap = new HashMap<>();
        JSONArray paramsJsonArray = new JSONArray();
        JSONArray headJsonArray = new JSONArray();
        LinkedHashMap<String, Object> resultHashMap = new LinkedHashMap<>();
        Document document = DocumentHelper.createDocument();
        Element xml = document.addElement(PushResultTypeEnum.XML.getDesc());
        Element successXml = xml.addElement(PushResultTypeEnum.SUCCESS.getDesc());
        Element errorXml = xml.addElement(PushResultTypeEnum.ERROR.getDesc());
        PlatformSyncConfig platformSyncConfig = platformSyncConfigMapper.selectByPrimaryKey(id);
        if (platformSyncConfig == null || !SourceTypeEnum.PUSH.getDesc().equals(platformSyncConfig.getSourceType())) {
            throw new ParamException("Error in config, please re-enter");
        }
        PushConfigValueBox pushConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), PushConfigValueBox.class);
        String url = "/platform/pushData/" + platformSyncConfig.getEnterpriseId() + pushConfigValueBox.getPushUri();
        List<PushParamConfigDto> paramConfig = pushConfigValueBox.getParamConfig();
        //入参类型
        String paramDataType = pushConfigValueBox.getParamDataType();
        //header路径
        String headValuePath = pushConfigValueBox.getHeadValuePath();
        //入参路径
        String paramValuePath = pushConfigValueBox.getParamValuePath();
        String[] splitParamValue = paramValuePath.split("\\$.");
        List<PushResultConfigDto> resultConfig = pushConfigValueBox.getResultConfig();
        List<PushResultConfigDto> successConfig = resultConfig.stream().filter(s -> s.getResultStatus().equals(PushResultTypeEnum.SUCCESS.getDesc())).collect(Collectors.toList());
        List<PushResultConfigDto> errorConfig = resultConfig.stream().filter(s -> s.getResultStatus().equals(PushResultTypeEnum.ERROR.getDesc())).collect(Collectors.toList());
        for (PushParamConfigDto pushParamConfigDto : paramConfig) {
            String paramWay = pushParamConfigDto.getParamWay();
            if (paramWay.equals(ApiParamWayEnum.HEAD.getDesc())) {
                headMap.put(pushParamConfigDto.getName(), CommonConstants.PUSH_RANDOM_CHARACTER);
            } else if (paramWay.equals(ApiParamWayEnum.PARAMS.getDesc())) {
                paramsMap.put(pushParamConfigDto.getName(), CommonConstants.PUSH_RANDOM_CHARACTER);
            } else if (paramWay.equals(ApiParamWayEnum.BODY.getDesc())) {
                bodyMap.put(pushParamConfigDto.getName(), CommonConstants.PUSH_RANDOM_CHARACTER);
            }
        }
        if (!CollectionUtils.isEmpty(paramsMap)) {
            StringBuffer stringBuffer = new StringBuffer();
            paramsMap.forEach((key, value) -> stringBuffer.append(key).append("=").append(value).append(CommonConstants.MESSAGE_POST_DATA_SPLIT));
            url = url + "?" + stringBuffer;
        }
        if (paramDataType.equals(PushParamtTypeEnum.LIST.getDesc())) {
            if (StringUtils.isNotBlank(paramValuePath) && !CollectionUtils.isEmpty(bodyMap)) {
                paramsJsonArray.add(bodyMap);
                paramsJson.put(splitParamValue[1], paramsJsonArray);
            } else {
                paramsJsonArray.add(bodyMap);
                paramsJson.putAll(bodyMap);
            }
        } else if (paramDataType.equals(PushParamtTypeEnum.OBJECT.getDesc())) {
            if (!CollectionUtils.isEmpty(bodyMap)) {
                paramsJson.putAll(bodyMap);
            }
        }
        if (StringUtils.isNotBlank(headValuePath)) {
            if (StringUtils.isNotBlank(paramValuePath)) {
                headJsonArray.add(headMap);
                headJson.put(splitParamValue[1], headJsonArray);
            } else {
                headJson.putAll(headMap);
            }
        }
        buildResultData(successMap, successConfig, null, pushConfigValueBox.getReturnDataType(), successXml, null);
        if (!pushConfigValueBox.getIsAsync()) {
            buildResultData(errorMap, errorConfig, CommonConstants.ERROR_REASON, pushConfigValueBox.getReturnDataType(), errorXml, null);
        }
        resultHashMap.put(CommonConstants.PUSH_URL, url);
        resultHashMap.put(CommonConstants.PUSH_REQUEST_MODE, !paramsMap.isEmpty() ? RequestMethod.GET.name() : RequestMethod.POST.name());
        if (!paramsJson.isEmpty()) {
            resultHashMap.put(CommonConstants.PUSH_BODY, paramsJson);
        }
        if (!headJson.isEmpty()) {
            resultHashMap.put(CommonConstants.PUSH_HEADER, headJson);
        }
        if (pushConfigValueBox.getReturnDataType().equals(PushResultTypeEnum.XML.getDesc())) {
            resultHashMap.put(CommonConstants.PUSH_RESULT, xml.asXML());
        } else {
            resultJson.put(CommonConstants.PUSH_SUCCESS, successMap);
            if (!errorMap.isEmpty()) {
                resultJson.put(CommonConstants.PUSH_ERROR, errorMap);
            }
            resultHashMap.put(CommonConstants.PUSH_RESULT, resultJson);
        }
        return resultHashMap;
    }

    public Object getSzgdPushResult(PushConfigValueBox pushConfigValueBox, Integer taskId){
        Map<String, Object> resultMap = new HashMap<>();
        return resultMap;
    }
}