package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs;
import com.fangcloud.thirdpartplatform.entity.custom.shjy.ShjyResultData;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ShjyServiceImpl {

    @Resource
    private RedisStringManager redisStringManager;

    private final static String syncInfoKey = "SYNC_INFO_KEY_";


    public void cachaSyncInfo(JSONArray jsonArray, Integer syncType , Integer taskId){
        log.info("shjy raw data: {}",jsonArray.toJSONString());
        Map<String, String> shjyResultDataMap = new HashMap<>();
        for (Object info : jsonArray) {
            ShjyResultData shjyResultData = JSONObject.parseObject(info.toString(), ShjyResultData.class);
            if (shjyResultData != null) {
                if (syncType.equals(SyncTypeEnum.DEPARTMENT.getSyncType())) {
                    shjyResultDataMap.put(shjyResultData.getAdmPrimaryKey(), JSON.toJSONString(shjyResultData));
                } else {
                    shjyResultDataMap.put(shjyResultData.getMdCode(), JSON.toJSONString(shjyResultData));
                }
            }
        }
        redisStringManager.set(syncInfoKey + taskId, JSON.toJSONString(shjyResultDataMap), 2*60*60L);

    }

    public void buildResultData(Integer taskId, Map<String, Object> resultMap, List<PlatformSyncTaskFailLogs> platformSyncTaskFailLogs) {
        List<ShjyResultData> shjyResultDataList = new ArrayList<>();
            String shjyResultDataMapStr = redisStringManager.get(syncInfoKey + taskId);
            Map<String, String> shjyResultDataMap = JSON.parseObject(shjyResultDataMapStr, new TypeReference<Map<String, String>>() {});
            List<String> failIdList = platformSyncTaskFailLogs.stream().map(PlatformSyncTaskFailLogs::getCustomeId).collect(Collectors.toList());
            shjyResultDataMap.forEach((key, value) -> {
                ShjyResultData shjyResultData = JSONObject.parseObject(value.toString(), ShjyResultData.class);
                shjyResultData.setAdmPrimaryKey(null);
                if (failIdList.contains(key)) {
                    shjyResultData.setStatus("E");
                    shjyResultData.setMessage("分发失败");
                } else {
                    shjyResultData.setStatus("S");
                    shjyResultData.setMessage("分发成功");
                }
                shjyResultDataList.add(shjyResultData);
            });
            if (platformSyncTaskFailLogs != null && platformSyncTaskFailLogs.size() > 0) {
                resultMap.put("status", "E");
                resultMap.put("code", "500");
                resultMap.put("message", "分发失败");
            } else {
                resultMap.put("status", "S");
                resultMap.put("code", "200");
                resultMap.put("message", "分发成功");
            }
        resultMap.put("responseData", shjyResultDataList);
    }


}
