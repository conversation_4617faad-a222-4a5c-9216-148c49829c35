package com.fangcloud.thirdpartplatform.service.impl.syncTask;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncResultStatusEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.constant.sync.TaskTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncTaskRecordMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.sync.CustomUser;
import com.fangcloud.thirdpartplatform.event.CreateTaskEvent;
import com.fangcloud.thirdpartplatform.helper.SyncTaskHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskService;
import com.fangcloud.thirdpartplatform.service.SyncTaskHandler;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.SyncServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.UserServiceImpl;
import com.google.common.collect.Lists;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class MultiTaskingSyncHandler implements SyncTaskHandler {

    @Resource
    private SyncServiceImpl syncServiceImpl;

    @Resource
    private UserServiceImpl userServiceimpl;

    @Resource
    private EnterpriseServiceImpl enterpriseServiceImpl;

    @Resource
    private RedisStringManager redisStringManager;
    @Autowired
    private ApplicationContext applicationContext;
    @Resource
    private PlatformSyncTaskRecordMapper platformSyncTaskRecordMapper;
    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;
    @Resource
    private SyncTaskHelper syncTaskHelper;
    @Resource
    private PlatformSyncTaskService platformSyncTaskService;

    @Override
    public ExecuteResult executeResult(PlatformSyncConfig platformSyncConfig, List<YfyUser> yfyUserList, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        Boolean isCreateDefaultPassword = false;
        String[] task_binding = syncTaskHelper.getTaskBinding(platformSyncConfig).split(",");
        List<String> taskList = Lists.newArrayList(task_binding);
        int count = taskList.indexOf(String.valueOf(platformSyncConfig.getId()));

        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(platformSyncConfig.getEnterpriseId()+ "");
        String isCreateDefaultPasswordConfig = configMap.get(PlatformGlobalConfigKeyEnum.CREATE_DEFAULT_PASSWORD.getKey());
        if (StringUtils.isNotBlank(isCreateDefaultPasswordConfig)) {
            isCreateDefaultPassword = Boolean.valueOf(isCreateDefaultPasswordConfig);
        }
        String customPassword = configMap.get(PlatformGlobalConfigKeyEnum.CUSTOM_PASSWORD.getKey());
        if (count != task_binding.length - 1) {
            String configId = taskList.get(count + 1);
            PlatformSyncConfig syncConfig = platformSyncConfigMapper.selectByPrimaryKey(Integer.valueOf(configId));
            if (syncConfig == null) {
                throw new ParamException("MultiTaskingSyncHandler-configId is not exist");
            }
            List<YfyUser> userList = JSONObject.parseArray(redisStringManager.get(SyncTaskConstants.STAGING_CUSTOM_USER_LIST), YfyUser.class);
            if (userList == null || userList.size() == 0) {
                redisStringManager.set(SyncTaskConstants.STAGING_CUSTOM_USER_LIST, JSONObject.toJSONString(yfyUserList), 0L);
            } else {
                userList.addAll(yfyUserList);
                redisStringManager.set(SyncTaskConstants.STAGING_CUSTOM_USER_LIST, JSONObject.toJSONString(userList), 0L);
            }
            applicationContext.publishEvent(new CreateTaskEvent(Integer.valueOf(configId), taskId));
            log.info("publishEvent id {}, info is null !",platformSyncConfig.getEnterpriseId());
        } else {
            log.info("start multitasking......");
            PlatformSyncTaskRecord platformSyncTaskRecord = platformSyncTaskRecordMapper.selectByPrimaryKey(taskId);
            try {
                List<YfyUser> userList = JSONObject.parseArray(redisStringManager.get(SyncTaskConstants.STAGING_CUSTOM_USER_LIST), YfyUser.class);
                userList.addAll(yfyUserList);
                if (CollectionUtils.isEmpty(userList)) {
                    log.info("stagingCustomUserList is null！！");
                    return executeResult;
                }

                // 1. 获取企业信息
                Enterprise enterprise = enterpriseServiceImpl.getEnterpriseById(platformSyncConfig.getEnterpriseId());
                if(Objects.isNull(enterprise)){
                    log.info("enterprise id {}, info is null !",platformSyncConfig.getEnterpriseId());
                    throw new ParamException("enterprise is null !");
                }

                // 2. 获取客户既存的用户信息
                List<CustomUser> customUserList = userServiceimpl.findAll(enterprise.getPlatformId());

                // 3. 同步用户数据
                executeResult = syncServiceImpl.syncUsers(customUserList, userList, taskId, enterprise, platformSyncConfig, isCreateDefaultPassword, customPassword);
                log.info("multitasking stop......");
                platformSyncTaskRecord.setSyncResult(SyncResultStatusEnum.FINISH.getStatus());
            } catch (Throwable e) {
                log.error("sync error, clean cache");
                PlatformSyncTaskFailLogs platformSyncTaskFailLogs = new PlatformSyncTaskFailLogs();
                platformSyncTaskFailLogs.setCreated(new Date().getTime());
                platformSyncTaskFailLogs.setSyncTaskId(taskId);
                platformSyncTaskFailLogs.setEnterpriseId(platformSyncConfig.getEnterpriseId());
                StackTraceElement[] stackTrace = e.getStackTrace();
                String jsonString = JSON.toJSONString(stackTrace);
                String s = jsonString == null ? "请查看日志" : jsonString;
                platformSyncTaskFailLogs.setReason(StringUtils.isBlank(e.getMessage()) ? s.substring(0, 150) : e.getMessage());
                List<PlatformSyncTaskFailLogs> records = Arrays.asList(platformSyncTaskFailLogs);
                platformSyncTaskService.batchInsertLogs(records);
                platformSyncTaskRecord.setSyncTaskStatus(SyncResultStatusEnum.FINISH.getStatus());
                platformSyncTaskRecord.setSyncResult(SyncResultStatusEnum.ERROR.getStatus());
            } finally {
                platformSyncTaskRecord.setSyncTaskStatus(SyncResultStatusEnum.FINISH.getStatus());
                platformSyncTaskRecord.setValueBox(JSON.toJSONString(executeResult));
                long endTime = new Date().getTime();
                platformSyncTaskRecord.setUpdated(endTime);
                platformSyncTaskRecord.setSyncEndat(endTime);
                platformSyncTaskRecordMapper.updateByPrimaryKeySelective(platformSyncTaskRecord);
                redisStringManager.delete(SyncTaskConstants.STAGING_CUSTOM_USER_LIST);
            }


            return executeResult;
        }


        return null;
    }

    @Override
    public String getSyncTaskType() {
        return TaskTypeEnum.MULTITASKING.getDesc();
    }
}