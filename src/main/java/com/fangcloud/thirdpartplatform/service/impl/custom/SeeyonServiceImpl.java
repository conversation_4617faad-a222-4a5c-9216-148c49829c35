package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.APIParamConfigDto;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class SeeyonServiceImpl implements ApiExecuteService {

    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @Resource
    private OpenClientHelper openClientHelper;

    private static final Integer PAGE_SIZE = 1000;
    private static final String ORG_ACCOUNT_ID = "orgAccountId";
    private static final String HOST = "host";
    private static final String SYNC_TYPE = "sync_type";
    private static final String USER_NAME = "userName";
    private static final String PASSWORD = "password";
    private static final String GET_TOKEN = "%s/seeyon/rest/token";
    private static final String GET_UNIT_INFO = "%s/seeyon/rest/orgAccounts?token=%s";
    private static final String GET_DEPARTMENT_INFO = "%s/seeyon/rest/orgDepartments/all/%s?token=%s&pageSize=%s&pageNo=%s";
    private static final String GET_USER_INFO = "%s/seeyon/rest/orgMembers/all/%s?token=%s";
    private static final String SSO_URI = "%s/seeyon/thirdpartyController.do?ticket=%s";

    private static Pattern PATTERN = Pattern.compile("\\s*|\t|\r|\n");


    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        String host = headMap.get(HOST);
        String syncType = headMap.get(SYNC_TYPE);
        JSONObject jsonObject = new JSONObject();
        List<String> orgAccountIds = new ArrayList<>();
        JSONArray unitInfo = getUnitInfo(host, headMap);
        unitInfo.forEach(unit -> {
            JSONObject unitJson = JSONObject.parseObject(unit.toString());
            String orgAccountId = unitJson.getString(ORG_ACCOUNT_ID);
            orgAccountIds.add(orgAccountId);
        });
        if (syncType.equals("user")) {
            JSONArray userInfo = getUserInfoByAccountId(orgAccountIds, host, headMap);
            jsonObject.put("data", userInfo);
        } else {
            JSONArray departmentInfo = getDepartmentInfo(orgAccountIds, host, headMap);
            departmentInfo.addAll(unitInfo);
            jsonObject.put("data", departmentInfo);
        }
        return jsonObject.toJSONString();
    }

    /**
     * 致远获取token接口
     */
    private String getToken(String host, Map<String, String> headMap) {

        String userName = headMap.get(USER_NAME);
        String password = headMap.get(PASSWORD);
        String tokenUrl = String.format(
                GET_TOKEN,
                host
        );
        String postData = postDataToken(userName, password);
        log.info("seeyon get_token url {} postData {}", tokenUrl, postData);
        try {
            Response response = httpClientHelper.postResponse(tokenUrl, postData);
            if (response == null || response.body() == null) {
                return null;
            }
            String result = response.body().string();
            log.info("seeyon get_token result {}", result);
            JSONObject jsonObject = JSON.parseObject(result);
            String token = (String) jsonObject.get("id");
            if (token == null) {
                return null;
            }
            return token;
        } catch (Exception e) {
            log.error("seeyon get_token error url {},postData {}", tokenUrl, postData, e);
        }
        return null;
    }

    /**
     * 致远获取所有单位id
     */
    private JSONArray getUnitInfo(String host, Map<String, String> headMap) {
        String url = String.format(
                GET_UNIT_INFO,
                host,
                getToken(host, headMap));
        log.info("seeyon get_unit_info url {}", url);
        try {
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("get unit info result {}", result);
            JSONArray jsonArray = JSONArray.parseArray(result);
            return jsonArray;
        } catch (Exception e) {
            log.error("seeyon get_unit_info error ,url {}", url, e);
        }
        return null;
    }

    /**
     * 获取指定单位的所有部门(包含停用)
     */
    private JSONArray getDepartmentInfo(List<String> orgAccountIds, String host, Map<String, String> headMap) {
        JSONArray jsonArrays = new JSONArray();
        String token = getToken(host, headMap);
        for (String orgAccountId : orgAccountIds) {
            Integer pageNo = 1;
            while (true) {
                if (StringUtils.isBlank(token)) {
                    break;
                }
                String url = String.format(
                        GET_DEPARTMENT_INFO,
                        host,
                        orgAccountId,
                        token,
                        PAGE_SIZE,
                        pageNo);
                log.info("seeyon get_department_info url {}", url);
                try {
                    pageNo++;
                    Response response = httpClientHelper.getResponse(url);
                    if (null == response || null == response.body()) {
                        break;
                    }
                    String result = Objects.requireNonNull(response.body().string());
                    log.info("seeyon get_department_info result {}", result);
                    JSONArray jsonArray = JSONArray.parseArray(result);
                    if (jsonArray.isEmpty()) {
                        break;
                    }
                    jsonArrays.addAll(jsonArray);
                } catch (Exception e) {
                    log.error("seeyon get_department_info error ,url {}", url, e);
                    break;
                }
            }
        }
        return jsonArrays;

    }

    /**
     * 取得指定单位的所有人员(包含停用人员)
     */
    private JSONArray getUserInfoByAccountId(List<String> orgAccountIds, String host, Map<String, String> headMap) {
        String token = getToken(host, headMap);
        JSONArray jsonArray = new JSONArray();
        for (String orgAccountId : orgAccountIds) {
            if (StringUtils.isBlank(token)) {
                continue;
            }
            String url = String.format(
                    GET_USER_INFO,
                    host,
                    orgAccountId,
                    token);
            try {
                log.info("seeyon getUserInfoByAccountId url {}", url);
                Response response = httpClientHelper.getResponse(url);
                if (null == response || null == response.body()) {
                    continue;
                }
                String result = Objects.requireNonNull(response.body().string());
                JSONArray userInfo = JSONArray.parseArray(result);
                if (userInfo.isEmpty()) {
                    continue;
                }
                jsonArray.addAll(userInfo);
            } catch (Exception e) {
                log.error("seeyon getUserInfoByAccountId error url {}", url, e);
                break;
            }
        }
        return jsonArray;
    }

    private String postDataToken(String userName, String password) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userName", userName);
        jsonObject.put("password", password);
        return jsonObject.toJSONString();
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.SEEYON.getDesc();
    }

    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        String ticket = request.getParameter("ticket");
        String loginName = getUserLoginName(ticket, configInfo);
        if (StringUtils.isBlank(loginName)) {
            return null;
        }
        try {
            EnterpriseParams enterpriseParams = new EnterpriseParams();
            enterpriseParams.setEnterpriseId(configInfo.getEnterpriseId());
            enterpriseParams.setIdentifier(loginName);
            enterpriseParams.setPlatformId(configInfo.getPlatformId());
            enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
            enterpriseParams.setClientId(configInfo.getClientId());
            String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
            log.info("seeyon getLoginUrl {}", loginUrl);
            return loginUrl;
        } catch (Exception e) {
            log.error("seeyon getLoginUrl error ticket:{},configInfo:{}", ticket, JSON.toJSONString(configInfo), e);
        }
        return null;
    }

    private String getUserLoginName(String ticket, ConfigInfo configInfo) {
        String userConfigId = configInfo.getUserConfigId();
        String deptConfigId = configInfo.getDeptConfigId();
        PlatformSyncConfig platformSyncConfig = platformSyncConfigMapper.selectByPrimaryKey(StringUtils.isNotBlank(userConfigId) ? Integer.valueOf(userConfigId) : Integer.valueOf(deptConfigId));
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        List<APIParamConfigDto> paramConfig = apiConfigValueBox.getApiConfig().getParamConfig();
        String uri = paramConfig.stream().filter(apiParamConfigDto -> apiParamConfigDto.getName().equals("host")).findFirst().map(APIParamConfigDto::getValue).orElse(null);
        String url = String.format(SSO_URI, uri, ticket);
        try {
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body().string());
            if (StringUtils.isNotBlank(result)) {
                Matcher matcher = PATTERN.matcher(result);
                result = matcher.replaceAll("");
                return result;
            }
        } catch (Exception e) {
            log.error("seeyon get_login_url error url {}", url, e);
        }
        return null;
    }


}
