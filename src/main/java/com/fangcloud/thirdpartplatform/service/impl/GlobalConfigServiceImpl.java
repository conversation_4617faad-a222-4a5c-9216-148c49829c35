package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.entity.dto.GlobalConfig;
import com.fangcloud.thirdpartplatform.entity.input.PlatformGlobalConfigParam;
import com.fangcloud.thirdpartplatform.entity.response.PlatformGlobalConfigResponse;
import com.fangcloud.thirdpartplatform.service.GlobalConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class GlobalConfigServiceImpl implements GlobalConfigService {

    @Resource
    private EnterpriseServiceImpl enterpriseService;

    @Resource
    private CustomNacosConfig nacosConfig;

    public static Map<String, Map<String, String>> GLOBAL_CONFIG_MAP = new HashMap();

    @PostConstruct
    public void initGlobalConfigMap() {

        if (nacosConfig.getCustomPublic()){
            return;
        }
        log.info("init global config map start!");
        List<Enterprise> enterpriseInfoList = enterpriseService.query();

        for (Enterprise enterpriseInfo : enterpriseInfoList) {
            String additionalInfo = enterpriseInfo.getAdditionalInfo();
            if(StringUtils.isNotEmpty(additionalInfo)){
                JSONArray platformGlobalConfigObj = (JSONArray) JSONPath.extract(additionalInfo, SyncTaskConstants.PLATFORM_GLOBAL_CONFIG_JSON_PATH);
                if(!Objects.isNull(platformGlobalConfigObj)){
                    Map<String, String> dataMap = new HashMap<>();
                    for (Object o : platformGlobalConfigObj) {
                        GlobalConfig globalConfig = JSONObject.parseObject(o.toString(), GlobalConfig.class);
                        if(PlatformGlobalConfigKeyEnum.keys().contains(globalConfig.getKey())){
                            dataMap.put(globalConfig.getKey(), globalConfig.getValue());
                        }
                    }
                    GLOBAL_CONFIG_MAP.put(enterpriseInfo.getId()+ "", dataMap);
                }
            }

            Map<String, String> map = GLOBAL_CONFIG_MAP.get(enterpriseInfo.getId() + "");
            if(CollectionUtils.isEmpty(map)){
                Map<String, String> dataMap = new HashMap<>();
                for (PlatformGlobalConfigKeyEnum en : PlatformGlobalConfigKeyEnum.values()) {
                    dataMap.put(en.getKey(), en.getDefaultValue());
                }
                GLOBAL_CONFIG_MAP.put(enterpriseInfo.getId()+ "", dataMap);
            }

        }
        log.info("GLOBAL_CONFIG_MAP:{}", JSON.toJSONString(GLOBAL_CONFIG_MAP));
        log.info("init global config map end!");
    }



    @Override
    public PlatformGlobalConfigResponse get(Integer enterpriseId) {

        Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
        if(Objects.isNull(enterpriseInfo)){
            throw new ParamException("企业不存在");
        }
        PlatformGlobalConfigResponse platformGlobalConfigResponse = new PlatformGlobalConfigResponse();
        platformGlobalConfigResponse.setEnterpriseId(enterpriseId);

        String additionalInfo = enterpriseInfo.getAdditionalInfo();

        List<GlobalConfig> globalConfigList = new ArrayList<>();

        // 若数据库中存在全局配置，返回数据库中配置，否则返回全局配置默认值
        if(StringUtils.isNotEmpty(additionalInfo)){
            Object platformGlobalConfigObj = JSONPath.extract(additionalInfo, SyncTaskConstants.PLATFORM_GLOBAL_CONFIG_JSON_PATH);
            if (!Objects.isNull(platformGlobalConfigObj)) {
                globalConfigList = JSON.parseArray(platformGlobalConfigObj.toString(), GlobalConfig.class);
            }
        }

        for (PlatformGlobalConfigKeyEnum en : PlatformGlobalConfigKeyEnum.values()) {
            if (globalConfigList.stream().noneMatch(config -> config.getKey().equals(en.getKey()))) {
                GlobalConfig globalConfig = new GlobalConfig();
                globalConfig.setKey(en.getKey());
                globalConfig.setValue(en.getDefaultValue());
                globalConfig.setDesc(en.getDesc());
                globalConfigList.add(globalConfig);
            }
        }
        platformGlobalConfigResponse.setConfigList(globalConfigList);
        return platformGlobalConfigResponse;
    }

    @Override
    public void save(PlatformGlobalConfigParam platformGlobalConfigParam) {
        Integer enterpriseId = platformGlobalConfigParam.getEnterpriseId();
        List<GlobalConfig> configList = platformGlobalConfigParam.getConfigList();
        if(CollectionUtils.isEmpty(configList)){
            return;
        }
        Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
        if(Objects.isNull(enterpriseInfo)){
            throw new ParamException("企业不存在");
        }
        String additionalInfo = enterpriseInfo.getAdditionalInfo();
        if(StringUtils.isEmpty(additionalInfo)){
            additionalInfo = "{}";
        }
        JSONObject jsonObject = (JSONObject) JSONObject.parse(additionalInfo);
        jsonObject.put("platform_global_config", configList);
        enterpriseInfo.setAdditionalInfo(JSON.toJSONString(jsonObject));
        enterpriseService.updateAdditionalInfoById(enterpriseInfo);
        // 重置企业配置
        initGlobalConfigMap();
    }

    
    public static Map<String, String> getGlobalConfigMap(String enterpriseId) {
        Map<String, String> map = GLOBAL_CONFIG_MAP.get(enterpriseId);
        if(CollectionUtils.isEmpty(map)){
            Map<String, String> dataMap = new HashMap<>();
            for (PlatformGlobalConfigKeyEnum en : PlatformGlobalConfigKeyEnum.values()) {
                dataMap.put(en.getKey(), en.getDefaultValue());
            }
            GLOBAL_CONFIG_MAP.put(enterpriseId, dataMap);
        }
        return GLOBAL_CONFIG_MAP.get(enterpriseId);
    }

}
