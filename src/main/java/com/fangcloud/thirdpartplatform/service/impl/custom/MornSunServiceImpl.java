package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.login.LoginParamEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class MornSunServiceImpl implements ApiExecuteService {

    @Resource
    private RedisStringManager redisStringManager;

    @Resource
    private OpenClientHelper openClientHelper;

    @Resource
    HttpClientHelper httpClientHelper;

    private static final String aeskey="01morsn09jsy0806";

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        JSONObject jsonObject = JSONObject.parseObject(apiResult);
        String userName = jsonObject.getString(LoginParamEnum.USER_NAME.getType());
        String password = jsonObject.getString(LoginParamEnum.PASSWORD.getType());
        String encrypt3DES = encrypt3DES(password);
        log.info("mornSun login username {}, encrypt password {}", userName, encrypt3DES);
        String userInfo = redisStringManager.get(SyncTaskConstants.CUSTOM_USER_PASSWORD + enterpriseId + "_" + userName);
        log.info("get redis user info {}", userInfo);
        YfyUser yfyUser = JSONObject.parseObject(userInfo, YfyUser.class);
        if (encrypt3DES.equals(yfyUser.getCustomPassword())) {
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("data", yfyUser);
            return jsonObject1.toJSONString();
        }
        return null;
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.MORNSUN.getDesc();
    }


    public static String encrypt3DES(String password) {
        try {
            // 使用MD5生成密钥
            final MessageDigest md = MessageDigest.getInstance("MD5");
            final byte[] digestOfPassword = md.digest("saas3deskey@ess123".getBytes(StandardCharsets.UTF_8));
            final byte[] keyBytes = new byte[24];
            System.arraycopy(digestOfPassword, 0, keyBytes, 0, Math.min(digestOfPassword.length, 24));
            for (int j = 0, k = 16; j < 8; ) {
                keyBytes[k++] = keyBytes[j++];
            }

            // 创建和初始化密码器
            SecretKey key = new SecretKeySpec(keyBytes, "DESede");
            Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, key);

            // 执行加密操作
            final byte[] plainTextBytes = password.getBytes(StandardCharsets.UTF_8);
            final byte[] cipherText = cipher.doFinal(plainTextBytes);

            // 返回Base64编码的加密字符串
            return Base64.getEncoder().encodeToString(cipherText);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取免密登陆url
     * @param request
     * @param configInfo
     * @return
     */
    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo)
    {

        //解析用户参数empCode
        String empCodeEncry=request.getParameter("empCode");
        if (StringUtils.isEmpty(empCodeEncry))
        {
            log.error("mornsun 免密登陆为空 code {}",empCodeEncry);
            return null;
        }
        String empCode=decryEmpcode(empCodeEncry);
        try {
            if (empCode != null) {
                log.info("the current empCode :{}",empCode);
                EnterpriseParams enterpriseParams = new EnterpriseParams();
                enterpriseParams.setEnterpriseId(configInfo.getEnterpriseId());
                enterpriseParams.setIdentifier(empCode);
                enterpriseParams.setPlatformId(configInfo.getPlatformId());
                enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
                enterpriseParams.setClientId(configInfo.getClientId());
                String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
                if (StringUtils.isNotEmpty(configInfo.getRedirectUrl())&&StringUtils.isNotEmpty(loginUrl)) {
                    loginUrl = loginUrl + "&redirect=" + URLEncoder.encode(configInfo.getRedirectUrl());
                }
                log.info("mornsun 免密登录url {}", loginUrl);
                return loginUrl;
            }
        } catch (Exception e) {
            log.error("mornsun 异常", e);
        }
        return null;
    }

    /**
     * 参数解密
     * @param empCodeEncry
     * @return
     */
    public static String decryEmpcode(String empCodeEncry)
    {

        try {
            KeyGenerator kgen = KeyGenerator.getInstance("AES");
            SecureRandom secureRandom = SecureRandom.getInstance("SHA1PRNG");
            secureRandom.setSeed(aeskey.getBytes());
            kgen.init(128, secureRandom);
            SecretKey secretKey = kgen.generateKey();
            byte[] enCodeFormat = secretKey.getEncoded();
            SecretKeySpec key = new SecretKeySpec(enCodeFormat, "AES");
            Cipher cipher = Cipher.getInstance("AES");
            cipher.init(2, key);
            String result = new String(cipher.doFinal(hex2byte(empCodeEncry.getBytes("UTF-8"))));
            return result.trim();

        }catch (Exception e)
        {
           log.error("参数解密失败异常 e {}",e);
        }
        return null;

    }

    private static byte[] hex2byte(byte[] b) {
        if (b.length % 2 != 0) {
            throw new IllegalArgumentException("长度不是偶数!");
        } else {
            byte[] b2 = new byte[b.length / 2];

            for(int n = 0; n < b.length; n += 2) {
                String item = new String(b, n, 2);
                b2[n / 2] = (byte)Integer.parseInt(item, 16);
            }

            return b2;
        }
    }


    public JSONObject sync_dept(HttpServletRequest request, HttpServletResponse response, JSONObject jSONObject) {
        try {
            //1.参数校验
            String token= jSONObject.getString("Token");
            String api=jSONObject.getString("ThirdUrl");
            String rootDeptName= StringUtils.isEmpty(jSONObject.getString("RootDeptName"))?"AB":jSONObject.getString("RootDeptName");
            if (StringUtils.isEmpty(token)||StringUtils.isEmpty(api))
            {
                log.error("mornsun 部门同步为空 token {}",token);
                return null;
            }
            //2.封装请求
            Map<String,Object> bodyMap=new HashMap<>();
            bodyMap.put("Token",token);
            bodyMap.put("PublicApiCode","up_jsy_org_group");
            Response res=httpClientHelper.postResponse(api,JSONObject.toJSONString(bodyMap));
            if (response == null || res.body() == null) {
                throw new ParamException("接口返回数据为空 url:" + api + "postData:" + JSONObject.toJSONString(bodyMap));
            }
            return covertApiData(res.body(),rootDeptName);

        }catch (Exception e)
        {
            log.error("部门同步异常 错误信息:{}",e);
            return null;
        }
    }

    private JSONObject covertApiData(ResponseBody body,String rootDeptName) throws Exception{
        //将String转成JSONobject
        JSONObject jsonObject= JSON.parseObject(body.string());
        //取出Data并遍历修改数据
        if (jsonObject.getJSONObject("Data")!=null&&jsonObject.getJSONObject("Data").getJSONArray("Table")!=null)
        {
           //在部门列表中加入虚拟部门跟部门（AB）
            JSONObject rootObject=new JSONObject();
            rootObject.put("ParentID","");
            rootObject.put("OrgTitle",rootDeptName);
            rootObject.put("OrgID","ab_0001");
            jsonObject.getJSONObject("Data").getJSONArray("Table").add(rootObject);
            //遍历部门找出AB公司且parentID=""的部门将其parentId设置成ab_0001

            for (int i = 0;i < jsonObject.getJSONObject("Data").getJSONArray("Table").size();i++)
            {
                JSONObject object = jsonObject.getJSONObject("Data").getJSONArray("Table").getJSONObject(i);
                if (StringUtils.isEmpty(object.getString("ParentID"))&&"AB".equals(object.getString("company")))
                {
                    object.put("ParentID","ab_0001");
                }
                if ("集团组织".equals(object.getString("OrgTitle"))){
                    object.put("OrgTitle","金升阳科技有限公司");
                }
            }
        }
        return jsonObject;
    }
}
