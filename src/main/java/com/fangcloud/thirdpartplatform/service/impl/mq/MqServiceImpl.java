package com.fangcloud.thirdpartplatform.service.impl.mq;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyInvokeConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformThirdpartyMessageMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.CodeScriptValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.PlatformThirdpartyMessageValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdPartyCall;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.service.MqService;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.service.factory.ThirdpartyExecuteServiceFactory;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.thirdparty.ThirdpartyInvokeServiceImpl;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Service
@Slf4j
public class MqServiceImpl implements MqService {

    public static String MQ_MSG_ADDITIONAL_INFO_PATH = "$.additional_infos[0]";

    @Autowired
    private ThirdpartyExecuteServiceFactory thirdpartyExecuteServiceFactory;

    @Autowired
    private PlatformThirdpartyMessageMapper platformThirdpartyMessageMapper;


    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private ThirdpartyInvokeServiceImpl thirdpartyInvokeService;

    @Resource
    private APIHelper apiHelper;

    @Override
    public void thirdPartyCall(String messageStr) throws Exception {

        JSONObject jsonObject = (JSONObject) JSONPath.extract(messageStr, MQ_MSG_ADDITIONAL_INFO_PATH);
        String type = jsonObject.getString("type");
        Long enterpriseId = jsonObject.getLong("enterprise_id");

        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId+ "");
        String thirdPartyCallUrl = configMap.get(PlatformGlobalConfigKeyEnum.THIRDPARTY_CALL_URL.getKey());


        CodeScriptValueBox codeScriptValueBox = thirdpartyInvokeService.getCodeScriptValueBox(Math.toIntExact(enterpriseId), ThirdpartyInvokeConstants.THIRDPARTY_INVOKE_BPM_CALL);

        // 若没有配置第三方地址，不进行任何操作
        if(StringUtils.isEmpty(thirdPartyCallUrl) && Objects.isNull(codeScriptValueBox)){
            log.info("enterpriseId is :{}, thirdPartyCallUrl is empty!", enterpriseId);
            return;
        }

        try {
            insertPlatformThirdpartyMessage(type, enterpriseId, jsonObject);
        }catch (Exception e){ //若插入数据库异常，则不请求往第三方系统推送数据
            log.info("insert platformThirdpartyMessage error:{}", e.getMessage());
            return;
        }

        doThirdPartyCall(jsonObject, type, thirdPartyCallUrl, configMap, codeScriptValueBox);

    }

    /**
     * 插入三方调用记录
     * @param type
     * @param enterpriseId
     * @param jsonObject
     */
    private void insertPlatformThirdpartyMessage(String type, Long enterpriseId, JSONObject jsonObject) {
        PlatformThirdpartyMessage platformThirdpartyMessage = new PlatformThirdpartyMessage();
        platformThirdpartyMessage.setMessageType(type);
        platformThirdpartyMessage.setEnterpriseId(enterpriseId);
        platformThirdpartyMessage.setUniqueId(jsonObject.getString("unique_id"));
        platformThirdpartyMessage.setDeleted(0);
        platformThirdpartyMessage.setCreated(new Date().getTime());
        platformThirdpartyMessage.setUpdated(new Date().getTime());

        PlatformThirdpartyMessageValueBox platformThirdpartyMessageValueBox = new PlatformThirdpartyMessageValueBox();
        platformThirdpartyMessageValueBox.setMqMessage(jsonObject);
        platformThirdpartyMessage.setValueBox(JSON.toJSONString(platformThirdpartyMessageValueBox));

        platformThirdpartyMessageMapper.insert(platformThirdpartyMessage);
    }

    /**
     * 往第三方系统推送数据
     * @param jsonObject
     * @param type
     * @param thirdPartyCallUrl
     * @param configMap
     * @param codeScriptValueBox
     * @throws IOException
     */
    private void doThirdPartyCall(JSONObject jsonObject, String type, String thirdPartyCallUrl, Map<String, String> configMap, CodeScriptValueBox codeScriptValueBox) throws Exception {
        ThirdpartyExecuteService handler = thirdpartyExecuteServiceFactory.getHandler(type);
        ThirdpartyParams thirdpartyParams = new ThirdpartyParams();
        thirdpartyParams.setData(jsonObject);

        // 组装标准调用参数
        Result<Object> execute = handler.execute(thirdpartyParams);
        ThirdPartyCall thirdPartyCall = (ThirdPartyCall) execute.getData();
        thirdPartyCall.setCallbackUrl(configMap.get(PlatformGlobalConfigKeyEnum.THIRDPARTY_CALLBACK_URL.getKey()));

        String result = null;
        if(Strings.isNotEmpty(thirdPartyCallUrl)){
            httpClientHelper.setHeaders(buildAuthHeader(configMap));
            Response response = httpClientHelper.postResponse(thirdPartyCallUrl, JSON.toJSONString(execute.getData()));
            if(!Objects.isNull(response.body())){
                result = response.body().string();
            }
        }else {
            result = (String) apiHelper.executeContext(URLDecoder.decode(codeScriptValueBox.getCodeScript(), "UTF-8"), null);
        }
        log.info("thirdPartyCall result {}", result);

        if(Objects.isNull(result)){
            log.info("thirdPartyCall result is null!");
        }else {
            updateThirdpartyMessage(result, jsonObject);
        }

    }

    private void updateThirdpartyMessage(String result, JSONObject jsonObject) {

        Long enterpriseId = jsonObject.getLong("enterprise_id");
        String uniqueId = jsonObject.getString("unique_id");
        PlatformThirdpartyMessage platformThirdpartyMessage = platformThirdpartyMessageMapper.selectByEnterpriseIdAndUniqueId(enterpriseId, uniqueId);

        if(Objects.isNull(platformThirdpartyMessage)){
            throw new ParamException("platformThirdpartyMessage is null !");
        }

        PlatformThirdpartyMessageValueBox platformThirdpartyMessageValueBox = JSON.parseObject(platformThirdpartyMessage.getValueBox(), PlatformThirdpartyMessageValueBox.class);
        platformThirdpartyMessageValueBox.setCallResult(JSONObject.parseObject(result));
        platformThirdpartyMessage.setValueBox(JSON.toJSONString(platformThirdpartyMessageValueBox));
        platformThirdpartyMessageMapper.updateByPrimaryKeySelective(platformThirdpartyMessage);

    }

    /**
     * 构建鉴权信息
     * @return
     * @param configMap
     */
    private Map<String, String> buildAuthHeader(Map<String, String> configMap) {

        String thirdPartyCallAuthUsername = configMap.get(PlatformGlobalConfigKeyEnum.THIRDPARTY_CALL_AUTH_USERNAME.getKey());
        String thirdPartyCallAuthPassword = configMap.get(PlatformGlobalConfigKeyEnum.THIRDPARTY_CALL_AUTH_PASSWORD.getKey());

        Map<String, String> authMap = new HashMap<>();
        authMap.put("Authorization", "Basic "+ Base64Utils.encode(thirdPartyCallAuthUsername + ":" +thirdPartyCallAuthPassword));

        return authMap;
    }
}
