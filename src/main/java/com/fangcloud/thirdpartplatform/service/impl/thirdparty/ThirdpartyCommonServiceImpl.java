package com.fangcloud.thirdpartplatform.service.impl.thirdparty;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.db.dao.PlatformThirdpartyMessageMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.PlatformThirdpartyMessageValueBox;
import com.fangcloud.thirdpartplatform.service.ThirdpartyCommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class ThirdpartyCommonServiceImpl implements ThirdpartyCommonService {

    @Autowired
    private PlatformThirdpartyMessageMapper platformThirdpartyMessageMapper;


    @Override
    public void checkCallbackResult(String uniqueId, long enterpriseId, String reviewType, boolean reviewResult) {
        PlatformThirdpartyMessage platformThirdpartyMessage = platformThirdpartyMessageMapper.selectByEnterpriseIdAndMessageTypeAndUniqueId(
                enterpriseId,
                reviewType,
                uniqueId);

        if(Objects.isNull(platformThirdpartyMessage)){
            throw new ParamException("platformThirdpartyMessage is null !");
        }

        PlatformThirdpartyMessageValueBox platformThirdpartyMessageValueBox = JSON.parseObject(platformThirdpartyMessage.getValueBox(), PlatformThirdpartyMessageValueBox.class);
        if(!Objects.isNull(platformThirdpartyMessageValueBox.getCallbackResult())){
            throw new ParamException("platformThirdpartyMessage already callback !");
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("reviewResult", reviewResult);
        platformThirdpartyMessageValueBox.setCallbackResult(jsonObject);
        platformThirdpartyMessage.setValueBox(JSON.toJSONString(platformThirdpartyMessageValueBox));
        platformThirdpartyMessageMapper.updateByPrimaryKeySelective(platformThirdpartyMessage);
    }
}
