package com.fangcloud.thirdpartplatform.service.impl.syncTask;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.TaskTypeEnum;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.sync.CustomUser;
import com.fangcloud.thirdpartplatform.service.SyncTaskHandler;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.SyncServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.UserServiceImpl;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class SingleTaskingSyncHandler implements SyncTaskHandler {

    @Resource
    private SyncServiceImpl syncServiceImpl;

    @Resource
    private UserServiceImpl userServiceimpl;

    @Resource
    private EnterpriseServiceImpl enterpriseServiceImpl;

    @Override
    public ExecuteResult executeResult(PlatformSyncConfig platformSyncConfig, List<YfyUser> yfyUserList, Integer taskId) {
        // 1. 获取企业信息
        Enterprise enterprise = enterpriseServiceImpl.getEnterpriseById(platformSyncConfig.getEnterpriseId());
        if(Objects.isNull(enterprise)){
            log.info("enterprise id {}, info is null !",platformSyncConfig.getEnterpriseId());
            throw new ParamException("enterprise is null !");
        }
        // 2. 获取客户既存的用户信息
        List<CustomUser> customUserList = new ArrayList<>();
        if (platformSyncConfig.getSourceType().equals(SourceTypeEnum.PUSH.getDesc())){
            //不查全量用户，只查此次推送仅有的
            List<String> yfyUserIdList = yfyUserList.stream().map(y -> y.getId()).collect(Collectors.toList());
            customUserList = userServiceimpl.findUsersByIds(enterprise.getPlatformId(),yfyUserIdList);

        }else {
            customUserList = userServiceimpl.findAll(enterprise.getPlatformId());
        }
        Boolean isCreateDefaultPassword = false;
        String customPassword = null;
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(platformSyncConfig.getEnterpriseId()+ "");
        if (!CollectionUtils.isEmpty(configMap)) {
            String isCreateDefaultPasswordConfig = configMap.get(PlatformGlobalConfigKeyEnum.CREATE_DEFAULT_PASSWORD.getKey());
            if (StringUtils.isNotBlank(isCreateDefaultPasswordConfig)) {
                isCreateDefaultPassword = Boolean.valueOf(isCreateDefaultPasswordConfig);
            }
            customPassword = configMap.get(PlatformGlobalConfigKeyEnum.CUSTOM_PASSWORD.getKey());
        }
        // 3. 同步用户数据
        log.info("sync user startTime:{}",System.currentTimeMillis());
        ExecuteResult executeResult = syncServiceImpl.syncUsers(customUserList, yfyUserList, taskId, enterprise, platformSyncConfig, isCreateDefaultPassword, customPassword);
        log.info("sync user endTime:{}",System.currentTimeMillis());
        return executeResult;
    }

    @Override
    public String getSyncTaskType() {
        return TaskTypeEnum.SINGLETASKING.getDesc();
    }
}