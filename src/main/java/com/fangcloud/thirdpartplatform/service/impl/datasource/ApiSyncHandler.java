package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.*;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.APIParamConfigDto;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.service.factory.ApiExecuteServiceFactory;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.utils.TimestampUtil;
import com.fangcloud.thirdpartplatform.utils.XMLUtils;
import com.mashape.unirest.http.HttpResponse;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.json.XML;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.fangcloud.thirdpartplatform.helper.APIHelper.OAUTH_HEADER_RESULT;

@Component
@Slf4j
public class ApiSyncHandler extends AbstractDataSourceSyncHandler {
    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private APIHelper apiHelper;

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private ApiExecuteServiceFactory apiExecuteServiceFactory;

    @Resource
    private RedisStringManager redisStringManager;

    public static void main(String[] args) {
        String s1 = buildDynamicDateValue("month::-10", "yyyy-MM-dd HH:mm:SS");
        String s = "{\"apiConfig\":{\"api\":\"https://mock.apifox.cn/m2/435008-0-default/35816815?apifoxApiId=35816815\",\"pagedVerify\":{\"path\":\"$.data.resultSize\",\"ruleScript\":\"resultSize > 0\"},\"paramConfig\":[{\"dataType\":\"int\",\"format\":\"++\",\"name\":\"pageNo\",\"paramWay\":\"PARAMS\",\"value\":\"1\"}],\"requestMethod\":\"GET\",\"resultConfig\":[{\"dataType\":\"String\",\"format\":\"\",\"mappingName\":\"id\",\"name\":\"0\",\"remark\":\"\",\"ruleScript\":\"\"},{\"dataType\":\"String\",\"format\":\"\",\"mappingName\":\"parentId\",\"name\":\"1\",\"remark\":\"\",\"ruleScript\":\"\"},{\"dataType\":\"String\",\"mappingName\":\"name\",\"format\":\"script::name.split(\\\"（\\\")[0]\",\"name\":\"3\"},{\"dataType\":\"String\",\"mappingName\":\"isTop\",\"name\":\"0\",\"ruleScript\":\"isTop == 1\"}],\"resultPath\":\"$.data.result.content\",\"resultVerify\":{\"path\":\"\",\"ruleScript\":\"\"}},\"depConfigDto\":{\"collabAutoAccepted\":false,\"publicFolder\":false},\"deptSpace\":20,\"oauth2Config\":{\"api\":\"\",\"paramConfig\":[{\"dataType\":\"String\",\"name\":\"tokenKey\",\"paramWay\":\"PARAMS\",\"value\":\"OITIcLGR-TgHVSK-BoCX90tXiaVc\"}],\"requestMethod\":\"GET\"},\"repeatHandle\":\"OVER\",\"verificationMode\":\"other\"}";
        String user = "{\"apiConfig\":{\"api\":\"https://mock.apifox.cn/m2/435008-0-default/35816815?apifoxApiId=35816815\",\"pagedVerify\":{\"path\":\"$.data.resultSize\",\"ruleScript\":\"resultSize > 0\"},\"paramConfig\":[{\"dataType\":\"int\",\"format\":\"++\",\"name\":\"pageNo\",\"paramWay\":\"PARAMS\",\"value\":\"1\"}],\"requestMethod\":\"GET\",\"resultConfig\":[{\"dataType\":\"String\",\"format\":\"\",\"mappingName\":\"fullName\",\"name\":\"0\",\"remark\":\"\",\"ruleScript\":\"\"},{\"dataType\":\"String\",\"format\":\"script::if(!email.contains(\\\"@\\\"))email=null;\",\"mappingName\":\"email\",\"name\":\"1\",\"remark\":\"\",\"ruleScript\":\"\"},{\"dataType\":\"String\",\"format\":\"\",\"mappingName\":\"id\",\"ruleScript\":\"id.indexOf(\\\"L\\\")==0\",\"name\":\"2\"},{\"dataType\":\"String\",\"mappingName\":\"isDelete\",\"name\":\"3\",\"ruleScript\":\"!isDelete.equals(\\\"在职\\\")\"},{\"dataType\":\"String\",\"format\":\"\",\"mappingName\":\"departmentId\",\"name\":\"5\"}],\"resultPath\":\"$.data.result.content\",\"resultVerify\":{\"path\":\"\",\"ruleScript\":\"\"}},\"userConfigDto\":{\"emailSuffix\":\"360.cn\",\"syncPhone\":\"false\",\"isActivate\":\"true\"},\"userSpace\":20,\"oauth2Config\":{\"api\":\"\",\"paramConfig\":[{\"dataType\":\"String\",\"name\":\"tokenKey\",\"paramWay\":\"PARAMS\",\"value\":\"OITIcLGR-TgHVSK-BoCX90tXiaVc\"}],\"requestMethod\":\"GET\"},\"repeatHandle\":\"OVER\",\"verificationMode\":\"other\"}";
        APIConfigValueBox apiConfigValueBox = JSON.parseObject(user, APIConfigValueBox.class);
        String ss = "{\"correlationSerialNum\":\"31255276-110d-4aa3-9b04-956cfff7d7d7\",\"data\":{\"page\":1,\"pageSize\":1000,\"resourceID\":248,\"result\":{\"content\":[[\"0101\",\"1\",2,\"光电制造学院（含现代制造与材料技术中心、莱特激光工程研究院、环保学院、环保研究院）\",\"A\",\"20211018\",\"2021-10-18 22:35:41\"],[\"1\",\"0\",1,\"浙江工贸职业技术学院\",\"A\",\"20211224\",\"2021-12-24 12:10:04\"]],\"head\":\"id,parentid,layer,DWH,DC_DCFIELDAUD,DC_DCDATADATE,DC_DCTIMESTAMP\",\"tablename\":\"NHRS_VIEW_XXZX_ZZJG\"},\"resultSize\":37,\"source\":\"SSJH\",\"totalSize\":37},\"errCode\":\"0\",\"errMsg\":\"\",\"msgCode\":\"C001\",\"msgFromSysID\":\"SJJH\",\"msgToSysID\":\"YFYP\",\"nonce\":\"-1793641741\",\"reqSerialNum\":\"SJJH20220823141910489\",\"sign\":\"fcIDNdcUJouRYSByCy7MATYuidiyIRxBgJAXzEARsIe9lpU1/Dcgk+ujv+iD0ufbU7rUWVx7ZlIeVkm7o70AS6HR1zr2fSuyGnEJ3KBem6J4GPAZBAy8QjE57bVhB5GYgnVn4FAKx1fqGgmsUeqj3DRyrHVJLAaRtYusDcNAOQ4=\",\"time\":\"20220823141910\"}";
        String sss = "{\"correlationSerialNum\":\"0c5a38f3-18cb-47d0-8e2d-0012acd4704f\",\"data\":{\"page\":1,\"pageSize\":2000,\"resourceID\":245,\"result\":{\"content\":[[\"叶佳佳\",null,\"20200044\",\"在职\",\"科研处\",\"0139\",\"A\",\"20220509\",\"2022-05-09 17:40:47\"]],\"head\":\"XM,DZXX,JGH,DQZTM,DWH,DWHID,DC_DCFIELDAUD,DC_DCDATADATE,DC_DCTIMESTAMP\",\"tablename\":\"NHRS_VIEW_XXZX_JZG\"},\"resultSize\":1395,\"source\":\"SSJH\",\"totalSize\":1395},\"errCode\":\"0\",\"errMsg\":\"\",\"msgCode\":\"C001\",\"msgFromSysID\":\"SJJH\",\"msgToSysID\":\"YFYP\",\"nonce\":\"1385610956\",\"reqSerialNum\":\"SJJH20220826105315606\",\"sign\":\"LaGwJkqny/SLdxlM/f0kxjPYe7bIxlYC5ffQA1Hj5RLTelmoQQIAiG07mkXtdUfUe57n56/5p2YGGZ8jqoWOWxhZIZi/RLzb7oJ7Le1SlLllzIOujxn1m+WPqTmbCGbIUn2O3qUs9eL3mNsBIGCnaokzv+DpxZsSKpNSrDdmgIk=\",\"time\":\"20220826105315\"}";
        APIHelper apiHelper = new APIHelper();
        JSONArray jsonArray = (JSONArray)JSONPath.extract(sss, "$.data.result.content");
        //Boolean aBoolean = apiHelper.checkRuleFromScript("$.data.data", resultDataByPath, "data != null");
        List<YfyUser> yfyDepartments = apiHelper.buildUser(jsonArray, apiConfigValueBox);
        System.out.println();
    }

    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        APIConfigValueBox apiConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        syncPublicSetTaskId(apiConfigValueBox, taskId);
        JSONArray apiConfigData = new JSONArray();
        if (ApiVerificationModeEnum.OAUTH2.getDesc().equals(apiConfigValueBox.getVerificationMode())) {
            String oauth2ApiConfigData = executeOAuthApi(apiConfigValueBox, platformSyncConfig.getEnterpriseId());
            log.info("oauth2ApiConfigData:{}", oauth2ApiConfigData);
            apiConfigData = getApiConfigData(apiConfigValueBox, oauth2ApiConfigData, platformSyncConfig.getEnterpriseId());
            log.info("apiConfigData:{}", apiConfigData);
            if (apiConfigData.isEmpty()) {
                throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
            }
        }
        if (ApiVerificationModeEnum.OTHER.getDesc().equals(apiConfigValueBox.getVerificationMode())) {
            apiConfigData = getApiConfigData(apiConfigValueBox, null, platformSyncConfig.getEnterpriseId());
            if (apiConfigData.isEmpty()) {
                throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
            }
        }
        ExecuteResult executeResult = JSONObject.parseObject(apiConfigData.get(0).toString(), ExecuteResult.class);
        return executeResult;
    }



    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        List<YfyUser> users = new ArrayList<>();
        APIConfigValueBox apiConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        log.info("syncUser-apiConfigValueBox:{}", apiConfigValueBox);
        getYfyUsersByConfigValueBox(users, apiConfigValueBox, platformSyncConfig.getEnterpriseId());
        log.info("apiSyncHandler-syncUser, users:{}, taskId:{}", JSON.toJSONString(users), taskId);
        if (CollectionUtils.isEmpty(users)) {
            return executeResult;
        }
        if (StringUtils.isNotBlank(apiConfigValueBox.getUserConfigDto().getCacheUserKey())) {
            users.forEach(user -> redisStringManager.set(apiConfigValueBox.getUserConfigDto().getCacheUserKey() + "_" + platformSyncConfig.getEnterpriseId() + "_" + user.getId(), JSON.toJSONString(user), 60 * 60 * 24L));
            return executeResult;
        }
        return syncUserList(platformSyncConfig, users, taskId);
    }

    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        List<YfyDepartment> departments = new ArrayList<>();
        APIConfigValueBox apiConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        getYfyDepartmentsByConfigValueBox(departments, apiConfigValueBox, platformSyncConfig.getEnterpriseId());
        log.info("apiSyncHandler-syncDepartment, department:{}, taskId:{}", JSON.toJSONString(departments), taskId);
        if (CollectionUtils.isEmpty(departments)) {
            return executeResult;
        }
        return syncDepartmentList(platformSyncConfig, departments, taskId);
    }

    public void getYfyUsersByConfigValueBox(List<YfyUser> users, APIConfigValueBox apiConfigValueBox, Integer enterpriseId) {
        if (ApiVerificationModeEnum.OAUTH2.getDesc().equals(apiConfigValueBox.getVerificationMode())) {
            String oauth2ApiConfigData = executeOAuthApi(apiConfigValueBox, enterpriseId);
            log.info("syncUser-oauth2ApiConfigData:{}", oauth2ApiConfigData);
            JSONArray apiConfigData = getApiConfigData(apiConfigValueBox, oauth2ApiConfigData, enterpriseId);
            log.info("syncUser-apiData:{}", apiConfigData);
            if (apiConfigData.isEmpty()) {
                throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
            }
            List<YfyUser> yfyUsers = apiHelper.buildUser(apiConfigData, apiConfigValueBox);
            users.addAll(yfyUsers);
        }
        if (ApiVerificationModeEnum.OTHER.getDesc().equals(apiConfigValueBox.getVerificationMode())) {
            JSONArray apiConfigData = getApiConfigData(apiConfigValueBox, null, enterpriseId);
            log.info("syncUser-apiConfigData:{}", apiConfigData);
            if (apiConfigData.isEmpty()) {
                throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
            }
            List<YfyUser> yfyUsers = apiHelper.buildUser(apiConfigData, apiConfigValueBox);
            users.addAll(yfyUsers);
        }
    }

    public void getYfyDepartmentsByConfigValueBox(List<YfyDepartment> departments, APIConfigValueBox apiConfigValueBox, Integer enterpriseId) {
        if (ApiVerificationModeEnum.OAUTH2.getDesc().equals(apiConfigValueBox.getVerificationMode())) {
            String oauth2ApiConfigData = executeOAuthApi(apiConfigValueBox, enterpriseId);
            log.info("oauth2ApiConfigData:{}", oauth2ApiConfigData);
            JSONArray apiConfigData = getApiConfigData(apiConfigValueBox, oauth2ApiConfigData, enterpriseId);
            log.info("apiConfigData:{}", apiConfigData);
            if (apiConfigData.isEmpty()) {
                throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
            }
            List<YfyDepartment> yfyDepartments = apiHelper.buildDept(apiConfigData, apiConfigValueBox);
            departments.addAll(yfyDepartments);
        }
        if (ApiVerificationModeEnum.OTHER.getDesc().equals(apiConfigValueBox.getVerificationMode())) {
            JSONArray apiConfigData = getApiConfigData(apiConfigValueBox, null, enterpriseId);
            if (apiConfigData.isEmpty()) {
                throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
            }
            log.info("apiConfigData:{}", apiConfigData);
            List<YfyDepartment> yfyDepartments = apiHelper.buildDept(apiConfigData, apiConfigValueBox);
            departments.addAll(yfyDepartments);
        }
    }



    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.API.getDesc();
    }

    public Object testConnectData(APIConfigValueBox apiConfigValueBox){
        Map<String, Object> map = new HashMap<>();
        String oauth2ApiConfigData = null;
        if (ApiVerificationModeEnum.OAUTH2.getDesc().equals(apiConfigValueBox.getVerificationMode())) {
            oauth2ApiConfigData = executeOAuthApi(apiConfigValueBox, null);
            map.put("oauth2Result",oauth2ApiConfigData);
        }
        String apiConfigData = executeApi(oauth2ApiConfigData, apiConfigValueBox, null, null);
        if (StringUtils.isNotEmpty(apiConfigData)) {
            map.put("apiResultData",apiConfigData);
        } else {
            map.put("apiResultData", "");
        }
        return map;
    }

    public JSONArray getApiConfigData(APIConfigValueBox apiConfigValueBox, String oauthResult, Integer enterpriseId) {
        APIConfigValueBox.APIConfig apiConfig = apiConfigValueBox.getApiConfig();
        Boolean isLoop = false;
        Integer count = 1;
        String apiResult = StringUtils.EMPTY;
        JSONArray jsonArray = new JSONArray();
        do {
            if (count >= 999) {
                return jsonArray;
            }
            apiResult = executeApi(oauthResult, apiConfigValueBox, apiResult, enterpriseId);
            count++;
            log.info("getApiConfigData-loop count:{}", count);
            JSONArray apiResultData = checkAndGet(apiConfig, apiResult);
            isLoop = needLoop(apiConfig, isLoop, apiResult);
            jsonArray.addAll(apiResultData);
        } while (isLoop);
        return jsonArray;
    }

    public String executeOAuthApi(APIConfigValueBox apiConfigValueBox, Integer enterpriseId) {
        return executeApi(null, apiConfigValueBox.getOauth2Config(), apiConfigValueBox.getProtocol(), null, enterpriseId);
    }

    private String executeApi(String oauthResult, APIConfigValueBox apiConfigValueBox, String apiResult, Integer enterpriseId) {
        return executeApi(oauthResult, apiConfigValueBox.getApiConfig(), apiConfigValueBox.getProtocol(), apiResult, enterpriseId);
    }

    public String executeLoginApi(String oauthResult,APIConfigValueBox.APIConfig apiConfig, String protocol, Integer enterpriseId, String userLoginInfo) {
        return executeApi(oauthResult, apiConfig, protocol, userLoginInfo, enterpriseId);
    }

    private String executeApi(String oauthResult, APIConfigValueBox.APIConfig apiConfig, String protocol, String apiResult, Integer enterpriseId) {
        List<APIParamConfigDto> paramConfig = apiConfig.getParamConfig();
        String url = getUrl(protocol, apiConfig);

        Map<String, String> headMap = new HashMap<>();
        Map<String, Object> bodyMap = new HashMap<>();
        Map<String, Object> paramMap = new HashMap<>();
        try {
            buildParam(oauthResult, paramConfig, headMap, bodyMap, paramMap, apiResult);
            if(!headMap.isEmpty() && StringUtils.isNotEmpty(headMap.get("Request_Type"))){
                apiResult = executeSpecialApi(headMap, paramMap, oauthResult, protocol, apiConfig, enterpriseId, apiResult);
            }else {
                if (RequestMethod.POST.name().equals(apiConfig.getRequestMethod())) {
                    apiResult = doPost(url, headMap, paramMap, bodyMap, enterpriseId);
                } else if (RequestMethod.GET.name().equals(apiConfig.getRequestMethod())) {
                    apiResult = doGet(headMap, paramMap, url, enterpriseId);
                }
            }
            // 对返回的结果进行特殊处理
            apiResult = resultProcessing(apiResult, headMap, bodyMap, paramMap, apiConfig, enterpriseId);
        } catch (Exception e) {
            log.error("executeApi-error",e);
            throw new ParamException("接口调用失败 url:" + url + "postData:" + JSONObject.toJSONString(paramMap) + "异常信息:" + e.getMessage());
        }
        log.info("executeApi url : {}, headMap:{}, paramMap : {}, bodyMap:{}, apiResult :{}",
                url, JSONObject.toJSONString(headMap), JSONObject.toJSONString(paramMap), JSONObject.toJSONString(bodyMap), apiResult);
        return apiResult;
    }

    private String executeSpecialApi(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {

        ApiExecuteService apiExecuteService = apiExecuteServiceFactory.getHandler(headMap.get("Request_Type"));

        return apiExecuteService.getApiResult(headMap, paramMap, oauthResult, protocol, apiConfig, enterpriseId, apiResult);
    }

    public JSONArray checkAndGet(APIConfigValueBox.APIConfig apiConfig, String apiResult) {
        APIConfigValueBox.VerifyConfig resultVerify = apiConfig.getResultVerify();
        if (resultVerify != null) {
            Object apiResultData = getResultDataByPath(resultVerify.getPath(), apiResult);
            Boolean checkApiResultData = apiHelper.checkRuleFromScript(resultVerify.getPath(), apiResultData, resultVerify.getRuleScript());
            if (!checkApiResultData) {
                throw new ParamException("result check fail, result:" + JSON.toJSONString(apiResultData));
            }
        }
        Object resultDataByPath = getResultDataByPath(apiConfig.getResultPath(), apiResult);
        if (resultDataByPath instanceof JSONArray) {
            return (JSONArray) resultDataByPath;
        }
        JSONArray objects = new JSONArray();
        objects.add(resultDataByPath);
        return objects;
    }

    private Boolean needLoop(APIConfigValueBox.APIConfig apiConfig, Boolean isLoop, String apiResult) {
        APIConfigValueBox.VerifyConfig pagedVerify = apiConfig.getPagedVerify();
        if (pagedVerify != null && StringUtils.isNotBlank(pagedVerify.getPath())) {
            isLoop = true;
        }
        if (isLoop) {
            Object pageData = getResultDataByPath(pagedVerify.getPath(), apiResult);
            Boolean check = apiHelper.checkRuleFromScript(pagedVerify.getPath(), pageData, pagedVerify.getRuleScript());
            if (check) {
                isLoop = false;
            } else {
                Object resultDataByPath = getResultDataByPath(apiConfig.getResultPath(), apiResult);
                if (Objects.isNull(resultDataByPath)) {
                    isLoop = false;
                }
            }
        }
        return isLoop;
    }

    public String getHttpResult(Response response) throws IOException {
        if (response == null || response.body() == null) {
            return null;
        }
        String apiResult = response.body().string();
        if (XMLUtils.isXmlStr(apiResult)) {
            apiResult = XML.toJSONObject(apiResult).toString();
        }

        JSONObject parse = (JSONObject) JSONObject.parse(apiResult);
        parse.put(OAUTH_HEADER_RESULT, response.headers().toMultimap());
        return JSON.toJSONString(parse);
    }

    private String getUnirestHttpResult(HttpResponse<String> response) throws IOException {
        if (response == null || response.getBody() == null) {
            return null;
        }
        String apiResult = response.getBody();
        if (XMLUtils.isXmlStr(apiResult)) {
            apiResult = XML.toJSONObject(apiResult).toString();
        }
        return apiResult;
    }

    public String doPost(String url, Map<String, String> headMap, Map<String, Object> paramMap, Map<String, Object> bodyMap, Integer enterpriseId) throws IOException {

        url = getUrlFromParam(paramMap, url);
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId+ "");
        String httpClientType = null;
        // 判断数据库中全局配置，若配置不为空，取数据库配置，若为空取启动项配置
        if(CollectionUtils.isEmpty(configMap)){
            httpClientType = customNacosConfig.getHttpClientType();
        }else {
            httpClientType = configMap.get(PlatformGlobalConfigKeyEnum.HTTP_CLIENT_TYPE.getKey());
        }
        if(CommonConstants.HTTP_CLIENT_TYPE_UNIREST.equals(httpClientType)){
            log.info("httpClientType is :{}", httpClientType);
            HttpResponse<String> httpResponse = httpClientHelper.unirestPost(headMap, url, JSON.toJSONString(bodyMap));
            return getUnirestHttpResult(httpResponse);
        }
        if (!headMap.isEmpty()) {
            httpClientHelper.setHeaders(headMap);
            for (String key : headMap.keySet()) {
                if (headMap.get(key).contains("application/x-www-form-urlencoded")) {
                    Response response = httpClientHelper.postResponse(url, toFormUrlencoded(bodyMap), httpClientHelper.getApplicationFormData());
                    if (response == null || response.body() == null) {
                        throw new ParamException("接口返回数据为空 url:" + url + "postData:" + JSONObject.toJSONString(bodyMap));
                    }
                    return getHttpResult(response);
                }
            }
        }

        Response response = httpClientHelper.postResponse(url, JSONObject.toJSONString(bodyMap));
        if (response == null || response.body() == null) {
            throw new ParamException("接口返回数据为空 url:" + url + "postData:" + JSONObject.toJSONString(bodyMap));
        }
        return getHttpResult(response);
    }

    private String getUrl(String protocol, APIConfigValueBox.APIConfig apiConfig) {
        if (StringUtils.isBlank(protocol)) {
            return apiConfig.getApi();
        }
        return protocol + SyncTaskConstants.URL_PREFIX + apiConfig.getApi();
    }

    public Object getResultDataByPath(String path, String apiResult) {
        Object parse = JSON.parse(apiResult);
        JSONPath compile = JSONPath.compile(path);
        return compile.eval(parse);
    }

    private void buildParam(String oauthResult, List<APIParamConfigDto> paramConfig, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, String apiResult) {
        for (APIParamConfigDto apiParamConfigDto : paramConfig) {

            Object obj;
            String value;
            if (StringUtils.isNotBlank(oauthResult) && apiHelper.isFetchParamFromResult(apiParamConfigDto.getValue())) {
                if (apiHelper.isFetchParamFromOAuthResult(apiParamConfigDto.getValue())) {

                    log.info("oauthResult :{}, apiParamConfigDto:{}", oauthResult, JSON.toJSONString(apiParamConfigDto));
                    // 先取值，再格式化
                    value = apiHelper.fetchValueFromScript(apiParamConfigDto.getValue(), oauthResult);
                    String valueByFormat = getValueByFormat(apiParamConfigDto, apiResult, value);

                    obj = DataTypeConstants.convert(valueByFormat, apiParamConfigDto.getDataType());
                } else {
                    if (StringUtils.isNotBlank(apiResult)) {
                        // 先取值，再格式化
                        value = apiHelper.fetchValueFromScript(apiParamConfigDto.getValue(), apiResult);
                        if (StringUtils.isBlank(value)) {
                            //value 为null就取默认值
                            value = apiParamConfigDto.getDefaultValue();
                        }
                        String valueByFormat = getValueByFormat(apiParamConfigDto, apiResult, value);

                        obj = DataTypeConstants.convert(valueByFormat, apiParamConfigDto.getDataType());
                    } else {
                        obj = apiParamConfigDto.getDefaultValue();
                    }
                }
            } else {
                String valueByFormat = getValueByFormat(apiParamConfigDto, apiResult, apiParamConfigDto.getValue());
                obj = DataTypeConstants.convert(valueByFormat, apiParamConfigDto.getDataType());
            }
            if (ApiParamWayEnum.HEAD.getDesc().equals(apiParamConfigDto.getParamWay())) {
                headMap.put(apiParamConfigDto.getName(), obj.toString());
            } else if (ApiParamWayEnum.BODY.getDesc().equals(apiParamConfigDto.getParamWay())) {
                if (apiParamConfigDto.getName().startsWith("empty:")) {
                    log.info("过滤需要排除的字段，为了匹配post请求 为空的请求");
                } else {
                    bodyMap.put(apiParamConfigDto.getName(), obj);
                }
            } else {
                paramMap.put(apiParamConfigDto.getName(), obj);
            }
        }
    }

    private String getValueByFormat(APIParamConfigDto apiParamConfigDto, String apiResult, String value) {
        String dataType = apiParamConfigDto.getDataType();
        String format = apiParamConfigDto.getFormat();
        if (StringUtils.isNotEmpty(format)) {
            if(CommonConstants.DYNAMIC_DATE.equals(dataType)){
                value = buildDynamicDateValue(value, format);
            } else if (DataTypeConstants.getClassByDataType(dataType) == String.class) {
                value = String.format(format, value);
            } else if (DataTypeConstants.getClassByDataType(dataType) == Date.class) {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                value = sdf.format(value);
            } else if (SyncTaskConstants.SELF_INCREASING.equals(format) && StringUtils.isNotBlank(apiResult)) {
                Object convert = DataTypeConstants.convert(value,dataType);
                long l = Long.parseLong(convert.toString());
                value =  String.valueOf(++l);
                apiParamConfigDto.setValue(value);
            }
        }
        log.info("valueByFormat is : {}", value);
        return value;
    }

    public static String buildDynamicDateValue(String value, String format) {
        if(StringUtils.isEmpty(format)){
            log.info("dynamicDate format is null!");
            return null;
        }
        if(StringUtils.isEmpty(value)){
            log.info("dynamicDate value is null!");
            return null;
        }
        log.info("dynamicDate value is :{}", value);

        String[] split = value.split("::");

        if(CommonConstants.NOW.equals(split[0])){
            return TimestampUtil.formatDate(new Date(), format);
        }

        if(split.length != 2 || !CommonConstants.dynamicDateTypeList.contains(split[0])){
            log.info("dynamicDate value format is wrong!");
            return null;
        }

        Date offsetDate = TimestampUtil.getOffsetDate(split[0], Integer.valueOf(split[1]));


        return TimestampUtil.formatDate(offsetDate, format);
    }

    public String doGet(Map<String, String> headMap, Map<String, Object> paramMap, String urlGet, Integer enterpriseId) throws IOException {
        urlGet = getUrlFromParam(paramMap, urlGet);
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId+ "");
        String httpClientType = null;
        // 判断数据库中全局配置，若配置不为空，取数据库配置，若为空取启动项配置
        if(CollectionUtils.isEmpty(configMap)){
            httpClientType = customNacosConfig.getHttpClientType();
        }else {
            httpClientType = configMap.get(PlatformGlobalConfigKeyEnum.HTTP_CLIENT_TYPE.getKey());
        }

        if(CommonConstants.HTTP_CLIENT_TYPE_UNIREST.equals(httpClientType)){
            log.info("httpClientType is :{}", httpClientType);
            HttpResponse<String> httpResponse = httpClientHelper.unirestGet(headMap, urlGet);
            return getUnirestHttpResult(httpResponse);
        }

        if (!headMap.isEmpty()) {
            httpClientHelper.setHeaders(headMap);
        }
        Response response = httpClientHelper.getResponse(urlGet);
        if (response == null || response.body() == null) {
            throw new ParamException("接口返回数据为空 url:" + urlGet + "postData:" + JSONObject.toJSONString(paramMap));
        }
        return getHttpResult(response);
    }


    private String getUrlFromParam(Map<String, Object> paramMap, String url) {
        if (!paramMap.isEmpty()) {
            for (String param : paramMap.keySet()) {
                if (url.contains("?")) {
                    url += "&" + param + "=" + paramMap.get(param);
                } else {
                    url += "?" + param + "=" + paramMap.get(param);
                }
            }
        }
        return url;
    }



    private String resultProcessing(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        if (StringUtils.isBlank(apiResult)) {
            return apiResult;
        }

        ApiExecuteService apiExecuteService = apiExecuteServiceFactory.getHandler(headMap.get("Response_Type"));
        if(Objects.isNull(apiExecuteService)){
            return apiResult;
        }

        return apiExecuteService.processApiResult(apiResult, headMap, bodyMap, paramMap, apiConfig, enterpriseId);
    }
    /**
     * 根据配置入参key获取Value
     * */
    public String getValueByParamConfig(List<APIParamConfigDto> paramConfig, String key) {
        for (APIParamConfigDto apiParamConfigDto : paramConfig) {
            if (apiParamConfigDto.getName().equals(key)) {
                return apiParamConfigDto.getValue();
            }
        }
        return null;
    }

    /**
     * 同步公有云将taskId传入用来记录错误日志
     * */
    private void syncPublicSetTaskId(APIConfigValueBox apiConfigValueBox, Integer taskId) {
        APIConfigValueBox.APIConfig apiConfig = apiConfigValueBox.getApiConfig();
        List<APIParamConfigDto> paramConfig = apiConfig.getParamConfig();
        APIParamConfigDto apiParamConfigDto = new APIParamConfigDto();
        apiParamConfigDto.setParamWay(ApiParamWayEnum.PARAMS.getDesc());
        apiParamConfigDto.setName("task_id");
        apiParamConfigDto.setDataType("int");
        apiParamConfigDto.setValue(taskId.toString());
        paramConfig.add(apiParamConfigDto);
    }

    /**
     * 请求体转化为form表单格式
     * @param bodyMap
     * @return
     */
    private String toFormUrlencoded(Map<String, Object> bodyMap) {
        try {
            StringBuilder result = new StringBuilder();
            for (String key : bodyMap.keySet()) {
                result.append(key).append('=').append(bodyMap.get(key).toString()).append('&');
            }
            result.deleteCharAt(result.length() - 1);
            log.info("Convert body to form，result is :{}", result);
            return result.toString();
        } catch (Exception e) {
            throw new ParamException("请求体编码异常!", e);
        }
    }
}
