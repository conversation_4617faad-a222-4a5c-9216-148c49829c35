package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.db.dao.EnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.service.EnterpriseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class EnterpriseServiceImpl implements EnterpriseService {

    @Autowired
    private EnterpriseMapper enterpriseMapper;


    @Override
    public Enterprise getEnterpriseById(Integer id) {
        Enterprise enterprise = enterpriseMapper.queryById(id);
        log.info("enterprise info {}", JSON.toJSONString(enterprise));
        return enterprise;
    }

    @Override
    public void updateAdditionalInfoById(Enterprise enterprise) {
        enterpriseMapper.updateAdditionalById(enterprise);
    }

    @Override
    public List<Enterprise> query() {
        return enterpriseMapper.query();
    }
}
