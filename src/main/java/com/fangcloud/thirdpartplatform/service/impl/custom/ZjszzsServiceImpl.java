package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.utils.EncryptUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 中建装饰免密登录
 */
@Component
@Slf4j
public class ZjszzsServiceImpl {

    @Resource
    private OpenClientHelper openClientHelper;

    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {

        String userTicketEncrypt = request.getParameter("user_ticket");
        String clientId = configInfo.getClientId();
        String enterpriseId = configInfo.getEnterpriseId();
        String platformId = configInfo.getPlatformId();

        // 解密userTicket
        String userTicket = EncryptUtils.decrypt(userTicketEncrypt, "h6lr677hdru3pt3b");
        log.info("zjszzs userTicket is :{}", userTicket);

        if(StringUtils.isEmpty(userTicket)){
            return null;
        }

        String loginName = checkUserTicket(userTicket);
        log.info("zjszzs loginName is :{}", loginName);
        try {
            if (loginName != null) {
                EnterpriseParams enterpriseParams = new EnterpriseParams();
                enterpriseParams.setEnterpriseId(enterpriseId);
                enterpriseParams.setIdentifier(loginName);
                enterpriseParams.setPlatformId(platformId);
                enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
                enterpriseParams.setClientId(clientId);
                String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
                log.info("zjszzs 免密登录url {}", loginUrl);
                return loginUrl;
            }
        } catch (Exception e) {
            log.error("zjszzs 异常", e);
        }
        return null;
    }

    private String checkUserTicket(String userTicket) {
        String[] split = userTicket.split("\\|");
        if(split.length != 3){
            return null;
        }

        if(!"360cloud".equals(split[2])){
            return null;
        }

        if(System.currentTimeMillis()-Long.parseLong(split[1]) > 5000){
            return null;
        }
        return split[0];
    }

    public static void main(String[] args) {
        String[] split = "123|123".split("\\|");
        System.out.println();
    }

}
