package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.*;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.sync.CustomDepartment;
import com.fangcloud.thirdpartplatform.entity.sync.DataBaseConfig;
import com.fangcloud.thirdpartplatform.helper.CacheHelper;
import com.fangcloud.thirdpartplatform.helper.SyncTaskHelper;
import com.fangcloud.thirdpartplatform.repository.database.mapper.BaseMapper;
import com.fangcloud.thirdpartplatform.service.DataSourceSyncHandler;
import com.fangcloud.thirdpartplatform.service.SyncTaskHandler;
import com.fangcloud.thirdpartplatform.service.factory.SyncTaskHandlerFactory;
import com.fangcloud.thirdpartplatform.service.impl.DepartmentServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.SyncServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.UserServiceImpl;
import com.fangcloud.thirdpartplatform.utils.DatabaseUtils;
import com.fangcloud.thirdpartplatform.utils.SyncStringUtils;
import com.google.common.collect.ImmutableList;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

import static com.sync.common.contants.SyncConstant.GB;

@Slf4j
public abstract class AbstractDataSourceSyncHandler implements DataSourceSyncHandler {

    @Resource
    private SyncServiceImpl syncServiceImpl;

    @Resource
    private UserServiceImpl userServiceimpl;

    @Resource
    private DepartmentServiceImpl departmentServiceImpl;

    @Resource
    private EnterpriseServiceImpl enterpriseServiceImpl;

    @Resource
    private CacheHelper cacheHelper;

    @Resource
    private SyncTaskHandlerFactory syncTaskHandlerFactory;

    @Resource
    protected SyncTaskHelper syncTaskHelper;

    private String NULL  = "null";

    /**
     * 从数据库中查询部门数据
     * @param platformSyncConfig
     * @return
     */
    public List<YfyDepartment> findDepartmentDataFromDB(PlatformSyncConfig platformSyncConfig, Integer taskId) {

        DepValueBoxDto depValueBoxDto = JSON.parseObject(platformSyncConfig.getValueBox(), DepValueBoxDto.class);

        if(Objects.isNull(depValueBoxDto) || Objects.isNull(depValueBoxDto.getSqlTaskConfig())){
           throw new ParamException("depValueBoxDto id null!");
        }

        DataBaseConfig dataBaseConfig = DataBaseConfig.builder()
                .type(platformSyncConfig.getSourceType())
                .url(depValueBoxDto.getUrl())
                .userName(depValueBoxDto.getUserName())
                .password(depValueBoxDto.getPassword()).build();

        BaseMapper baseMapper = DatabaseUtils.getBaseMapper(dataBaseConfig);

        // 将任务的mapper放入缓存
        cacheHelper.putObject(SyncTaskConstants.SYNC_BASE_MAPPER + platformSyncConfig.getEnterpriseId() + CommonConstants.SPLIT_UNDERLINE + taskId, baseMapper);

        return buildYfyDeptList(depValueBoxDto, baseMapper);
    }



    /**
     * 从数据库中查询用户数据
     * @param platformSyncConfig
     * @return
     */
    public List<YfyUser> findUserDataFromDB(PlatformSyncConfig platformSyncConfig, Integer taskId) {

        UserValueBoxDto userValueBoxDto = JSON.parseObject(platformSyncConfig.getValueBox(), UserValueBoxDto.class);

        if(Objects.isNull(userValueBoxDto) || Objects.isNull(userValueBoxDto.getSqlTaskConfig())){
            throw new ParamException("depValueBoxDto is null!");
        }

        DataBaseConfig dataBaseConfig = DataBaseConfig.builder()
                .type(platformSyncConfig.getSourceType())
                .url(userValueBoxDto.getUrl())
                .userName(userValueBoxDto.getUserName())
                .password(userValueBoxDto.getPassword()).build();
        BaseMapper baseMapper = DatabaseUtils.getBaseMapper(dataBaseConfig);

        // 将任务的mapper放入缓存
        cacheHelper.putObject(SyncTaskConstants.SYNC_BASE_MAPPER + platformSyncConfig.getEnterpriseId() + CommonConstants.SPLIT_UNDERLINE + taskId, baseMapper);

        return buildYfyUserList(userValueBoxDto, baseMapper);
    }

    /**
     * 同步部门数据
     * @param platformSyncConfig
     * @return
     */
    public ExecuteResult syncDepartmentList (PlatformSyncConfig platformSyncConfig, List<YfyDepartment> yfyDepartmentList, Integer taskId) {

        // 1. 获取企业信息
        Enterprise enterprise = enterpriseServiceImpl.getEnterpriseById(platformSyncConfig.getEnterpriseId());
        if(Objects.isNull(enterprise)){
            log.info("enterprise is null !");
            return new ExecuteResult();
        }

        // 2. 获取既存的客户部门信息
        List<CustomDepartment> customDepartmentList = departmentServiceImpl.findAll(enterprise.getPlatformId());

        // 3. 判断部门的名称，若太长进行截取
        checkDepartmentName(yfyDepartmentList);

        // 4. 同步部门数据
        return syncServiceImpl.syncDepartments(customDepartmentList, yfyDepartmentList, taskId, enterprise, platformSyncConfig);
    }

    /**
     * 将部门名字过长的部门名称进行截取
     * @param yfyDepartmentList
     */
    private void checkDepartmentName(List<YfyDepartment> yfyDepartmentList) {
        for (YfyDepartment yfyDepartment : yfyDepartmentList) {
            String departmentName = yfyDepartment.getName();
            // 超过30个字符截取30个
            if (departmentName.length() > 39) {
                departmentName = departmentName.substring(0, 35) + "．．．";
                yfyDepartment.setName(departmentName);
            }
        }
    }


    public ExecuteResult syncUserList (PlatformSyncConfig platformSyncConfig, List<YfyUser> yfyUserList, Integer taskId) {
        String taskType = TaskTypeEnum.SINGLETASKING.getDesc();
        if (syncTaskHelper.checkMultiTask(platformSyncConfig)) {
            taskType = TaskTypeEnum.MULTITASKING.getDesc();
        }
        SyncTaskHandler handler = syncTaskHandlerFactory.getHandler(taskType);
        return handler.executeResult(platformSyncConfig, yfyUserList, taskId);
    }



    /**
     * 获取用户列表
     * @param userValueBoxDto
     * @param baseMapper
     * @return
     */
    private List<YfyUser> buildYfyUserList(UserValueBoxDto userValueBoxDto, BaseMapper baseMapper){

        SqlTaskConfigDto sqlTaskConfig = userValueBoxDto.getSqlTaskConfig();
        List<YfyUser> yfyUserList = new ArrayList<>();

        // 用户不区分高校和非高校
        sqlTaskConfig.getSqlConfigs().forEach(sqlConfig -> {
            if(StringUtils.isEmpty(sqlConfig.getSql()) || StringUtils.isEmpty(sqlConfig.getTaskType())){
                return;
            }
            if(SyncTaskTypeEnum.ADD.getStatus().equals(sqlConfig.getTaskType())){
                // 获取全量同步用户信息
                List<Map<String, Object>> baseSqlData = DatabaseUtils.getBaseSqlData(baseMapper, sqlConfig.getSql());
                if(CollectionUtils.isEmpty(baseSqlData)){
                    return;
                }
                // 组装同步用户信息
                yfyUserList.addAll(buildSyncYfyUserList(baseSqlData, userValueBoxDto, sqlConfig.getTableType(), sqlTaskConfig.getCompanyType()));
            }else {
                return;
            }
        });
        return yfyUserList;
    }

    /**
     * 获取部门列表
     * @param depValueBoxDto
     * @param baseMapper
     * @return
     */
    private List<YfyDepartment> buildYfyDeptList(DepValueBoxDto depValueBoxDto, BaseMapper baseMapper){

        SqlTaskConfigDto sqlTaskConfig = depValueBoxDto.getSqlTaskConfig();

        List<YfyDepartment> yfyDepartmentList = new ArrayList<>();
        String companyType = sqlTaskConfig.getCompanyType();
        // 若为高校企业需要创建本科生，研究生和教师三个顶级部门
        if(SyncCompanyTypeEnum.COLLEGE.getStatus().equals(companyType)){
            DepValueBoxDto.SyncRule syncRule = depValueBoxDto.getSyncRule();
            Long deptSpace = Long.valueOf(depValueBoxDto.getDeptSpace());
            yfyDepartmentList.add(buildNormalDept(SyncTableTypeEnum.GRADUATE,syncRule, deptSpace));
            yfyDepartmentList.add(buildNormalDept(SyncTableTypeEnum.STUDENT,syncRule, deptSpace));
            yfyDepartmentList.add(buildNormalDept(SyncTableTypeEnum.TEACHER,syncRule, deptSpace));
        }
        sqlTaskConfig.getSqlConfigs().forEach(sqlConfig -> {
            if(StringUtils.isEmpty(sqlConfig.getSql()) || StringUtils.isEmpty(sqlConfig.getTaskType())){
                return;
            }
            // 是否需要过滤登记部门，若不需要，不需要更改
            if(SyncTaskTypeEnum.ADD.getStatus().equals(sqlConfig.getTaskType())){
                // 获取全量同步部门信息
                List<Map<String, Object>> baseSqlData = DatabaseUtils.getBaseSqlData(baseMapper, sqlConfig.getSql());
                log.info("获取全量同步部门信息:{}", baseSqlData);
                if(CollectionUtils.isEmpty(baseSqlData)){
                    return;
                }
                // 组装同步部门信息
                yfyDepartmentList.addAll(buildCompanySyncYfyDeptList(baseSqlData, depValueBoxDto, sqlTaskConfig.getCompanyType(), sqlConfig.getTableType()));
            }else {
                return;
            }
        });


        return yfyDepartmentList;
    }


    /**
     * 构建同步部门列表
     * @param baseSqlData
     * @param depValueBoxDto
     * @param companyType
     * @param tableType
     * @return
     */
    private List<YfyDepartment> buildCompanySyncYfyDeptList(List<Map<String, Object>> baseSqlData, DepValueBoxDto depValueBoxDto, String companyType, String tableType){
        DepValueBoxDto.SyncRule syncRule = depValueBoxDto.getSyncRule();
        DepValueBoxDto.RootDept rootDept = syncRule.getRootDept();
        Long deptSpace = Long.valueOf(depValueBoxDto.getDeptSpace());
        String filterDept = depValueBoxDto.getFilterDept();

        // 解析过滤部门列表
        List<String> filterDeptList = new ArrayList<>();
        if(StringUtils.isNotEmpty(filterDept)){
            String[] split = filterDept.split(";");
            for (String s : split) {
                filterDeptList.add(s);
            }
        }
        List<YfyDepartment> yfyDepartmentList = new ArrayList<>();
        log.info("filterDeptList info {}", JSON.toJSONString(filterDeptList));

        baseSqlData.forEach(stringObjectMap -> {
            String id = "";
            String name = "";
            String parentId = "";
            String directorId = "";
            Long order = 0L;
            for(String actualKey : stringObjectMap.keySet()) {
                Object resultObject = stringObjectMap.get(actualKey);
                String data = Objects.isNull(resultObject) ? null : String.valueOf(resultObject);
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.DEPARTMENT_PARAMETER_ID.toLowerCase())) {
                    id = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.DEPARTMENT_PARAMETER_NAME.toLowerCase())) {
                    name = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.DEPARTMENT_PARAMETER_PARENT_ID.toLowerCase())) {
                    parentId = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.DEPARTMENT_PARAMETER_DIRECTOR_ID.toLowerCase())) {
                    directorId = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.DEPARTMENT_PARAMETER_ORDER.toLowerCase())) {
                    order = StringUtils.isEmpty(data) ? 0L : Long.parseLong(data);
                }
            }

            log.info("base sql data id {}, name {}, parentId {}, directorId {}, order {}", id, name, parentId, directorId, order);
            // 校验数据库数据
            if(StringUtils.isEmpty(id) || NULL.equals(id) || StringUtils.isEmpty(name) || NULL.equals(name)){
                return;
            }
            // 过滤部门
            if(filterDeptList.contains(id)){
                log.info("department is be filtered！id is {}", id);
                return;
            }
            if(!Objects.isNull(rootDept)){
                if(rootDept.isFilter() ){
                    if(rootDept.getRootId().equals(id)){
                        log.info("top department is be filtered！id is {}", id);
                        return;
                    }else if(rootDept.getRootId().equals(parentId)){
                        parentId = null;
                    }
                }

            }
            // 高校部门id重置
            if(SyncCompanyTypeEnum.COLLEGE.getStatus().equals(companyType)){
                // 重置顶级部门id
                if(StringUtils.isEmpty(parentId)){
                    if(SyncTableTypeEnum.GRADUATE.getStatus().equals(tableType)){//研究生 设置研究生顶级部门
                        id = SyncTaskConstants.COLLEGE_GRADUATE_PREFIX + id;
                        parentId = SyncTableTypeEnum.GRADUATE.getId();
                    }else if(SyncTableTypeEnum.STUDENT.getStatus().equals(tableType)){//本科生 设置研究生顶级部门
                        id = SyncTaskConstants.COLLEGE_STUDENT_PREFIX + id;
                        parentId = SyncTableTypeEnum.STUDENT.getId();
                    }else  if(SyncTableTypeEnum.TEACHER.getStatus().equals(tableType)){// 教师 设置教师顶级部门
                        id = SyncTaskConstants.COLLEGE_TEACHER_PREFIX + id;
                        parentId = SyncTableTypeEnum.TEACHER.getId();
                    }
                } else {// 所有高校部门添加前缀
                    if(SyncTableTypeEnum.GRADUATE.getStatus().equals(tableType)){//研究生 设置研究生顶级部门
                        id = SyncTaskConstants.COLLEGE_GRADUATE_PREFIX + id;
                        parentId = SyncTaskConstants.COLLEGE_GRADUATE_PREFIX + parentId;
                    }else if(SyncTableTypeEnum.STUDENT.getStatus().equals(tableType)){//本科生 设置研究生顶级部门
                        id = SyncTaskConstants.COLLEGE_STUDENT_PREFIX + id;
                        parentId = SyncTaskConstants.COLLEGE_STUDENT_PREFIX + parentId;
                    }else  if(SyncTableTypeEnum.TEACHER.getStatus().equals(tableType)){// 教师 设置教师顶级部门
                        id = SyncTaskConstants.COLLEGE_TEACHER_PREFIX + id;
                        parentId = SyncTaskConstants.COLLEGE_TEACHER_PREFIX + parentId;
                    }
                }
            }
            YfyDepartment build = YfyDepartment.builder()
                    .id(id)
                    .name(SyncStringUtils.formatString(name))
                    .parentId(parentId)
                    .publicFolder(syncRule.isPublicFolder())
                    .spaceTotal(deptSpace)
                    .directorId(directorId)
                    .collabAutoAccepted(syncRule.isCollabAutoAccepted())
                    .createTime(new Date())
                    .status("1")
                    .order(order)
                    .build();
            yfyDepartmentList.add(build);
        });
        return yfyDepartmentList;
    }

    /**
     * 高校构建虚拟顶级部门
     * @param syncTableTypeEnum
     * @param syncRule
     * @param deptSpace
     * @return
     */
    private YfyDepartment buildNormalDept(SyncTableTypeEnum syncTableTypeEnum, DepValueBoxDto.SyncRule syncRule, Long deptSpace){

        return YfyDepartment.builder()
                .id(syncTableTypeEnum.getId())
                .name(syncTableTypeEnum.getName())
                .parentId(null)
                .publicFolder(syncRule.isPublicFolder())
                .spaceTotal(deptSpace)
                .collabAutoAccepted(syncRule.isCollabAutoAccepted())
                .createTime(new Date())
                .status("1")
                .order(syncTableTypeEnum.getOrder())
                .build();
    };

    /**
     * 构建同步用户列表
     * @param baseSqlData
     * @param userValueBoxDto
     * @param tableType
     * @param companyType
     * @return
     */
    private List<YfyUser> buildSyncYfyUserList(List<Map<String, Object>> baseSqlData, UserValueBoxDto userValueBoxDto, String tableType, String companyType){
        UserValueBoxDto.SyncRule syncRule = userValueBoxDto.getSyncRule();
        List<YfyUser> yfyUserList = new ArrayList<>();

        baseSqlData.forEach(stringObjectMap -> {
            String email = "";
            String phone = "";
            String departmentId = "";
            String id = "";
            String fullName = "";
            Long order = 0L;

            for(String actualKey : stringObjectMap.keySet()) {
                Object resultObject = stringObjectMap.get(actualKey);
                String data = Objects.isNull(resultObject) ? null : String.valueOf(resultObject);

                if (actualKey.equalsIgnoreCase(SyncTaskConstants.USER_PARAMETER_EMAIL.toLowerCase())) {
                    email = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.USER_PARAMETER_PHONE.toLowerCase())) {
                    phone = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.USER_PARAMETER_DEPARTMENT_ID.toLowerCase())) {
                    departmentId = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.USER_PARAMETER_ID.toLowerCase())) {
                    id = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.USER_PARAMETER_FULL_NAME.toLowerCase())) {
                    fullName = data;
                }
                if (actualKey.equalsIgnoreCase(SyncTaskConstants.USER_PARAMETER_ORDER.toLowerCase())) {
                    order = Long.valueOf(data);
                }
            }

            log.info("base sql data id {}, name {}, deptId {}, phone {}, email {} order {}", id, fullName, departmentId, phone, email, order);
            // 校验数据库数据
            if(StringUtils.isEmpty(id) || NULL.equals(id) || StringUtils.isEmpty(fullName) || NULL.equals(fullName)){
                return;
            }

            // 若为高校企业，部门id添加前缀
            if (SyncCompanyTypeEnum.COLLEGE.getStatus().equals(companyType)){
                if(SyncTableTypeEnum.GRADUATE.getStatus().equals(tableType)){//研究生 设置研究生部门id前缀
                    departmentId = SyncTaskConstants.COLLEGE_GRADUATE_PREFIX + departmentId;
                }else if(SyncTableTypeEnum.STUDENT.getStatus().equals(tableType)){//本科生 设置研究生部门id前缀
                    departmentId = SyncTaskConstants.COLLEGE_STUDENT_PREFIX + departmentId;
                }else  if(SyncTableTypeEnum.TEACHER.getStatus().equals(tableType)){// 教师 设置教师部门id前缀
                    departmentId = SyncTaskConstants.COLLEGE_TEACHER_PREFIX + departmentId;
                }
            }
            List<String> departmentIds = ImmutableList.of(departmentId);
            YfyUser yfyUser = YfyUser.builder()
                    .fullName(SyncStringUtils.formatString(fullName))
                    .id(id)
                    .createTime(new Date())
                    .spaceTotal(Long.valueOf(userValueBoxDto.getUserSpace()) * GB)
                    .departmentIds(departmentIds)
                    .order(order)
                    .forced(true)
                    .build();

            if(StringUtils.isNotEmpty(email)){
                yfyUser.setEmail(email);
            }else {
                String emailSuffix = syncRule.getEmailSuffix();
                if(StringUtils.isNotEmpty(emailSuffix)){
                    yfyUser.setEmail(id + "@" +emailSuffix);
                } else{
                    log.info("email is null! user info {}", JSON.toJSONString(stringObjectMap));
                    return;
                }
            }
            if(syncRule.isSyncPhone()){
                yfyUser.setPhone(phone);
            }
            if (syncRule.isActivate()) {
                yfyUser.setStatus("1");
            } else {
                yfyUser.setStatus("3");
            }
            yfyUserList.add(yfyUser);
        });
        return yfyUserList;
    };

}