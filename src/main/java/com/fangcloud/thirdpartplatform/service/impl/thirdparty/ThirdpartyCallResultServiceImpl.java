package com.fangcloud.thirdpartplatform.service.impl.thirdparty;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformThirdpartyMessageMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.PlatformThirdpartyMessageValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCallParam;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class ThirdpartyCallResultServiceImpl implements ThirdpartyExecuteService {

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PlatformThirdpartyMessageMapper platformThirdpartyMessageMapper;

    private static String JSON_PATH_CALL_RESULT_SUCCESS = "$.success";



    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        ThirdpartyCallParam thirdpartyCallParam = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getData()), ThirdpartyCallParam.class);
        String uniqueId = thirdpartyCallParam.getUniqueId();
        Long enterpriseId = thirdpartyCallParam.getEnterpriseId();

        PlatformThirdpartyMessage platformThirdpartyMessage = platformThirdpartyMessageMapper.selectByEnterpriseIdAndUniqueId(enterpriseId, uniqueId);
        
        if(Objects.isNull(platformThirdpartyMessage)){
            log.info("platformThirdpartyMessage is not null!");
            throw new ParamException("platformThirdpartyMessage is not null!");
        }else {
            log.info("platformThirdpartyMessage info is :{}", JSONObject.toJSONString(platformThirdpartyMessage));
            PlatformThirdpartyMessageValueBox platformThirdpartyMessageValueBox = JSON.parseObject(platformThirdpartyMessage.getValueBox(), PlatformThirdpartyMessageValueBox.class);
            JSONObject callResultJSONObject = platformThirdpartyMessageValueBox.getCallResult();

            if(Objects.isNull(callResultJSONObject)){
                return ResultUtils.getSuccessResult(false);
            }else {
                Boolean success = (Boolean) JSONPath.extract(JSONObject.toJSONString(callResultJSONObject), JSON_PATH_CALL_RESULT_SUCCESS);
                return ResultUtils.getSuccessResult(success);
            }
        }

    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {

       return null;
    }


    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.THIRDPARTY_CALL_RESULT.getDesc();
    }
}
