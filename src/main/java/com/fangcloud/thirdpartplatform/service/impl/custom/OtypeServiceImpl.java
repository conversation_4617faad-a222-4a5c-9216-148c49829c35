package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.constant.login.LoginParamEnum;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.utils.otype.AESUtil;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class OtypeServiceImpl implements ApiExecuteService {

    @Resource
    private HttpClientHelper httpClientHelper;
    private static final String ASE_KEY = "ase_key";
    private static final String TOKEN = "token";

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        JSONObject jsonObject = JSONObject.parseObject(apiResult);
        String userName = jsonObject.getString(LoginParamEnum.USER_NAME.getType());
        String password = jsonObject.getString(LoginParamEnum.PASSWORD.getType());
        String aseKey = headMap.get(ASE_KEY);
        String token = (String) paramMap.get(TOKEN);
        String url = apiConfig.getApi() + "?" + TOKEN + "=" +  token;
        String postData = postData(userName, password, aseKey);
        try {
            log.info("verify user url:{}  postData:{}", url, postData);
            Response response = httpClientHelper.postResponse(url, postData);
            String result = Objects.requireNonNull(response.body().string());
            Integer code = (Integer) JSONPath.extract(result, "code");
            if (code == 0) {
                return buildYfyUser(userName);
            }
        } catch (Exception e) {
            log.info("verify user url:{}", url, e);
        }
        return null;
    }

    private String buildYfyUser(String userName) {
        JSONObject jsonObject = new JSONObject();
        YfyUser yfyUser = YfyUser.builder()
                .id(userName)
                .build();
        jsonObject.put("data", yfyUser);
        return jsonObject.toJSONString();
    }

    private String postData(String userName, String password, String aseKey) {
        String encrypt = AESUtil.encrypt(userName + ":" + password, aseKey);
        return "{\"data\":\"" + encrypt + "\"}";
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.OTYPE.getDesc();
    }

}
