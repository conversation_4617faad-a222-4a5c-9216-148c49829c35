package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.sync.*;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.sync.*;
import com.fangcloud.thirdpartplatform.helper.CacheHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.repository.database.mapper.BaseMapper;
import com.fangcloud.thirdpartplatform.service.PlatformAsyncQueueService;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskService;
import com.fangcloud.thirdpartplatform.service.SyncService;
import com.fangcloud.thirdpartplatform.service.factory.DataSourceSyncHandlerFactory;
import com.fangcloud.thirdpartplatform.service.impl.datasource.LdapSyncHandler;
import com.fangcloud.thirdpartplatform.utils.DatabaseUtils;
import com.google.common.collect.Lists;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.enums.DepartmentStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SyncServiceImpl implements SyncService {

    final static int SYNC_SIZE = 30;

    final static int SYNC_MINI_SIZE = 1;

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Resource
    private CacheHelper cacheHelper;

    @Resource
    private RedisStringManager redisStringManager;

    @Resource
    private DataSourceSyncHandlerFactory dataSourceSyncHandlerFactory;

    @Resource
    private PlatformSyncTaskService platformSyncTaskRecordService;

    @Resource
    private PlatformAsyncQueueService platformAsynQueueService;

    @Resource
    private CustomNacosConfig nacosConfig;

    @Autowired
    private UserMapper userMapper;

    @Resource
    private LdapSyncHandler ldapSyncHandler;

    /**
     * 同步企业用户数据
     * @param customUserList
     * @param yfyUserList
     * @param taskId
     * @param enterprise
     * @param platformSyncConfig
     * @return
     */
    public ExecuteResult syncUsers(List<CustomUser> customUserList, List<YfyUser> yfyUserList, Integer taskId, Enterprise enterprise, PlatformSyncConfig platformSyncConfig, Boolean isCreateDefaultPassword, String customPassword) {

        ExecuteResult executeResult = new ExecuteResult();
        Map<String, CustomUser> customUserMap = new HashMap<>();
        if(customUserList.size() > 0){
            customUserList.forEach(customUser -> {
                customUserMap.put(customUser.getCustomId(), customUser);
            });
        }

        List<YfyUser> syncYfyUsers = new ArrayList<>();

        AtomicInteger addRows = new AtomicInteger();
        AtomicInteger updateRows = new AtomicInteger();
        AtomicInteger errorRows = new AtomicInteger();

        // 获取亿方云存在，客户数据不存在的用户数据
        List<String> yfyUserIdList = yfyUserList.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<CustomUser> notExistCustomUserList = customUserList.stream().filter(p -> !yfyUserIdList.contains(p.getCustomId())).collect(Collectors.toList());

        UserValueBoxDto userValueBoxDto = JSON.parseObject(platformSyncConfig.getValueBox(), UserValueBoxDto.class);
        if(!Objects.isNull(userValueBoxDto)) {

            // 禁用客户需要禁用的用户数据
            disableYfyUserList(yfyUserList, customUserMap, enterprise.getAdminUserId(), enterprise.getId());

            // 组装需要删除的用户数据
            for (CustomUser customUser : notExistCustomUserList) {
                // 判断亿方云数据是否需要删除
                   if(checkCustomUserNeedDelete(customUser, userValueBoxDto, platformSyncConfig, taskId)){
                    log.info("customUser need delete, customUserInfo {}", JSON.toJSONString(customUser));
                    syncYfyUsers.add(checkSpecialHandling(customUser, enterprise.getAdminUserId(), enterprise.getId()));
                }
                // 判断是否需要特殊处理
                if (isSpecialHandling(customUser,platformSyncConfig, userValueBoxDto)) {
                      YfyUser yfyUser = checkSpecialHandling(customUser, enterprise.getAdminUserId(), enterprise.getId());
                      if(!Objects.isNull(yfyUser)){
                          syncYfyUsers.add(yfyUser);
                      }
                }
                // 每30条记录提交一次
                if (syncYfyUsers.size() >= SYNC_SIZE) {
                    // 调用v2同步用户，失败的数量
                    Integer errorRowsCount = doSyncUser(syncYfyUsers, enterprise, taskId, platformSyncConfig.getSourceType(), isCreateDefaultPassword, customPassword);
                    if(!Objects.isNull(errorRowsCount)){
                        errorRows.getAndAdd(errorRowsCount);
                    }
                    syncYfyUsers.clear();
                }
            }
        }

        // 组装需要同步的数据
        yfyUserList.forEach(yfyUser -> {
            CustomUser customUser = customUserMap.get(yfyUser.getId());

            // 每30条记录提交一次
            if (syncYfyUsers.size() >= SYNC_SIZE) {
                // 调用v2同步用户，失败的数量
                Integer errorRowsCount = doSyncUser(syncYfyUsers, enterprise, taskId, platformSyncConfig.getSourceType(), isCreateDefaultPassword, customPassword);
                if(!Objects.isNull(errorRowsCount)){
                    errorRows.getAndAdd(errorRowsCount);
                }
                syncYfyUsers.clear();
            }

            //过滤数据源中本身删除的用户 在我们库中也没有的数据
            if (yfyUser.getStatus().equals("2") && customUser == null) {
                return;
            }

            Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterprise.getId() + "");
            if (Boolean.valueOf(configMap.get(PlatformGlobalConfigKeyEnum.ADD_USERS_SET_TO_NULL_DEPARTMENT.getKey()))) {
                yfyUser.setDepartmentIds(Lists.newArrayList());
            }

            if (null != customUser) {
                // 我们库中有，数据源中删除的数据
                if (yfyUser.getStatus().equals("2")) {
                    syncYfyUsers.add(yfyUser);
                    updateRows.getAndAdd(1);
                    return;
                }
                // 判断覆盖操作，若为直接跳过，若为OVER进行覆盖
                if("SKIP".equals(userValueBoxDto.getRepeatHandle())){
                    log.info("repeatHandle is skip ! yfyUser info{}", JSON.toJSONString(yfyUser));
                    return;
                }
                // 存在判断 用户，手机号，邮箱, 部门 是否有变化
                boolean isPhoneChange = yfyUser.getPhone() != null && Boolean.FALSE.equals(yfyUser.getPhone().equals(customUser.getPhone()));
                boolean isNameChange = yfyUser.getFullName() != null && Boolean.FALSE.equals(yfyUser.getFullName().equals(customUser.getName()));
                boolean isEmailChange = yfyUser.getEmail() != null && Boolean.FALSE.equals(yfyUser.getEmail().equals(customUser.getEmail()));
                boolean isDeptChange = checkUserDepartment(yfyUser.getDepartmentIds(), customUser.getDepartmentIds());
                boolean isOrderChange = yfyUser.getOrder() != null && Boolean.FALSE.equals(yfyUser.getOrder().equals(customUser.getOrder()));
                if (isPhoneChange || isNameChange || isEmailChange || isDeptChange || isOrderChange) {
                    updateRows.getAndAdd(1);
                    syncYfyUsers.add(yfyUser);
                }
            } else {
                syncYfyUsers.add(yfyUser);
                addRows.getAndAdd(1);
            }
        });

        // 若同步列表不为空继续同步
        if(syncYfyUsers.size() > 0){
            Integer errorRowsCount = doSyncUser(syncYfyUsers, enterprise, taskId, platformSyncConfig.getSourceType(), isCreateDefaultPassword, customPassword);
            if(!Objects.isNull(errorRowsCount)){
                errorRows.getAndAdd(errorRowsCount);
            }
        }
        // 设置用户总数
        executeResult.setTotalRows(yfyUserList.size());
        executeResult.setUpdateRows(updateRows.get());
        executeResult.setAddRows(addRows.get());
        executeResult.setErrorRows(errorRows.get());
        return executeResult;
    }


    /**
     * 同步企业部门数据
     * @param customDepartmentList
     * @param yfyDepartmentList
     * @param taskId
     * @param enterprise
     * @param platformSyncConfig
     * @return
     */
    public ExecuteResult syncDepartments(List<CustomDepartment> customDepartmentList,
                                         List<YfyDepartment> yfyDepartmentList,
                                         Integer taskId,
                                         Enterprise enterprise,
                                         PlatformSyncConfig platformSyncConfig) {

        List<YfyDepartment> syncYfyDepartments = new ArrayList<>();

        Map<String, CustomDepartment> customDepartmentMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(customDepartmentList)){
            customDepartmentList.forEach(customDepartment -> {
                customDepartmentMap.put(customDepartment.getCustomId(), customDepartment);
            });
        }

        ExecuteResult executeResult = new ExecuteResult();
        AtomicInteger addRows = new AtomicInteger();
        AtomicInteger updateRows = new AtomicInteger();
        AtomicInteger errorRows = new AtomicInteger();

        // 获取亿方云存在，客户数据不存在的部门数据
        List<String> yfyDeptIdList = yfyDepartmentList.stream().map(p -> p.getId()).collect(Collectors.toList());
        List<CustomDepartment> notExistCustomDepartmentList = customDepartmentList.stream().filter(p -> !yfyDeptIdList.contains(p.getCustomId())).collect(Collectors.toList());
        log.info("notExistCustomDepartmentList {}", JSON.toJSONString(notExistCustomDepartmentList));

        DepValueBoxDto depValueBoxDto = JSON.parseObject(platformSyncConfig.getValueBox(), DepValueBoxDto.class);
        if(!Objects.isNull(depValueBoxDto)) {
            // 组装需要删除的部门数据
            for (CustomDepartment customDepartment : notExistCustomDepartmentList) {
                // 判断亿方云数据是否需要删除
                if(checkCustomDepartmentNeedDelete(customDepartment, depValueBoxDto, platformSyncConfig, taskId)){
                    log.info("customDepartment need delete, customDepartmentId {}", customDepartment.getCustomId() );
                    syncYfyDepartments.add(buildDeleteYfyDepartment(customDepartment));
                }
                if(checkSpecialHandlingByDepartment(depValueBoxDto, enterprise.getId())) {
                    YfyDepartment yfyDepartment =  specialHandlingByDepartment(customDepartment);
                    if (Objects.nonNull(yfyDepartment)) {
                        syncYfyDepartments.add(yfyDepartment);
                    }
                }
                // 每30条记录提交一次
                if (syncYfyDepartments.size() >= SYNC_SIZE) {
                    // 调用v2同步部门，失败的数量
                    Integer errorRowsCount = doSyncDepartment(syncYfyDepartments, enterprise, taskId, platformSyncConfig.getSourceType());
                    if(!Objects.isNull(errorRowsCount)){
                        errorRows.getAndAdd(errorRowsCount);
                    }
                    syncYfyDepartments.clear();
                }
            }
        }

        //按层级从上至下单条同步部门，会增加同步所需时间
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterprise.getId()+ "");
        Boolean isLevelSyncDept = Boolean.valueOf(configMap.get(PlatformGlobalConfigKeyEnum.IS_LEVEL_SYNC_DEPARTMENT.getKey()));
        if (isLevelSyncDept) {
            yfyDepartmentList = sortByHierarchy(yfyDepartmentList);
        }

        // 组装需要同步的数据
        yfyDepartmentList.forEach(yfyDepartment -> {

            CustomDepartment customDepartment  = customDepartmentMap.get(yfyDepartment.getId());

            // 每30条记录提交一次
            if (syncYfyDepartments.size() >= (isLevelSyncDept ? SYNC_MINI_SIZE : SYNC_SIZE)) {
                // 调用v2同步部门数据
                Integer errorRowsCount = doSyncDepartment(syncYfyDepartments, enterprise, taskId, platformSyncConfig.getSourceType());
                if(!Objects.isNull(errorRowsCount)){
                    errorRows.getAndAdd(errorRowsCount);
                }
                syncYfyDepartments.clear();
            }

            // 处理删除数据
            if ((yfyDepartment.getStatus().equals("0") || yfyDepartment.getStatus().equals("2")) && customDepartment == null) {
                return;
            }

            if (!Objects.isNull(customDepartment)) {

                //
                if (yfyDepartment.getStatus().equals("0")) {
                    syncYfyDepartments.add(yfyDepartment);
                    updateRows.getAndAdd(1);
                    return;
                }
                if (yfyDepartment.getStatus().equals("2")) {
                    yfyDepartment.setStatus("1");
                    syncYfyDepartments.add(yfyDepartment);
                    updateRows.getAndAdd(1);
                    return;
                }
                // 判断覆盖操作，若为SKIP直接跳过，若为OVER进行覆盖
                if("SKIP".equals(depValueBoxDto.getRepeatHandle())){
                    log.info("repeatHandle is skip ! yfyDepartment info{}", JSON.toJSONString(yfyDepartment));
                    return;
                }

                // 存在判断 部门名是否有变化
                boolean isParentIdChange = !Objects.equals(yfyDepartment.getParentId(), customDepartment.getCustomParentId());
                boolean isNameChange = !Objects.equals(yfyDepartment.getName(), customDepartment.getName());
                boolean directorIdChange = !Objects.equals(yfyDepartment.getDirectorId(), customDepartment.getCustomDirectorId());
                boolean orderChange = !Objects.equals(yfyDepartment.getOrder(), customDepartment.getOrder());

                log.info("部门比较yfyDepartment:{},openDepartment:{}", JSONObject.toJSONString(yfyDepartment), JSONObject.toJSONString(customDepartment));
                boolean isChange = isParentIdChange || isNameChange || directorIdChange || orderChange;
                if (isChange) {
                    // 部分字段会在云盘修改，所以按云盘中的来同步
                    syncYfyDepartments.add(yfyDepartment);
                    updateRows.getAndAdd(1);
                }
            } else {
                // 不存在新增, 新增部门时需要过滤部门负责人字段才能创建部门
                yfyDepartment.setDirectorId(null);
                syncYfyDepartments.add(yfyDepartment);
                addRows.getAndAdd(1);
            }
        });
        if (syncYfyDepartments.size() > 0) {
            // 调用v2同步部门数据
            Integer errorRowsCount = doSyncDepartment(syncYfyDepartments, enterprise, taskId, platformSyncConfig.getSourceType());
            if(!Objects.isNull(errorRowsCount)){
                errorRows.getAndAdd(errorRowsCount);
            }
        }

        // 设置用户总数
        executeResult.setTotalRows(yfyDepartmentList.size());
        executeResult.setErrorRows(errorRows.get());
        executeResult.setAddRows(addRows.get());
        executeResult.setUpdateRows(updateRows.get());
        return executeResult;
    }

    private boolean checkSpecialHandlingByDepartment(DepValueBoxDto depValueBoxDto, long enterpriseId) {
        if (!Objects.isNull(depValueBoxDto.getSqlTaskConfig())) {
            return false;
        }
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId + "");
        if (CollectionUtils.isEmpty(configMap)) {
            return false;
        }
        String departmentDelete = configMap.get(PlatformGlobalConfigKeyEnum.DEPARTMENT_NAME_SUFFIX.getKey());
        return Boolean.valueOf(departmentDelete);

    }

    private YfyDepartment specialHandlingByDepartment(CustomDepartment customDepartment) {
        String name = customDepartment.getName();
        if (name.contains(SyncTaskConstants.DEPARTMENT_DISABLE_PREFIX)) {
            return null;
        }
        if (name.length() > 27) {
            name = SyncTaskConstants.DEPARTMENT_DISABLE_PREFIX + name.substring(0, 23) + "．．．";
        } else {
            name = SyncTaskConstants.DEPARTMENT_DISABLE_PREFIX + name;
        }
        YfyDepartment yfyDepartment = YfyDepartment.builder()
                .id(customDepartment.getCustomId())
                .name(name)
                .parentId(customDepartment.getCustomParentId())
                .status(DepartmentStatusEnum.SAVE.getCode())
                .updateTime(new Date())
                .build();
        return yfyDepartment;
    }


    /**
     * 同步用户信息，返回失败的次数
     * @param syncYfyUsers
     * @param enterprise
     * @param sourceType
     * @return
     */
    private Integer doSyncUser(List<YfyUser> syncYfyUsers, Enterprise enterprise, Integer taskId, String sourceType, Boolean isCreateDefaultPassword, String customPassword) {
        log.info("sync user data {}", JSON.toJSONString(syncYfyUsers));
        try {
            SyncUserBean syncUserBean = new SyncUserBean();
            syncUserBean.setUsers(syncYfyUsers);
            syncUserBean.setEnterpriseId(enterprise.getId());
            syncUserBean.setPlatformId((long) enterprise.getPlatformId());
            syncUserBean.setRequestId(MDC.get("uuid"));
            syncUserBean.setCreateDefaultPassword(isCreateDefaultPassword);
            if (StringUtils.isNotBlank(customPassword)) {
                syncUserBean.setCustomPassword(customPassword);
            }

            SyncPlatformResult syncPlatformResult = v2ClientHelper.syncUser(syncUserBean);
            if(Objects.isNull(syncPlatformResult)){
                List<String> ids = syncYfyUsers.stream().map(YfyUser::getId).collect(Collectors.toList());
                if (SourceTypeEnum.PUSH.getDesc().equals(sourceType)){
                    List<PlatformSyncTaskFailLogs> logs = platformSyncTaskRecordService.selectByTaskId(taskId, null);
                    if (!CollectionUtils.isEmpty(logs)) {
                        return null;
                    }
                }
                List<PlatformSyncTaskFailLogs> records = new ArrayList<>();
                PlatformSyncTaskFailLogs platformSyncTaskFailLogs = new PlatformSyncTaskFailLogs();
                platformSyncTaskFailLogs.setSyncTaskId(taskId);
                platformSyncTaskFailLogs.setCreated(new Date().getTime());
                platformSyncTaskFailLogs.setCustomeId(JSON.toJSONString(ids));
                platformSyncTaskFailLogs.setEnterpriseId(Math.toIntExact(enterprise.getId()));
                platformSyncTaskFailLogs.setReason("sync syncYfyUsers failed !");
                platformSyncTaskFailLogs.setValueBox(JSON.toJSONString(syncYfyUsers));
                records.add(platformSyncTaskFailLogs);
                platformSyncTaskRecordService.batchInsertLogs(records);
                if (SourceTypeEnum.PUSH.getDesc().equals(sourceType)){
                    List<PlatformAsyncQueue> platformAsyncQueues = platformAsynQueueService.selectByTaskId(taskId);
                    if (!CollectionUtils.isEmpty(platformAsyncQueues)) {
                        //更新同步错误人员
                        for (PlatformAsyncQueue platformAsyncQueue : platformAsyncQueues) {
                            platformAsyncQueue.setValueBox(JSON.toJSONString(syncYfyUsers));
                            platformAsynQueueService.updateByPrimaryKeySelective(platformAsyncQueue);
                        }
                        return null;
                    }
                    PlatformAsyncQueue platformAsyncQueue = new PlatformAsyncQueue();
                    platformAsyncQueue.setEnterpriseId(Math.toIntExact(enterprise.getId()));
                    platformAsyncQueue.setSyncType(SyncTypeEnum.USERS.getSyncType());
                    platformAsyncQueue.setTaskId(taskId);
                    platformAsyncQueue.setCreated(System.currentTimeMillis());
                    platformAsyncQueue.setUpdated(System.currentTimeMillis());
                    platformAsyncQueue.setSyncDated(System.currentTimeMillis());
                    platformAsyncQueue.setStatus(2);
                    platformAsyncQueue.setCount(0);
                    platformAsyncQueue.setValueBox(JSON.toJSONString(syncYfyUsers));
                    // 推送批量插入异常数据
                    platformAsynQueueService.insert(platformAsyncQueue);
                }
                return null;
            }else {
                List<SyncPlatformResult.FailedItemInfo> failedItems = syncPlatformResult.getFailedItems();
                if(CollectionUtils.isEmpty(failedItems)){
                    return null;
                }else {
                    // 写入错误日志
                    recordErrorLog(syncYfyUsers, null, enterprise, failedItems, taskId, sourceType);
                    AtomicInteger count = new AtomicInteger();
                    syncPlatformResult.getFailedItems().forEach(failedItemInfo -> {
                        count.getAndAdd(failedItemInfo.getIds().size());
                    });
                    return count.get();
                }
            }
        } catch (Exception e) {
            log.error("sync user error ", e);
            return null;
        }
    }

    /**
     * 同步部门信息，返回失败的次数
     * @param syncYfyDepartments
     * @param taskId
     * @return
     */
    private Integer doSyncDepartment(List<YfyDepartment> syncYfyDepartments, Enterprise enterprise, Integer taskId, String sourceType) {
        log.info("sync department data {}", JSON.toJSONString(syncYfyDepartments));
        try {
            SyncDepartmentBean syncDepartmentBean = new SyncDepartmentBean();
            syncDepartmentBean.setDepartments(syncYfyDepartments);
            syncDepartmentBean.setEnterpriseId(enterprise.getId());
            syncDepartmentBean.setPlatformId((long) enterprise.getPlatformId());
            syncDepartmentBean.setRequestId(MDC.get("uuid"));

            SyncPlatformResult syncPlatformResult = v2ClientHelper.syncDept(syncDepartmentBean);
            if(Objects.isNull(syncPlatformResult)){
                List<String> ids = syncYfyDepartments.stream().map(YfyDepartment::getId).collect(Collectors.toList());
                if (SourceTypeEnum.PUSH.getDesc().equals(sourceType)) {
                    List<PlatformSyncTaskFailLogs> logs = platformSyncTaskRecordService.selectByTaskId(taskId, null);
                    if (!CollectionUtils.isEmpty(logs)) {
                        return null;
                    }
                }
                List<PlatformSyncTaskFailLogs> records = new ArrayList<>();
                PlatformSyncTaskFailLogs platformSyncTaskFailLogs = new PlatformSyncTaskFailLogs();
                platformSyncTaskFailLogs.setSyncTaskId(taskId);
                platformSyncTaskFailLogs.setCreated(new Date().getTime());
                platformSyncTaskFailLogs.setCustomeId(JSON.toJSONString(ids));
                platformSyncTaskFailLogs.setEnterpriseId(Math.toIntExact(enterprise.getId()));
                platformSyncTaskFailLogs.setReason("sync syncYfyDepartments failed !");
                platformSyncTaskFailLogs.setValueBox(JSON.toJSONString(syncYfyDepartments));

                records.add(platformSyncTaskFailLogs);
                platformSyncTaskRecordService.batchInsertLogs(records);
                if (SourceTypeEnum.PUSH.getDesc().equals(sourceType)){
                    List<PlatformAsyncQueue> platformAsyncQueues = platformAsynQueueService.selectByTaskId(taskId);
                    if (!CollectionUtils.isEmpty(platformAsyncQueues)) {
                        //更新同步错误部门
                        for (PlatformAsyncQueue platformAsyncQueue : platformAsyncQueues) {
                            platformAsyncQueue.setValueBox(JSON.toJSONString(syncYfyDepartments));
                            platformAsynQueueService.updateByPrimaryKeySelective(platformAsyncQueue);
                        }
                        return null;
                    }
                    PlatformAsyncQueue platformAsyncQueue = new PlatformAsyncQueue();
                    platformAsyncQueue.setEnterpriseId(Math.toIntExact(enterprise.getId()));
                    platformAsyncQueue.setSyncType(SyncTypeEnum.DEPARTMENT.getSyncType());
                    platformAsyncQueue.setTaskId(taskId);
                    platformAsyncQueue.setCreated(System.currentTimeMillis());
                    platformAsyncQueue.setUpdated(System.currentTimeMillis());
                    platformAsyncQueue.setSyncDated(System.currentTimeMillis());
                    platformAsyncQueue.setStatus(2);
                    platformAsyncQueue.setCount(0);
                    platformAsyncQueue.setValueBox(JSON.toJSONString(syncYfyDepartments));
                    // 推送批量插入异常数据
                    platformAsynQueueService.insert(platformAsyncQueue);
                }
                return null;
            }else {
                List<SyncPlatformResult.FailedItemInfo> failedItems = syncPlatformResult.getFailedItems();
                if(CollectionUtils.isEmpty(failedItems)){
                    return null;
                }else {
                    // 写入错误日志
                    recordErrorLog(null, syncYfyDepartments, enterprise, failedItems, taskId, sourceType);

                    AtomicInteger count = new AtomicInteger();
                    syncPlatformResult.getFailedItems().forEach(failedItemInfo -> {
                        count.getAndAdd(failedItemInfo.getIds().size());
                    });
                    return count.get();

                }
            }
        } catch (Exception e) {
            log.error("sync department error", e);
            return null;
        }
    }

    /**
     * 同步群组信息，数据库中不插入日志
     * @param syncYfyGroups
     * @param enterprise
     */
    public void doSyncGroup(List<YfyGroup> syncYfyGroups, Enterprise enterprise) {
        log.info("sync group data {}", JSON.toJSONString(syncYfyGroups));
        try {
            SyncGroupBean syncGroupBean = new SyncGroupBean();
            syncGroupBean.setGroups(syncYfyGroups);
            syncGroupBean.setEnterpriseId(enterprise.getId());
            syncGroupBean.setPlatformId((long) enterprise.getPlatformId());
            syncGroupBean.setRequestId(MDC.get("uuid"));

            v2ClientHelper.syncGroup(syncGroupBean);

        } catch (Exception e) {
            log.error("sync group error", e);
        }
    }




    /**
     * 记录同步错误日志
     * @param syncYfyUsers
     * @param syncYfyDepartments
     * @param enterprise
     * @param failedItems
     * @param taskId
     * @param sourceType
     */
    public void recordErrorLog(List<YfyUser> syncYfyUsers,
                               List<YfyDepartment> syncYfyDepartments,
                               Enterprise enterprise,
                               List<SyncPlatformResult.FailedItemInfo> failedItems,
                               Integer taskId, String sourceType) {
        int enterpriseId = (int) enterprise.getId();
        // 获取所有同步失败的id
        List<String> errorIds = new ArrayList<>();
        failedItems.forEach(failedItemInfo -> {
            errorIds.addAll(failedItemInfo.getIds());
        });

        Map<String, String> syncDataMap = new HashMap<String, String>();

        if(!CollectionUtils.isEmpty(syncYfyUsers)){
            syncDataMap = syncYfyUsers
                    .stream().filter(yfyDepartment -> errorIds.contains(yfyDepartment.getId()))
                    .collect(Collectors.toMap(YfyUser::getId, yfyUser -> {
                                return JSON.toJSONString(yfyUser);
                            }
                    ));
        }else{
            syncDataMap = syncYfyDepartments
                    .stream().filter(yfyDepartment -> errorIds.contains(yfyDepartment.getId()))
                    .collect(Collectors.toMap(YfyDepartment::getId, yfyDepartment -> {
                                return JSON.toJSONString(yfyDepartment);
                            }
                    ));
        }

        List<PlatformSyncTaskFailLogs> records = new ArrayList<>();
        Map<String, String> finalSyncDataMap = syncDataMap;
        failedItems.forEach(failedItemInfo -> {
            failedItemInfo.getIds().forEach(id -> {
                if (SourceTypeEnum.PUSH.getDesc().equals(sourceType)) {
                    List<PlatformSyncTaskFailLogs> logs = platformSyncTaskRecordService.selectByTaskId(taskId, id);
                    if (!CollectionUtils.isEmpty(logs)) {
                        //更新错误日志
                        for (PlatformSyncTaskFailLogs platformSyncTaskFailLogs : logs) {
                            platformSyncTaskFailLogs.setValueBox(finalSyncDataMap.get(id));
                            platformSyncTaskRecordService.updateByPrimaryKeySelective(platformSyncTaskFailLogs);
                        }
                        return;
                    }
                }

                // 部门名重名时，需要更新部门名
                if (SyncErrorEnum.DEPARTMENT_NAME_EXIST.getDesc().equals(failedItemInfo.getReason())) {
                    String data = finalSyncDataMap.get(id);
                    YfyDepartment yfyDepartment = JSON.parseObject(data, YfyDepartment.class);

                    StringBuilder name = new StringBuilder(yfyDepartment.getName());
                    String[] nameArr = name.toString().split("_");
                    if (nameArr.length > 1 && nameArr[nameArr.length-1].matches("\\d+")) {
                        StringBuilder newName = new StringBuilder();
                        for(int i = 0; i < nameArr.length-1; i++) {
                            newName.append(nameArr[i]).append("_");
                        }
                        newName.append(Integer.parseInt(nameArr[nameArr.length - 1]) + 1);
                        name = newName;
                    } else {
                        name.append("_1");
                    }
                    yfyDepartment.setName(name.toString());

                    List<YfyDepartment> rebuildYfyDepartments = new ArrayList<>();
                    rebuildYfyDepartments.add(yfyDepartment);
                    log.info("rebuild department name info {}", rebuildYfyDepartments);
                    //同步部门
                    try {
                        doSyncDepartment(rebuildYfyDepartments, enterprise, taskId, sourceType);
                    } catch (Exception e) {
                        log.error("rebuild department name sync department error", e);
                    }
                    return;
                }

                PlatformSyncTaskFailLogs platformSyncTaskFailLogs = new PlatformSyncTaskFailLogs();
                platformSyncTaskFailLogs.setSyncTaskId(taskId);
                platformSyncTaskFailLogs.setCreated(new Date().getTime());
                platformSyncTaskFailLogs.setCustomeId(id);
                platformSyncTaskFailLogs.setEnterpriseId(enterpriseId);
                platformSyncTaskFailLogs.setReason(failedItemInfo.getReason());
                platformSyncTaskFailLogs.setValueBox(finalSyncDataMap.get(id));

                records.add(platformSyncTaskFailLogs);
                if (SourceTypeEnum.PUSH.getDesc().equals(sourceType)) {
                    List<PlatformAsyncQueue> platformAsyncQueues = platformAsynQueueService.selectByTaskId(taskId);
                    if (!CollectionUtils.isEmpty(platformAsyncQueues)) {
                        return;
                    }
                    PlatformAsyncQueue platformAsyncQueue = new PlatformAsyncQueue();
                    platformAsyncQueue.setEnterpriseId(enterpriseId);
                    platformAsyncQueue.setSyncType(!CollectionUtils.isEmpty(syncYfyUsers) ? SyncTypeEnum.USERS.getSyncType() : SyncTypeEnum.DEPARTMENT.getSyncType());
                    platformAsyncQueue.setTaskId(taskId);
                    platformAsyncQueue.setCreated(System.currentTimeMillis());
                    platformAsyncQueue.setUpdated(System.currentTimeMillis());
                    platformAsyncQueue.setSyncDated(System.currentTimeMillis());
                    platformAsyncQueue.setStatus(2);
                    platformAsyncQueue.setValueBox(finalSyncDataMap.get(id));
                    platformAsyncQueue.setCount(0);
                    platformAsynQueueService.insert(platformAsyncQueue);
                }
            });
        });

        if (!CollectionUtils.isEmpty(records)) {
            // 批量插入异常日志
            platformSyncTaskRecordService.batchInsertLogs(records);
        }
    }


    /**
     * 判断用户的部门是否发生变化
     * @param departmentIds
     * @param departmentIds1
     * @return
     */
    private boolean checkUserDepartment(List<String> departmentIds, List<DepartmentIds> departmentIds1) {
        if (departmentIds == null && departmentIds1 == null) {
            return false;
        }

        if (departmentIds == null && departmentIds1 != null) {
            return true;
        }

        if (departmentIds != null && departmentIds1 == null) {
            return true;
        }

        if (departmentIds.size() != departmentIds1.size()) {
            return true;
        }


        for(DepartmentIds dept : departmentIds1) {
            if (Boolean.FALSE.equals(departmentIds.contains(dept.getCustomDepartmentId()))) {
                return true;
            }
        }

        return false;
    }

    /**
     * 判断亿方云用户数据是否需要被删除
     * @param customUser
     * @param userValueBoxDto
     * @param platformSyncConfig
     * @param taskId
     * @return
     */
    private boolean checkCustomUserNeedDelete(CustomUser customUser,
                                              UserValueBoxDto userValueBoxDto,
                                              PlatformSyncConfig platformSyncConfig,
                                              Integer taskId) {
        try {
            String sourceType = platformSyncConfig.getSourceType();
            if (userValueBoxDto.getSqlTaskConfig() == null) {
                return false;
            }
            if (!userValueBoxDto.getSqlTaskConfig().isDeleteIfExist()) {
                return false;
            }
            // 获取需要删除的数据,check删除数据是否是否存在
            String baseMapperKey = SyncTaskConstants.SYNC_BASE_MAPPER + platformSyncConfig.getEnterpriseId() + CommonConstants.SPLIT_UNDERLINE + taskId;
            BaseMapper baseMapper = (BaseMapper) cacheHelper.getObject(baseMapperKey);
            if(Objects.isNull(baseMapper)){
                DataBaseConfig dataBaseConfig = DataBaseConfig.builder()
                        .type(sourceType)
                        .url(userValueBoxDto.getUrl())
                        .userName(userValueBoxDto.getUserName())
                        .password(userValueBoxDto.getPassword()).build();
                baseMapper = DatabaseUtils.getBaseMapper(dataBaseConfig);
                if(Objects.isNull(baseMapper)){
                    return false;
                }
                cacheHelper.putObject(baseMapperKey, baseMapper);
            }
            SqlTaskConfigDto sqlTaskConfig = userValueBoxDto.getSqlTaskConfig();
            List<String> collect = sqlTaskConfig.getSqlConfigs().stream().filter(p ->"CHECK".equals(p.getTaskType())).map(p -> p.getSql()).collect(Collectors.toList());

            String customId = customUser.getCustomId();
            boolean hasData = false;
            for (String sql : collect) {
                String checkUserSql = sql.replace("$", customId);
                log.info("check user sql info {}",checkUserSql);
                List<Map<String, Object>> baseSqlData = DatabaseUtils.getBaseSqlData(baseMapper, checkUserSql);
                hasData = checkDeleteData(baseSqlData, customId);
                if(hasData){
                    break;
                }
            }
            if(!hasData){
                return false;
            }
            // 判断删除规则
            if(sqlTaskConfig.isDeletedNow()){
                // 添加到删除列表中
                return true;
            }else {
                String deleteStartDateKey = SyncTaskConstants.DELETE_USER + platformSyncConfig.getEnterpriseId() + CommonConstants.SPLIT_UNDERLINE + customId;
                String deleteStartDate = redisStringManager.get(deleteStartDateKey);
                if(StringUtils.isNotEmpty(deleteStartDate)){
                    Integer delayDeletedDays = sqlTaskConfig.getDelayDeletedDays();

                    Long nowTime = System.currentTimeMillis() / 1000;
                    Long deleteStartTime = Long.parseLong(deleteStartDate);
                    int pastDay = Double.valueOf(Math.ceil((nowTime - deleteStartTime) / (60 * 60 * 24))).intValue();
                    if(pastDay > delayDeletedDays){
                        log.info("delete user ! userId:{}", customId);
                        // 删除缓存
                        redisStringManager.delete(deleteStartDateKey);
                        // 添加删除用户信息
                        return true;
                    }
                    log.info("delete user conditions are not met ! userId {} deleteStartTime {} after {} days to delete",
                            customId, nowTime, delayDeletedDays - pastDay);
                }else {
                    // 设置删除起始计算时间
                    redisStringManager.set(deleteStartDateKey, String.valueOf(System.currentTimeMillis() / 1000),0L);
                }
            }

        }catch (Exception e){
            log.error("check customUser error ! customUser info{}", JSON.toJSONString(customUser), e);
        }
        return false;
    }

    /**
     * 判断亿方云部门数据是否需要被删除
     * @param customDepartment
     * @param depValueBoxDto
     * @param platformSyncConfig
     * @param taskId
     * @return
     */
    private boolean checkCustomDepartmentNeedDelete(CustomDepartment customDepartment, DepValueBoxDto depValueBoxDto, PlatformSyncConfig platformSyncConfig, Integer taskId) {
        try {
            if (Objects.isNull(depValueBoxDto.getSqlTaskConfig())) {
                return false;
            }
            if (!depValueBoxDto.getSqlTaskConfig().isDeleteIfExist()) {
                return false;
            }
            // 获取需要删除的数据,check删除数据是否是否存在
            String baseMapperKey = SyncTaskConstants.SYNC_BASE_MAPPER + platformSyncConfig.getEnterpriseId() + CommonConstants.SPLIT_UNDERLINE + taskId;
            BaseMapper baseMapper = (BaseMapper) cacheHelper.getObject(baseMapperKey);
            if(Objects.isNull(baseMapper)){
                DataBaseConfig dataBaseConfig = DataBaseConfig.builder()
                        .type(platformSyncConfig.getSourceType())
                        .url(depValueBoxDto.getUrl())
                        .userName(depValueBoxDto.getUserName())
                        .password(depValueBoxDto.getPassword()).build();
                baseMapper = DatabaseUtils.getBaseMapper(dataBaseConfig);
                if(Objects.isNull(baseMapper)){
                    return false;
                }
                cacheHelper.putObject(baseMapperKey, baseMapper);
            }
            SqlTaskConfigDto sqlTaskConfig = depValueBoxDto.getSqlTaskConfig();

            String customDepartmentId = customDepartment.getCustomId();
            String companyType = sqlTaskConfig.getCompanyType();
            boolean hasData = false;
            // 若为高校企业，部门的id需要去除前缀
            if(SyncCompanyTypeEnum.COLLEGE.getStatus().equals(companyType)){
                List<SqlTaskConfigDto.SqlConfig> collect = sqlTaskConfig.getSqlConfigs().stream().filter(p -> SyncTaskTypeEnum.CHECK.getStatus().equals(p.getTaskType())).collect(Collectors.toList());
                String originalCustomDepartmentId = null;
                String sql = null;
                if(customDepartmentId.startsWith(SyncTaskConstants.COLLEGE_STUDENT_PREFIX)){ // 校验学生部门
                    originalCustomDepartmentId = customDepartmentId.replace(SyncTaskConstants.COLLEGE_STUDENT_PREFIX, "");
                    sql = collect.stream()
                            .filter(p -> SyncTableTypeEnum.STUDENT.getStatus().equals(p.getTableType()))
                            .map(p -> p.getSql()).collect(Collectors.toList()).get(0);

                }else if(customDepartmentId.startsWith(SyncTaskConstants.COLLEGE_TEACHER_PREFIX)){ // 校验教职工部门
                    originalCustomDepartmentId = customDepartmentId.replace(SyncTaskConstants.COLLEGE_TEACHER_PREFIX, "");
                    sql = collect.stream()
                            .filter(p -> SyncTableTypeEnum.TEACHER.getStatus().equals(p.getTableType()))
                            .map(p -> p.getSql()).collect(Collectors.toList()).get(0);
                }else if(customDepartmentId.startsWith(SyncTaskConstants.COLLEGE_GRADUATE_PREFIX)){ // 校验老师部门
                    originalCustomDepartmentId = customDepartmentId.replace(SyncTaskConstants.COLLEGE_GRADUATE_PREFIX, "");
                    sql = collect.stream()
                            .filter(p -> SyncTableTypeEnum.GRADUATE.getStatus().equals(p.getTableType()))
                            .map(p -> p.getSql()).collect(Collectors.toList()).get(0);
                }
                log.info("originalCustomDepartmentId {}", originalCustomDepartmentId);

                if(StringUtils.isNotEmpty(sql) && StringUtils.isNotEmpty(originalCustomDepartmentId)){
                    String checkDeptSql = sql.replace("$", originalCustomDepartmentId);

                    log.info("check department sql info {}",checkDeptSql);
                    List<Map<String, Object>> baseSqlData = DatabaseUtils.getBaseSqlData(baseMapper, checkDeptSql);
                    hasData = checkDeleteData(baseSqlData, originalCustomDepartmentId);
                }

            }else {
                List<String> collect = sqlTaskConfig.getSqlConfigs().stream().filter(p ->"CHECK".equals(p.getTaskType())).map(p -> p.getSql()).collect(Collectors.toList());
                for (String sql : collect) {

                    String checkDeptSql = sql.replace("$", customDepartmentId);

                    log.info("check department sql info {}",checkDeptSql);
                    List<Map<String, Object>> baseSqlData = DatabaseUtils.getBaseSqlData(baseMapper, checkDeptSql);
                    hasData = checkDeleteData(baseSqlData, customDepartmentId);
                    if(hasData){
                        break;
                    }
                }
            }

            if(!hasData){
                return false;
            }

            // 判断删除规则
            if(sqlTaskConfig.isDeletedNow()){
                // 添加到删除列表中
                return true;
            }else {
                String deleteStartDateKey = SyncTaskConstants.DELETE_DEPARTMENT + platformSyncConfig.getEnterpriseId() + CommonConstants.SPLIT_UNDERLINE + customDepartmentId;
                String deleteStartDate = redisStringManager.get(deleteStartDateKey);
                if(StringUtils.isNotEmpty(deleteStartDate)){
                    Integer delayDeletedDays = sqlTaskConfig.getDelayDeletedDays();

                    Long nowTime = System.currentTimeMillis() / 1000;
                    Long deleteStartTime = Long.parseLong(deleteStartDate);
                    int pastDay = Double.valueOf(Math.ceil((nowTime - deleteStartTime) / (60 * 60 * 24))).intValue();
                    if(pastDay > delayDeletedDays){
                        // 删除缓存
                        redisStringManager.delete(deleteStartDateKey);
                        // 添加删除用户信息
                        return true;
                    }
                    log.info("delete department conditions are not met ! deptId {} deleteStartTime {} after {} days to delete",
                            customDepartmentId, nowTime, delayDeletedDays - pastDay);
                }else {
                    // 设置删除起始计算时间
                    redisStringManager.set(deleteStartDateKey, String.valueOf(System.currentTimeMillis() / 1000),0L);
                }
            }

        }catch (Exception e){
            log.info("check customDepartment error ! customDepartment info{}", JSON.toJSONString(customDepartment));
        }
        return false;
    }

    /**
     * 构建需要同步删除的亿方云用户
     * @param customUser
     * @return
     */
    private YfyUser buildDeleteYfyUser(CustomUser customUser) {
        return YfyUser.builder()
                .id(customUser.getCustomId())
                .fullName(customUser.getName())
                .phone(customUser.getPhone())
                .email(customUser.getEmail())
                .createTime(new Date())
                .status("2")
                .build();
    }

    /**
     * 构建需要同步删除的亿方云部门
     * @param customDepartment
     * @return
     */
    private YfyDepartment buildDeleteYfyDepartment(CustomDepartment customDepartment) {
        String name = customDepartment.getName();
        name = name.replace(SyncTaskConstants.DEPARTMENT_DISABLE_PREFIX, "");
        if (name.length() > 27) {
            name = SyncTaskConstants.DEPARTMENT_DISABLE_PREFIX + name.substring(0, 23) + "．．．";
        } else {
            name = SyncTaskConstants.DEPARTMENT_DISABLE_PREFIX + name;
        }

        return YfyDepartment.builder()
                .id(customDepartment.getCustomId())
                .name(name)
                .updateTime(new Date())
                .spaceTotal(customDepartment.getTotalSpace())
                .order(customDepartment.getOrder())
                .status(DepartmentStatusEnum.SAVE.getCode())
                .build();
    }

    /**
     * 判断删除数据是否存在
     * @param baseSqlData
     * @param id
     * @return
     */
    private boolean checkDeleteData(List<Map<String, Object>> baseSqlData, String id){
        // 数据不存在不删除
        if(!CollectionUtils.isEmpty(baseSqlData)){
            if(baseSqlData.size() > 1){
                return false;
            }

            String customeId = StringUtils.EMPTY;
            Map<String, Object> stringObjectMap = baseSqlData.get(0);
            for (String key : stringObjectMap.keySet()) {
                Object resultObject = stringObjectMap.get(key);
                String data = Objects.isNull(resultObject) ? null : String.valueOf(resultObject);
                if (key.equalsIgnoreCase(SyncTaskConstants.USER_PARAMETER_ID.toLowerCase())) {
                    customeId = data;
                }
            }
            if(StringUtils.isBlank(customeId)){
                return false;
            }
            if(customeId.equals(id)){
               return true;
            }
        }
        return false;
    }

    private YfyUser checkSpecialHandling(CustomUser customUser, long adminUserId, long enterpriseId) {
        YfyUser yfyUser = YfyUser.builder()
                .id(customUser.getCustomId())
                .fullName(customUser.getName())
                .phone(customUser.getPhone())
                .email(customUser.getEmail())
                .createTime(new Date())
                .status("1")
                .build();
        Boolean isChange = false;
        String name = customUser.getName();

        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId+ "");
        Boolean customSyncUserSetDepartmentIdNullEnable = null;
        Boolean customSyncUserSetActiveClose = null;
        String customUserNameSuffix = null;
        // 判断数据库中全局配置，若配置不为空，取数据库配置，若为空取启动项配置
        if(CollectionUtils.isEmpty(configMap)){
            customSyncUserSetDepartmentIdNullEnable = nacosConfig.getCustomSyncUserSetDepartmentIdNullEnable();
            customSyncUserSetActiveClose = nacosConfig.getCustomSyncUserSetActiveClose();
            customUserNameSuffix = nacosConfig.getCustomUserNameSuffix();
        }else {
            customSyncUserSetDepartmentIdNullEnable = Boolean.valueOf(configMap.get(PlatformGlobalConfigKeyEnum.SET_DEPARTMENT_ID_NULL_ENABLE.getKey()));
            customSyncUserSetActiveClose = Boolean.valueOf(configMap.get(PlatformGlobalConfigKeyEnum.SET_USER_ACTIVE_CLOSE.getKey()));
            customUserNameSuffix = configMap.get(PlatformGlobalConfigKeyEnum.USER_NAME_SUFFIX.getKey());
        }

        if (customSyncUserSetDepartmentIdNullEnable) {
            yfyUser.setDepartmentIds(Lists.newArrayList());
            isChange = true;
        }
        if (StringUtils.isNotBlank(customUserNameSuffix) && !name.contains(customUserNameSuffix)) {
            yfyUser.setFullName(name + customUserNameSuffix);
            isChange = true;
        }
        if (customSyncUserSetActiveClose && customUser.isActive()) {
            setUserActiveDisable(customUser.getId(), adminUserId);
            isChange = true;
        }
        if(!isChange){
            return null;
        }
        log.info("customUser checkSpecialHandling , customUserInfo {}, afterChange yfyUserInfo{}", JSON.toJSONString(customUser) , JSON.toJSONString(yfyUser));
        return yfyUser;
    }

    private boolean isSpecialHandling(CustomUser customUser, PlatformSyncConfig platformSyncConfig, UserValueBoxDto userValueBoxDto) {
        String sourceType = platformSyncConfig.getSourceType();
        String valueBox = customUser.getValueBox();
        if (userValueBoxDto.getSqlTaskConfig() != null || sourceType.equals(SourceTypeEnum.PUSH.getDesc())) {
            return false;
        }
        if (StringUtils.isBlank(valueBox)) {
            return true;
        }
        try {
            boolean isFilter = (boolean) JSONPath.extract(customUser.getValueBox(), SyncTaskConstants.IS_FILTER_PLATFORM_USER_PATH);
            if (isFilter) {
                return false;
            }
        }catch (Exception e) {
            return true;
        }
        return true;
    }


    /**
     * 禁用明确需要禁用的用户数据
     * @param yfyUserList
     * @param customUserMap
     * @param adminUserId
     * @param enterpriseId
     */
    private void disableYfyUserList(List<YfyUser> yfyUserList, Map<String, CustomUser> customUserMap, long adminUserId, long enterpriseId) {
        for (YfyUser yfyUser : yfyUserList) {
            CustomUser customUser = customUserMap.get(yfyUser.getId());
            if(yfyUser.isDisable()){
                // 若云盘不存在改用户，不需要同步禁用用户
                if(Objects.isNull(customUser)){
                    yfyUser.setStatus("2");
                    continue;
                }
                Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId+ "");
                Boolean customSetDisabledUserDepartmentIsNull = null;
                String customDisabledUserNameSuffix = null;
                // 判断数据库中全局配置，若配置不为空，取数据库配置，若为空取启动项配置
                if(CollectionUtils.isEmpty(configMap)){
                    customSetDisabledUserDepartmentIsNull = nacosConfig.getCustomSetDisabledUserDepartmentIsNull();
                    customDisabledUserNameSuffix = nacosConfig.getCustomDisabledUserNameSuffix();
                }else {
                    customSetDisabledUserDepartmentIsNull = Boolean.valueOf(configMap.get(PlatformGlobalConfigKeyEnum.SET_DISABLED_USER_DEPARTMENT_ID_IS_NULL.getKey()));
                    customDisabledUserNameSuffix = configMap.get(PlatformGlobalConfigKeyEnum.DISABLED_USER_NAME_SUFFIX.getKey());
                }


                if (customSetDisabledUserDepartmentIsNull) {
                    yfyUser.setDepartmentIds(Lists.newArrayList());
                }
                yfyUser.setFullName(yfyUser.getFullName() + customDisabledUserNameSuffix);
                if(!Objects.isNull(customUser)
                        && customUser.isActive()){
                    log.info("isDisableYfyUser info is :{}", JSON.toJSONString(yfyUser));
                    setUserActiveDisable(customUser.getId(), adminUserId);
                }
            }
        }
    }

    public void setUserActiveDisable(Long userId, long adminUserId){
        User user = userMapper.queryById(userId);
        // 如果用户是手动同步用户且没有设置为系统托管，不更改用户状态
        if(!user.getIsSystemHosting()){
            log.info("user is not system hosting, user id is:{}!", userId);
            return;
        }
        SyncActiveUsersBatchBean syncActiveUsersBatchBean = new SyncActiveUsersBatchBean();
        syncActiveUsersBatchBean.setUserIds(Arrays.asList(userId));
        syncActiveUsersBatchBean.setStatus("disable");
        v2ClientHelper.active_users_batch(syncActiveUsersBatchBean, adminUserId);
    }

    public static List<YfyDepartment> sortByHierarchy(List<YfyDepartment> yfyDepartments) {
        if (yfyDepartments == null || yfyDepartments.isEmpty())  {
            return new ArrayList<>();
        }

        List<YfyDepartment> sortedList = new ArrayList<>();

        Map<String, YfyDepartment> idToDept = new HashMap<>();
        Map<String, List<YfyDepartment>> parentToChildren = new HashMap<>();

        for (YfyDepartment dept : yfyDepartments) {
            idToDept.put(dept.getId(),  dept);
            String parentId = dept.getParentId();
            parentToChildren.computeIfAbsent(parentId,  k -> new ArrayList<>()).add(dept);
        }

        List<YfyDepartment> topLevelDepts = new ArrayList<>();

        if (parentToChildren.containsKey(null))  {
            topLevelDepts.addAll(parentToChildren.get(null));
        }

        for (YfyDepartment dept : yfyDepartments) {
            String parentId = dept.getParentId();
            if (parentId != null && !idToDept.containsKey(parentId))  {
                topLevelDepts.add(dept);
            }
        }

        Queue<YfyDepartment> queue = new LinkedList<>(topLevelDepts);
        Set<String> processedIds = new HashSet<>();

        while (!queue.isEmpty())  {
            YfyDepartment current = queue.poll();

            if (processedIds.contains(current.getId()))  {
                continue;
            }
            processedIds.add(current.getId());

            sortedList.add(current);

            List<YfyDepartment> children = parentToChildren.get(current.getId());
            if (children != null) {
                queue.addAll(children);
            }
        }

        for (YfyDepartment dept : yfyDepartments) {
            if (!processedIds.contains(dept.getId()))  {
                sortedList.add(dept);
            }
        }

        return sortedList;
    }
}
