package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.service.DataSourceSyncHandler;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskExecutor;
import com.fangcloud.thirdpartplatform.service.factory.DataSourceSyncHandlerFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class PlatformSyncTaskExecutorImpl implements PlatformSyncTaskExecutor {

    @Resource
    private DataSourceSyncHandlerFactory dataSourceSyncHandlerFactory;

    @Override
    public ExecuteResult execute(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        DataSourceSyncHandler handler = dataSourceSyncHandlerFactory.getHandler(platformSyncConfig.getSourceType());
        log.info("platformSyncConfig info {}", JSON.toJSONString(platformSyncConfig));
        Integer syncType = platformSyncConfig.getSyncType();
        ExecuteResult executeResult = null;
        if (syncType == SyncTypeEnum.DEPARTMENT.getSyncType()) {
            executeResult = handler.syncDepartment(platformSyncConfig, taskId);
        }
        if (syncType == SyncTypeEnum.USERS.getSyncType()) {
            executeResult = handler.syncUser(platformSyncConfig, taskId);
        }
        if (syncType == SyncTypeEnum.SYNC.getSyncType()) {
            executeResult = handler.sync(platformSyncConfig, taskId);
        }
        return executeResult;
    }
}
