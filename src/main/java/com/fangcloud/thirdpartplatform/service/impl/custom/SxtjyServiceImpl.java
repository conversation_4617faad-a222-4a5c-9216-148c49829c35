package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.util.IOUtils;
import com.fangcloud.thirdpartplatform.check.LoginControllerCheck;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.APIParamConfigDto;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.ui.Model;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
public class SxtjyServiceImpl implements ApiExecuteService {

    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private OpenClientHelper openClientHelper;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    private LoginControllerCheck loginControllerCheck;

    public Model getParams(Model model, String config) {
        model.addAttribute("config", config);
        return model;
    }

    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        String code = request.getParameter("code");
        String config = request.getParameter("config");
        log.info("sxjcy code {}", code);
        String deptConfigId = configInfo.getDeptConfigId();
        PlatformSyncConfig platformSyncConfig = platformSyncConfigMapper.selectByPrimaryKey(Integer.valueOf(deptConfigId));
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        List<APIParamConfigDto> paramConfig = apiConfigValueBox.getApiConfig().getParamConfig();
        String accessToken = getAccessToken(code, config, paramConfig);
        if (StringUtils.isBlank(accessToken)) {
            return null;
        }
        String loginName = getUserLoginName(accessToken, paramConfig);
        try {
            if (loginName != null) {
                EnterpriseParams enterpriseParams = new EnterpriseParams();
                enterpriseParams.setEnterpriseId(configInfo.getEnterpriseId());
                enterpriseParams.setIdentifier(loginName);
                enterpriseParams.setPlatformId(configInfo.getPlatformId());
                enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
                enterpriseParams.setClientId(configInfo.getClientId());
                String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
                log.info("sxjcy 免密登录url {}", loginUrl);
                loginUrl = loginControllerCheck.checkTargetHost(request, loginUrl);
                log.info("auto login url {}" , loginUrl);
                return loginUrl;
            }
        } catch (Exception e) {
            log.error("sxjcy 异常", e);
        }
        return null;
    }

    private String getAccessToken(String code, String config, List<APIParamConfigDto> paramConfig) {
        String redirectUrl = apiSyncHandler.getValueByParamConfig(paramConfig, "redirect_url");
        String getAccessTokenUrl = apiSyncHandler.getValueByParamConfig(paramConfig, "get_accessToken_url");
        String appId = apiSyncHandler.getValueByParamConfig(paramConfig, "app_id");
        String appSecret = apiSyncHandler.getValueByParamConfig(paramConfig, "app_secret");
        String redirectUlr = String.format(redirectUrl, config);
        String url = String.format(getAccessTokenUrl, appId, appSecret, redirectUlr, code);
        log.info("get access_token url {}", url);
        try {
            Response response = httpClientHelper.postResponse(url, "", httpClientHelper.getApplicationFormData());
            String result = Objects.requireNonNull(response.body()).string();
            log.info("get access_token result {}", result);
            return (String) JSONPath.extract(result, "$.access_token");
        } catch (Exception e) {
            log.error("get access_token error url {}", e);
        }
        return null;
    }

    private String getUserLoginName(String accessToken, List<APIParamConfigDto> paramConfig) {
        String getUserLoginNameUrl = apiSyncHandler.getValueByParamConfig(paramConfig, "get_user_login_name_url");
        String url = String.format(getUserLoginNameUrl, accessToken);
        log.info("get user info url {}", url);
        try {
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("get user info result {}", result);
            return (String) JSONPath.extract(result, "$.id");
        } catch (Exception e) {
            log.error("get user info error url {}", e);
        }
        return null;
    }

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        return null;
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        String resultPath = apiConfig.getResultPath();
        if (StringUtils.isBlank(resultPath)) {
            return apiResult;
        }
        String[] split = resultPath.split("\\$.");
        String key = split[1];
        JSONObject jsonObject = JSONObject.parseObject(apiResult);
        String processing = decodeBase64AndUnGzip(jsonObject.getString(key));
        JSONArray jsonArray = JSONArray.parseArray(processing);
        jsonObject.put(key, jsonArray);
        return jsonObject.toJSONString();
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.SXJCY.getDesc();
    }

    private static String decodeBase64AndUnGzip(String apiResult) {
        GZIPInputStream gis = null;
        BufferedReader bf = null;
        try {
            gis = new GZIPInputStream(new ByteArrayInputStream(Base64.getDecoder().decode(apiResult)));
            bf = new BufferedReader(new InputStreamReader(gis, "UTF-8"));
            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            while ((line = bf.readLine()) != null) {
                jsonBuilder.append(line);
            }
            apiResult = jsonBuilder.toString();
        } catch (Exception e) {
            log.error("resultProcessing decodeBase64AndUnGzip error", e);
            apiResult = StringUtils.EMPTY;
        } finally {
            IOUtils.close(bf);
            IOUtils.close(gis);
        }
        return apiResult;
    }
}
