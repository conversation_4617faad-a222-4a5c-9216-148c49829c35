package com.fangcloud.thirdpartplatform.service.impl.thirdparty.worlflow;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdPartyCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.ShareWorkflow;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.ShareWorkflowCall;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.ark.WorkflowApprovalBean;
import com.fangcloud.thirdpartplatform.helper.ArkClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyCommonService;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
@Slf4j
public class WorkflowShareCallServiceImpl implements ThirdpartyExecuteService {


    @Autowired
    private PlatformUserMapper platformUserMapper;


    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ArkClientHelper arkClientHelper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private ThirdpartyCommonService thirdpartyCommonService;

    @Autowired
    private GroupMapper groupMapper;


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        ShareWorkflow shareWorkflow = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getData()), ShareWorkflow.class);

        ShareWorkflowCall shareWorkflowCall = shareWorkflowConvertToshareWorkflowCall(shareWorkflow);

        ThirdPartyCall thirdPartyCall = ThirdPartyCall.builder()
                .type(ThirdpartyCallbackTypeEnum.CREATE_SHARE_CALLBACK.getDesc())
                .data(shareWorkflowCall)
                .build();

        return ResultUtils.getSuccessResult(thirdPartyCall);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {
        ShareWorkflowCall shareWorkflowCall = JSON.parseObject(JSON.toJSONString(thirdpartyCallbackParams.getData()), ShareWorkflowCall.class);
        // 校验回调记录
        thirdpartyCommonService.checkCallbackResult(
                shareWorkflowCall.getUniqueId(),
                shareWorkflowCall.getEnterpriseId(),
                shareWorkflowCall.getReviewType(),
                thirdpartyCallbackParams.isReviewResult());

        WorkflowApprovalBean workflowApprovalBean = new WorkflowApprovalBean();
        workflowApprovalBean.setPass(thirdpartyCallbackParams.isReviewResult());
        workflowApprovalBean.setTaskId(Long.parseLong(shareWorkflowCall.getTaskId()));

        boolean workflowApprovalResult = arkClientHelper.workflowApproval(workflowApprovalBean, shareWorkflowCall.getReceiverId());

        return ResultUtils.getSuccessResult(workflowApprovalResult);
    }

    private ShareWorkflowCall shareWorkflowConvertToshareWorkflowCall(ShareWorkflow shareWorkflow) {

        Long enterpriseId = shareWorkflow.getEnterpriseId();
        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);
        if(Objects.isNull(platformEnterprises)){
            throw new ParamException("platformEnterprises is null !");
        }

        ShareWorkflowCall shareWorkflowCall = new ShareWorkflowCall();

        // 通用参数
        shareWorkflowCall.setEnterpriseId(enterpriseId);
        shareWorkflowCall.setReceiverId(shareWorkflow.getReceiverId());
        shareWorkflowCall.setTaskId(shareWorkflow.getTaskId());
        shareWorkflowCall.setUniqueId(shareWorkflow.getUniqueId());

        // 分享参数
        shareWorkflowCall.setAccess(shareWorkflow.getAccess());
        shareWorkflowCall.setCurrentVersion(shareWorkflow.isCurrentVersion());
        shareWorkflowCall.setDescription(shareWorkflow.getDescription());
        shareWorkflowCall.setDisableDownload(shareWorkflow.isDisableDownload());
        shareWorkflowCall.setDownloadLimitV2(shareWorkflow.getDownloadLimitV2());
        shareWorkflowCall.setDueTime(shareWorkflow.getDueTime());
        shareWorkflowCall.setEnableDownloadLimitV2(shareWorkflow.isEnableDownloadLimitV2());
        shareWorkflowCall.setEnablePreviewLimit(shareWorkflow.isEnablePreviewLimit());
        shareWorkflowCall.setEnableShareWpsEdit(shareWorkflow.isEnableShareWpsEdit());
        shareWorkflowCall.setPassword(shareWorkflow.getPassword());
        shareWorkflowCall.setPasswordProtected(shareWorkflow.isPasswordProtected());
        shareWorkflowCall.setPreviewLimit(shareWorkflow.getPreviewLimit());
        shareWorkflowCall.setWatermarkTemplateInfo(shareWorkflow.getWatermarkTemplateInfo());
        String[] split = shareWorkflow.getItemTypedId().split("_");
        shareWorkflowCall.setItemType(split[0]);
        shareWorkflowCall.setItemId(split[1]);
        shareWorkflowCall.setItemName(shareWorkflow.getItemName());


        Long authorId = shareWorkflow.getAuthorId();
        ThirdpartyCustom authorCustomUser = new ThirdpartyCustom();
        authorCustomUser.setId(authorId);
        PlatformUser authorPlatformUser = platformUserMapper.queryByPlatformIdUserId(platformEnterprises.getPlatformId(), authorId);
        if(!Objects.isNull(authorPlatformUser)){
            authorCustomUser.setCustomId(authorPlatformUser.getUserTicket());
        }
        User authorUser = userMapper.queryById(authorId);
        if(!Objects.isNull(authorUser)){
            authorCustomUser.setName(authorUser.getFullName());
        }
        shareWorkflowCall.setCreateUser(authorCustomUser);
        shareWorkflowCall.setReviewType(ThirdpartyCallTypeEnum.WOEKFLOW_SHARE_CALL.getDesc());

        // 只有分享类型为指定类型才需要组装邀请对对象
        if("collaborators".equals(shareWorkflow.getAccess())){

            // 若邀请用户不为空，则组装CustomUser
            List<Long> invitedUserIds = shareWorkflow.getInvitedUserIds();
            if(!CollectionUtils.isEmpty(invitedUserIds)){
                Map<Long, User> userMap = new HashMap<>();
                List<User> users = userMapper.queryByIds(invitedUserIds);
                for (User user : users) {
                    userMap.put(user.getId(), user);
                }
                List<PlatformUser> platformUsers = platformUserMapper.queryByPlatformIdUserIds(platformEnterprises.getPlatformId(), invitedUserIds);
                Map<Long, String> platformUserMap= new HashMap<>();
                for (PlatformUser platformUser : platformUsers) {
                    platformUserMap.put(platformUser.getUserId(), platformUser.getUserTicket());
                }
                List<ThirdpartyCustom> customUserList = new ArrayList<>();
                for (Long invitedUserId : invitedUserIds) {
                    ThirdpartyCustom customUser = new ThirdpartyCustom();
                    customUser.setId(invitedUserId);
                    customUser.setCustomId(platformUserMap.get(invitedUserId));
                    User user = userMap.get(invitedUserId);
                    if(!Objects.isNull(user)){
                        customUser.setName(user.getFullName());
                    }
                    customUserList.add(customUser);
                }
                shareWorkflowCall.setInvitedUserList(customUserList);
            }


            List<Long> departmentIds = new ArrayList<>();
            Map<Long, Group> groupMap = new HashMap<>();
            // 若邀请群组不为空，则组装CustomGroup
            List<Long> groupIds = shareWorkflow.getGroupIds();
            if(!CollectionUtils.isEmpty(groupIds)){
                List<Group> groups = groupMapper.queryByIds(groupIds);

                List<ThirdpartyCustom> customGroupList = new ArrayList<>();

                for (Group group : groups) {
                    long departmentId = group.getDepartmentId();
                    if(departmentId > 0){// 群组有对应的部门
                        departmentIds.add(departmentId);
                        groupMap.put(departmentId, group);
                    }else {
                        ThirdpartyCustom customGroup = new ThirdpartyCustom();
                        customGroup.setId(group.getId());
                        customGroup.setName(group.getName());
                        customGroupList.add(customGroup);
                    }
                }
                shareWorkflowCall.setGroupList(customGroupList);
            }

            // 若邀请部门不为空，则组装CustomDepartment
            if(!CollectionUtils.isEmpty(departmentIds)){
                List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentIds(platformEnterprises.getPlatformId(), departmentIds);
                Map<Long, String> platformDepartmentMap= new HashMap<>();
                for (PlatformDepartment platformDepartment : platformDepartmentList) {
                    platformDepartmentMap.put(platformDepartment.getYfyDepartmentId(), platformDepartment.getDepartmentId());
                }
                List<ThirdpartyCustom> customDepartments = new ArrayList<>();
                for (Long departmentId : departmentIds) {
                    ThirdpartyCustom customDepartment = new ThirdpartyCustom();
                    customDepartment.setId(departmentId);
                    customDepartment.setCustomId(platformDepartmentMap.get(departmentId));
                    Group group = groupMap.get(departmentId);
                    if(!Objects.isNull(group)){
                        customDepartment.setName(group.getName());
                    }
                    customDepartments.add(customDepartment);
                }
                shareWorkflowCall.setDeptList(customDepartments);
            }
        }
        return shareWorkflowCall;
    }

    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.WOEKFLOW_SHARE_CALL.getDesc();
    }
}
