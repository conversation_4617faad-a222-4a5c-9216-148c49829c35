package com.fangcloud.thirdpartplatform.service.impl.custom;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.FtpBaseInfo;
import com.fangcloud.thirdpartplatform.entity.FtpFilesInfo;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.sync.SyncDepartmentBean;
import com.fangcloud.thirdpartplatform.entity.sync.SyncUserBean;
import com.fangcloud.thirdpartplatform.helper.FtpHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.service.impl.datasource.AbstractDataSourceSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.datasource.CodeScriptSyncHandler;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.utils.Base64Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class FTPServiceImpl extends AbstractDataSourceSyncHandler implements ApiExecuteService {

    private final String userFileDirTmp = "%s/users/";
    private final String deptFileDirTmp = "%s/departments/";
    private final String userFile = "user_%s.json";
    private final String deptFile = "department_%s.json";

    @Resource
    private PlatformSyncConfigMapper syncConfigMapper;

    @Resource
    private FtpHelper ftpHelper;

    @Resource
    private CodeScriptSyncHandler codeScriptSyncHandler;

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        if (StringUtils.isEmpty(platformSyncConfig.getConfigName())) {
            return null;
        }
        if ("outside".equals(platformSyncConfig.getConfigName())){
            FTPValueBox ftpValueBox = JSON.parseObject(platformSyncConfig.getValueBox(),FTPValueBox.class);
            log.info("ftpValueBox:{}",ftpValueBox);
            String jsonUsers = getOneFileResult("users",ftpValueBox);
            if (org.springframework.util.StringUtils.isEmpty(jsonUsers)){
                return null;
            }
            SyncUserBean userResult = JSON.parseObject(jsonUsers, SyncUserBean.class);
            List<YfyUser> userList = userResult.getUsers();
            if (CollectionUtil.isNotEmpty(userList)){
                return syncUserList(platformSyncConfig, userList, taskId);
            }

        }
        return null;
    }

    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        if (StringUtils.isEmpty(platformSyncConfig.getConfigName())) {
            return null;
        }
        if ("outside".equals(platformSyncConfig.getConfigName()))
        {
            FTPValueBox ftpValueBox = JSON.parseObject(platformSyncConfig.getValueBox(),FTPValueBox.class);
            log.info("ftpValueBox:{}",ftpValueBox);
            String jsonDepartments = getOneFileResult("departments",ftpValueBox);
            if (org.springframework.util.StringUtils.isEmpty(jsonDepartments)){
                return null;
            }
            SyncDepartmentBean departmentResult = JSON.parseObject(jsonDepartments, SyncDepartmentBean.class);
            List<YfyDepartment> departmentList = departmentResult.getDepartments();
            if (CollectionUtil.isNotEmpty(departmentList))
            {
                return syncDepartmentList(platformSyncConfig,departmentList,taskId);
            }
        }
        return null;
    }

    public void sync_data(HttpServletRequest request, HttpServletResponse response, @RequestBody JSONObject jSONObject) {
        String ftpEnv= jSONObject.getString("customFtpEnv");
        if (StringUtils.isEmpty(ftpEnv)) {
            return;
        }
        try {
            ExecuteResult executeResult = new ExecuteResult();
            if (ftpEnv.equals("inside")) {
                log.info("ftpValueBox:{}",jSONObject);
                putFile(jSONObject);
            }
        }catch (Exception e){
            throw new RuntimeException(e.getMessage());
        }

    }

    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return null;
    }

    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.FTP.getDesc();
    }

    /**
     * 内网上传客户文件到ftp
     */
    private void putFile(JSONObject ftpValueBox) {
        try {
            // 获取内网接口数据
            String usersFileDir = String.format(userFileDirTmp, ftpValueBox.getString("ftpBaseDir"));
            String departmentsFileDir = String.format(deptFileDirTmp, ftpValueBox.getString("ftpBaseDir"));
            List<PlatformSyncConfig> syncConfigs = syncConfigMapper.query();
            if (CollectionUtil.isNotEmpty(syncConfigs)){
                List<YfyDepartment> departments = new ArrayList<>();
                List<YfyUser> users = new ArrayList<>();
                for (PlatformSyncConfig config : syncConfigs){
                    if (config.getSyncType()==SyncTypeEnum.DEPARTMENT.getSyncType()
                            &&!config.getConfigName().equals("inside")){
                        if (SourceTypeEnum.API.getDesc().equals(config.getSourceType())) {
                            APIConfigValueBox apiConfigValueBox = JSON.parseObject(config.getValueBox(), APIConfigValueBox.class);
                            apiSyncHandler.getYfyDepartmentsByConfigValueBox(departments, apiConfigValueBox, config.getEnterpriseId());
                            log.info("ftpSyncHandler-syncDepartment, departments:{}", JSON.toJSONString(departments));
                        }
                    } else if (config.getSyncType()==SyncTypeEnum.USERS.getSyncType()
                            &&!config.getConfigName().equals("inside")) {
                        if (SourceTypeEnum.CODE_SCRIPT.getDesc().equals(config.getSourceType())){
                            CodeScriptValueBox codeScriptValueBox = JSON.parseObject(config.getValueBox(), CodeScriptValueBox.class);
                            codeScriptSyncHandler.getYfyUsersByConfigValueBox(users, codeScriptValueBox);
                            log.info("ftpSyncHandler-syncUsers, users:{}", JSON.toJSONString(users));
                        }
                    }

                }

                // 后续处理逻辑
                SyncDepartmentBean departmentBean = new SyncDepartmentBean();
                departmentBean.setDepartments(departments);
                String departmentString = Base64Utils.encode(JSON.toJSONString(departmentBean));

                SyncUserBean userBean=new SyncUserBean();
                userBean.setUsers(users);
                String userString = Base64Utils.encode(JSON.toJSONString(userBean));
                
                // 上传ftp
                FtpBaseInfo ftpBaseInfo = FtpBaseInfo.builder()
                        .ip(ftpValueBox.getString("ftpIp"))
                        .port(ftpValueBox.getInteger("ftpPort"))
                        .username(ftpValueBox.getString("ftpUserName"))
                        .password(ftpValueBox.getString("ftpPassword"))
                        .build();

                // 上传部门数据
                if (StringUtils.isNotEmpty(departmentString)) {
                    log.info("upload department info :{}",departmentString);
                    uploadDepartmentData(departmentString, departmentsFileDir, ftpBaseInfo);
                }

                // 上传用户数据
                if (StringUtils.isNotEmpty(userString)) {
                    log.info("upload department info :{}",userString);
                    uploadUserData(userString, usersFileDir, ftpBaseInfo);
                }

                // 内网删除保留最近5个文件
                deleteFileFromFtp(5,ftpValueBox);
            }
        } catch (Exception e) {
            log.error("putFile方法执行失败", e);
        }
    }

    // 抽取上传部门数据的方法
    private void uploadDepartmentData(String departmentString, String departmentsFileDir, FtpBaseInfo ftpBaseInfo) {
        InputStream deptInput = new ByteArrayInputStream(departmentString.getBytes());
        String deptFileName = String.format(deptFile, System.currentTimeMillis());
        try {
            ftpHelper.uploadToFtp(ftpBaseInfo, departmentsFileDir, deptFileName, deptInput);
            log.info("上传部门文件到FTP成功 fileDir {} fileName {}", departmentsFileDir, deptFileName);
        } catch (Exception e) {
            log.error("上传部门文件到FTP异常 fileDir {} fileName {}", departmentsFileDir, deptFileName, e);
        }
    }

    // 抽取上传用户数据的方法
    private void uploadUserData(String userString, String usersFileDir, FtpBaseInfo ftpBaseInfo) {
        InputStream userInput = new ByteArrayInputStream(userString.getBytes());
        String userFileName = String.format(userFile, System.currentTimeMillis());
        try {
            ftpHelper.uploadToFtp(ftpBaseInfo, usersFileDir, userFileName, userInput);
            log.info("上传用户文件到FTP成功 fileDir {} fileName {}", usersFileDir, userFileName);
        } catch (Exception e) {
            log.error("上传用户文件到FTP异常 fileDir {} fileName {}", usersFileDir, userFileName, e);
        }
    }

    private void deleteFileFromFtp(int holdLimit,JSONObject ftpValueBox) {

        log.info("开始从ftp host {} 删除文件", ftpValueBox.getString("ftpBaseDir"));
        FtpBaseInfo ftpBaseInfo = FtpBaseInfo.builder().ip(ftpValueBox.getString("ftpIp"))
                .port(ftpValueBox.getInteger("ftpPort"))
                .username(ftpValueBox.getString("ftpUserName"))
                .password(ftpValueBox.getString("ftpPassword"))
                .build();

        String userFileDir = String.format(userFileDirTmp, ftpValueBox.getString("ftpBaseDir"));
        String departmentFileDir = String.format(deptFileDirTmp, ftpValueBox.getString("ftpBaseDir"));
        try {
            ftpHelper.deleteFromFtp(ftpBaseInfo, userFileDir, holdLimit);
            ftpHelper.deleteFromFtp(ftpBaseInfo, departmentFileDir, holdLimit);
        } catch (Exception e) {
            log.error("从ftp 删除文件异常", e);
        }

    }

    /**
     * 获取文件内容
     * @param fileType users, departments
     * @return
     */
    public String getOneFileResult(String fileType,FTPValueBox ftpValueBox) {

        FtpBaseInfo ftpBaseInfo = FtpBaseInfo.builder().ip(ftpValueBox.getFtpIp())
                .port(ftpValueBox.getFtpPort())
                .username(ftpValueBox.getFtpUserName())
                .password(ftpValueBox.getFtpPassword())
                .build();

        if (fileType.equals("users")) {
            String userFileDir = String.format(userFileDirTmp, ftpValueBox.getFtpBaseDir());
            try {
                List<FtpFilesInfo> ftpFilesInfos = ftpHelper.getFileListFromFtp(ftpBaseInfo, userFileDir);
                ftpFilesInfos.sort(Comparator.comparing(FtpFilesInfo::getFileName).reversed());
                // 取最新一个文件
                FtpFilesInfo ftpFilesInfo = ftpFilesInfos.get(0);
                log.info("获取最新user文件 fileInfo {}", ftpFilesInfo.getFileName());

                // 数据解密
                return Base64Utils.decode(inputStreamToString(ftpHelper.getFileInfoFromFtp(ftpBaseInfo, userFileDir, ftpFilesInfo.getFileName())));
            } catch (Exception e) {
                log.error("从FTP 获取文件列表异常 userFileDit {}", userFileDir, e);
            }

        }

        if (fileType.equals("departments")) {
            String departmentFileDir = String.format(deptFileDirTmp, ftpValueBox.getFtpBaseDir());
            try {
                List<FtpFilesInfo> ftpFilesInfos = ftpHelper.getFileListFromFtp(ftpBaseInfo, departmentFileDir);
                ftpFilesInfos.sort(Comparator.comparing(FtpFilesInfo::getFileName).reversed());
                // 取最新一个文件
                FtpFilesInfo ftpFilesInfo = ftpFilesInfos.get(0);
                log.info("获取最新department文件 fileInfo {}", ftpFilesInfo.getFileName());
                // 数据解密
                return Base64Utils.decode(inputStreamToString(ftpHelper.getFileInfoFromFtp(ftpBaseInfo, departmentFileDir, ftpFilesInfo.getFileName())));
            } catch (Exception e) {
                log.error("从FTP 获取文件列表异常 departmentFileDit {}", departmentFileDir, e);
            }
        }

        return null;
    }


    private String inputStreamToString(InputStream in) throws IOException {
        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = in.read(buffer)) != -1) {
            result.write(buffer, 0, length);
        }
        return result.toString("UTF-8");
    }

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        return null;
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.FTP.getDesc();
    }
}
