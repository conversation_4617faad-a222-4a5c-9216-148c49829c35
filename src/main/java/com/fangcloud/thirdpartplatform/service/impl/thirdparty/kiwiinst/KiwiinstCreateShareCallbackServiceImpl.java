package com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.GroupMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformEnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.Group;
import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.CreateShareWorkflowMain;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.CreateWorkflowDt1;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.v2.CreateShareBean;
import com.fangcloud.thirdpartplatform.entity.response.kiwiinst.CreateShareCallbackResponse;
import com.fangcloud.thirdpartplatform.helper.ChameleonClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst.KiwiinstCreateShareCallServiceImpl.NEED_BUILD_DT1;
import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst.KiwiinstInviteCollabCallbackServiceImpl.BM_TYPE;
import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst.KiwiinstInviteCollabCallbackServiceImpl.QZ_TYPE;

@Service
@Slf4j
public class KiwiinstCreateShareCallbackServiceImpl implements ThirdpartyExecuteService {

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Resource
    private ChameleonClientHelper chameleonClientHelper;

    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {


        // 请求v2，创建分享
        CreateShareCallbackResponse createShareCallbackResponse = sendCreateShare(thirdpartyParams);
        return ResultUtils.getSuccessResult(createShareCallbackResponse);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {
        return null;
    }

    /**
     * 请求v2创建分享
     * @param thirdpartyParams
     * @return
     */
    private CreateShareCallbackResponse sendCreateShare(ThirdpartyParams thirdpartyParams) {


        JSONObject jsonObject = (JSONObject)  JSONObject.parse(JSON.toJSONString(thirdpartyParams.getValueBox()));

        CreateShareWorkflowMain createShareWorkflowMain = JSON.parseObject(JSON.toJSONString(jsonObject.getJSONObject("main")), CreateShareWorkflowMain.class);
        List createWorkflowDt1List = JSON.parseObject(JSON.toJSONString(jsonObject.getJSONArray("dt1")), List.class);


        // 获取协作文件夹的所有者用户id
        String enterpriseId = createShareWorkflowMain.getEnterpriseId().getValue();
        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(Long.parseLong(enterpriseId));
        if(Objects.isNull(platformEnterprises)){
            throw new ParamException("platformEnterprises is null !");
        }
        int platformId = platformEnterprises.getPlatformId();
        PlatformUser createSharePlatformUser = platformUserMapper.queryByPlatformIdUserTicket(platformId, createShareWorkflowMain.getSqr().getValue());
        if(Objects.isNull(createSharePlatformUser)){
            throw new ParamException("createSharePlatformUser is null!");
        }

        CreateShareBean createShareBean = buildCreateShareBean(createShareWorkflowMain, createWorkflowDt1List, platformId);

        // 创建协作
        String result = v2ClientHelper.createShare(createShareBean, createSharePlatformUser.getUserId());

        CreateShareCallbackResponse createShareCallbackResponse = buildCreateShareCallbackResponse(result, platformId, createShareBean);

        return createShareCallbackResponse;
    }

    /**
     * 构建oa分享回调返回值
     * @param result
     * @param platformId
     * @param createShareBean
     * @return
     */
    private CreateShareCallbackResponse buildCreateShareCallbackResponse(String result, int platformId, CreateShareBean createShareBean) {
        boolean success = (boolean) JSONPath.extract(result, "$.success");
        if(!success){
            throw new ParamException("create share  error!");
        }



        CreateShareCallbackResponse createShareCallbackResponse = new CreateShareCallbackResponse();

        String password = "";
        String shareLink = "";
        if(createShareBean.getPasswordProtected()){
            password = createShareBean.getPassword();
        }
        if(!NEED_BUILD_DT1.contains(createShareBean.getAccess())){
            String baseUrl = chameleonClientHelper.getBaseUrl(platformId);
            String uniqueName = (String) JSONPath.extract(result, "$.process.share_link.unique_name");
            shareLink = baseUrl + "share/" + uniqueName;
        }

        createShareCallbackResponse.setPassword(password);
        createShareCallbackResponse.setShareLink(shareLink);
        return  createShareCallbackResponse;
    }

    /**
     * 构建创建分享参数
     * @param createShareWorkflowMain
     * @param createWorkflowDt1List
     * @param platformId
     * @param createWorkflowDt1List
     * @return
     */
    private CreateShareBean buildCreateShareBean(CreateShareWorkflowMain createShareWorkflowMain, List createWorkflowDt1List, int platformId) {

        CreateShareBean createShareBean = new CreateShareBean();
        String fxlx = createShareWorkflowMain.getFxlx().getValue();
        createShareBean.setAccess(fxlx);
        createShareBean.setCurrentVersion(Boolean.parseBoolean(createShareWorkflowMain.getCurrentVersion().getValue()));
        createShareBean.setDisableDownload(Boolean.parseBoolean(createShareWorkflowMain.getDisableDownload().getValue()));
        createShareBean.setDueTime(Long.parseLong(createShareWorkflowMain.getDueTime().getValue()));
        createShareBean.setPasswordProtected(Boolean.parseBoolean(createShareWorkflowMain.getPasswordProtected().getValue()));
        createShareBean.setPassword(createShareWorkflowMain.getPassword().getValue());




        String value = createShareWorkflowMain.getDescription().getValue();
        if(StringUtils.isNotEmpty(value)){
            String description = Base64Utils.decodeGbk(value);
            createShareBean.setDescription(description);
        }else {
            createShareBean.setDescription("");
        }
        createShareBean.setEnableShareWpsEdit(Boolean.parseBoolean(createShareWorkflowMain.getIsShareWpsEdit().getValue()));
        createShareBean.setItemTypedId(createShareWorkflowMain.getDataType().getValue() + "_" + createShareWorkflowMain.getDataId().getValue());
        if(!NEED_BUILD_DT1.contains(fxlx)){
            createShareBean.setEnablePreviewLimit(Boolean.parseBoolean(createShareWorkflowMain.getIsPreviewLimit().getValue()));
            createShareBean.setEnableDownloadLimitV2(Boolean.parseBoolean(createShareWorkflowMain.getIsDownloadLimitV2().getValue()));
            createShareBean.setPreviewLimit(Long.parseLong(createShareWorkflowMain.getPreviewLimit().getValue()));
            createShareBean.setDownloadLimitV2(Long.parseLong(createShareWorkflowMain.getDownloadLimitV2().getValue()));
            String watermarkTemplateInfo = createShareWorkflowMain.getWatermarkTemplateInfo().getValue();
            JSONObject jsonObject = JSONObject.parseObject(watermarkTemplateInfo);
            String customTextBase64 = jsonObject.getString("custom_text");
            String customText = Base64Utils.decodeGbk(customTextBase64);
            jsonObject.put("custom_text",customText);
            createShareBean.setWatermarkTemplateInfo(jsonObject);
        }
        createShareBean.setFromBehaviorReview(true);

        List<Long> userList = new ArrayList<>();
        List<Long> groupList = new ArrayList<>();
        if(NEED_BUILD_DT1.contains(fxlx)){
            for (Object o : createWorkflowDt1List) {
                CreateWorkflowDt1 createWorkflowDt1 = JSON.parseObject(JSON.toJSONString(o), CreateWorkflowDt1.class);
                String bdlx = Base64Utils.decodeGbk(createWorkflowDt1.getBdlx().getValue());
                log.info("buildCreateShareBean bdlx:{}", bdlx);

                if(BM_TYPE.equals(bdlx) || QZ_TYPE.equals(bdlx)){ // 群组类型或者部门类型
                    Group group = groupMapper.queryById(Long.valueOf(createWorkflowDt1.getQz().getValue()));
                    if(Objects.isNull(group)){
                        throw new ParamException("group is null!");
                    }
                    groupList.add(group.getId());
                }else{ // 人力资源类型
                    String bdr = createWorkflowDt1.getBdr().getValue();
                    PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserTicket(platformId, bdr);
                    if(Objects.isNull(platformUser)){
                        throw new ParamException("platformDepartment is null!");
                    }
                    userList.add(platformUser.getUserId());
                }
            }
            createShareBean.setGroupIds(groupList);
            createShareBean.setInvitedUserIds(userList);
        }


        return createShareBean;
    }


    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallbackTypeEnum.KIWIINST_CREATE_SHARE_CALLBACK.getDesc();
    }
}
