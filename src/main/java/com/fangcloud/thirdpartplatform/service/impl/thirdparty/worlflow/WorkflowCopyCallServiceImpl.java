package com.fangcloud.thirdpartplatform.service.impl.thirdparty.worlflow;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.DepartmentMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformEnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.Department;
import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.db.model.User;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.CopyInfo;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdPartyCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.CopyWorkflow;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.CopyWorkflowCall;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.ark.WorkflowApprovalBean;
import com.fangcloud.thirdpartplatform.helper.ArkClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyCommonService;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class WorkflowCopyCallServiceImpl implements ThirdpartyExecuteService {

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private ArkClientHelper arkClientHelper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private ThirdpartyCommonService thirdpartyCommonService;


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        CopyWorkflow copyWorkflow = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getData()), CopyWorkflow.class);

        CopyWorkflowCall copyWorkflowCall = copyWorkflowConvertToCopyWorkflowCall(copyWorkflow);

        ThirdPartyCall thirdPartyCall = ThirdPartyCall.builder()
                .type(ThirdpartyCallbackTypeEnum.COPY_CALLBACK.getDesc())
                .data(copyWorkflowCall)
                .build();

        return ResultUtils.getSuccessResult(thirdPartyCall);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {

        CopyWorkflowCall copyWorkflowCall = JSON.parseObject(JSON.toJSONString(thirdpartyCallbackParams.getData()), CopyWorkflowCall.class);

        // 校验回调记录
        thirdpartyCommonService.checkCallbackResult(
                copyWorkflowCall.getUniqueId(),
                copyWorkflowCall.getEnterpriseId(),
                copyWorkflowCall.getReviewType(),
                thirdpartyCallbackParams.isReviewResult());

        WorkflowApprovalBean workflowApprovalBean = new WorkflowApprovalBean();
        workflowApprovalBean.setPass(thirdpartyCallbackParams.isReviewResult());
        workflowApprovalBean.setTaskId(Long.parseLong(copyWorkflowCall.getTaskId()));

        boolean workflowApprovalResult = arkClientHelper.workflowApproval(workflowApprovalBean, copyWorkflowCall.getReceiverId());

        return ResultUtils.getSuccessResult(workflowApprovalResult);
    }

    /**
     * 转化对应字段
     * @param copyWorkflow
     * @return
     */
    private CopyWorkflowCall copyWorkflowConvertToCopyWorkflowCall(CopyWorkflow copyWorkflow) {
        CopyWorkflowCall copyWorkflowCall = new CopyWorkflowCall();

        Long enterpriseId = copyWorkflow.getEnterpriseId();
        copyWorkflowCall.setEnterpriseId(enterpriseId);
        copyWorkflowCall.setReceiverId(copyWorkflow.getReceiverId());
        copyWorkflowCall.setTaskId(copyWorkflow.getTaskId());
        copyWorkflowCall.setUniqueId(copyWorkflow.getUniqueId());
        copyWorkflowCall.setTargetFolderId(copyWorkflow.getTargetFolderId());
        if(0 == copyWorkflow.getTargetFolderId()){
            Department department = departmentMapper.queryById(copyWorkflow.getDepartmentId());
            if(!Objects.isNull(department)){
                copyWorkflowCall.setTargetFolderName(department.getName());
            }
        }else {
            copyWorkflowCall.setTargetFolderName(copyWorkflow.getTargetFolderName());
        }

        List<CopyWorkflowCall.Item> itemList = new ArrayList<>();
        List<CopyInfo.Item> items = copyWorkflow.getItems();
        for (CopyInfo.Item item : items) {
            CopyWorkflowCall.Item itemNew = new CopyWorkflowCall.Item();
            itemNew.setItemName(item.getItemName());
            itemNew.setItemId(item.getItemId());
            itemNew.setItemType(item.getItemType());
            itemList.add(itemNew);
        }
        copyWorkflowCall.setItemList(itemList);

        Long authorId = copyWorkflow.getAuthorId();
        ThirdpartyCustom thirdpartyCustom = new ThirdpartyCustom();
        thirdpartyCustom.setId(authorId);

        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);
        if(!Objects.isNull(platformEnterprises)){
            PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserId(platformEnterprises.getPlatformId(), authorId);
            if(!Objects.isNull(platformUser)){
                thirdpartyCustom.setCustomId(platformUser.getUserTicket());
            }
        }
        User authorUser = userMapper.queryById(authorId);
        if(!Objects.isNull(authorUser)){
            thirdpartyCustom.setName(authorUser.getFullName());
        }
        copyWorkflowCall.setCreateUser(thirdpartyCustom);
        copyWorkflowCall.setReviewType(ThirdpartyCallTypeEnum.WOEKFLOW_COPY_CALL.getDesc());
        return copyWorkflowCall;
    }

    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.WOEKFLOW_COPY_CALL.getDesc();
    }
}
