package com.fangcloud.thirdpartplatform.service.impl;


import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.User;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.AliFaruiService;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.net.URLDecoder;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


@Component
@Slf4j
public class AliFaruiServiceImpl implements AliFaruiService {
    
    private String ALIFARUI_APP_ID = "d80bf3df";
    private String ALIFARUI_APP_KEY = "a50b9d7866f5edc0";
    private String ALIFARUI_APP_SECRET = "8c8f6ee22461e505";
    private String ALIFAUI_UIL = "https://tyfarui.biz.aliyun.com";
    private String V2_APP_CLIENT_ID = "alifarui-20241212-00001";
    private String V2_APP_CLIENT_SECRET = "WfeacBn2OZ6t2d97Pr75RKpZoAzct9hY";
    
    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private UserMapper userMapper;

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Override
    public String getToken(HttpServletRequest request, HttpServletResponse response) {
        Map<String, String> userInfo = userInfo(request);
        if (userInfo.isEmpty()) {
            log.error("User information is empty");
            return "";
        }
        String userId = userInfo.get("userId");
        String enterpriseId = userInfo.get("enterpriseId");
        String token = getToken(userId, enterpriseId);
        return token;
    }


    @Override
    public String review(HttpServletRequest request, HttpServletResponse response) {

        Map<String, String> userInfo = userInfo(request);
        if (userInfo.isEmpty()) {
            log.error("User information is empty");
            return "";
        }
        String userId = userInfo.get("userId");
        String enterpriseId = userInfo.get("enterpriseId");
        String fileId = request.getParameter("fileId");
        String documentUrl = getDocumentUrl(userInfo.get("userId"), enterpriseId, fileId);
        String token = getToken(userId, enterpriseId);

        if (documentUrl == "") {
            return "";
        }
        return documentUrl + "&token=" + token + "&sideFlagHide=true&breadFlagHide=true&ruleHideFlag=true&logoHideFlag=true";
    }

    @Override
    public String homepage(HttpServletRequest request, HttpServletResponse response) {
        String url = "https://tongyi.aliyun.com/farui/fangcloudReview";
        Map<String, String> userInfo = userInfo(request);
        if (userInfo.isEmpty()) {
            log.error("User information is empty");
            return "";
        }
        String userId = userInfo.get("userId");
        String enterpriseId = userInfo.get("enterpriseId");
        String token = getToken(userId, enterpriseId);
        return url + "?token=" + token  + "&sideFlagHide=true&breadFlagHide=true&ruleHideFlag=true&logoHideFlag=true";
    }


    @Override
    public String reviewWithLocalFile(HttpServletRequest request, HttpServletResponse response, String path, String fileName) {
        Map<String, String> userInfo = userInfo(request);
        if (userInfo.isEmpty()) {
            log.error("User information is empty");
            return "";
        }
        String userId = userInfo.get("userId");
        String enterpriseId = userInfo.get("enterpriseId");
       
        String documentUrl = getDocumentUrlByLocalFile(userInfo.get("userId"), enterpriseId, path, fileName);
        String token = getToken(userId, enterpriseId);

        if (documentUrl == "") {
            return "";
        }
        return documentUrl + "&token=" + token + "&sideFlagHide=true&breadFlagHide=true&ruleHideFlag=true&logoHideFlag=true";
    }


    private Map<String, String> userInfo(HttpServletRequest request) {
        String userId = request.getHeader(CommonConstants.HEADER_USER_ID);
        String userToken = request.getParameter(CommonConstants.PARAM_USER_TOKEN);
        Map<String, String> userMap = new HashMap<>();
        if (userId != null && userId.length() > 0) {
            // 用户id 进行一次查询获取企业id
            User user = userMapper.queryById(userId);
            if (user != null) {
                userMap.put("userId", userId);
                userMap.put("enterpriseId", String.valueOf(user.getEnterpriseId()));
            } else {
                log.error("userId {} usrinfo is null", userId);
            }
        }

        if (userToken != null && userToken.length() > 0) {
            // 调用v2接口获取用户信息
            String clientIdSecret = V2_APP_CLIENT_ID + ":" + V2_APP_CLIENT_SECRET;
            String yfySource = Base64.encode(clientIdSecret);
            String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl19() + userToken;
            HttpResponse response = HttpUtil.createGet(url)
                .header("Yfy-Source", yfySource)
                .execute();
            JSONObject json = JSONUtil.parseObj(response.body());
            String uid = json.getByPath(".data.user_info.id").toString();
            String enterpriseId = json.getByPath(".data.user_info.enterprise_id").toString();
            userMap.put("userId", uid);
            userMap.put("enterpriseId", enterpriseId);

        }

        return userMap;
    }

    /**
     * 文件地址上传
     * @param userId
     * @param enterpriseId
     * @param fileId
     * @return
     */
    private String getDocumentUrl(String userId, String enterpriseId, String fileId) {
        String url = "/open/contract/v1/createReviewRecord";
        JSONObject data = getBasePostBody(userId, enterpriseId);
        Map<String, String> fileInfo = getFileInfo(fileId, userId);
        String fileUrl = fileInfo.get("fileUrl");
        String fileName = fileInfo.get("fileName");
        data.set("fileUrl", fileUrl);
        data.set("fileName", fileName);
        data.set("standpoint", "0");
        
        if (fileUrl == null ) {
            log.error("云盘文件获取失败 userId {}, enterpriseId {}, fileId {}", userId, enterpriseId, fileId);
            return "";
        }
        String result = getResponse(url, data.toString());
        return JSONUtil.parseObj(result).getByPath("data.webUrl", String.class);
    }

    /**
     * 本地上传
     * @param userId
     * @param enterpriseId
     * @param fileId
     * @return
     */

    private String getDocumentUrlByLocalFile(String userId, String enterpriseId, String path, String fileName) {
        String url = "/open/contract/v1/createReviewRecord";
        File file = FileUtil.file(path);
        if (!file.exists()) {
            log.error("本地文件不存在 path {}", path);
            return "";
        }
        JSONObject data = getBasePostBody(userId, enterpriseId);
        Map<String, Object> dataMap = new HashMap<>();
        for (String key : data.keySet()) {
            dataMap.put(key, data.get(key));
        }
        
        dataMap.put("file", file);
        dataMap.put("fileName", fileName);
        dataMap.put("standpoint", "0");
        String result = getResponse(url, dataMap);
        return JSONUtil.parseObj(result).getByPath("data.webUrl", String.class);
    }

    private  Map<String, String> getFileInfo(String fileId, String userId) {
        // 文件预签名获取下载url
        Map<String, String> fileInfo = new HashMap<>();
        String result = v2ClientHelper.downloadFile(fileId, userId);
        
        JSONObject jsonResult = JSONUtil.parseObj(result);
        String downloadUrl = jsonResult.getByPath(".download_url", String.class);
        try {
            String fileName = java.net.URLDecoder.decode(downloadUrl.substring(downloadUrl.lastIndexOf('/') + 1), "UTF-8");
            fileInfo.put("fileName", fileName);
        }catch (Exception e) {
            fileInfo.put("fileName", "fileName");
        }
        fileInfo.put("fileUrl", downloadUrl);

        
        return fileInfo;
    }

    private String getToken(String userId, String enterpriseId) {
        String url = "/open/thirdLogin/getLoginTempCode";
        JSONObject data = getBasePostBody(userId, enterpriseId);
        data.set("thirdAppId", ALIFARUI_APP_ID);
        data.set("thirdCheckCode", "0");

        String resutl = getResponse(url, data.toString());
        return JSONUtil.parseObj(resutl).getByPath(".data").toString();
    }

    private JSONObject getBasePostBody(String userId, String enterpriseId) {
        String randomValue = UUID.randomUUID().toString();
        long timestamp = System.currentTimeMillis();
        String text = ALIFARUI_APP_ID + ALIFARUI_APP_KEY + timestamp + randomValue + ALIFARUI_APP_SECRET;
        JSONObject jsonObject = JSONUtil.createObj();
        jsonObject.set("appId", ALIFARUI_APP_ID);
        jsonObject.set("appKey", ALIFARUI_APP_KEY);
        jsonObject.set("signature", calculateMD5(text));
        jsonObject.set("timestamp", timestamp);
        jsonObject.set("random", randomValue);
        jsonObject.set("thirdUserId", userId);
        jsonObject.set("thirdTenantId", enterpriseId);

        return jsonObject;
    }

    private String getResponse(String url, String postData) {
        log.info("url {}, postData {}", ALIFAUI_UIL + url, postData);
        HttpRequest post = HttpRequest.post(ALIFAUI_UIL + url)
            .header("Content-Type", "application/json")
            .body(postData);
        HttpResponse response = post.execute();
        String result = response.body().toString();
        log.info("farui url {} result {}", url, result);
        return result;
    }

    private String getResponse(String url, Map<String, Object> params) {
        log.info("url {}, form-data {}", ALIFAUI_UIL + url, params);
        HttpResponse response = HttpUtil.createPost(ALIFAUI_UIL + url)
                                                    .form(params)
                                                    .execute();
        String result = response.body().toString();
        log.info("farui url {} result {}", ALIFAUI_UIL + url, result);
        return result;
    }

    private String calculateMD5(String input) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(input.getBytes());
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(Integer.toHexString((b & 0xFF) | 0x100).substring(1, 3));
            }
            return sb.toString();
        } catch (java.security.NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return null;
}


}
