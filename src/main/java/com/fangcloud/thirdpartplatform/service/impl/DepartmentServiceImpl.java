package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.db.dao.DepartmentMapper;
import com.fangcloud.thirdpartplatform.db.dao.EnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformDepartmentMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.sync.CustomDepartment;
import com.fangcloud.thirdpartplatform.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DepartmentServiceImpl implements DepartmentService {

    final static int DEPARTMENT_PAGE_CAPACITY = 500;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Autowired
    private EnterpriseMapper enterpriseMapper;

    public List<CustomDepartment> findAll(int platformId) {

        List<PlatformDepartment> platformDepartmentList = getPlatformDepartment(platformId);

        List<CustomDepartment> customDepartmentList = getCustomDepartment(platformDepartmentList);
        log.info("customDepartmentList info {}", JSON.toJSONString(customDepartmentList));

        return customDepartmentList;
    }

    /**
     * 获取部门信息
     * @param platformId
     * @return
     */
    public List<PlatformDepartment> getPlatformDepartment(int platformId) {
        Map<String, Integer> paramMap = new HashMap<>();
        paramMap.put("platformId", platformId);
        paramMap.put("pageSize", DEPARTMENT_PAGE_CAPACITY);

        int totalCount = platformDepartmentMapper.queryCountByPlatformId(platformId);
        // 部门列表
        List<PlatformDepartment> platformDepartmentList = new ArrayList<>();
        int pageId = 0;

        // 循环分页查询部门信息
        while (platformDepartmentList.size() < totalCount){
            paramMap.put("start", pageId * DEPARTMENT_PAGE_CAPACITY);
            List<PlatformDepartment> platformDepartments = platformDepartmentMapper.queryByPlatformIdWithPage(paramMap);
            platformDepartmentList.addAll(platformDepartments);

            ++pageId;
        }
        return platformDepartmentList;
    }


    /**
     * 拼装部门列表
     * @param platformDepartments
     * @return
     */
    private List<CustomDepartment> getCustomDepartment(List<PlatformDepartment> platformDepartments) {
        // 组装返回的departments
        List<CustomDepartment> customDepartments = new ArrayList<>();


        if (CollectionUtils.isEmpty(platformDepartments)) {
            return customDepartments;
        }
        long platformId = platformDepartments.get(0).getPlatformId();

        Map<Long, PlatformDepartment> platformDepartmentMap = platformDepartments.stream().
                collect(Collectors.toMap(PlatformDepartment::getYfyDepartmentId, platformDepartment -> platformDepartment));
        // 获取当前部门的的departments
        List<Department> departments = departmentMapper.queryByIds(platformDepartmentMap.keySet().stream().collect(Collectors.toList()));
        Map<Long, Department> departmentMap = departments.stream().collect(Collectors.toMap(Department::getId, department -> department));

        // 获取上级部门的id
        List<Long> departmentParentIds = departments.stream().map(Department::getParentId).collect(Collectors.toList());
        // 获取部门负责人id
        List<Long> departmentDirectorIds = departments.stream().map(Department::getDirectorId).collect(Collectors.toList());

        // 去Platform_departments 表查客户的部门id
        List<PlatformDepartment> platformDepartmentParentList = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentIds(platformId, departmentParentIds);
        Map<Long, PlatformDepartment> platformDepartmentParentMap = platformDepartmentParentList.stream().
                collect(Collectors.toMap(PlatformDepartment::getYfyDepartmentId, platformDepartment -> platformDepartment));

        // 去platform_users 表查客户信息
        List<PlatformUser> platformUsers = platformUserMapper.queryByPlatformIdUserIds(platformId, departmentDirectorIds);
        Map<Long, PlatformUser> platformUserMap = platformUsers.stream().filter(s -> StringUtils.isNumeric(s.getUserTicket()))
                .collect(Collectors.toMap(PlatformUser::getUserId, platformUser -> platformUser));

        platformDepartments.forEach(platformDepartment -> {
            if (departmentMap.get(platformDepartment.getYfyDepartmentId()) == null) {
                return;
            }
            CustomDepartment customDepartment = new CustomDepartment();
            Long parentId = departmentMap.get(platformDepartment.getYfyDepartmentId()).getParentId();
            Long directorId = departmentMap.get(platformDepartment.getYfyDepartmentId()).getDirectorId();
            String customDirectorId = platformUserMap.get(directorId) == null ? null : platformUserMap.get(directorId).getUserTicket();
            String customParentId = platformDepartmentParentMap.get(parentId) == null ? null : platformDepartmentParentMap.get(parentId).getDepartmentId();

            customDepartment.setId(platformDepartment.getYfyDepartmentId());
            customDepartment.setName(departmentMap.get(platformDepartment.getYfyDepartmentId()).getName());
            customDepartment.setTotalSpace(departmentMap.get(platformDepartment.getYfyDepartmentId()).getSpaceTotal());
            customDepartment.setParentId(parentId);
            customDepartment.setDirectorId(directorId);
            customDepartment.setOrder(departmentMap.get(platformDepartment.getYfyDepartmentId()).getOrder());
            customDepartment.setCustomId(platformDepartment.getDepartmentId());
            customDepartment.setCustomDirectorId(customDirectorId);
            customDepartment.setCustomParentId(customParentId);

            customDepartments.add(customDepartment);
        });

        return customDepartments;
    }
}
