package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 广州印钞免密登录
 */
@Component
@Slf4j
public class GzycServiceImpl {

    @Resource
    private OpenClientHelper openClientHelper;

    private static final String EMAIL = "@gzyc.cbpm";

    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        String userId = request.getParameter("userId");
        String decode = Base64Utils.decode(userId);
        String loginName = encryptAndDencrypt(decode, '8');
        log.info("gzyc userId {} , loginName {}", userId, loginName);
        try {
            if (loginName != null) {
                EnterpriseParams enterpriseParams = new EnterpriseParams();
                enterpriseParams.setEnterpriseId(configInfo.getEnterpriseId());
                enterpriseParams.setIdentifier(loginName + EMAIL);
                enterpriseParams.setPlatformId(configInfo.getPlatformId());
                enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
                enterpriseParams.setClientId(configInfo.getClientId());
                String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
                log.info("gzyc getLoginUrl {}", loginUrl);
                return loginUrl;
            }
        } catch (Exception e) {
            log.error("gzyc 异常", e);
        }
        return null;
    }

    public static String encryptAndDencrypt(String value, char secret) {
        byte[] bt = value.getBytes();
        for (int i = 0; i < bt.length; i++) {
            bt[i] = (byte) (bt[i] ^ (int) secret);
        }
        String newresult = new String(bt, 0, bt.length);
        return newresult;
    }
}
