package com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.*;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.v2.CreateCommentBean;
import com.fangcloud.thirdpartplatform.helper.DateHelper;
import com.fangcloud.thirdpartplatform.helper.KiwiinstClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst.KiwiinstInviteCollabCallbackServiceImpl.*;

@Service
@Slf4j
public class KiwiinstEditCollabCallServiceImpl implements ThirdpartyExecuteService {

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private GroupsUsersMapper groupsUsersMapper;

    @Autowired
    private KiwiinstClientHelper kiwiinstClientHelper;

    @Resource
    private V2ClientHelper v2ClientHelper;


    static final String XGXZSP_WORK_FLOW_FLAG = "XGXZSP";

    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        KiwiinstEditCollabCallValueBox kiwiinstEditCollabCallValueBox = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getValueBox()), KiwiinstEditCollabCallValueBox.class);

        long editUserId = kiwiinstEditCollabCallValueBox.getEditUserId();
        User user = userMapper.queryById(editUserId);

        if(Objects.isNull(user)){
            throw new ParamException("editUser is null !");
        }
        long enterpriseId = user.getEnterpriseId();
        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);

        if(Objects.isNull(platformEnterprises)){
            throw new ParamException("platformEnterprises is null !");
        }

        int platformId = platformEnterprises.getPlatformId();
        PlatformUser editPlatformUser = platformUserMapper.queryByPlatformIdUserId(platformId, editUserId);
        if(Objects.isNull(editPlatformUser)){
            throw new ParamException("editPlatformUser is null!");
        }

        CreateWorkflow createWorkflow = new CreateWorkflow();

        createWorkflow.setBase(buildCreateWorkflowBase(editPlatformUser));
        createWorkflow.setMain(buildCreateWorkflowMain(kiwiinstEditCollabCallValueBox, editPlatformUser, enterpriseId));
        createWorkflow.setDt1(buildCreateWorkflowDt1(kiwiinstEditCollabCallValueBox, platformId));
        List<CreateWorkflow> list = new ArrayList<>();
        list.add(createWorkflow);

        // 调用创建流程接口
        String result = kiwiinstClientHelper.createWorkflow(JSON.toJSONString(list), enterpriseId);

        String status = (String) JSONPath.extract(result, "$.resultlist[0].status");
        if(!"0".equals(status)){
            throw new ParamException("createWorkflow error!");
        }

        try {
            String msg = (String) JSONPath.extract(result, "$.resultlist[0].msg");
            if(StringUtils.isNotEmpty(msg)){
                // 新增评论
                CreateCommentBean createCommentBean = new CreateCommentBean("folder", msg, kiwiinstEditCollabCallValueBox.getFolderId());
                v2ClientHelper.createComment(createCommentBean, kiwiinstEditCollabCallValueBox.getFolderOwnerId());
            }
        }catch (Exception e){
            log.info("createWorkflow create comment error !");
        }
        return  ResultUtils.getSuccessResult(true);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {
        return null;
    }

    private List<CreateWorkflowDt1> buildCreateWorkflowDt1(KiwiinstEditCollabCallValueBox kiwiinstEditCollabCallValueBox, long platformId) {
        List<CreateWorkflowDt1> dt1List = new ArrayList<>();
        CreateWorkflowDt1 createWorkflowDt1 = new CreateWorkflowDt1();
        String dataType = kiwiinstEditCollabCallValueBox.getDataType();
        createWorkflowDt1.setXzlx(new CreateWorkflowValue(kiwiinstEditCollabCallValueBox.getRole()));
        createWorkflowDt1.setYsxzlx(new CreateWorkflowValue(kiwiinstEditCollabCallValueBox.getOriginalRole()));
        if("user".equals(dataType)){ //类型为用户
            long userId = kiwiinstEditCollabCallValueBox.getDataId();
            PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserId(platformId, userId);
            if(Objects.isNull(platformUser)){
                throw new ParamException("platformUser is null!");
            }
            createWorkflowDt1.setBdr(new CreateWorkflowValue(platformUser.getUserTicket()));
            createWorkflowDt1.setBdlx(new CreateWorkflowValue(BDR_TYPE));
        }else { //类型为群组
            long groupId = kiwiinstEditCollabCallValueBox.getDataId();

            Group group = groupMapper.queryById(groupId);
            if(Objects.isNull(group)){
                throw new ParamException("group is null !");
            }

            if(group.getDepartmentId() > 0){ // 群组有对应的部门
                PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentId(platformId, group.getDepartmentId());
                if(Objects.isNull(platformDepartment)){
                    throw new ParamException("platformDepartment is null !");
                }
                createWorkflowDt1.setBm(new CreateWorkflowValue(platformDepartment.getDepartmentId()));
                createWorkflowDt1.setBdlx(new CreateWorkflowValue(BM_TYPE));
                createWorkflowDt1.setQz(new CreateWorkflowValue(String.valueOf(groupId)));

            }else { // 群组没有对应的部门
                List<GroupsUser> groupsUsers = groupsUsersMapper.queryByGroupId(groupId);
                List<Long> userIdList = new ArrayList<>();
                for (GroupsUser groupsUser : groupsUsers) {
                    userIdList.add(groupsUser.getUserId());
                }

                List<PlatformUser> platformUsers = platformUserMapper.queryByPlatformIdUserIds(platformId, userIdList);
                List<String> collect = platformUsers.stream().map(p -> p.getUserTicket()).collect(Collectors.toList());
                String join = String.join(",", collect);

                createWorkflowDt1.setBdr(new CreateWorkflowValue(join));
                createWorkflowDt1.setBdlx(new CreateWorkflowValue(QZ_TYPE));
                createWorkflowDt1.setQz(new CreateWorkflowValue(String.valueOf(groupId)));
            }
        }

        dt1List.add(createWorkflowDt1);
        return dt1List;
    }

    private CreateWorkflowMain buildCreateWorkflowMain(KiwiinstEditCollabCallValueBox kiwiinstEditCollabCallValueBox, PlatformUser editPlatformUser, long enterpriseId) {
        CreateCollabWorkflowMain createWorkflowMain = new CreateCollabWorkflowMain();
        createWorkflowMain.setSqr(new CreateWorkflowValue(editPlatformUser.getUserTicket()));
        createWorkflowMain.setStartdate(new CreateWorkflowValue(DateHelper.transformTimeStampToDate(System.currentTimeMillis()/1000)));
        createWorkflowMain.setFolderId(new CreateWorkflowValue(kiwiinstEditCollabCallValueBox.getFolderId()));
        createWorkflowMain.setFolderName(new CreateWorkflowValue(kiwiinstEditCollabCallValueBox.getFolderName()));
        createWorkflowMain.setCollabId(new CreateWorkflowValue(String.valueOf(kiwiinstEditCollabCallValueBox.getCollabId())));
        createWorkflowMain.setCurrentFolder(new CreateWorkflowValue(String.valueOf(kiwiinstEditCollabCallValueBox.isCurrentFolder())));

        PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentId(editPlatformUser.getPlatformId(), kiwiinstEditCollabCallValueBox.getFolderDepartmentId());
        if(Objects.isNull(platformDepartment)){
            throw new ParamException("folderDepartment is null!");
        }
        createWorkflowMain.setFolderDeptId(new CreateWorkflowValue(platformDepartment.getDepartmentId()));


        PlatformUser ownPlatformUser = platformUserMapper.queryByPlatformIdUserId(editPlatformUser.getPlatformId(), kiwiinstEditCollabCallValueBox.getFolderOwnerId());
        if(Objects.isNull(ownPlatformUser)){
            throw new ParamException("ownPlatformUser error!");
        }
        createWorkflowMain.setOwnerId(new CreateWorkflowValue(ownPlatformUser.getUserTicket()));
        createWorkflowMain.setEnterpriseId(new CreateWorkflowValue(String.valueOf(enterpriseId)));

        return createWorkflowMain;
    }

    private CreateWorkflowBase buildCreateWorkflowBase(PlatformUser editPlatformUser) {
        CreateWorkflowBase createWorkflowBase = new CreateWorkflowBase();
        createWorkflowBase.setCreator(new CreateWorkflowValue(editPlatformUser.getUserTicket()));
        createWorkflowBase.setFpkid(XGXZSP_WORK_FLOW_FLAG);
        createWorkflowBase.setWorkflowflag(XGXZSP_WORK_FLOW_FLAG);
        return createWorkflowBase;
    }


    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.KIWIINST_EDIT_COLLAB_CALL.getDesc();
    }
}
