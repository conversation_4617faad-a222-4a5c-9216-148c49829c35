package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.entity.input.*;
import com.fangcloud.thirdpartplatform.entity.output.CcWorkDepartment;
import com.fangcloud.thirdpartplatform.entity.output.CcWorkUser;
import com.fangcloud.thirdpartplatform.entity.output.QiyeWeixinDepartment;
import com.fangcloud.thirdpartplatform.entity.output.QiyeWeixinUser;
import com.fangcloud.thirdpartplatform.entity.sync.*;
import com.fangcloud.thirdpartplatform.helper.CcWorkClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.helper.WeixinWorkClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.SyncPublishService;
import com.fangcloud.thirdpartplatform.service.impl.custom.FeiShuServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.datasource.CcWorKSassSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.datasource.DingdingSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.datasource.PublicSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.datasource.QiyeWeixinSyncHandler;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.utils.SyncStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SyncPublishServiceImpl implements SyncPublishService {


    private static String TYPE_WEIXIN = "weixin";

    private static String TYPE_DING_TALK = "dingtalk";

    private static String TYPE_CCWORK = "ccwork";

    private static String TYPE_FEI_SHU = "feishu";

    private static String TYPE_CUSTOM = "custom";

    private static String DELETE_PUBLIC_USER_PREFIX = "DELETE_PUBLIC_USER_";
    private static String DELETE_PUBLIC_DEPARTMENT_PREFIX = "DELETE_PUBLIC_DEPARTMENT_";

    private static String SYNC_WEIXIN_PUBLIC_CLOUD_PREFIX = "SYNC_WEIXIN_PUBLIC_CLOUD_";
    private static String SYNC_DING_TALK_PUBLIC_CLOUD_PREFIX = "SYNC_DING_TALK_PUBLIC_CLOUD_";
    private static String SYNC_FEISHU_PUBLIC_CLOUD_PREFIX = "SYNC_FEISHU_PUBLIC_CLOUD_";

    private static String SYNC_CUSTOM_PUBLIC_CLOUD_PREFIX = "SYNC_CUSTOM_PUBLIC_CLOUD_";

    private static Long SYNC_WEIXIN_PUBLIC_CLOUD_TIME_OUT = Long.valueOf(60 * 30);


    private static Long DEPARTMENT_POSITION_STAFF = 0L;
    private static Long DEPARTMENT_POSITION_DIRECTOR = 2L;


    private static String WEIXIN_EXTATTR_PARAMETER_NAME = "name";
    private static String WEIXIN_EXTATTR_PARAMETER_VALUE = "value";
    private static String WEIXIN_PARAMETER_YFYPHONE = "yfyphone";

    private String DOING_STATUS = "doing";
    private String DONE_STATUS = "done";

    private static int WEIXIN_TOP_DEPARTMENT_ID = 1;
    private static int WEIXIN_TOP_DEPARTMENT_PARENT_ID = 0;


    private static Long USER_DEFAULT_SPACE_TOTAL = 5L;
    private static Long DEPARTMENT_DEFAULT_SPACE_TOTAL = 50L;


    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private RedisStringManager redisStringManager;


    @Resource
    private V2ClientHelper v2ClientHelper;

    @Resource
    private QiyeWeixinSyncHandler weixinSyncHandler;

    @Resource
    private CcWorKSassSyncHandler ccWorKSassSyncHandler;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;

    @Resource
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Autowired
    private EnterpriseServiceImpl enterpriseService;

    @Autowired
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @Resource
    private PublicSyncHandler publicSyncHandler;

    @Resource
    private UserServiceImpl userServiceImpl;

    @Resource
    private DepartmentServiceImpl departmentServiceImpl;

    @Resource
    private FeiShuServiceImpl feiShuService;

    @Resource
    private DingdingSyncHandler dingdingSyncHandler;

    @Resource
    private EnterpriseMapper enterpriseMapper;

    @Override
    @Async
    public void syncBatchyCcworkOrganization() {

        List<Enterprise> enterprises  = enterpriseMapper.queryByAdditionalInfo("tuitui_saas_link");
         for (Enterprise enterprise : enterprises) {
             long enterpriseId = enterprise.getId();
             PlatformEnterprises platformEnterprise = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);
             if (!platformEnterprise.getEnterpriseTicket().isEmpty()) {
                 syncOrganization(Math.toIntExact(enterpriseId), TYPE_CCWORK, "");
             }
         }
    }


    @Override
    @Async
    public void syncOrganization(Integer enterpriseId, String syncType, String requestId) {

        if (TYPE_WEIXIN.equals(syncType)) {
            syncWeixin(enterpriseId, requestId);
        } else if (TYPE_DING_TALK.equals(syncType)) {
            syncDingTalk(enterpriseId, requestId);
        } else if (TYPE_CCWORK.equals(syncType)) {
            syncCcwork(enterpriseId, requestId);
        } else if (TYPE_FEI_SHU.equals(syncType)) {
            syncFeiShu(enterpriseId, requestId);
        } else {
            throw new ParamException("syncType is not support !");
        }
    }

    private void syncDingTalk(Integer enterpriseId, String requestId) {
        String syncRedisKey = SYNC_DING_TALK_PUBLIC_CLOUD_PREFIX + enterpriseId + requestId;

        String syncRedisValue = redisStringManager.get(syncRedisKey);
        if (StringUtils.isNotEmpty(syncRedisValue)) {
            log.info("sync dingTalk public cloud is running!");
            return;
        } else {
            log.info("sync dingTalk public cloud start!");
            redisStringManager.set(syncRedisKey, "1", SYNC_WEIXIN_PUBLIC_CLOUD_TIME_OUT);
        }

        Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);

        List<DingTalkInitParams> dingTalkParamsList = dingdingSyncHandler.getDingdingInfo(enterpriseInfo);
        log.info("dingTalk initParams info list : {}", JSON.toJSONString(dingTalkParamsList));

        for (DingTalkInitParams initParams : dingTalkParamsList) {
            List<YfyDepartment> yfyDepartmentsByDingTalk = dingdingSyncHandler.getYfyDepartmentsByDingTalk(initParams);
            List<YfyUser> yfyUsersByDingTalk = dingdingSyncHandler.getYfyUsersByDingTalk(initParams);
            sync(yfyDepartmentsByDingTalk, yfyUsersByDingTalk, initParams.getAppId(), initParams.getEnterpriseParams());
        }

        log.info(" sync dingTalk public cloud end!");
        redisStringManager.delete(syncRedisKey);
    }

    private void syncFeiShu(Integer enterpriseId, String requestId) {
        String syncRedisKey = SYNC_FEISHU_PUBLIC_CLOUD_PREFIX + enterpriseId + requestId;

        String syncRedisValue = redisStringManager.get(syncRedisKey);
        if (StringUtils.isNotEmpty(syncRedisValue)) {
            log.info(" sync feiShu public cloud is running!");
            return;
        } else {
            log.info(" sync feiShu public cloud start!");
            redisStringManager.set(syncRedisKey, "1", SYNC_WEIXIN_PUBLIC_CLOUD_TIME_OUT);
        }

        FeiShuInitParams initParams = feiShuService.getInitParams(enterpriseId);
        log.info("feiShu initParams info : {}", JSON.toJSONString(initParams));

        List<YfyDepartment> yfyDepartments = feiShuService.getYfyDepartmentByFeiShu(initParams.getAppId(), initParams.getAppSecret(), initParams.getHost());

        List<YfyUser> yfyUsers = feiShuService.getYfyUsersByFeiShu(initParams.getAppId(), initParams.getAppSecret(), initParams.getHost());

        sync(yfyDepartments, yfyUsers, initParams.getAppId(), initParams.getEnterpriseParams());

        log.info(" sync feiShu public cloud end!");
        redisStringManager.delete(syncRedisKey);
    }

    /**
     * 同步微信组织架构
     *
     * @param enterpriseId
     */
    private void syncWeixin(Integer enterpriseId, String requestId) {

        String syncRedisKey = SYNC_WEIXIN_PUBLIC_CLOUD_PREFIX + enterpriseId + requestId;

        String syncRedisValue = redisStringManager.get(syncRedisKey);
        if (StringUtils.isNotEmpty(syncRedisValue)) {
            log.info(" sync weixin public cloud is running!");
            return;
        } else {
            log.info(" sync weixin public cloud start!");
            redisStringManager.set(syncRedisKey, "1", SYNC_WEIXIN_PUBLIC_CLOUD_TIME_OUT);
        }

        //1.获取企业微信配置信息
        WeixinWorkInitParams params = weixinSyncHandler.getWeixinInfo(enterpriseId);
        log.info("weixinWorkInitParams info : {}", JSON.toJSONString(params));

        WeixinWorkClientHelper weixinWorkClientHelper = new WeixinWorkClientHelper(params);

        // 2、获取所有用户信息
        List<PlatformUser> platformUserList = platformUserMapper.queryByEnterpriseTicket(params.getCorpId());
        log.info("platformUserList size is : {}", platformUserList.size());

        // 2.1 所有用户映射关系：亿方云用户id - 微信用户id
        Map<Long, String> yfyUserIdMap = new HashMap<>();

        for (PlatformUser platformUser : platformUserList) {
            yfyUserIdMap.put(platformUser.getUserId(), platformUser.getUserTicket());
        }

        List<Long> yfyUserIdList = platformUserList.stream().map(PlatformUser::getUserId).collect(Collectors.toList());

        // 2.3 所有用户映射关系：微信用户id - 用户信息
        Map<String, User> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(yfyUserIdList)) {
            List<User> users = userMapper.queryByIds(yfyUserIdList);
            for (User user : users) {
                userMap.put(yfyUserIdMap.get(user.getId()), user);
            }
        }

        // 3.获取所有部门信息
        List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByEnterpriseTicket(params.getCorpId());
        log.info("platformDepartmentList size is : {}", platformDepartmentList.size());

        // 3.1 所有部门映射关系：亿方云部门id - 微信部门id
        Map<Long, String> yfyDepartmentIdMap = new HashMap<>();

        // 3.2 所有部门映射关系：2. 微信部门id - 平台部门id
        Map<String, PlatformDepartment> platformDepartmentMap = new HashMap<>();
        for (PlatformDepartment platformDepartment : platformDepartmentList) {
            yfyDepartmentIdMap.put(platformDepartment.getYfyDepartmentId(), platformDepartment.getDepartmentId());
            platformDepartmentMap.put(platformDepartment.getDepartmentId(), platformDepartment);
        }
        List<Long> yfyDepartmentIdList = platformDepartmentList.stream().map(PlatformDepartment::getYfyDepartmentId).collect(Collectors.toList());


        // 3.3 需要删除的PlatformDepartment
        List<Long> needDeletePlatformDepartmentIdList = new ArrayList<>();

        // 3.4 所有部门映射关系：微信部门id - 部门信息
        Map<String, Department> departmentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(yfyDepartmentIdList)) {
            List<Department> departments = departmentMapper.queryByIds(yfyDepartmentIdList);

            log.info("departments info :{}", JSON.toJSONString(departments));
            // 真正的部门id列表
            List<Long> departmentIdList = departments.stream().map(Department::getId).collect(Collectors.toList());
            // 对比部门查询结果，后台部门删除时候，部门表数据不存在
            for (Long aLong : yfyDepartmentIdList) {
                if (!departmentIdList.contains(aLong)) {
                    log.info("department delete in the background! id is :{}", aLong);

                    needDeletePlatformDepartmentIdList.add(platformDepartmentMap.get(yfyDepartmentIdMap.get(aLong)).getId());
                    platformDepartmentMap.remove(yfyDepartmentIdMap.get(aLong));
                    yfyDepartmentIdMap.remove(aLong);
                }
            }
            for (Department department : departments) {
                departmentMap.put(yfyDepartmentIdMap.get(department.getId()), department);
            }
            log.info("departmentMap info :{}", JSON.toJSONString(departmentMap));
        }

        // 3.5 删除多余的PlatformDepartment
        if (!CollectionUtils.isEmpty(needDeletePlatformDepartmentIdList)) {
            log.info("needDeletePlatformDepartmentIdList info :{}", JSON.toJSONString(needDeletePlatformDepartmentIdList));
            platformDepartmentMapper.deleteByIds(needDeletePlatformDepartmentIdList);
        }

        // 4. 获取企业的顶级部门id
        Department rootDepartment = departmentMapper.queryRootDepartment(Long.parseLong(params.getEnterpriseParams().getEnterpriseId()));


        // 5.获取微信组织架构列表
        List<QiyeWeixinDepartment> qiyeWeixinDepartmentList = weixinWorkClientHelper.getDepartments(null);
        log.info("qiyeWeixinDepartment count :{}", qiyeWeixinDepartmentList.size());

        // 5.1 所有微信组织架构映射关系：微信部门id - 微信部门信息
        Map<Integer, QiyeWeixinDepartment> qiyeWeixinDepartmentMap = new HashMap<>();

        // 6. 将部门进行进行分级
        Map<Integer, List<Integer>> levelMap = new HashMap<>();
        int count = 1;
        int level = 1;
        List<Integer> list = new ArrayList<>();
        // 第一次循环将顶级部门加入其中
        list.add(1);
        while (count < qiyeWeixinDepartmentList.size()) {
            List<Integer> listLevel = new ArrayList<>();
            for (QiyeWeixinDepartment qiyeWeixinDepartment : qiyeWeixinDepartmentList) {
                if (list.contains(qiyeWeixinDepartment.getParentId())) {
                    count++;
                    listLevel.add(qiyeWeixinDepartment.getId());
                    qiyeWeixinDepartmentMap.put(qiyeWeixinDepartment.getId(), qiyeWeixinDepartment);
                }
            }
            list.clear();
            list.addAll(listLevel);
            levelMap.put(level, listLevel);
            level++;
        }
        log.info("levelMap info :{}", JSON.toJSONString(levelMap));

        // 7. 从顶级部门依次同步微信组织信息
        for (int i = 1; i < level; i++) {
            log.info("sync department, level is :{}", level);
            List<Integer> departmentIdList = levelMap.get(i);
            // 同步微信部门列表
            syncWeixinPublicDepartments(departmentIdList, qiyeWeixinDepartmentMap, departmentMap, platformDepartmentMap, userMap, params, rootDepartment);
        }

        // 8. 删除微信已经删除的部门
        deleteDepartment(qiyeWeixinDepartmentList, params);

        // 9.获取微信用户列表
        // 所有部门映射关系：微信用户id - 微信用户信息
        Map<String, QiyeWeixinUser> qiyeWeixinUserMap = new HashMap<>();
        for (QiyeWeixinDepartment qiyeWeixinDepartment : qiyeWeixinDepartmentList) {
            List<QiyeWeixinUser> weixinUsers = weixinWorkClientHelper.getUsers(qiyeWeixinDepartment.getId(), false);
            if (!CollectionUtils.isEmpty(weixinUsers)) {
                for (QiyeWeixinUser weixinUser : weixinUsers) {
                    qiyeWeixinUserMap.put(weixinUser.getUserid(), weixinUser);
                }
            }
        }
        log.info("qiyeWeixinUser count :{}", qiyeWeixinUserMap.size());

        // 10. 同步微信用户列表
        syncWeixinUsers(qiyeWeixinUserMap, platformDepartmentMap, userMap, params, rootDepartment, yfyUserIdMap);

        // 11. 删除微信已经删除的用户
        deleteUser(qiyeWeixinUserMap, params);

        log.info(" sync weixin public cloud end!");
        redisStringManager.delete(syncRedisKey);

    }

    /**
     * 删除微信已经删除的用户
     *
     * @param qiyeWeixinUserMap
     * @param params
     */
    private void deleteUser(Map<String, QiyeWeixinUser> qiyeWeixinUserMap, WeixinWorkInitParams params) {
        Set<String> qiyeWeixinUserIdList = qiyeWeixinUserMap.keySet();

        if (qiyeWeixinUserIdList.size() == 0) {
            log.error("delete users when qiyeWeixinUserIdList is null");
            return;
        }
        List<PlatformUser> platformUserList = platformUserMapper.queryByEnterpriseTicket(params.getCorpId());

        for (PlatformUser platformUser : platformUserList) {
            // 亿方云存在，企业微信不存在，进行删除判断
            if (!qiyeWeixinUserIdList.contains(platformUser.getUserTicket())) {
                log.info("need delete userTicket is :{}", platformUser.getUserTicket());
                String redisKey = DELETE_PUBLIC_USER_PREFIX + platformUser.getUserId();
                //累计删除超过5次，执行删除操作
                if (checkNeedDeleteCount(redisKey)) {
                    // 设置文件接受者
                    List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(platformUser.getUserId());
                    DeleteUserBean deleteUserBean = new DeleteUserBean();
                    if (!CollectionUtils.isEmpty(departmentsUsers)) {
                        List<Long> yfyDepartmentIdList = departmentsUsers.stream().map(DepartmentsUsers::getDepartmentId).collect(Collectors.toList());
                        List<Department> departments = departmentMapper.queryByIds(yfyDepartmentIdList);
                        for (Department department : departments) {
                            if (department.getDirectorId() != 0) {
                                deleteUserBean.setUserReceivedItems(department.getDirectorId());
                                break;
                            }
                        }
                    } else {
                        deleteUserBean.setUserReceivedItems(params.getEnterpriseParams().getAdminUserId());
                    }

                    // 删除用户
                    v2ClientHelper.deleteUser(params.getEnterpriseParams().getAdminUserId(), platformUser.getUserId(), deleteUserBean);
                }
            }
        }
    }

    /**
     * 删除微信已经删除的部门
     *
     * @param qiyeWeixinDepartmentList
     * @param params
     */
    private void deleteDepartment(List<QiyeWeixinDepartment> qiyeWeixinDepartmentList, WeixinWorkInitParams params) {
        List<Integer> qiyeWeixinDepartmentIdList = qiyeWeixinDepartmentList.stream().map(QiyeWeixinDepartment::getId).collect(Collectors.toList());

        if (qiyeWeixinDepartmentIdList.size() == 0) {
            log.error("delete users when qiyeWeixinDepartmentIdList is null");
            return;
        }

        List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByEnterpriseTicket(params.getCorpId());


        for (PlatformDepartment platformDepartment : platformDepartmentList) {
            String originalQiyeWeixinId = platformDepartment.getDepartmentId().replace(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE, "");
            log.info("original QiyeWeixinDepartmentId is :{}", originalQiyeWeixinId);

            // 亿方云存在，企业微信不存在，进行删除判断
            if (!qiyeWeixinDepartmentIdList.contains(Integer.valueOf(originalQiyeWeixinId))) {
                String redisKey = DELETE_PUBLIC_DEPARTMENT_PREFIX + platformDepartment.getYfyDepartmentId();
                if (checkNeedDeleteCount(redisKey)) {
                    // 删除部门
                    boolean deleteResult = v2ClientHelper.deleteDepartment(params.getEnterpriseParams().getAdminUserId(), platformDepartment.getYfyDepartmentId());
                    // 若删除成功，删除platform_departments数据
                    if (deleteResult) {
                        List<Long> ids = new ArrayList<>();
                        ids.add(platformDepartment.getId());
                        platformDepartmentMapper.deleteByIds(ids);
                    }
                }
            }
        }


    }

    /**
     * 同步微信用户列表
     *
     * @param qiyeWeixinUserMap
     * @param platformDepartmentMap
     * @param userMap
     * @param params
     * @param rootDepartment
     * @param yfyUserIdMap
     */
    private void syncWeixinUsers(
            Map<String, QiyeWeixinUser> qiyeWeixinUserMap,
            Map<String, PlatformDepartment> platformDepartmentMap,
            Map<String, User> userMap,
            WeixinWorkInitParams params,
            Department rootDepartment,
            Map<Long, String> yfyUserIdMap) {


        for (Map.Entry<String, QiyeWeixinUser> stringQiyeWeixinUserEntry : qiyeWeixinUserMap.entrySet()) {
            try {
                PublicUserBean syncPublicUserBean = new PublicUserBean();

                QiyeWeixinUser value = stringQiyeWeixinUserEntry.getValue();
                JSONArray extAttributes = value.getExtAttributes();
                for (Object extAttribute : extAttributes) {
                    JSONObject jsonObject = (JSONObject) extAttribute;
                    String name = jsonObject.getString(WEIXIN_EXTATTR_PARAMETER_NAME);
                    if (WEIXIN_PARAMETER_YFYPHONE.equals(name)) {
                        List<String> list = new ArrayList<>();
                        list.add(jsonObject.getString(WEIXIN_EXTATTR_PARAMETER_VALUE));
                        syncPublicUserBean.setIdentifiers(list);
                    }
                }
                // 没有手机号和状态不为1不进行同步
                if (CollectionUtils.isEmpty(syncPublicUserBean.getIdentifiers()) || value.getStatus() != 1) {
                    continue;
                }
                syncPublicUserBean.setName(value.getName());
                String userTicket = stringQiyeWeixinUserEntry.getKey();
                User user = userMap.get(userTicket);

                if (Objects.isNull(user)) { // user不存在，创建用户

                    createUser(syncPublicUserBean, params, yfyUserIdMap, value, rootDepartment, platformDepartmentMap);
                } else { // user存在，进行对比是否发生变化

                    editUser(user, value, syncPublicUserBean, params, platformDepartmentMap, rootDepartment);

                }
            } catch (Exception e) {
                log.info("syncWeixinUsers error!", e);
            }
        }

    }

    /**
     * 编辑用户
     *
     * @param user
     * @param value
     * @param syncPublicUserBean
     * @param params
     * @param platformDepartmentMap
     * @param rootDepartment
     */
    private void editUser(User user, QiyeWeixinUser value, PublicUserBean syncPublicUserBean, WeixinWorkInitParams params, Map<String, PlatformDepartment> platformDepartmentMap, Department rootDepartment) {
        log.info("QiyeWeixinUser info :{}, user info:{}", JSON.toJSONString(value), JSON.toJSONString(user));
        // 1. 名称发生改变，更新用户名称
        if (!user.getName().equals(value.getName())) {
            log.info("user name has change! user id :{}", user.getId());
            v2ClientHelper.editUser(syncPublicUserBean, params.getEnterpriseParams().getAdminUserId(), user.getId());
        }

        // 2. 对比部门列表
        List<Long> yfyDepartmentIdList = new ArrayList<>();
        List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(user.getId());
        if (!CollectionUtils.isEmpty(departmentsUsers)) {
            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                int position = departmentsUser.getPosition();
                // 当用户职位是员工或者主管的时候，对比部门信息
                if (position == DEPARTMENT_POSITION_STAFF || position == DEPARTMENT_POSITION_DIRECTOR) {
                    yfyDepartmentIdList.add(departmentsUser.getDepartmentId());
                }
            }
        }

        List<Long> departmentIdList = value.getDepartment();
        List<Long> weixinDepartmentIdList = new ArrayList<>();
        // 将微信部门id转为亿方云id
        if (!CollectionUtils.isEmpty(departmentIdList)) {
            for (Long departmentId : departmentIdList) {
                long yfyDepartmentId = 0;
                PlatformDepartment platformDepartment = platformDepartmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + departmentId);
                // 将用户添加到对应的部门中
                if (!Objects.isNull(platformDepartment)) {
                    yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                } else if (WEIXIN_TOP_DEPARTMENT_ID == departmentId) {
                    yfyDepartmentId = rootDepartment.getId();
                }
                weixinDepartmentIdList.add(yfyDepartmentId);
            }
        }

        List<Long> addDepartmentList = new ArrayList<>();
        List<Long> deleteDepartmentList = new ArrayList<>();

        if (CollectionUtils.isEmpty(departmentIdList) && CollectionUtils.isEmpty(departmentsUsers)) { // 微信和亿方云都是空的，没有发生变化

        } else if (!CollectionUtils.isEmpty(departmentIdList) && CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云为空的，微信不为空，发生改变
            // 将所有微信部门同步到亿方云
            for (Long yfyDepartmentId : weixinDepartmentIdList) {
                addDepartmentList.add(yfyDepartmentId);
            }
        } else if (CollectionUtils.isEmpty(departmentIdList) && !CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云不为空，微信为空，发生改变
            // 将人员从部门中移除
            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                deleteDepartmentList.add(departmentsUser.getDepartmentId());
            }
        } else {
            // 找出需要删除的部门
            for (Long aLong : yfyDepartmentIdList) {
                if (!weixinDepartmentIdList.contains(aLong)) {
                    deleteDepartmentList.add(aLong);
                }
            }
            // 找出需要添加的部门
            for (Long aLong : weixinDepartmentIdList) {
                if (!yfyDepartmentIdList.contains(aLong)) {
                    addDepartmentList.add(aLong);
                }
            }
        }

        List<Long> userIds = new ArrayList<>();
        userIds.add(user.getId());

        // 循环删除部门成员
        if (!CollectionUtils.isEmpty(deleteDepartmentList)) {
            for (Long aLong : deleteDepartmentList) {
                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                editDepartmentUserBean.setDeleteUserIds(userIds);
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, params.getEnterpriseParams().getAdminUserId(), aLong);
                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                    log.info("department delete user success! user id :{}, department id :{}", user.getId(), aLong);
                }
            }
        }

        // 循环添加部门成员
        if (!CollectionUtils.isEmpty(addDepartmentList)) {
            for (Long aLong : addDepartmentList) {
                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                editDepartmentUserBean.setAddUserIds(userIds);
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, params.getEnterpriseParams().getAdminUserId(), aLong);
                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                    log.info("department add user success! user id :{}, department id :{}", user.getId(), aLong);
                }
            }
        }
    }

    /**
     * 创建用户
     *
     * @param syncPublicUserBean
     * @param params
     * @param yfyUserIdMap
     * @param value
     * @param rootDepartment
     * @param platformDepartmentMap
     */
    private void createUser(PublicUserBean syncPublicUserBean, WeixinWorkInitParams params, Map<Long, String> yfyUserIdMap, QiyeWeixinUser value, Department rootDepartment, Map<String, PlatformDepartment> platformDepartmentMap) {
        syncPublicUserBean.setPassword(customNacosConfig.getDefaultPassword());
        syncPublicUserBean.setSpaceTotal(USER_DEFAULT_SPACE_TOTAL);
        // 1.创建用户
        CreateUserResult createUserResult = v2ClientHelper.createUser(syncPublicUserBean, params.getEnterpriseParams().getAdminUserId());

        if (!Objects.isNull(createUserResult) && !CollectionUtils.isEmpty(createUserResult.getIdentifiersAlreadyInvited())) {
            log.info("identifiers_already_invited, identifiers info :{}", JSON.toJSONString(syncPublicUserBean.getIdentifiers()));
            User userInvited = userMapper.queryByPhone(createUserResult.getIdentifiersAlreadyInvited().get(0), Long.valueOf(params.getEnterpriseParams().getEnterpriseId()));

            String userTicketExist = yfyUserIdMap.get(userInvited.getId());
            if (!Objects.isNull(userInvited) && StringUtils.isEmpty(userTicketExist)) {
                UserResult userResult = new UserResult();
                userResult.setId(userInvited.getId());
                List<UserResult> list = new ArrayList<>();
                list.add(userResult);
                createUserResult.setUsers(list);
                log.info("identifiers_already_invited, userResult info :{}", JSON.toJSONString(userResult));
            }
        }
        if (!Objects.isNull(createUserResult) && !CollectionUtils.isEmpty(createUserResult.getUsers())) {
            // 2.创建用户成功后，维护用户的映射关系
            PlatformUser platformUser = new PlatformUser();
            platformUser.setUserTicket(value.getUserid());
            platformUser.setPlatformId(Long.parseLong(params.getEnterpriseParams().getPlatformId()));
            platformUser.setEnterpriseTicket(params.getCorpId());
            platformUser.setUserInnerId(value.getUserid());
            platformUser.setUserId(createUserResult.getUsers().get(0).getId());
            platformUser.setPlatformUserAvatar("");
            platformUser.setValueBox("{}");
            platformUserMapper.insert(platformUser);

            // 3.修改用户名称
            UserResult userResult = v2ClientHelper.editUser(syncPublicUserBean, params.getEnterpriseParams().getAdminUserId(), createUserResult.getUsers().get(0).getId());

            // 3.修改将用户添加入对应部门中
            List<Long> departmentIdList = value.getDepartment();
            if (!CollectionUtils.isEmpty(departmentIdList)) {
                for (Long departmentId : departmentIdList) {
                    PlatformDepartment platformDepartment = platformDepartmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + departmentId);
                    // 将用户添加到对应的部门中

                    EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                    long yfyDepartmentId = 0;
                    if (!Objects.isNull(platformDepartment)) {
                        List<Long> addUserIds = new ArrayList<>();
                        addUserIds.add(createUserResult.getUsers().get(0).getId());
                        editDepartmentUserBean.setAddUserIds(addUserIds);
                        yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                    } else if (WEIXIN_TOP_DEPARTMENT_ID == departmentId) {
                        List<Long> addUserIds = new ArrayList<>();
                        addUserIds.add(createUserResult.getUsers().get(0).getId());
                        editDepartmentUserBean.setAddUserIds(addUserIds);
                        yfyDepartmentId = rootDepartment.getId();
                    }
                    EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, params.getEnterpriseParams().getAdminUserId(), yfyDepartmentId);
                    if (!Objects.isNull(editDepartmentResult) && !CollectionUtils.isEmpty(editDepartmentResult.getAddUsers())) {
                        log.info("department add user success! user id :{}, department id :{}", userResult.getId(), yfyDepartmentId);
                    }
                }
            }

        }
    }

    /**
     * 校验需要删除次数
     *
     * @param redisKey
     * @return
     */
    private boolean checkNeedDeleteCount(String redisKey) {
        String deleteCountStr = redisStringManager.get(redisKey);
        Integer deleteCount = StringUtils.isEmpty(deleteCountStr) ? 1 : Integer.valueOf(deleteCountStr);
        if (deleteCount >= 10) {
            log.info("data need delete, redisKey :{}, deleteCount :{}", redisKey, deleteCount);
            return true;
        } else {
            redisStringManager.increment(redisKey, 1);
            log.info("data do not need delete, redisKey :{}, deleteCount :{}", redisKey, deleteCount);
            return false;
        }

    }

    /**
     * 同步微信部门列表
     *
     * @param departmentIdiLst        需要同步的微信id列表
     * @param qiyeWeixinDepartmentMap 微信部门id - 微信部门信息
     * @param departmentMap           微信部门id - 部门信息
     * @param platformDepartmentMap   微信部门id - 平台部门id
     * @param userMap                 微信用户id - 用户信息
     * @param params                  企业微信信息
     * @param rootDepartment          顶级部门信息
     * @return
     */
    private void syncWeixinPublicDepartments(
            List<Integer> departmentIdiLst,
            Map<Integer, QiyeWeixinDepartment> qiyeWeixinDepartmentMap,
            Map<String, Department> departmentMap,
            Map<String, PlatformDepartment> platformDepartmentMap,
            Map<String, User> userMap,
            WeixinWorkInitParams params,
            Department rootDepartment) {

        // 1、获取该层级的需要同步的部门列表
        List<QiyeWeixinDepartment> qiyeWeixinDepartmentList = new ArrayList<>();
        for (Integer integer : departmentIdiLst) {
            qiyeWeixinDepartmentList.add(qiyeWeixinDepartmentMap.get(integer));
        }

        // 2.构造部门信息
        for (QiyeWeixinDepartment qiyeWeixinDepartment : qiyeWeixinDepartmentList) {
            try {
                if (WEIXIN_TOP_DEPARTMENT_PARENT_ID == qiyeWeixinDepartment.getParentId()) { // 顶级部门不进行同步
                    continue;
                }
                Department department = departmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + qiyeWeixinDepartment.getId());
                if (Objects.isNull(department)) {// 该部门不存在，是新增的

                    PublicDepartmentBean syncPublicDepartmentBean = buildCreatePublicDepartmentBean(qiyeWeixinDepartment, params, departmentMap, userMap);
                    if (Objects.isNull(syncPublicDepartmentBean)) {
                        continue;
                    }
                    //  创建部门
                    createPlatformDepartment(syncPublicDepartmentBean, platformDepartmentMap, departmentMap, params, qiyeWeixinDepartment);

                } else { // 该部门存在，进行对比

                    PublicDepartmentBean syncPublicDepartmentBean = compareAndBuildEditPublicDepartmentBean(qiyeWeixinDepartment, department, userMap, rootDepartment, departmentMap, params);

                    if (Objects.isNull(syncPublicDepartmentBean)) {
                        continue;
                    }
                    // 更新平台部门信息
                    updatePlatformDepartment(syncPublicDepartmentBean, platformDepartmentMap, departmentMap, params, qiyeWeixinDepartment, department);
                }
            } catch (Exception e) {
                log.info("syncWeixinPublicDepartments error!", e);
            }
        }
    }

    /**
     * 更新平台部门信息
     *
     * @param syncPublicDepartmentBean
     * @param platformDepartmentMap
     * @param departmentMap
     * @param params
     * @param qiyeWeixinDepartment
     * @param department
     */
    private void updatePlatformDepartment(PublicDepartmentBean syncPublicDepartmentBean, Map<String, PlatformDepartment> platformDepartmentMap, Map<String, Department> departmentMap, WeixinWorkInitParams params, QiyeWeixinDepartment qiyeWeixinDepartment, Department department) {
        //  修改部门
        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.editDepartment(syncPublicDepartmentBean, department.getId(), params.getEnterpriseParams().getAdminUserId());

        // 若部门修改成功，维护部门映射关系
        if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {

            // 若部门的name发生改变，更新platformDepartment的name
            PlatformDepartment platformDepartment = platformDepartmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + qiyeWeixinDepartment.getId());
            if (!syncPublicDepartmentResult.getName().equals(platformDepartment.getName())) {
                platformDepartment.setName(syncPublicDepartmentResult.getName());
                platformDepartmentMapper.updateNameById(platformDepartment);
            }

            Department departmentResult = new Department();
            departmentResult.setName(syncPublicDepartmentResult.getName());
            MiniUser director = syncPublicDepartmentResult.getDirector();
            if (!Objects.isNull(director)) {
                departmentResult.setDirectorId(director.getId());
            }
            departmentResult.setParentId(syncPublicDepartmentResult.getParentId());
            departmentResult.setId(syncPublicDepartmentResult.getId());
            departmentResult.setOrder(syncPublicDepartmentResult.getOrder());

            // 将新生成的部门信息放入总的部门数据中
            departmentMap.put(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + qiyeWeixinDepartment.getId(), departmentResult);
        }
    }

    /**
     * 对比部门信息并构建更新部门信息
     *
     * @param qiyeWeixinDepartment
     * @param department
     * @param userMap
     * @param rootDepartment
     * @param departmentMap
     * @param params
     * @return
     */
    private PublicDepartmentBean compareAndBuildEditPublicDepartmentBean(QiyeWeixinDepartment qiyeWeixinDepartment, Department department, Map<String, User> userMap, Department rootDepartment, Map<String, Department> departmentMap, WeixinWorkInitParams params) {
        boolean hasChange = false;

        log.info("qiyeWeixinDepartment info :{}, department:{}", JSON.toJSONString(qiyeWeixinDepartment), JSON.toJSONString(department));
        // 对比主管id
        List<String> directorIds = qiyeWeixinDepartment.getDirectorIds();
        if (CollectionUtils.isEmpty(directorIds)) {
            if (department.getDirectorId() != 0) {
                hasChange = true;
            }
        } else {
            String directorId = directorIds.get(0);
            User user = userMap.get(directorId);
            if (Objects.isNull(user)) {
                hasChange = true;
            } else if (user.getId() != department.getDirectorId()) {
                hasChange = true;
            }
        }

        // 对比名称
        if (!department.getName().equals(qiyeWeixinDepartment.getName())) {
            hasChange = true;
        }
        // 对比order
        if (department.getOrder() != qiyeWeixinDepartment.getOrder()) {
            hasChange = true;
        }

        // 对比父部门id
        int parentId = qiyeWeixinDepartment.getParentId();
        if (WEIXIN_TOP_DEPARTMENT_ID == parentId && department.getParentId() != rootDepartment.getId()) {
            hasChange = true;
        } else {
            Department parentDepartment = departmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + parentId);
            if (!Objects.isNull(parentDepartment) && parentDepartment.getId() != department.getParentId()) {
                hasChange = true;
            }
        }

        // 部门发生变化
        if (hasChange) {
            PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
            // 1. 设置部门名称
            syncPublicDepartmentBean.setDepartmentName(qiyeWeixinDepartment.getName());

            // 2. 设置父部门id
            if (WEIXIN_TOP_DEPARTMENT_ID == parentId) { // 次顶级部门，父部门设置为0
                log.info("parent is is top!");
                syncPublicDepartmentBean.setParentId(0L);
            } else {// 父部门需要换取真正的部门id
                Department parentDepartment = departmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + parentId);
                if (!Objects.isNull(parentDepartment)) {
                    syncPublicDepartmentBean.setParentId(parentDepartment.getId());
                }
            }

            // 3. 设置主管id
            if (!CollectionUtils.isEmpty(directorIds)) {
                User user = userMap.get(directorIds.get(0));
                if (!Objects.isNull(user)) {
                    syncPublicDepartmentBean.setDirectorId(user.getId());
                }
            }
            // 4. 设置排序
            syncPublicDepartmentBean.setOrder((long) qiyeWeixinDepartment.getOrder());

            return syncPublicDepartmentBean;
        }
        return null;
    }

    /**
     * 创建平台部门信息
     *
     * @param syncPublicDepartmentBean
     * @param platformDepartmentMap
     * @param departmentMap
     * @param params
     * @param qiyeWeixinDepartment
     */
    private void createPlatformDepartment(PublicDepartmentBean syncPublicDepartmentBean, Map<String, PlatformDepartment> platformDepartmentMap, Map<String, Department> departmentMap, WeixinWorkInitParams params, QiyeWeixinDepartment qiyeWeixinDepartment) {

        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.createDepartment(syncPublicDepartmentBean, params.getEnterpriseParams().getAdminUserId());


        // 若部门创建成功，插入部门信息
        if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {
            // 创建部门映射关系
            PlatformDepartment platformDepartment = new PlatformDepartment();
            platformDepartment.setPlatformId(Long.parseLong(params.getEnterpriseParams().getPlatformId()));
            platformDepartment.setEnterpriseTicket(params.getCorpId());
            platformDepartment.setDepartmentId(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + qiyeWeixinDepartment.getId());
            platformDepartment.setYfyDepartmentId(syncPublicDepartmentResult.getId());
            platformDepartment.setName(syncPublicDepartmentResult.getName());

            platformDepartmentMapper.insert(platformDepartment);

            // 将新生成的部门信息放入总的部门数据中
            platformDepartmentMap.put(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + qiyeWeixinDepartment.getId(), platformDepartment);

            Department department = new Department();
            department.setName(syncPublicDepartmentResult.getName());
            department.setId(syncPublicDepartmentResult.getId());
            MiniUser director = syncPublicDepartmentResult.getDirector();
            if (!Objects.isNull(director)) {
                department.setDirectorId(director.getId());
            }
            department.setParentId(syncPublicDepartmentResult.getParentId());

            // 将新生成的部门信息放入总的部门数据中
            departmentMap.put(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + qiyeWeixinDepartment.getId(), department);
        }
    }


    /**
     * 构建同步的部门信息
     *
     * @param qiyeWeixinDepartment
     * @param params
     * @param departmentMap
     * @param userMap
     * @return
     */
    private PublicDepartmentBean buildCreatePublicDepartmentBean(QiyeWeixinDepartment qiyeWeixinDepartment, WeixinWorkInitParams params, Map<String, Department> departmentMap, Map<String, User> userMap) {

        PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
        // 2.1 设置部门名称
        syncPublicDepartmentBean.setDepartmentName(qiyeWeixinDepartment.getName());

        // 2.2 设置父部门id
        if (WEIXIN_TOP_DEPARTMENT_ID == qiyeWeixinDepartment.getParentId()) { // 次顶级部门，父部门设置为null
            syncPublicDepartmentBean.setParentId(0L);
        } else {// 父部门需要换取真正的部门id
            Department departmentParent = departmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + qiyeWeixinDepartment.getParentId());
            if (!Objects.isNull(departmentParent)) {
                syncPublicDepartmentBean.setParentId(departmentParent.getId());
            }
        }

        // 2.3 设置主管id
        List<String> directorIds = qiyeWeixinDepartment.getDirectorIds();
        if (!CollectionUtils.isEmpty(directorIds)) {
            User user = userMap.get(directorIds.get(0));
            if (!Objects.isNull(user)) {
                syncPublicDepartmentBean.setDirectorId(user.getId());
            }
        }

        // 2.4 设置部门空间，默认设置100G
        syncPublicDepartmentBean.setSpaceTotal(DEPARTMENT_DEFAULT_SPACE_TOTAL);
        // 2.5 设置是否创建公共资料库，默认为true
        syncPublicDepartmentBean.setCreateCommonFolder(true);
        // 2.6 设置部门是否自动接受协作，默认为true
        syncPublicDepartmentBean.setCollabAutoAccepted(true);
        // 2.7 设置排序
        syncPublicDepartmentBean.setOrder((long) qiyeWeixinDepartment.getOrder());
        // 2.7 设置是否隐藏手机号，默认和上级部门保持一致
        // 2.8 设置是否禁止分享，默认和上级部门保持一致
        // 2.9 设置是否是否开启水印预览，默认和上级部门保持一致
        return syncPublicDepartmentBean;
    }


    @Override
    public ExecuteResult syncPublic(String productId, List<Integer> configIds, Integer enterpriseId, Integer taskId, String password,  String requestId) {
        ExecuteResult executeResult = new ExecuteResult();
        AtomicInteger addRows = new AtomicInteger();
        AtomicInteger updateRows = new AtomicInteger();
        AtomicInteger errorRows = new AtomicInteger();

        String syncRedisKey = SYNC_CUSTOM_PUBLIC_CLOUD_PREFIX + enterpriseId  + requestId;

        String syncRedisValue = redisStringManager.get(syncRedisKey);
        if (StringUtils.isNotEmpty(syncRedisValue)) {
            log.info(" sync custom public cloud is running!");
            return executeResult;
        } else {
            log.info(" sync custom public cloud start!");
            redisStringManager.set(syncRedisKey, "1", SYNC_WEIXIN_PUBLIC_CLOUD_TIME_OUT);
        }
        //获取企业配置
        Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
        String platformId = String.valueOf(enterpriseInfo.getPlatformId());
        List<YfyUser> yfyUsers = new ArrayList<>();
        List<YfyDepartment> yfyDepartments = new ArrayList<>();
        Boolean isSyncDepartment = true;
        List<PlatformSyncConfig> platformSyncConfigs = platformSyncConfigMapper.queryByIds(configIds);
        //根据同步配置获取组织架构和用户数据
        for (PlatformSyncConfig platformSyncConfig : platformSyncConfigs) {
            String sourceType = platformSyncConfig.getSourceType();
            if (SourceTypeEnum.API.getDesc().equals(sourceType)) {
                publicSyncHandler.syncPublicByApi(yfyUsers, yfyDepartments, platformSyncConfig);
            }
        }
        if (CollectionUtils.isEmpty(yfyDepartments)) {
            isSyncDepartment = false;
        }
        // 2、获取所有用户信息
        List<PlatformUser> platformUserList = userServiceImpl.getPlatformUser(Integer.parseInt(platformId));
        log.info("platformUserList size is : {}", platformUserList.size());

        // 2.1 所有用户映射关系：亿方云用户id - 客户用户id
        Map<Long, String> yfyUserIdMap = new HashMap<>();

        for (PlatformUser platformUser : platformUserList) {
            yfyUserIdMap.put(platformUser.getUserId(), platformUser.getUserTicket());
        }

        List<Long> yfyUserIdList = platformUserList.stream().map(PlatformUser::getUserId).collect(Collectors.toList());

        // 2.3 所有用户映射关系：客户用户id - 用户信息
        Map<String, User> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(yfyUserIdList)) {
            List<User> users = userMapper.queryByIds(yfyUserIdList);
            for (User user : users) {
                userMap.put(yfyUserIdMap.get(user.getId()), user);
            }
        }
        // 3.获取所有部门信息
        List<PlatformDepartment> platformDepartmentList = departmentServiceImpl.getPlatformDepartment(Integer.parseInt(platformId));
        log.info("platformDepartmentList size is : {}", platformDepartmentList.size());
        // 3.1 所有部门映射关系：亿方云部门id - 客户部门id
        Map<Long, String> yfyDepartmentIdMap = new HashMap<>();

        // 3.2 所有部门映射关系：2. 客户部门id - 平台部门id
        Map<String, PlatformDepartment> platformDepartmentMap = new HashMap<>();
        for (PlatformDepartment platformDepartment : platformDepartmentList) {
            yfyDepartmentIdMap.put(platformDepartment.getYfyDepartmentId(), platformDepartment.getDepartmentId());
            platformDepartmentMap.put(platformDepartment.getDepartmentId(), platformDepartment);
        }
        List<Long> yfyDepartmentIdList = platformDepartmentList.stream().map(PlatformDepartment::getYfyDepartmentId).collect(Collectors.toList());


        // 3.3 需要删除的PlatformDepartment
        List<Long> needDeletePlatformDepartmentIdList = new ArrayList<>();

        // 3.4 所有部门映射关系：客户部门id - 部门信息
        Map<String, Department> departmentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(yfyDepartmentIdList)) {
            List<Department> departments = departmentMapper.queryByIds(yfyDepartmentIdList);

            log.info("departments info :{}", JSON.toJSONString(departments));
            // 真正的部门id列表
            List<Long> departmentIdList = departments.stream().map(Department::getId).collect(Collectors.toList());
            // 对比部门查询结果，后台部门删除时候，部门表数据不存在
            for (Long aLong : yfyDepartmentIdList) {
                if (!departmentIdList.contains(aLong)) {
                    log.info("department delete in the background! id is :{}", aLong);

                    needDeletePlatformDepartmentIdList.add(platformDepartmentMap.get(yfyDepartmentIdMap.get(aLong)).getId());
                    platformDepartmentMap.remove(yfyDepartmentIdMap.get(aLong));
                    yfyDepartmentIdMap.remove(aLong);
                }
            }
            for (Department department : departments) {
                departmentMap.put(yfyDepartmentIdMap.get(department.getId()), department);
            }
            log.info("departmentMap info :{}", JSON.toJSONString(departmentMap));
        }

        // 3.5 删除多余的PlatformDepartment
        if (!CollectionUtils.isEmpty(needDeletePlatformDepartmentIdList)) {
            log.info("needDeletePlatformDepartmentIdList info :{}", JSON.toJSONString(needDeletePlatformDepartmentIdList));
            platformDepartmentMapper.deleteByIds(needDeletePlatformDepartmentIdList);
        }

        if (isSyncDepartment) {
            // 4. 获取企业的顶级部门id
            Department rootDepartment = departmentMapper.queryRootDepartment(Long.valueOf(enterpriseId));

            // 5.1 所有客户组织架构映射关系：客户部门id - 客户部门信息
            Map<String, YfyDepartment> customDepartmentMap = new HashMap<>();

            // 6. 将部门进行进行分级
            int level = 1;
            int count = 0;
            Map<Integer, List<String>> levelMap = new HashMap<>();
            List<String> list = new ArrayList<>();
            // 第一次循环将顶级部门加入其中
            list.add("");
            while (count < yfyDepartments.size()) {
                List<String> listLevel = new ArrayList<>();
                for (YfyDepartment yfyDepartment : yfyDepartments) {
                    if (list.contains(yfyDepartment.getParentId())) {
                        count++;
                        listLevel.add(yfyDepartment.getId());
                        customDepartmentMap.put(yfyDepartment.getId(), yfyDepartment);
                    }
                }
                list.clear();
                list.addAll(listLevel);
                levelMap.put(level, listLevel);
                level++;
            }
            log.info("levelMap info :{}", JSON.toJSONString(levelMap));

            // 7. 从顶级部门依次同步客户组织信息
            for (int i = 1; i < level; i++) {
                log.info("sync department, level is :{}", level);
                List<String> departmentIdList = levelMap.get(i);
                // 同步客户部门列表
                publicSyncHandler.syncCustomPublicDepartments(departmentIdList, customDepartmentMap, departmentMap, platformDepartmentMap, userMap, rootDepartment, enterpriseInfo, taskId, addRows, updateRows, errorRows);
            }

            // 8. 删除客户已经删除的部门
            publicSyncHandler.deleteDepartment(yfyDepartments, enterpriseInfo, taskId, updateRows, errorRows);
        }
        // 9.获取客户用户列表
        // 所有部门映射关系：客户用户id - 客户用户信息
        Map<String, YfyUser> customUserMap = yfyUsers.stream().collect(Collectors.toMap(
                YfyUser::getId, YfyUser -> YfyUser,
                (val1, val2) -> val2)
        );
        log.info("customUserMap count :{}", customUserMap.size());

        if (StringUtils.isBlank(password)) {
            password = customNacosConfig.getDefaultPassword();
        }
        // 10. 同步客户用户列表
        publicSyncHandler.syncCustomUsers(customUserMap, platformDepartmentMap, userMap, yfyUserIdMap, enterpriseInfo, isSyncDepartment, taskId, addRows, updateRows, errorRows, password);

        // 11. 客户已经删除的用户进行特殊处理
        publicSyncHandler.deleteUser(customUserMap, userMap, enterpriseInfo, taskId, addRows, updateRows, errorRows);

        executeResult.setAddRows(addRows.get());
        executeResult.setUpdateRows(updateRows.get());
        executeResult.setErrorRows(errorRows.get());
        executeResult.setTotalRows(addRows.get() + updateRows.get() + errorRows.get());
        log.info(" sync custom public cloud end!");
        redisStringManager.delete(syncRedisKey);
        return executeResult;
    }

    private void syncCcwork(Integer enterpriseId, String requestId) {

        String syncRedisKey = SYNC_CUSTOM_PUBLIC_CLOUD_PREFIX + enterpriseId + requestId;

        String syncRedisValue = redisStringManager.get(syncRedisKey);
        if (StringUtils.isNotEmpty(syncRedisValue)) {
            log.info(" sync ccwork public cloud is running!");
            return;
        } else {
            log.info(" sync ccwork public cloud start!");
            redisStringManager.set(syncRedisKey, "1", SYNC_WEIXIN_PUBLIC_CLOUD_TIME_OUT);
        }

        CcWorkInitParam params = ccWorKSassSyncHandler.getCcWorkInfo(enterpriseId);
        log.info("CcWorkInitParam info : {}", JSON.toJSONString(params));

        CcWorkClientHelper ccWorkClientHelper = new CcWorkClientHelper(params);

        // 2、获取所有用户信息
        List<PlatformUser> platformUserList = platformUserMapper.queryByEnterpriseTicket(params.getCorpId());
        log.info("platformUserList size is : {}", platformUserList.size());

        // 2.1 所有用户映射关系：亿方云用户id - 推推用户id
        Map<Long, String> yfyUserIdMap = new HashMap<>();

        for (PlatformUser platformUser : platformUserList) {
            yfyUserIdMap.put(platformUser.getUserId(), platformUser.getUserTicket());
        }

        List<Long> yfyUserIdList = platformUserList.stream().map(PlatformUser::getUserId).collect(Collectors.toList());

        // 2.3 所有用户映射关系：推推用户id - 用户信息
        Map<String, User> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(yfyUserIdList)) {
            List<User> users = userMapper.queryByIds(yfyUserIdList);
            for (User user : users) {
                userMap.put(yfyUserIdMap.get(user.getId()), user);
            }
        }

        // 3.获取所有部门信息
        List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByEnterpriseTicket(params.getCorpId());
        log.info("platformDepartmentList size is : {}", platformDepartmentList.size());

        // 3.1 所有部门映射关系：亿方云部门id - 推推部门id
        Map<Long, String> yfyDepartmentIdMap = new HashMap<>();

        // 3.2 所有部门映射关系：2. 推推部门id - 平台部门id
        Map<String, PlatformDepartment> platformDepartmentMap = new HashMap<>();
        for (PlatformDepartment platformDepartment : platformDepartmentList) {
            yfyDepartmentIdMap.put(platformDepartment.getYfyDepartmentId(), platformDepartment.getDepartmentId());
            platformDepartmentMap.put(platformDepartment.getDepartmentId(), platformDepartment);
        }
        List<Long> yfyDepartmentIdList = platformDepartmentList.stream().map(PlatformDepartment::getYfyDepartmentId).collect(Collectors.toList());


        // 3.3 需要删除的PlatformDepartment
        List<Long> needDeletePlatformDepartmentIdList = new ArrayList<>();

        // 3.4 所有部门映射关系：推推部门id - 部门信息
        Map<String, Department> departmentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(yfyDepartmentIdList)) {
            List<Department> departments = departmentMapper.queryByIds(yfyDepartmentIdList);

            log.info("departments info :{}", JSON.toJSONString(departments));
            // 真正的部门id列表
            List<Long> departmentIdList = departments.stream().map(Department::getId).collect(Collectors.toList());
            // 对比部门查询结果，后台部门删除时候，部门表数据不存在
            for (Long aLong : yfyDepartmentIdList) {
                if (!departmentIdList.contains(aLong)) {
                    log.info("department delete in the background! id is :{}", aLong);

                    needDeletePlatformDepartmentIdList.add(platformDepartmentMap.get(yfyDepartmentIdMap.get(aLong)).getId());
                    platformDepartmentMap.remove(yfyDepartmentIdMap.get(aLong));
                    yfyDepartmentIdMap.remove(aLong);
                }
            }
            for (Department department : departments) {
                departmentMap.put(yfyDepartmentIdMap.get(department.getId()), department);
            }
            log.info("departmentMap info :{}", JSON.toJSONString(departmentMap));
        }

        // 3.5 删除多余的PlatformDepartment
        if (!CollectionUtils.isEmpty(needDeletePlatformDepartmentIdList)) {
            log.info("needDeletePlatformDepartmentIdList info :{}", JSON.toJSONString(needDeletePlatformDepartmentIdList));
            platformDepartmentMapper.deleteByIds(needDeletePlatformDepartmentIdList);
        }

        // 4. 获取企业的顶级部门id
        Department rootDepartment = departmentMapper.queryRootDepartment(Long.parseLong(params.getEnterpriseParams().getEnterpriseId()));


        // 5.获取推推组织架构列表
        List<CcWorkDepartment> ccWorkDepartmentList = ccWorkClientHelper.getDepartmentRelation();
        log.info("ccWorkDepartmentList count :{}", ccWorkDepartmentList.size());

        // 5.1 所有推推组织架构映射关系：推推部门id - 推推部门信息
        Map<String, CcWorkDepartment> ccWorkDepartmentMap = new HashMap<>();

        // 6. 将部门进行进行分级
        Map<Integer, List<String>> levelMap = new HashMap<>();
        int count = 0;
        int level = 1;
        List<String> list = new ArrayList<>();
        // 第一次循环将顶级部门加入其中
        list.add("0");
        while (count < ccWorkDepartmentList.size()) {
            List<String> listLevel = new ArrayList<>();
            for (CcWorkDepartment ccWorkDepartment : ccWorkDepartmentList) {
                if (list.contains(ccWorkDepartment.getParentId())) {
                    count++;
                    listLevel.add(ccWorkDepartment.getId());
                    ccWorkDepartmentMap.put(ccWorkDepartment.getId(), ccWorkDepartment);
                }
            }
            list.clear();
            list.addAll(listLevel);
            levelMap.put(level, listLevel);
            level++;
        }
        log.info("levelMap info :{}", JSON.toJSONString(levelMap));

        // 7. 从顶级部门依次同步推推组织信息
        for (int i = 1; i < level; i++) {
            log.info("sync department, level is :{}", level);
            List<String> departmentIdList = levelMap.get(i);
            // 同步推推部门列表
            syncCcWorkPublicDepartments(departmentIdList, ccWorkDepartmentMap, departmentMap, platformDepartmentMap, userMap, params, rootDepartment);
        }

        // 8. 删除推推已经删除的部门
        deleteDepartment(ccWorkDepartmentList, params);

        // 9.获取推推用户列表
        // 所有部门映射关系：推推用户id - 推推用户信息
        Map<String, CcWorkUser> ccWorkUserMap = new HashMap<>();
        for (CcWorkDepartment ccWorkDepartment : ccWorkDepartmentList) {
            List<CcWorkUser> ccWorkUsers = ccWorkClientHelper.getUserList(ccWorkDepartment.getId());
            if (!CollectionUtils.isEmpty(ccWorkUsers)) {
                for (CcWorkUser ccWorkUser : ccWorkUsers) {
                    ccWorkUserMap.put(ccWorkUser.getId(), ccWorkUser);
                }
            }
        }
        log.info("ccWork count :{}", ccWorkUserMap.size());

        // 10. 同步推推用户列表
        syncCcWorkUsers(ccWorkUserMap, platformDepartmentMap, userMap, params, rootDepartment, yfyUserIdMap);

        // 11. 删除推推已经删除的用户
        deleteUser(ccWorkUserMap, params);

        log.info(" sync ccWork public cloud end!");
        redisStringManager.delete(syncRedisKey);

    }



    private void sync(List<YfyDepartment> yfyDepartments, List<YfyUser> yfyUsers, String corpId, EnterpriseParams enterpriseParams) {
        Map<Long, String> yfyUserIdMap = new HashMap<>();

        Map<String, User> userMap = getUserMap(corpId, yfyUserIdMap);

        List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByEnterpriseTicket(corpId);
        log.info("platformDepartmentList size is : {}", platformDepartmentList.size());

        Map<Long, String> yfyDepartmentIdMap = new HashMap<>();

        Map<String, PlatformDepartment> platformDepartmentMap = new HashMap<>();
        for (PlatformDepartment platformDepartment : platformDepartmentList) {
            yfyDepartmentIdMap.put(platformDepartment.getYfyDepartmentId(), platformDepartment.getDepartmentId());
            platformDepartmentMap.put(platformDepartment.getDepartmentId(), platformDepartment);
        }
        List<Long> yfyDepartmentIdList = platformDepartmentList.stream().map(PlatformDepartment::getYfyDepartmentId).collect(Collectors.toList());


        List<Long> needDeletePlatformDepartmentIdList = new ArrayList<>();

        Map<String, Department> departmentMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(yfyDepartmentIdList)) {
            List<Department> departments = departmentMapper.queryByIds(yfyDepartmentIdList);

            log.info("departments info :{}", JSON.toJSONString(departments));
            List<Long> departmentIdList = departments.stream().map(Department::getId).collect(Collectors.toList());
            for (Long aLong : yfyDepartmentIdList) {
                if (!departmentIdList.contains(aLong)) {
                    log.info("department delete in the background! id is :{}", aLong);

                    needDeletePlatformDepartmentIdList.add(platformDepartmentMap.get(yfyDepartmentIdMap.get(aLong)).getId());
                    platformDepartmentMap.remove(yfyDepartmentIdMap.get(aLong));
                    yfyDepartmentIdMap.remove(aLong);
                }
            }
            for (Department department : departments) {
                departmentMap.put(yfyDepartmentIdMap.get(department.getId()), department);
            }
            log.info("departmentMap info :{}", JSON.toJSONString(departmentMap));
        }

        if (!CollectionUtils.isEmpty(needDeletePlatformDepartmentIdList)) {
            log.info("needDeletePlatformDepartmentIdList info :{}", JSON.toJSONString(needDeletePlatformDepartmentIdList));
            platformDepartmentMapper.deleteByIds(needDeletePlatformDepartmentIdList);
        }

        Department rootDepartment = departmentMapper.queryRootDepartment(Long.parseLong(enterpriseParams.getEnterpriseId()));

        log.info("yfy Departments count :{}", yfyDepartments.size());

        Map<String, YfyDepartment> customDepartmentMap = new HashMap<>();

        // 6. 将部门进行进行分级
        Map<Integer, List<String>> levelMap = new HashMap<>();
        int count = 0;
        int level = 1;
        List<String> list = new ArrayList<>();
        // 第一次循环将顶级部门加入其中
        list.add("");
        while (count < yfyDepartments.size()) {
            List<String> listLevel = new ArrayList<>();
            for (YfyDepartment yfyDepartment : yfyDepartments) {
                if (list.contains(yfyDepartment.getParentId())) {
                    count++;
                    listLevel.add(yfyDepartment.getId());
                    customDepartmentMap.put(yfyDepartment.getId(), yfyDepartment);
                }
            }
            list.clear();
            list.addAll(listLevel);
            levelMap.put(level, listLevel);
            level++;
        }
        log.info("levelMap info :{}", JSON.toJSONString(levelMap));

        // 7. 从顶级部门依次同步推推组织信息
        for (int i = 1; i < level; i++) {
            log.info("sync department, level is :{}", level);
            List<String> departmentIdList = levelMap.get(i);
            syncYfyPublicDepartments(departmentIdList, customDepartmentMap, departmentMap, platformDepartmentMap, userMap, corpId, rootDepartment, enterpriseParams);
        }

        deleteDepartment(yfyDepartments, corpId, enterpriseParams);

        Map<String, YfyUser> yfyUserMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(yfyUsers)) {
            for (YfyUser yfyUser : yfyUsers) {
                yfyUserMap.put(yfyUser.getId(), yfyUser);
            }
        }
        log.info("yfyUsers count :{}", yfyUserMap.size());

        syncYfyUsers(yfyUserMap, platformDepartmentMap, userMap, corpId, enterpriseParams, rootDepartment, yfyUserIdMap);

        deleteUser(yfyUserMap, corpId, enterpriseParams);



    }


    private void syncCcWorkPublicDepartments(
            List<String> departmentIdiLst,
            Map<String, CcWorkDepartment> ccWorkDepartmentMap,
            Map<String, Department> departmentMap,
            Map<String, PlatformDepartment> platformDepartmentMap,
            Map<String, User> userMap,
            CcWorkInitParam params,
            Department rootDepartment) {

        // 1、获取该层级的需要同步的部门列表
        List<CcWorkDepartment> ccWorkDepartmentList = new ArrayList<>();
        for (String id : departmentIdiLst) {
            ccWorkDepartmentList.add(ccWorkDepartmentMap.get(id));
        }

        // 2.构造部门信息
        for (CcWorkDepartment ccWorkDepartment : ccWorkDepartmentList) {
            try {
                Department department = departmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + ccWorkDepartment.getId());
                if (Objects.isNull(department)) {// 该部门不存在，是新增的

                    PublicDepartmentBean syncPublicDepartmentBean = buildCreatePublicDepartmentBean(ccWorkDepartment, params, departmentMap, userMap);
                    if (Objects.isNull(syncPublicDepartmentBean)) {
                        continue;
                    }
                    //  创建部门
                    createPlatformDepartment(syncPublicDepartmentBean, platformDepartmentMap, departmentMap, params, ccWorkDepartment);

                } else { // 该部门存在，进行对比

                    PublicDepartmentBean syncPublicDepartmentBean = compareAndBuildEditPublicDepartmentBean(ccWorkDepartment, department, userMap, rootDepartment, departmentMap, params);

                    if (Objects.isNull(syncPublicDepartmentBean)) {
                        continue;
                    }
                    // 更新平台部门信息
                    updatePlatformDepartment(syncPublicDepartmentBean, platformDepartmentMap, departmentMap, params, ccWorkDepartment, department);
                }
            } catch (Exception e) {
                log.info("syncWeixinPublicDepartments error!", e);
            }
        }
    }

    private PublicDepartmentBean buildCreatePublicDepartmentBean(CcWorkDepartment ccWorkDepartment, CcWorkInitParam params, Map<String, Department> departmentMap, Map<String, User> userMap) {

        PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
        // 2.1 设置部门名称
        syncPublicDepartmentBean.setDepartmentName(SyncStringUtils.formatString(ccWorkDepartment.getName()));

        // 2.2 设置父部门id
        if (ccWorkDepartment.getParentId().equals("0")) {  // 次顶级部门，父部门设置为null
            syncPublicDepartmentBean.setParentId(0L);
        } else {// 父部门需要换取真正的部门id
            Department departmentParent = departmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + ccWorkDepartment.getParentId());
            if (!Objects.isNull(departmentParent)) {
                syncPublicDepartmentBean.setParentId(departmentParent.getId());
            }
        }

        // 2.4 设置部门空间，默认设置100G
        syncPublicDepartmentBean.setSpaceTotal(DEPARTMENT_DEFAULT_SPACE_TOTAL);
        // 2.5 设置是否创建公共资料库，默认为true
        syncPublicDepartmentBean.setCreateCommonFolder(true);
        // 2.6 设置部门是否自动接受协作，默认为true
        syncPublicDepartmentBean.setCollabAutoAccepted(true);
        // 2.7 设置排序
        syncPublicDepartmentBean.setOrder(Long.valueOf(ccWorkDepartment.getOrder()));
        // 2.7 设置是否隐藏手机号，默认和上级部门保持一致
        // 2.8 设置是否禁止分享，默认和上级部门保持一致
        // 2.9 设置是否是否开启水印预览，默认和上级部门保持一致
        return syncPublicDepartmentBean;
    }

    /**
     * 创建平台部门信息
     *
     * @param syncPublicDepartmentBean
     * @param platformDepartmentMap
     * @param departmentMap
     * @param params
     * @param ccWorkDepartment
     */
    private void createPlatformDepartment(PublicDepartmentBean syncPublicDepartmentBean, Map<String, PlatformDepartment> platformDepartmentMap, Map<String, Department> departmentMap, CcWorkInitParam params, CcWorkDepartment ccWorkDepartment) {

        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.createDepartment(syncPublicDepartmentBean, params.getEnterpriseParams().getAdminUserId());


        // 若部门创建成功，插入部门信息
        if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {
            // 创建部门映射关系
            PlatformDepartment platformDepartment = new PlatformDepartment();
            platformDepartment.setPlatformId(Long.parseLong(params.getEnterpriseParams().getPlatformId()));
            platformDepartment.setEnterpriseTicket(params.getCorpId());
            platformDepartment.setDepartmentId(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + ccWorkDepartment.getId());
            platformDepartment.setYfyDepartmentId(syncPublicDepartmentResult.getId());
            platformDepartment.setName(syncPublicDepartmentResult.getName());

            platformDepartmentMapper.insert(platformDepartment);

            // 将新生成的部门信息放入总的部门数据中
            platformDepartmentMap.put(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + ccWorkDepartment.getId(), platformDepartment);

            Department department = new Department();
            department.setName(syncPublicDepartmentResult.getName());
            department.setId(syncPublicDepartmentResult.getId());
            MiniUser director = syncPublicDepartmentResult.getDirector();
            if (!Objects.isNull(director)) {
                department.setDirectorId(director.getId());
            }
            department.setParentId(syncPublicDepartmentResult.getParentId());

            // 将新生成的部门信息放入总的部门数据中
            departmentMap.put(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + ccWorkDepartment.getId(), department);
        }
    }

    /**
     * 对比部门信息并构建更新部门信息
     *
     * @param ccWorkDepartment
     * @param department
     * @param userMap
     * @param rootDepartment
     * @param departmentMap
     * @param params
     * @return
     */
    private PublicDepartmentBean compareAndBuildEditPublicDepartmentBean(CcWorkDepartment ccWorkDepartment, Department department, Map<String, User> userMap, Department rootDepartment, Map<String, Department> departmentMap, CcWorkInitParam params) {
        boolean hasChange = false;

        log.info("ccWorkDepartment info :{}, department:{}", JSON.toJSONString(ccWorkDepartment), JSON.toJSONString(department));

        // 对比名称
        if (!department.getName().equals(ccWorkDepartment.getName())) {
            hasChange = true;
        }
        // 对比order
        if (department.getOrder() != Long.valueOf(ccWorkDepartment.getOrder())) {
            hasChange = true;
        }

        // 对比父部门id
        String parentId = ccWorkDepartment.getParentId();
        if (parentId.equals("0") && department.getParentId() != rootDepartment.getId()) {
            hasChange = true;
        } else {
            Department parentDepartment = departmentMap.get(parentId);
            if (!Objects.isNull(parentDepartment) && parentDepartment.getId() != department.getParentId()) {
                hasChange = true;
            }
        }

        // 部门发生变化
        if (hasChange) {
            PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
            // 1. 设置部门名称
            syncPublicDepartmentBean.setDepartmentName(ccWorkDepartment.getName());

            // 2. 设置父部门id
            if (parentId.equals("0")) { // 次顶级部门，父部门设置为0
                log.info("parent is is top!");
                syncPublicDepartmentBean.setParentId(0L);
            } else {// 父部门需要换取真正的部门id
                Department parentDepartment = departmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + parentId);
                if (!Objects.isNull(parentDepartment)) {
                    syncPublicDepartmentBean.setParentId(parentDepartment.getId());
                }
            }

            // 4. 设置排序
            syncPublicDepartmentBean.setOrder(Long.valueOf(ccWorkDepartment.getOrder()));

            return syncPublicDepartmentBean;
        }
        return null;
    }

    /**
     * 更新平台部门信息
     *
     * @param syncPublicDepartmentBean
     * @param platformDepartmentMap
     * @param departmentMap
     * @param params
     * @param ccWorkDepartment
     * @param department
     */
    private void updatePlatformDepartment(PublicDepartmentBean syncPublicDepartmentBean, Map<String, PlatformDepartment> platformDepartmentMap, Map<String, Department> departmentMap, CcWorkInitParam params, CcWorkDepartment ccWorkDepartment, Department department) {
        //  修改部门
        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.editDepartment(syncPublicDepartmentBean, department.getId(), params.getEnterpriseParams().getAdminUserId());

        // 若部门修改成功，维护部门映射关系
        if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {

            // 若部门的name发生改变，更新platformDepartment的name
            PlatformDepartment platformDepartment = platformDepartmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + ccWorkDepartment.getId());
            if (!syncPublicDepartmentResult.getName().equals(platformDepartment.getName())) {
                platformDepartment.setName(syncPublicDepartmentResult.getName());
                platformDepartmentMapper.updateNameById(platformDepartment);
            }

            Department departmentResult = new Department();
            departmentResult.setName(syncPublicDepartmentResult.getName());
            MiniUser director = syncPublicDepartmentResult.getDirector();
            if (!Objects.isNull(director)) {
                departmentResult.setDirectorId(director.getId());
            }
            departmentResult.setParentId(syncPublicDepartmentResult.getParentId());
            departmentResult.setId(syncPublicDepartmentResult.getId());
            departmentResult.setOrder(syncPublicDepartmentResult.getOrder());

            // 将新生成的部门信息放入总的部门数据中
            departmentMap.put(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + ccWorkDepartment.getId(), departmentResult);
        }
    }

    /**
     * 删除推推已经删除的部门
     *
     * @param ccWorkDepartmentList
     * @param params
     */
    private void deleteDepartment(List<CcWorkDepartment> ccWorkDepartmentList, CcWorkInitParam params) {
        List<String> ccWorkDepartmentIdList = ccWorkDepartmentList.stream().map(CcWorkDepartment::getId).collect(Collectors.toList());

        if (ccWorkDepartmentIdList.size() == 0) {
            log.error("delete users when ccWorkDepartmentIdList is null");
            return;
        }

        List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByEnterpriseTicket(params.getCorpId());


        for (PlatformDepartment platformDepartment : platformDepartmentList) {
            String originalCcWorkId = platformDepartment.getDepartmentId().replace(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE, "");
            log.info("original ccWorkDepartmentId is :{}", originalCcWorkId);

            // 亿方云存在，推推不存在，进行删除判断
            if (!ccWorkDepartmentIdList.contains(Integer.valueOf(originalCcWorkId))) {
                String redisKey = DELETE_PUBLIC_DEPARTMENT_PREFIX + platformDepartment.getYfyDepartmentId();
                if (checkNeedDeleteCount(redisKey)) {
                    // 删除部门
                    boolean deleteResult = v2ClientHelper.deleteDepartment(params.getEnterpriseParams().getAdminUserId(), platformDepartment.getYfyDepartmentId());
                    // 若删除成功，删除platform_departments数据
                    if (deleteResult) {
                        List<Long> ids = new ArrayList<>();
                        ids.add(platformDepartment.getId());
                        platformDepartmentMapper.deleteByIds(ids);
                    }
                }
            }
        }
    }

    /**
     * 同步推推用户列表
     *
     * @param ccWorkUserMap
     * @param platformDepartmentMap
     * @param userMap
     * @param params
     * @param rootDepartment
     * @param yfyUserIdMap
     */
    private void syncCcWorkUsers(
            Map<String, CcWorkUser> ccWorkUserMap,
            Map<String, PlatformDepartment> platformDepartmentMap,
            Map<String, User> userMap,
            CcWorkInitParam params,
            Department rootDepartment,
            Map<Long, String> yfyUserIdMap) {


        for (Map.Entry<String, CcWorkUser> stringCcWorkUserEntry : ccWorkUserMap.entrySet()) {
            try {
                PublicUserBean syncPublicUserBean = new PublicUserBean();

                CcWorkUser value = stringCcWorkUserEntry.getValue();
                List<String> identifiers = new ArrayList<>();
                if (value.getId().contains("@")) {
                    identifiers.add(value.getId());
                } else {
                    identifiers.add(value.getEmail());
                }
                syncPublicUserBean.setIdentifiers(identifiers);
                // 没有手机号和状态不为1不进行同步
                if (CollectionUtils.isEmpty(syncPublicUserBean.getIdentifiers()) || !value.getStatus().equals("1")) {
                    continue;
                }
                String name = value.getName();
                if (name.contains("@")) {
                    name = name.split("@")[0];
                }
                syncPublicUserBean.setName(name);
                String userTicket = stringCcWorkUserEntry.getKey();
                User user = userMap.get(userTicket);

                if (Objects.isNull(user)) { // user不存在，创建用户

                    createUser(syncPublicUserBean, params, yfyUserIdMap, value, rootDepartment, platformDepartmentMap);
                } else { // user存在，进行对比是否发生变化

                    editUser(user, value, syncPublicUserBean, params, platformDepartmentMap, rootDepartment);

                }
            } catch (Exception e) {
                log.info("syncCcWorkUsers error!", e);
            }
        }

    }

    /**
     * 编辑用户
     *
     * @param user
     * @param value
     * @param syncPublicUserBean
     * @param params
     * @param platformDepartmentMap
     * @param rootDepartment
     */
    private void editUser(User user, CcWorkUser value, PublicUserBean syncPublicUserBean, CcWorkInitParam params, Map<String, PlatformDepartment> platformDepartmentMap, Department rootDepartment) {
        log.info("CcWorkUser info :{}, user info:{}", JSON.toJSONString(value), JSON.toJSONString(user));
        // 1. 名称发生改变，更新用户名称
        if (!user.getName().equals(value.getName())) {
            log.info("user name has change! user id :{}", user.getId());
            v2ClientHelper.editUser(syncPublicUserBean, params.getEnterpriseParams().getAdminUserId(), user.getId());
        }

        // 2. 对比部门列表
        List<Long> yfyDepartmentIdList = new ArrayList<>();
        List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(user.getId());
        if (!CollectionUtils.isEmpty(departmentsUsers)) {
            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                int position = departmentsUser.getPosition();
                // 当用户职位是员工或者主管的时候，对比部门信息
                if (position == DEPARTMENT_POSITION_STAFF || position == DEPARTMENT_POSITION_DIRECTOR) {
                    yfyDepartmentIdList.add(departmentsUser.getDepartmentId());
                }
            }
        }

        String departmentId = value.getDepartmentId();
        List<Long> ccWorkDepartmentIdList = new ArrayList<>();
        // 将推推部门id转为亿方云id
        if (StringUtils.isNotBlank(departmentId)) {
            long yfyDepartmentId = 0;
            PlatformDepartment platformDepartment = platformDepartmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + departmentId);
            // 将用户添加到对应的部门中
            if (!Objects.isNull(platformDepartment)) {
                yfyDepartmentId = platformDepartment.getYfyDepartmentId();
            } else if (departmentId.equals("0")) {
                yfyDepartmentId = rootDepartment.getId();
            }
            ccWorkDepartmentIdList.add(yfyDepartmentId);
        }

        List<Long> addDepartmentList = new ArrayList<>();
        List<Long> deleteDepartmentList = new ArrayList<>();

        if (StringUtils.isNotBlank(departmentId) && CollectionUtils.isEmpty(departmentsUsers)) { // 推推和亿方云都是空的，没有发生变化

        } else if (StringUtils.isNotBlank(departmentId) && CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云为空的，推推不为空，发生改变
            // 将所有微信部门同步到亿方云
            for (Long yfyDepartmentId : ccWorkDepartmentIdList) {
                addDepartmentList.add(yfyDepartmentId);
            }
        } else if (StringUtils.isBlank(departmentId) && !CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云不为空，推推为空，发生改变
            // 将人员从部门中移除
            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                deleteDepartmentList.add(departmentsUser.getDepartmentId());
            }
        } else {
            // 找出需要删除的部门
            for (Long aLong : yfyDepartmentIdList) {
                if (!ccWorkDepartmentIdList.contains(aLong)) {
                    deleteDepartmentList.add(aLong);
                }
            }
            // 找出需要添加的部门
            for (Long aLong : ccWorkDepartmentIdList) {
                if (!yfyDepartmentIdList.contains(aLong)) {
                    addDepartmentList.add(aLong);
                }
            }
        }

        List<Long> userIds = new ArrayList<>();
        userIds.add(user.getId());

        // 循环删除部门成员
        if (!CollectionUtils.isEmpty(deleteDepartmentList)) {
            for (Long aLong : deleteDepartmentList) {
                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                editDepartmentUserBean.setDeleteUserIds(userIds);
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, params.getEnterpriseParams().getAdminUserId(), aLong);
                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                    log.info("department delete user success! user id :{}, department id :{}", user.getId(), aLong);
                }
            }
        }

        // 循环添加部门成员
        if (!CollectionUtils.isEmpty(addDepartmentList)) {
            for (Long aLong : addDepartmentList) {
                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                editDepartmentUserBean.setAddUserIds(userIds);
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, params.getEnterpriseParams().getAdminUserId(), aLong);
                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                    log.info("department add user success! user id :{}, department id :{}", user.getId(), aLong);
                }
            }
        }
    }

    /**
     * 创建用户
     *
     * @param syncPublicUserBean
     * @param params
     * @param yfyUserIdMap
     * @param value
     * @param rootDepartment
     * @param platformDepartmentMap
     */
    private void createUser(PublicUserBean syncPublicUserBean, CcWorkInitParam params, Map<Long, String> yfyUserIdMap, CcWorkUser value, Department rootDepartment, Map<String, PlatformDepartment> platformDepartmentMap) {
        syncPublicUserBean.setPassword(customNacosConfig.getDefaultPassword());
        syncPublicUserBean.setSpaceTotal(USER_DEFAULT_SPACE_TOTAL);
        // 1.创建用户
        CreateUserResult createUserResult = v2ClientHelper.createUser(syncPublicUserBean, params.getEnterpriseParams().getAdminUserId());

        if (!Objects.isNull(createUserResult) && !CollectionUtils.isEmpty(createUserResult.getIdentifiersAlreadyInvited())) {
            log.info("identifiers_already_invited, identifiers info :{}", JSON.toJSONString(syncPublicUserBean.getIdentifiers()));
            User userInvited = userMapper.queryByEmail(createUserResult.getIdentifiersAlreadyInvited().get(0), Long.valueOf(params.getEnterpriseParams().getEnterpriseId()));

            String userTicketExist = yfyUserIdMap.get(userInvited.getId());
            if (!Objects.isNull(userInvited) && StringUtils.isEmpty(userTicketExist)) {
                UserResult userResult = new UserResult();
                userResult.setId(userInvited.getId());
                List<UserResult> list = new ArrayList<>();
                list.add(userResult);
                createUserResult.setUsers(list);
                log.info("identifiers_already_invited, userResult info :{}", JSON.toJSONString(userResult));
            }
        }
        if (!Objects.isNull(createUserResult) && !CollectionUtils.isEmpty(createUserResult.getUsers())) {
            // 2.创建用户成功后，维护用户的映射关系
            PlatformUser platformUser = new PlatformUser();
            String userTicket = value.getId();
            platformUser.setUserTicket(userTicket);
            platformUser.setPlatformId(Long.parseLong(params.getEnterpriseParams().getPlatformId()));
            platformUser.setEnterpriseTicket(params.getCorpId());
            platformUser.setUserInnerId(userTicket);
            platformUser.setUserId(createUserResult.getUsers().get(0).getId());
            platformUser.setPlatformUserAvatar("");
            platformUser.setValueBox("{}");
            platformUserMapper.insert(platformUser);
            // 3.修改用户名称
            UserResult userResult = v2ClientHelper.editUser(syncPublicUserBean, params.getEnterpriseParams().getAdminUserId(), createUserResult.getUsers().get(0).getId());

            // 3.修改将用户添加入对应部门中
            String departmentId = value.getDepartmentId();
            if (StringUtils.isNotBlank(departmentId)) {
                PlatformDepartment platformDepartment = platformDepartmentMap.get(params.getCorpId() + CommonConstants.SPLIT_UNDERLINE + departmentId);
                // 将用户添加到对应的部门中

                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                long yfyDepartmentId = 0;
                if (!Objects.isNull(platformDepartment)) {
                    List<Long> addUserIds = new ArrayList<>();
                    addUserIds.add(createUserResult.getUsers().get(0).getId());
                    editDepartmentUserBean.setAddUserIds(addUserIds);
                    yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                } else if (departmentId.equals("0")) {
                    List<Long> addUserIds = new ArrayList<>();
                    addUserIds.add(createUserResult.getUsers().get(0).getId());
                    editDepartmentUserBean.setAddUserIds(addUserIds);
                    yfyDepartmentId = rootDepartment.getId();
                }
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, params.getEnterpriseParams().getAdminUserId(), yfyDepartmentId);
                if (!Objects.isNull(editDepartmentResult) && !CollectionUtils.isEmpty(editDepartmentResult.getAddUsers())) {
                    log.info("department add user success! user id :{}, department id :{}", userResult.getId(), yfyDepartmentId);
                }
            }

        }
    }



    private void deleteUser(Map<String, CcWorkUser> ccWorkUserMap, CcWorkInitParam params) {
        Set<String> ccWorkUserIdList = ccWorkUserMap.keySet();

        if (ccWorkUserIdList.size() == 0) {
            log.error("delete users when ccWorkUserIdList is null");
            return;
        }
        List<PlatformUser> platformUserList = platformUserMapper.queryByEnterpriseTicket(params.getCorpId());

        for (PlatformUser platformUser : platformUserList) {
            // 亿方云存在，企业微信不存在，进行删除判断
            if (!ccWorkUserIdList.contains(platformUser.getUserTicket())) {
                log.info("need delete userTicket is :{}", platformUser.getUserTicket());
                String redisKey = DELETE_PUBLIC_USER_PREFIX + platformUser.getUserId();
                //累计删除超过5次，执行删除操作
                if (checkNeedDeleteCount(redisKey)) {
                    // 设置文件接受者
                    List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(platformUser.getUserId());
                    DeleteUserBean deleteUserBean = new DeleteUserBean();
                    if (!CollectionUtils.isEmpty(departmentsUsers)) {
                        List<Long> yfyDepartmentIdList = departmentsUsers.stream().map(DepartmentsUsers::getDepartmentId).collect(Collectors.toList());
                        List<Department> departments = departmentMapper.queryByIds(yfyDepartmentIdList);
                        for (Department department : departments) {
                            if (department.getDirectorId() != 0) {
                                deleteUserBean.setUserReceivedItems(department.getDirectorId());
                                break;
                            }
                        }
                    } else {
                        deleteUserBean.setUserReceivedItems(params.getEnterpriseParams().getAdminUserId());
                    }

                    // 删除用户
                    v2ClientHelper.deleteUser(params.getEnterpriseParams().getAdminUserId(), platformUser.getUserId(), deleteUserBean);
                }
            }
        }
    }

    private Map<String, User> getUserMap(String enterpriseTicket, Map<Long, String> yfyUserIdMap) {
        List<PlatformUser> platformUserList = platformUserMapper.queryByEnterpriseTicket(enterpriseTicket);
        log.info("platformUserList size is : {}", platformUserList.size());

        for (PlatformUser platformUser : platformUserList) {
            yfyUserIdMap.put(platformUser.getUserId(), platformUser.getUserTicket());
        }

        List<Long> yfyUserIdList = platformUserList.stream().map(PlatformUser::getUserId).collect(Collectors.toList());

        Map<String, User> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(yfyUserIdList)) {
            List<User> users = userMapper.queryByIds(yfyUserIdList);
            for (User user : users) {
                userMap.put(yfyUserIdMap.get(user.getId()), user);
            }
        }
        return userMap;
    }


    private void syncYfyPublicDepartments(
            List<String> departmentIdiLst,
            Map<String, YfyDepartment> yfyDepartmentMap,
            Map<String, Department> departmentMap,
            Map<String, PlatformDepartment> platformDepartmentMap,
            Map<String, User> userMap,
            String corpId,
            Department rootDepartment,
            EnterpriseParams enterpriseParams) {

        // 1、获取该层级的需要同步的部门列表
        List<YfyDepartment> YfyDepartmentList = new ArrayList<>();
        for (String id : departmentIdiLst) {
            YfyDepartmentList.add(yfyDepartmentMap.get(id));
        }

        // 2.构造部门信息
        for (YfyDepartment yfyDepartment : YfyDepartmentList) {
            try {
                Department department = departmentMap.get(corpId + CommonConstants.SPLIT_UNDERLINE + yfyDepartment.getId());
                if (Objects.isNull(department)) {// 该部门不存在，是新增的

                    PublicDepartmentBean syncPublicDepartmentBean = buildCreatePublicDepartmentBean(yfyDepartment, corpId, departmentMap, userMap);
                    if (Objects.isNull(syncPublicDepartmentBean)) {
                        continue;
                    }
                    //  创建部门
                    createPlatformDepartment(syncPublicDepartmentBean, platformDepartmentMap, departmentMap, corpId, yfyDepartment, enterpriseParams);

                } else { // 该部门存在，进行对比

                    PublicDepartmentBean syncPublicDepartmentBean = compareAndBuildEditPublicDepartmentBean(yfyDepartment, department, userMap, rootDepartment, departmentMap, corpId);

                    if (Objects.isNull(syncPublicDepartmentBean)) {
                        continue;
                    }
                    // 更新平台部门信息
                    updatePlatformDepartment(syncPublicDepartmentBean, platformDepartmentMap, departmentMap, corpId, yfyDepartment, department, enterpriseParams);
                }
            } catch (Exception e) {
                log.info("syncYfyPublicDepartments error!", e);
            }
        }
    }

    private PublicDepartmentBean buildCreatePublicDepartmentBean(YfyDepartment yfyDepartment, String corpId, Map<String, Department> departmentMap, Map<String, User> userMap) {

        PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
        // 2.1 设置部门名称
        syncPublicDepartmentBean.setDepartmentName(SyncStringUtils.formatString(yfyDepartment.getName()));

        // 2.2 设置父部门id
        if (StringUtils.isBlank(yfyDepartment.getParentId()) || yfyDepartment.getParentId().equals("0")) {  // 次顶级部门，父部门设置为null
            syncPublicDepartmentBean.setParentId(0L);
        } else {// 父部门需要换取真正的部门id
            Department departmentParent = departmentMap.get(corpId + CommonConstants.SPLIT_UNDERLINE + yfyDepartment.getParentId());
            if (!Objects.isNull(departmentParent)) {
                syncPublicDepartmentBean.setParentId(departmentParent.getId());
            }
        }

        // 2.4 设置部门空间，默认设置100G
        syncPublicDepartmentBean.setSpaceTotal(DEPARTMENT_DEFAULT_SPACE_TOTAL);
        // 2.5 设置是否创建公共资料库，默认为true
        syncPublicDepartmentBean.setCreateCommonFolder(yfyDepartment.getPublicFolder());
        // 2.6 设置部门是否自动接受协作，默认为true
        syncPublicDepartmentBean.setCollabAutoAccepted(yfyDepartment.getCollabAutoAccepted());
        // 2.7 设置排序
        syncPublicDepartmentBean.setOrder(Long.valueOf(yfyDepartment.getOrder()));
        // 2.7 设置是否隐藏手机号，默认和上级部门保持一致
        // 2.8 设置是否禁止分享，默认和上级部门保持一致
        // 2.9 设置是否是否开启水印预览，默认和上级部门保持一致
        return syncPublicDepartmentBean;
    }


    private void createPlatformDepartment(PublicDepartmentBean syncPublicDepartmentBean, Map<String, PlatformDepartment> platformDepartmentMap, Map<String, Department> departmentMap, String corpId, YfyDepartment yfyDepartment, EnterpriseParams enterpriseParams) {

        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.createDepartment(syncPublicDepartmentBean, enterpriseParams.getAdminUserId());


        // 若部门创建成功，插入部门信息
        if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {
            // 创建部门映射关系
            PlatformDepartment platformDepartment = new PlatformDepartment();
            platformDepartment.setPlatformId(Long.parseLong(enterpriseParams.getPlatformId()));
            platformDepartment.setEnterpriseTicket(corpId);
            platformDepartment.setDepartmentId(corpId + CommonConstants.SPLIT_UNDERLINE + yfyDepartment.getId());
            platformDepartment.setYfyDepartmentId(syncPublicDepartmentResult.getId());
            platformDepartment.setName(syncPublicDepartmentResult.getName());

            platformDepartmentMapper.insert(platformDepartment);

            // 将新生成的部门信息放入总的部门数据中
            platformDepartmentMap.put(corpId + CommonConstants.SPLIT_UNDERLINE + yfyDepartment.getId(), platformDepartment);

            Department department = new Department();
            department.setName(syncPublicDepartmentResult.getName());
            department.setId(syncPublicDepartmentResult.getId());
            MiniUser director = syncPublicDepartmentResult.getDirector();
            if (!Objects.isNull(director)) {
                department.setDirectorId(director.getId());
            }
            department.setParentId(syncPublicDepartmentResult.getParentId());

            // 将新生成的部门信息放入总的部门数据中
            departmentMap.put(corpId + CommonConstants.SPLIT_UNDERLINE + yfyDepartment.getId(), department);
        }
    }

    private PublicDepartmentBean compareAndBuildEditPublicDepartmentBean(YfyDepartment yfyDepartment,
                                                                         Department department,
                                                                         Map<String, User> userMap,
                                                                         Department rootDepartment,
                                                                         Map<String, Department> departmentMap,
                                                                         String corpId) {
        boolean hasChange = false;

        log.info("yfyDepartment info :{}, department:{}", JSON.toJSONString(yfyDepartment), JSON.toJSONString(department));

        // 对比名称
        if (!department.getName().equals(yfyDepartment.getName())) {
            hasChange = true;
        }
        // 对比order
        if (department.getOrder() != Long.valueOf(yfyDepartment.getOrder())) {
            hasChange = true;
        }

        // 对比父部门id
        String parentId = yfyDepartment.getParentId();
        if (StringUtils.isBlank(parentId) && department.getParentId() != rootDepartment.getId()) {
            hasChange = true;
        } else {
            Department parentDepartment = departmentMap.get(parentId);
            if (!Objects.isNull(parentDepartment) && parentDepartment.getId() != department.getParentId()) {
                hasChange = true;
            }
        }

        // 部门发生变化
        if (hasChange) {
            PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
            // 1. 设置部门名称
            syncPublicDepartmentBean.setDepartmentName(yfyDepartment.getName());

            // 2. 设置父部门id
            if (StringUtils.isBlank(parentId)) { // 次顶级部门，父部门设置为0
                log.info("parent is is top!");
                syncPublicDepartmentBean.setParentId(0L);
            } else {// 父部门需要换取真正的部门id
                Department parentDepartment = departmentMap.get(corpId + CommonConstants.SPLIT_UNDERLINE + parentId);
                if (!Objects.isNull(parentDepartment)) {
                    syncPublicDepartmentBean.setParentId(parentDepartment.getId());
                }
            }

            // 4. 设置排序
            syncPublicDepartmentBean.setOrder(Long.valueOf(yfyDepartment.getOrder()));

            return syncPublicDepartmentBean;
        }
        return null;
    }


    private void updatePlatformDepartment(PublicDepartmentBean syncPublicDepartmentBean,
                                          Map<String, PlatformDepartment> platformDepartmentMap,
                                          Map<String, Department> departmentMap,
                                          String corpId,
                                          YfyDepartment yfyDepartment,
                                          Department department,
                                          EnterpriseParams enterpriseParams) {
        //  修改部门
        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.editDepartment(syncPublicDepartmentBean, department.getId(), enterpriseParams.getAdminUserId());

        // 若部门修改成功，维护部门映射关系
        if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {

            // 若部门的name发生改变，更新platformDepartment的name
            PlatformDepartment platformDepartment = platformDepartmentMap.get(corpId + CommonConstants.SPLIT_UNDERLINE + yfyDepartment.getId());
            if (!syncPublicDepartmentResult.getName().equals(platformDepartment.getName())) {
                platformDepartment.setName(syncPublicDepartmentResult.getName());
                platformDepartmentMapper.updateNameById(platformDepartment);
            }

            Department departmentResult = new Department();
            departmentResult.setName(syncPublicDepartmentResult.getName());
            MiniUser director = syncPublicDepartmentResult.getDirector();
            if (!Objects.isNull(director)) {
                departmentResult.setDirectorId(director.getId());
            }
            departmentResult.setParentId(syncPublicDepartmentResult.getParentId());
            departmentResult.setId(syncPublicDepartmentResult.getId());
            departmentResult.setOrder(syncPublicDepartmentResult.getOrder());

            // 将新生成的部门信息放入总的部门数据中
            departmentMap.put(corpId + CommonConstants.SPLIT_UNDERLINE + yfyDepartment.getId(), departmentResult);
        }
    }

    private void deleteDepartment(List<YfyDepartment> yfyDepartmentList, String corpId, EnterpriseParams enterpriseParams) {
        List<String> yfyDepartmentIdList = yfyDepartmentList.stream().map(YfyDepartment::getId).collect(Collectors.toList());

        if (yfyDepartmentIdList.size() == 0) {
            log.error("delete users when yfyDepartmentIdList is null");
            return;
        }

        List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByEnterpriseTicket(corpId);


        for (PlatformDepartment platformDepartment : platformDepartmentList) {
            String originalYfyId = platformDepartment.getDepartmentId().replace(corpId + CommonConstants.SPLIT_UNDERLINE, "");
            log.info("original yfyDepartmentId is :{}", originalYfyId);

            if (!yfyDepartmentIdList.contains(originalYfyId)) {
                String redisKey = DELETE_PUBLIC_DEPARTMENT_PREFIX + platformDepartment.getYfyDepartmentId();
                if (checkNeedDeleteCount(redisKey)) {
                    // 删除部门
                    boolean deleteResult = v2ClientHelper.deleteDepartment(enterpriseParams.getAdminUserId(), platformDepartment.getYfyDepartmentId());
                    // 若删除成功，删除platform_departments数据
                    if (deleteResult) {
                        List<Long> ids = new ArrayList<>();
                        ids.add(platformDepartment.getId());
                        platformDepartmentMapper.deleteByIds(ids);
                    }
                }
            }
        }
    }

    private void syncYfyUsers(
            Map<String, YfyUser> yfyUserMap,
            Map<String, PlatformDepartment> platformDepartmentMap,
            Map<String, User> userMap,
            String corpId,
            EnterpriseParams enterpriseParams,
            Department rootDepartment,
            Map<Long, String> yfyUserIdMap) {


        for (Map.Entry<String, YfyUser> stringYfyUserEntry : yfyUserMap.entrySet()) {
            try {
                PublicUserBean syncPublicUserBean = new PublicUserBean();

                YfyUser value = stringYfyUserEntry.getValue();
                List<String> identifiers = new ArrayList<>();
                if(StringUtils.isNotBlank(value.getEmail())) {
                    identifiers.add(value.getEmail());
                } else {
                    identifiers.add(value.getPhone());
                }
                syncPublicUserBean.setIdentifiers(identifiers);
                // 没有手机号和状态不为1不进行同步
                if (CollectionUtils.isEmpty(syncPublicUserBean.getIdentifiers())) {
                    continue;
                }
                syncPublicUserBean.setName(value.getFullName());
                String userTicket = value.getId();
                User user = userMap.get(userTicket);

                if (Objects.isNull(user)) {
                    createUser(syncPublicUserBean, yfyUserIdMap, value, rootDepartment, platformDepartmentMap, corpId, enterpriseParams);
                } else {
                    editUser(user, value, syncPublicUserBean, platformDepartmentMap, rootDepartment, corpId, enterpriseParams);
                }
            } catch (Exception e) {
                log.info("syncCcWorkUsers error!", e);
            }
        }

    }


    private void createUser(PublicUserBean syncPublicUserBean,
                            Map<Long, String> yfyUserIdMap,
                            YfyUser value,
                            Department rootDepartment,
                            Map<String, PlatformDepartment> platformDepartmentMap,
                            String corpId,
                            EnterpriseParams enterpriseParams) {
        syncPublicUserBean.setPassword(customNacosConfig.getDefaultPassword());
        syncPublicUserBean.setSpaceTotal(USER_DEFAULT_SPACE_TOTAL);
        // 1.创建用户
        CreateUserResult createUserResult = v2ClientHelper.createUser(syncPublicUserBean, enterpriseParams.getAdminUserId());

        if (!Objects.isNull(createUserResult) && !CollectionUtils.isEmpty(createUserResult.getIdentifiersAlreadyInvited())) {
            log.info("identifiers_already_invited, identifiers info :{}", JSON.toJSONString(syncPublicUserBean.getIdentifiers()));
            User userInvited = userMapper.queryByEmail(createUserResult.getIdentifiersAlreadyInvited().get(0), Long.valueOf(enterpriseParams.getEnterpriseId()));

            String userTicketExist = yfyUserIdMap.get(userInvited.getId());
            if (!Objects.isNull(userInvited) && StringUtils.isEmpty(userTicketExist)) {
                UserResult userResult = new UserResult();
                userResult.setId(userInvited.getId());
                List<UserResult> list = new ArrayList<>();
                list.add(userResult);
                createUserResult.setUsers(list);
                log.info("identifiers_already_invited, userResult info :{}", JSON.toJSONString(userResult));
            }
        }
        if (!Objects.isNull(createUserResult) && !CollectionUtils.isEmpty(createUserResult.getUsers())) {
            // 2.创建用户成功后，维护用户的映射关系
            PlatformUser platformUser = new PlatformUser();
            String userTicket = value.getId();
            platformUser.setUserTicket(userTicket);
            platformUser.setPlatformId(Long.parseLong(enterpriseParams.getPlatformId()));
            platformUser.setEnterpriseTicket(corpId);
            platformUser.setUserInnerId(userTicket);
            platformUser.setUserId(createUserResult.getUsers().get(0).getId());
            platformUser.setPlatformUserAvatar("");
            platformUser.setValueBox("{}");
            platformUserMapper.insert(platformUser);
            // 3.修改用户名称
            UserResult userResult = v2ClientHelper.editUser(syncPublicUserBean, enterpriseParams.getAdminUserId(), createUserResult.getUsers().get(0).getId());

            // 3.修改将用户添加入对应部门中
            List<String> departmentIds = value.getDepartmentIds();
            if (!CollectionUtils.isEmpty(departmentIds)) {
                for (String departmentId : departmentIds) {
                    PlatformDepartment platformDepartment = platformDepartmentMap.get(corpId + CommonConstants.SPLIT_UNDERLINE + departmentId);
                    EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                    long yfyDepartmentId = 0;
                    if (!Objects.isNull(platformDepartment)) {
                        List<Long> addUserIds = new ArrayList<>();
                        addUserIds.add(createUserResult.getUsers().get(0).getId());
                        editDepartmentUserBean.setAddUserIds(addUserIds);
                        yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                    } else if (departmentId.equals("0")) {
                        List<Long> addUserIds = new ArrayList<>();
                        addUserIds.add(createUserResult.getUsers().get(0).getId());
                        editDepartmentUserBean.setAddUserIds(addUserIds);
                        yfyDepartmentId = rootDepartment.getId();
                    }
                    EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, enterpriseParams.getAdminUserId(), yfyDepartmentId);
                    if (!Objects.isNull(editDepartmentResult) && !CollectionUtils.isEmpty(editDepartmentResult.getAddUsers())) {
                        log.info("department add user success! user id :{}, department id :{}", userResult.getId(), yfyDepartmentId);
                    }
                }
            }

        }
    }

    private void editUser(User user,
                          YfyUser value,
                          PublicUserBean syncPublicUserBean,
                          Map<String, PlatformDepartment> platformDepartmentMap,
                          Department rootDepartment,
                          String corpId,
                          EnterpriseParams enterpriseParams) {
        log.info("yfyUser info :{}, user info:{}", JSON.toJSONString(value), JSON.toJSONString(user));
        // 1. 名称发生改变，更新用户名称
        if (!user.getName().equals(value.getFullName())) {
            log.info("user name has change! user id :{}", user.getId());
            v2ClientHelper.editUser(syncPublicUserBean, enterpriseParams.getAdminUserId(), user.getId());
        }

        // 2. 对比部门列表
        List<Long> yfyDepartmentIdList = new ArrayList<>();
        List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(user.getId());
        if (!CollectionUtils.isEmpty(departmentsUsers)) {
            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                int position = departmentsUser.getPosition();
                // 当用户职位是员工或者主管的时候，对比部门信息
                if (position == DEPARTMENT_POSITION_STAFF || position == DEPARTMENT_POSITION_DIRECTOR) {
                    yfyDepartmentIdList.add(departmentsUser.getDepartmentId());
                }
            }
        }

        List<String> departmentIdList = value.getDepartmentIds();
        List<Long> customDepartmentIdList = new ArrayList<>();
        // 将客户部门id转为亿方云id
        if (!CollectionUtils.isEmpty(departmentIdList)) {
            for (String departmentId : departmentIdList) {
                long yfyDepartmentId = 0;
                PlatformDepartment platformDepartment = platformDepartmentMap.get(corpId + CommonConstants.SPLIT_UNDERLINE + departmentId);
                // 将用户添加到对应的部门中
                if (!Objects.isNull(platformDepartment)) {
                    yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                } else if (departmentId.equals("0")) {
                    yfyDepartmentId = rootDepartment.getId();
                }
                customDepartmentIdList.add(yfyDepartmentId);
            }
        }

        List<Long> addDepartmentList = new ArrayList<>();
        List<Long> deleteDepartmentList = new ArrayList<>();

        if (CollectionUtils.isEmpty(departmentIdList) && CollectionUtils.isEmpty(departmentsUsers)) { // 数据源和亿方云都是空的，没有发生变化

        } else if (!CollectionUtils.isEmpty(departmentIdList) && CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云为空的，数据源不为空，发生改变
            // 将所有客户部门同步到亿方云
            for (Long yfyDepartmentId : customDepartmentIdList) {
                addDepartmentList.add(yfyDepartmentId);
            }
        } else if (CollectionUtils.isEmpty(departmentIdList) && !CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云不为空，数据源为空，发生改变
            // 将人员从部门中移除
            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                deleteDepartmentList.add(departmentsUser.getDepartmentId());
            }
        } else {
            // 找出需要删除的部门
            for (Long aLong : yfyDepartmentIdList) {
                if (!customDepartmentIdList.contains(aLong)) {
                    deleteDepartmentList.add(aLong);
                }
            }
            // 找出需要添加的部门
            for (Long aLong : customDepartmentIdList) {
                if (!yfyDepartmentIdList.contains(aLong)) {
                    addDepartmentList.add(aLong);
                }
            }
        }

        List<Long> userIds = new ArrayList<>();
        userIds.add(user.getId());

        // 循环删除部门成员
        if (!CollectionUtils.isEmpty(deleteDepartmentList)) {
            for (Long aLong : deleteDepartmentList) {
                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                editDepartmentUserBean.setDeleteUserIds(userIds);
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, enterpriseParams.getAdminUserId(), aLong);
                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                    log.info("department delete user success! user id :{}, department id :{}", user.getId(), aLong);
                }
            }
        }

        // 循环添加部门成员
        if (!CollectionUtils.isEmpty(addDepartmentList)) {
            for (Long aLong : addDepartmentList) {
                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                editDepartmentUserBean.setAddUserIds(userIds);
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, enterpriseParams.getAdminUserId(), aLong);
                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                    log.info("department add user success! user id :{}, department id :{}", user.getId(), aLong);
                }
            }
        }
    }


    private void deleteUser(Map<String, YfyUser> yfyUserMap, String corpId, EnterpriseParams enterpriseParams) {
        Set<String> yfyUserIdList = yfyUserMap.keySet();

        if (yfyUserIdList.size() == 0) {
            log.error("delete users when yfyUserIdList is null");
            return;
        }
        List<PlatformUser> platformUserList = platformUserMapper.queryByEnterpriseTicket(corpId);

        for (PlatformUser platformUser : platformUserList) {
            if (!yfyUserIdList.contains(platformUser.getUserTicket())) {
                log.info("need delete userTicket is :{}", platformUser.getUserTicket());
                String redisKey = DELETE_PUBLIC_USER_PREFIX + platformUser.getUserId();
                //累计删除超过5次，执行删除操作
                if (checkNeedDeleteCount(redisKey)) {
                    // 设置文件接受者
                    List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(platformUser.getUserId());
                    DeleteUserBean deleteUserBean = new DeleteUserBean();
                    if (!CollectionUtils.isEmpty(departmentsUsers)) {
                        List<Long> yfyDepartmentIdList = departmentsUsers.stream().map(DepartmentsUsers::getDepartmentId).collect(Collectors.toList());
                        List<Department> departments = departmentMapper.queryByIds(yfyDepartmentIdList);
                        for (Department department : departments) {
                            if (department.getDirectorId() != 0) {
                                deleteUserBean.setUserReceivedItems(department.getDirectorId());
                                break;
                            }
                        }
                    } else {
                        deleteUserBean.setUserReceivedItems(enterpriseParams.getAdminUserId());
                    }

                    // 删除用户
                    v2ClientHelper.deleteUser(enterpriseParams.getAdminUserId(), platformUser.getUserId(), deleteUserBean);
                }
            }
        }
    }

    @Override
    public String syncStatus(Integer enterpriseId, String syncType, String requestId) {
        String syncRedisKey = "";
        if (TYPE_WEIXIN.equals(syncType)) {
            syncRedisKey = SYNC_WEIXIN_PUBLIC_CLOUD_PREFIX + enterpriseId + requestId;
        } else if (TYPE_DING_TALK.equals(syncType)) {
            syncRedisKey = SYNC_DING_TALK_PUBLIC_CLOUD_PREFIX + enterpriseId + requestId;
        } else if (TYPE_CCWORK.equals(syncType) || TYPE_CUSTOM.equals(syncType)) {
            syncRedisKey = SYNC_CUSTOM_PUBLIC_CLOUD_PREFIX + enterpriseId + requestId;
        } else if (TYPE_FEI_SHU.equals(syncType)) {
            syncRedisKey = SYNC_FEISHU_PUBLIC_CLOUD_PREFIX + enterpriseId + requestId;
        } else {
            throw new ParamException("syncType is not support !");
        }
        String syncRedisValue = redisStringManager.get(syncRedisKey);
        if (StringUtils.isNotEmpty(syncRedisValue)) {
            return DOING_STATUS;
        }
        return DONE_STATUS;
    }

}
