package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 琳玛扫码登录
 */
@Component
@Slf4j
public class LinMaServiceImpl {


    @Resource
    private RedisStringManager redisStringManager;

    @Resource
    private ApiSyncHandler apiSyncHandler;


    public Object query(PlatformSyncConfigResponse platformSyncConfig, String params) {
        //琳玛需求：扫码返回的userId需进行处理换取真正的user_ticket
        String syncConfig = platformSyncConfig.getValueBox();
        if (syncConfig == null) {
            return null;
        }
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(syncConfig, APIConfigValueBox.class);
        apiConfigValueBox.check();
        String userId = (String) JSONPath.extract(params, "$.user_id");
        Boolean isCache = (Boolean) JSONPath.extract(params, "$.is_cache");
        String cacheUser = null;
        Map<String, String> userMap = new HashMap<>();
        if (isCache) {
            cacheUser = redisStringManager.get("LinMa" + userId);
        }

        if (cacheUser != null) {
            userMap.put("user_ticket", cacheUser);
            log.info("LinMa get user_ticket from cache, result: {}", JSON.toJSONString(userMap));
            return JSON.toJSONString(userMap);
        } else {
            JSONArray apiResult = apiSyncHandler.getApiConfigData(apiConfigValueBox, null, null);
            for (Object user : apiResult) {
                JSONObject userObject = (JSONObject) JSONObject.toJSON(user);
                if (userObject.get("person_code") != null && userObject.get("person_code").equals(userId)) {
                    Object person_type = userObject.get("person_type");
                    if (!Objects.isNull(person_type) && person_type.equals("office")) {
                        log.info("LinMa user person_type is office, user {}, ", user);
                        if (isCache) {
                            redisStringManager.set("LinMa" + userId, userId, 10 * 60L);
                        }
                        userMap.put("user_ticket", userId);
                        return JSON.toJSONString(userMap);
                    } else if (!Objects.isNull(person_type) && person_type.equals("store")) {
                        if (userObject.get("login_id") != null) {
                            log.info("LinMa user person_type is store, user {}, ", user);
                            if (isCache) {
                                redisStringManager.set("LinMa" + userId, userObject.get("login_id").toString(), 10 * 60L);
                            }
                            userMap.put("user_ticket", userObject.get("login_id").toString());
                            return JSON.toJSONString(userMap);
                        }
                    }
                }
            }
            log.info("LinMa userId is not exist, userId {}", userId);
            return null;
        }
    }

}
