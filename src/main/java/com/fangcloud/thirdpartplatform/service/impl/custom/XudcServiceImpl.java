package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

@Component
@Slf4j
public class XudcServiceImpl implements ApiExecuteService {

    // 接口类型
    final private static String XUDC_TYPE = "type";
    final private static String XUDC_TYPE_USER_INFO = "sync_user";
    final private static String XUDC_ALL_ORG_URL = "all_org_url";
    // 不激活用户的顶级部门列表
    final private static String XUDC_DEPT_LIST = "dept_list";
    final private static String XUDC_DATA_JSON_PATH = "$.Service.Body.Response.data.result";

    @Resource
    private HttpClientHelper httpClientHelper;

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        return null;
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        String type = headMap.get(XUDC_TYPE);

        Map<String, List<String>> deptPathMap = getDeptPathMap(headMap);

        switch (type){
            case XUDC_TYPE_USER_INFO:
                return getUserInfo(apiResult, deptPathMap, headMap);
            default:
                return null;
        }
    }

    private String getUserInfo(String apiResult, Map<String, List<String>> deptPathMap, Map<String, String> headMap) {
        JSONArray userArray  = (JSONArray) JSONPath.extract(apiResult, XUDC_DATA_JSON_PATH);
        String deptStr = headMap.get(XUDC_DEPT_LIST);
        List<String> deptList = JSON.parseObject(deptStr, List.class);

        for (Object userObject : userArray) {
            if(checkDept(userObject, deptPathMap, deptList)){
                ((JSONObject) userObject).put("personType","3");
            }
        }
        Map<String, Object> userMap = new HashMap<>();
        userMap.put("data", userArray);
        return JSON.toJSONString(userMap);
    }

    private boolean checkDept(Object userObject, Map<String, List<String>> deptPathMap, List<String> deptList) {
        List<String> orgIdList = (List<String>) JSONPath.extract(userObject.toString(), "$.positionMemberList[*].orgId");
        for (String s : orgIdList) {
            List<String> list = deptPathMap.get(s);
            for (String s1 : deptList) {
                if(list.contains(s1)){
                    return true;
                }
            }
        }
        return false;
    }

    private Map<String, List<String>> getDeptPathMap(Map<String, String> headMap) {
        String url = headMap.get(XUDC_ALL_ORG_URL);
        try {
            httpClientHelper.setHeaders(headMap);
            Response response = httpClientHelper.getResponse(url);
            String userJson = Objects.requireNonNull(response.body()).string();
            return buildDeptPathMap(userJson);
        } catch (IOException e) {
            log.error("get h3c login error url {}", url, e);
            return null;
        }

    }

    private Map<String, List<String>> buildDeptPathMap(String userJson) {
        JSONArray deptArray  = (JSONArray) JSONPath.extract(userJson, XUDC_DATA_JSON_PATH);

        Map<String, List<String>> deptPathMap = new HashMap<>();
        Map<String, JSONObject> deptMap = new HashMap<>();
        for (Object object : deptArray) {
            JSONObject jsonObject = (JSONObject) object;
            deptMap.put((String) jsonObject.get("id"), jsonObject);
        }


        for (Object deptObj : deptArray) {
            JSONObject deptJsonObject = (JSONObject) deptObj;
            deptPathMap.put((String) deptJsonObject.get("id"), getDeptPathList(deptJsonObject, deptMap));
        }
        return deptPathMap;
    }

    public static List<String> getDeptPathList(JSONObject deptJsonObject, Map<String, JSONObject> deptMap) {
        JSONObject parentJsonObject = deptMap.get(deptJsonObject.get("parentId"));
        List<String> list = new ArrayList();
        while (!Objects.isNull(parentJsonObject)){
            list.add((String) parentJsonObject.get("id"));
            parentJsonObject = deptMap.get(parentJsonObject.get("parentId"));
        }
        return list;
    }


    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.XUDC.getDesc();
    }
}
