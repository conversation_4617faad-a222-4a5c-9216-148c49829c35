package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.entity.sync.*;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.SyncServiceImpl;
import com.fangcloud.thirdpartplatform.utils.ByteconverUtils;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Component
@Slf4j
public class PublicSyncHandler {

    private static String DELETE_PUBLIC_USER_PREFIX = "DELETE_PUBLIC_USER_";
    private static String DELETE_PUBLIC_DEPARTMENT_PREFIX = "DELETE_PUBLIC_DEPARTMENT_";
    private static Long DEPARTMENT_POSITION_STAFF = 0L;
    private static Long DEPARTMENT_POSITION_DIRECTOR = 2L;

    @Resource
    private ApiSyncHandler apiSyncHandler;
    @Resource
    private CustomNacosConfig customNacosConfig;
    @Resource
    private V2ClientHelper v2ClientHelper;
    @Autowired
    private PlatformUserMapper platformUserMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;
    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;
    @Resource
    private RedisStringManager redisStringManager;

    @Resource
    private SyncServiceImpl syncServiceImpl;

    @Resource
    private PlatformSyncTaskFailLogsMapper platformSyncTaskFailLogsMapper;

    public void syncPublicByApi(List<YfyUser> yfyUsers, List<YfyDepartment> yfyDepartments, PlatformSyncConfig platformSyncConfig) {
        APIConfigValueBox apiConfigValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        if (platformSyncConfig.getSyncType().equals(SyncTypeEnum.USERS.getSyncType())) {
            //根据同步配置获取用户数据并且转换成yfyUsers
            apiSyncHandler.getYfyUsersByConfigValueBox(yfyUsers, apiConfigValueBox, platformSyncConfig.getEnterpriseId());
        }
        if (platformSyncConfig.getSyncType().equals(SyncTypeEnum.DEPARTMENT.getSyncType())) {
            //根据同步配置获取用户数据并且转换成yfyDepartments
            apiSyncHandler.getYfyDepartmentsByConfigValueBox(yfyDepartments, apiConfigValueBox, platformSyncConfig.getEnterpriseId());
        }
    }

    public void syncCustomPublicDepartments(
            List<String> departmentIdiLst,
            Map<String, YfyDepartment> customDepartmentMap,
            Map<String, Department> departmentMap,
            Map<String, PlatformDepartment> platformDepartmentMap,
            Map<String, User> userMap,
            Department rootDepartment,
            Enterprise enterpriseInfo,
            Integer taskId,
            AtomicInteger addRows,
            AtomicInteger updateRows,
            AtomicInteger errorRows) {

        // 1、获取该层级的需要同步的部门列表
        List<YfyDepartment> customDepartmentList = new ArrayList<>();
        for (String departmentId : departmentIdiLst) {
            customDepartmentList.add(customDepartmentMap.get(departmentId));
        }

        // 2.构造部门信息
        for (YfyDepartment yfyDepartment : customDepartmentList) {
            try {
                Department department = departmentMap.get(yfyDepartment.getId());
                if (Objects.isNull(department)) {// 该部门不存在，是新增的

                    PublicDepartmentBean syncPublicDepartmentBean = buildCreatePublicDepartmentBean(yfyDepartment, departmentMap, userMap);
                    if (Objects.isNull(syncPublicDepartmentBean)) {
                        continue;
                    }
                    //  创建部门
                    createPlatformDepartment(syncPublicDepartmentBean, platformDepartmentMap, departmentMap, yfyDepartment, enterpriseInfo, taskId, addRows, errorRows);

                } else { // 该部门存在，进行对比

                    PublicDepartmentBean syncPublicDepartmentBean = compareAndBuildEditPublicDepartmentBean(yfyDepartment, department, userMap, rootDepartment, departmentMap, enterpriseInfo);

                    if (Objects.isNull(syncPublicDepartmentBean)) {
                        continue;
                    }
                    // 更新平台部门信息
                    updatePlatformDepartment(syncPublicDepartmentBean, platformDepartmentMap, departmentMap, yfyDepartment, department, enterpriseInfo, taskId, updateRows, errorRows);
                }
            } catch (Exception e) {
                log.info("syncCustomPublicDepartments error!", e);
            }
        }
    }

    private PublicDepartmentBean buildCreatePublicDepartmentBean(YfyDepartment yfyDepartment, Map<String, Department> departmentMap, Map<String, User> userMap) {

        PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
        // 2.1 设置部门名称
        syncPublicDepartmentBean.setDepartmentName(yfyDepartment.getName());

        // 2.2 设置父部门id
        if (StringUtils.isBlank(yfyDepartment.getParentId())) { // 次顶级部门，父部门设置为null
            syncPublicDepartmentBean.setParentId(0L);
        } else {// 父部门需要换取真正的部门id
            Department departmentParent = departmentMap.get(yfyDepartment.getParentId());
            if (!Objects.isNull(departmentParent)) {
                syncPublicDepartmentBean.setParentId(departmentParent.getId());
            }
        }

        // 2.3 设置主管id
        String directorId = yfyDepartment.getDirectorId();
        if (StringUtils.isNotBlank(directorId)) {
            User user = userMap.get(directorId);
            if (!Objects.isNull(user)) {
                syncPublicDepartmentBean.setDirectorId(user.getId());
            }
        }

        // 2.4 设置部门空间，默认设置100G
        syncPublicDepartmentBean.setSpaceTotal(ByteconverUtils.getConver(yfyDepartment.getSpaceTotal()));
        // 2.5 设置是否创建公共资料库，默认为true
        syncPublicDepartmentBean.setCreateCommonFolder(yfyDepartment.getPublicFolder());
        // 2.6 设置部门是否自动接受协作，默认为true
        syncPublicDepartmentBean.setCollabAutoAccepted(yfyDepartment.getCollabAutoAccepted());
        // 2.7 设置排序
        syncPublicDepartmentBean.setOrder(yfyDepartment.getOrder());
        // 2.7 设置是否隐藏手机号，默认和上级部门保持一致
        // 2.8 设置是否禁止分享，默认和上级部门保持一致
        // 2.9 设置是否是否开启水印预览，默认和上级部门保持一致
        return syncPublicDepartmentBean;
    }

    private void createPlatformDepartment(PublicDepartmentBean syncPublicDepartmentBean, Map<String, PlatformDepartment> platformDepartmentMap, Map<String, Department> departmentMap, YfyDepartment yfyDepartment, Enterprise enterpriseInfo, Integer taskId, AtomicInteger addRows, AtomicInteger errorRows) {

        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.createDepartment(syncPublicDepartmentBean, enterpriseInfo.getAdminUserId());

        if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {
            // 若没有错误信息代表部门创建成功，插入部门信息
            // 创建部门映射关系
            PlatformDepartment platformDepartment = new PlatformDepartment();
            platformDepartment.setPlatformId(Long.valueOf(enterpriseInfo.getPlatformId()));
            platformDepartment.setEnterpriseTicket(String.valueOf(enterpriseInfo.getPlatformId()));
            platformDepartment.setDepartmentId(yfyDepartment.getId());
            platformDepartment.setYfyDepartmentId(syncPublicDepartmentResult.getId());
            platformDepartment.setName(syncPublicDepartmentResult.getName());

            platformDepartmentMapper.insert(platformDepartment);

            // 将新生成的部门信息放入总的部门数据中
            platformDepartmentMap.put(yfyDepartment.getId(), platformDepartment);

            Department department = new Department();
            department.setName(syncPublicDepartmentResult.getName());
            department.setId(syncPublicDepartmentResult.getId());
            MiniUser director = syncPublicDepartmentResult.getDirector();
            if (!Objects.isNull(director)) {
                department.setDirectorId(director.getId());
            }
            department.setParentId(syncPublicDepartmentResult.getParentId());

            // 将新生成的部门信息放入总的部门数据中
            departmentMap.put(yfyDepartment.getId(), department);
            addRows.getAndAdd(1);
        } else {
            recordErrorLog(enterpriseInfo, null, yfyDepartment, syncPublicDepartmentResult.getErrorMessage(), taskId);
            errorRows.getAndAdd(1);
        }
    }

    /**
     * 记录同步错误日志
     */
    public void recordErrorLog(Enterprise enterprise,
                               YfyUser yfyUser,
                               YfyDepartment yfyDepartment,
                               String errorMessage, Integer taskId) {

        int enterpriseId = (int) enterprise.getId();

        PlatformSyncTaskFailLogs platformSyncTaskFailLogs = new PlatformSyncTaskFailLogs();
        platformSyncTaskFailLogs.setSyncTaskId(taskId);
        platformSyncTaskFailLogs.setCreated(new Date().getTime());
        platformSyncTaskFailLogs.setCustomeId(yfyUser == null ? yfyDepartment.getId() : yfyUser.getId());
        platformSyncTaskFailLogs.setEnterpriseId(enterpriseId);
        platformSyncTaskFailLogs.setReason(errorMessage);
        platformSyncTaskFailLogs.setValueBox((yfyUser == null ? JSON.toJSONString(yfyDepartment) : JSON.toJSONString(yfyUser)));
        // 批量插入异常日志
        platformSyncTaskFailLogsMapper.insert(platformSyncTaskFailLogs);
    }

    private PublicDepartmentBean compareAndBuildEditPublicDepartmentBean(YfyDepartment yfyDepartment, Department department, Map<String, User> userMap, Department rootDepartment, Map<String, Department> departmentMap, Enterprise enterpriseInfo) {
        boolean hasChange = false;

        log.info("customDepartment info :{}, department:{}", JSON.toJSONString(yfyDepartment), JSON.toJSONString(department));
        // 对比主管id
        String directorId = yfyDepartment.getDirectorId();
        if (StringUtils.isBlank(directorId)) {
            if (department.getDirectorId() != 0) {
                hasChange = true;
            }
        } else {
            User user = userMap.get(directorId);
            if (Objects.isNull(user)) {
                hasChange = true;
            } else if (user.getId() != department.getDirectorId()) {
                hasChange = true;
            }
        }

        // 对比名称
        if (!department.getName().equals(yfyDepartment.getName())) {
            hasChange = true;
        }
        // 对比order
        if (department.getOrder() != yfyDepartment.getOrder()) {
            hasChange = true;
        }
        // 对比父部门id
        String parentId = yfyDepartment.getParentId();
        if (StringUtils.isBlank(parentId) && department.getParentId() != rootDepartment.getId()) {
            hasChange = true;
        } else {
            Department parentDepartment = departmentMap.get(parentId);
            if (!Objects.isNull(parentDepartment) && parentDepartment.getId() != department.getParentId()) {
                hasChange = true;
            }
        }

        // 部门发生变化
        if (hasChange) {
            PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
            // 1. 设置部门名称
            syncPublicDepartmentBean.setDepartmentName(yfyDepartment.getName());

            // 2. 设置父部门id
            if (StringUtils.isBlank(parentId)) { // 次顶级部门，父部门设置为0
                log.info("parent is top!");
                syncPublicDepartmentBean.setParentId(0L);
            } else {// 父部门需要换取真正的部门id
                Department parentDepartment = departmentMap.get(parentId);
                if (!Objects.isNull(parentDepartment)) {
                    syncPublicDepartmentBean.setParentId(parentDepartment.getId());
                }
            }

            // 3. 设置主管id
            if (StringUtils.isNotBlank(directorId)) {
                User user = userMap.get(directorId);
                if (!Objects.isNull(user)) {
                    syncPublicDepartmentBean.setDirectorId(user.getId());
                }
            }
            // 4. 设置排序
            syncPublicDepartmentBean.setOrder(yfyDepartment.getOrder());

            return syncPublicDepartmentBean;
        }
        return null;
    }

    private void updatePlatformDepartment(PublicDepartmentBean syncPublicDepartmentBean, Map<String, PlatformDepartment> platformDepartmentMap, Map<String, Department> departmentMap, YfyDepartment yfyDepartment, Department department, Enterprise enterpriseInfo, Integer taskId, AtomicInteger updateRows, AtomicInteger errorRows) {
        //  修改部门
        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.editDepartment(syncPublicDepartmentBean, department.getId(), enterpriseInfo.getAdminUserId());
        // 若部门修改成功，维护部门映射关系
        if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {

            // 若部门的name发生改变，更新platformDepartment的name
            PlatformDepartment platformDepartment = platformDepartmentMap.get(yfyDepartment.getId());
            if (!syncPublicDepartmentResult.getName().equals(platformDepartment.getName())) {
                platformDepartment.setName(syncPublicDepartmentResult.getName());
                platformDepartmentMapper.updateNameById(platformDepartment);
            }
            Department departmentResult = new Department();
            departmentResult.setName(syncPublicDepartmentResult.getName());
            MiniUser director = syncPublicDepartmentResult.getDirector();
            if (!Objects.isNull(director)) {
                departmentResult.setDirectorId(director.getId());
            }
            departmentResult.setParentId(syncPublicDepartmentResult.getParentId());
            departmentResult.setId(syncPublicDepartmentResult.getId());
            departmentResult.setOrder(syncPublicDepartmentResult.getOrder());

            // 将新生成的部门信息放入总的部门数据中
            departmentMap.put(yfyDepartment.getId(), departmentResult);
            updateRows.getAndAdd(1);
        } else {
            recordErrorLog(enterpriseInfo, null, yfyDepartment, syncPublicDepartmentResult.getErrorMessage(), taskId);
            errorRows.getAndAdd(1);
        }
    }

    /**
     * 删除客户已经删除的部门
     */
    public void deleteDepartment(List<YfyDepartment> yfyDepartments, Enterprise enterpriseInfo, Integer taskId, AtomicInteger updateRows, AtomicInteger errorRows) {
        List<String> customDepartmentIdList = yfyDepartments.stream().map(YfyDepartment::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(customDepartmentIdList)) {
            log.error("delete department when customDepartmentIdList is null");
            return;
        }

        List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByEnterpriseTicket(String.valueOf(enterpriseInfo.getPlatformId()));


        for (PlatformDepartment platformDepartment : platformDepartmentList) {
            String originalCustomId = platformDepartment.getDepartmentId();
            log.info("original originalCustomId is :{}", originalCustomId);

            // 亿方云存在，客户数据源不存在，进行删除判断
            if (!customDepartmentIdList.contains(originalCustomId)) {
                String redisKey = DELETE_PUBLIC_DEPARTMENT_PREFIX + platformDepartment.getYfyDepartmentId();
                if (checkNeedDeleteCount(redisKey)) {
                    YfyDepartment yfyDepartment = new YfyDepartment();
                    yfyDepartment.setId(originalCustomId);
                    // 删除部门
                    boolean deleteResult = v2ClientHelper.deleteDepartment(enterpriseInfo.getAdminUserId(), platformDepartment.getYfyDepartmentId());
                    // 若删除成功，删除platform_departments数据
                    if (deleteResult) {
                        List<Long> ids = new ArrayList<>();
                        ids.add(platformDepartment.getId());
                        platformDepartmentMapper.deleteByIds(ids);
                        updateRows.getAndAdd(1);
                    } else {
                        recordErrorLog(enterpriseInfo, null, yfyDepartment, "删除部门失败", taskId);
                        errorRows.getAndAdd(1);
                    }
                }
            }
        }

    }

    /**
     * 构建同步的部门信息
     */
    public void syncCustomUsers(
            Map<String, YfyUser> customUserMap,
            Map<String, PlatformDepartment> platformDepartmentMap,
            Map<String, User> userMap,
            Map<Long, String> yfyUserIdMap,
            Enterprise enterpriseInfo,
            Boolean isSyncDepartment, Integer taskId, AtomicInteger addRows, AtomicInteger updateRows, AtomicInteger errorRows, String password) {


        for (Map.Entry<String, YfyUser> stringCustomUserEntry : customUserMap.entrySet()) {
            try {
                PublicUserBean syncPublicUserBean = new PublicUserBean();

                YfyUser value = stringCustomUserEntry.getValue();
                if (StringUtils.isBlank(value.getId()) || StringUtils.isAllBlank(value.getPhone(), value.getEmail())) {
                    continue;
                }
                List<String> list = new ArrayList<>();
                String email = value.getEmail();
                String phone = value.getPhone();
                if (StringUtils.isNotBlank(email)) {
                    list.add(email);
                } else {
                    if (phone.contains(SyncTaskConstants.USER_PHONE_CN)) {
                        phone = phone.substring(Math.max(0, phone.length() - 11));
                    }
                    list.add(phone);
                }
                syncPublicUserBean.setIdentifiers(list);
                syncPublicUserBean.setName(value.getFullName());
                User user = userMap.get(value.getId());
                if (Objects.isNull(user)) {
                    // user不存在，创建用户
                    createUser(syncPublicUserBean, yfyUserIdMap, value, platformDepartmentMap, enterpriseInfo, taskId, addRows, errorRows, password);
                } else {
                    // user存在，进行对比是否发生变化
                    editUser(user, value, syncPublicUserBean, platformDepartmentMap, enterpriseInfo, isSyncDepartment, taskId, updateRows, errorRows);

                }
            } catch (Exception e) {
                log.info("syncCustomUsers error!", e);
            }
        }

    }

    /**
     * 创建用户
     */
    private void createUser(PublicUserBean syncPublicUserBean, Map<Long, String> yfyUserIdMap, YfyUser value, Map<String, PlatformDepartment> platformDepartmentMap, Enterprise enterpriseInfo, Integer taskId, AtomicInteger addRows, AtomicInteger errorRows,String password) {
        syncPublicUserBean.setPassword(password);
        syncPublicUserBean.setSpaceTotal(ByteconverUtils.getConver(value.getSpaceTotal()));
        // 1.创建用户
        CreateUserResult createUserResult = v2ClientHelper.createUser(syncPublicUserBean, enterpriseInfo.getAdminUserId());
        if (StringUtils.isNotBlank(createUserResult.getErrorMessage())) {
            recordErrorLog(enterpriseInfo, value, null, createUserResult.getErrorMessage(), taskId);
            errorRows.getAndAdd(1);
            return;
        }
        if (value.getStatus().equals("3")) {
            if (createUserResult.getUsers() != null) {
                syncServiceImpl.setUserActiveDisable(createUserResult.getUsers().get(0).getId(), enterpriseInfo.getAdminUserId());
            }
        }
        if (!Objects.isNull(createUserResult) && !CollectionUtils.isEmpty(createUserResult.getIdentifiersAlreadyInvited())) {
            log.info("identifiers_already_invited, identifiers info :{}", JSON.toJSONString(syncPublicUserBean.getIdentifiers()));
            User userInvited = null;
            if (StringUtils.isNotBlank(value.getEmail())) {
                userInvited = userMapper.queryByEmail(createUserResult.getIdentifiersAlreadyInvited().get(0), Long.valueOf(enterpriseInfo.getId()));
            } else {
                userInvited = userMapper.queryByPhone(createUserResult.getIdentifiersAlreadyInvited().get(0), Long.valueOf(enterpriseInfo.getId()));
            }

            String userTicketExist = yfyUserIdMap.get(userInvited.getId());
            if (!Objects.isNull(userInvited) && StringUtils.isEmpty(userTicketExist)) {
                UserResult userResult = new UserResult();
                userResult.setId(userInvited.getId());
                List<UserResult> list = new ArrayList<>();
                list.add(userResult);
                createUserResult.setUsers(list);
                log.info("identifiers_already_invited, userResult info :{}", JSON.toJSONString(userResult));
            }
        }
        if (!Objects.isNull(createUserResult) && !CollectionUtils.isEmpty(createUserResult.getUsers())) {
            // 2.创建用户成功后，维护用户的映射关系
            PlatformUser platformUser = new PlatformUser();
            platformUser.setUserTicket(value.getId());
            platformUser.setPlatformId(Long.valueOf(enterpriseInfo.getPlatformId()));
            platformUser.setEnterpriseTicket(String.valueOf(enterpriseInfo.getPlatformId()));
            platformUser.setUserInnerId(value.getId());
            platformUser.setUserId(createUserResult.getUsers().get(0).getId());
            platformUser.setPlatformUserAvatar("");
            platformUser.setValueBox("{}");
            platformUserMapper.insert(platformUser);

            // 3.修改用户名称
            UserResult userResult = v2ClientHelper.editUser(syncPublicUserBean, enterpriseInfo.getAdminUserId(), createUserResult.getUsers().get(0).getId());
            if (StringUtils.isNotBlank(userResult.getErrorMessage())) {
                recordErrorLog(enterpriseInfo, value, null, userResult.getErrorMessage(), taskId);
                errorRows.getAndAdd(1);
            }
            // 3.修改将用户添加入对应部门中
            List<String> departmentIdList = value.getDepartmentIds();
            if (!CollectionUtils.isEmpty(departmentIdList)) {
                for (String departmentId : departmentIdList) {
                    PlatformDepartment platformDepartment = platformDepartmentMap.get(departmentId);
                    // 将用户添加到对应的部门中

                    EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                    long yfyDepartmentId = 0;
                    if (!Objects.isNull(platformDepartment)) {
                        List<Long> addUserIds = new ArrayList<>();
                        addUserIds.add(createUserResult.getUsers().get(0).getId());
                        editDepartmentUserBean.setAddUserIds(addUserIds);
                        yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                    }
                    EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, enterpriseInfo.getAdminUserId(), yfyDepartmentId);
                    if (!Objects.isNull(editDepartmentResult) && !CollectionUtils.isEmpty(editDepartmentResult.getAddUsers())) {
                        addRows.getAndAdd(1);
                        log.info("department add user success! user id :{}, department id :{}", userResult.getId(), yfyDepartmentId);
                    } else {
                        recordErrorLog(enterpriseInfo, value, null, editDepartmentResult.getErrorMessage(), taskId);
                        errorRows.getAndAdd(1);
                    }
                }
            }

        }
    }

    /**
     * 编辑用户
     */
    private void editUser(User user, YfyUser value, PublicUserBean syncPublicUserBean, Map<String, PlatformDepartment> platformDepartmentMap, Enterprise enterpriseInfo, Boolean isSyncDepartment, Integer taskId, AtomicInteger updateRows, AtomicInteger errorRows) {
        log.info("customUser info :{}, user info:{}", JSON.toJSONString(value), JSON.toJSONString(user));
        // 1. 名称发生改变，更新用户名称
        if (!user.getName().equals(value.getFullName())) {
            log.info("user name has change! user id :{}", user.getId());
            UserResult userResult = v2ClientHelper.editUser(syncPublicUserBean, enterpriseInfo.getAdminUserId(), user.getId());
            if (StringUtils.isNotBlank(userResult.getErrorMessage())) {
                recordErrorLog(enterpriseInfo, value, null, userResult.getErrorMessage(), taskId);
                errorRows.getAndAdd(1);
            }
        }

        if (!isSyncDepartment) {
            return;
        }

        // 2. 对比部门列表
        List<Long> yfyDepartmentIdList = new ArrayList<>();
        List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(user.getId());
        if (!CollectionUtils.isEmpty(departmentsUsers)) {
            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                int position = departmentsUser.getPosition();
                // 当用户职位是员工或者主管的时候，对比部门信息
                if (position == DEPARTMENT_POSITION_STAFF || position == DEPARTMENT_POSITION_DIRECTOR) {
                    yfyDepartmentIdList.add(departmentsUser.getDepartmentId());
                }
            }
        }

        List<String> departmentIdList = value.getDepartmentIds();
        List<Long> customDepartmentIdList = new ArrayList<>();
        // 将客户部门id转为亿方云id
        if (!CollectionUtils.isEmpty(departmentIdList)) {
            for (String departmentId : departmentIdList) {
                long yfyDepartmentId = 0;
                PlatformDepartment platformDepartment = platformDepartmentMap.get(departmentId);
                // 将用户添加到对应的部门中
                if (!Objects.isNull(platformDepartment)) {
                    yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                }
                customDepartmentIdList.add(yfyDepartmentId);
            }
        }

        List<Long> addDepartmentList = new ArrayList<>();
        List<Long> deleteDepartmentList = new ArrayList<>();

        if (CollectionUtils.isEmpty(departmentIdList) && CollectionUtils.isEmpty(departmentsUsers)) { // 数据源和亿方云都是空的，没有发生变化

        } else if (!CollectionUtils.isEmpty(departmentIdList) && CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云为空的，数据源不为空，发生改变
            // 将所有客户部门同步到亿方云
            for (Long yfyDepartmentId : customDepartmentIdList) {
                addDepartmentList.add(yfyDepartmentId);
            }
        } else if (CollectionUtils.isEmpty(departmentIdList) && !CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云不为空，数据源为空，发生改变
            // 将人员从部门中移除
            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                deleteDepartmentList.add(departmentsUser.getDepartmentId());
            }
        } else {
            // 找出需要删除的部门
            for (Long aLong : yfyDepartmentIdList) {
                if (!customDepartmentIdList.contains(aLong)) {
                    deleteDepartmentList.add(aLong);
                }
            }
            // 找出需要添加的部门
            for (Long aLong : customDepartmentIdList) {
                if (!yfyDepartmentIdList.contains(aLong)) {
                    addDepartmentList.add(aLong);
                }
            }
        }

        List<Long> userIds = new ArrayList<>();
        userIds.add(user.getId());

        // 循环删除部门成员
        if (!CollectionUtils.isEmpty(deleteDepartmentList)) {
            for (Long aLong : deleteDepartmentList) {
                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                editDepartmentUserBean.setDeleteUserIds(userIds);
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, enterpriseInfo.getAdminUserId(), aLong);
                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                    log.info("department delete user success! user id :{}, department id :{}", user.getId(), aLong);
                }
            }
        }

        // 循环添加部门成员
        if (!CollectionUtils.isEmpty(addDepartmentList)) {
            for (Long aLong : addDepartmentList) {
                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                editDepartmentUserBean.setAddUserIds(userIds);
                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, enterpriseInfo.getAdminUserId(), aLong);
                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                    log.info("department add user success! user id :{}, department id :{}", user.getId(), aLong);
                }
            }
        }
        updateRows.getAndAdd(1);
    }

    /**
     * 客户已经删除的用户进行特殊处理
     */
    public void deleteUser(Map<String, YfyUser> customUserMap, Map<String, User> userMap, Enterprise enterpriseInfo, Integer taskId, AtomicInteger addRows, AtomicInteger updateRows, AtomicInteger errorRows) {
        Set<String> customUserIdList = customUserMap.keySet();

        if (customUserIdList.size() == 0) {
            log.error("delete users when customUserIdList is null");
            return;
        }
        List<PlatformUser> platformUserList = platformUserMapper.queryByEnterpriseTicket(String.valueOf(enterpriseInfo.getPlatformId()));

        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseInfo.getId()+ "");
        Boolean customSyncUserSetDepartmentIdNullEnable = null;
        Boolean customSyncUserSetActiveClose = null;
        String customUserNameSuffix = null;
        // 判断数据库中全局配置，若配置不为空，取数据库配置，若为空取启动项配置
        if(CollectionUtils.isEmpty(configMap)){
            customSyncUserSetDepartmentIdNullEnable = customNacosConfig.getCustomSyncUserSetDepartmentIdNullEnable();
            customSyncUserSetActiveClose = customNacosConfig.getCustomSyncUserSetActiveClose();
            customUserNameSuffix = customNacosConfig.getCustomUserNameSuffix();
        }else {
            customSyncUserSetDepartmentIdNullEnable = Boolean.valueOf(configMap.get(PlatformGlobalConfigKeyEnum.SET_DEPARTMENT_ID_NULL_ENABLE.getKey()));
            customSyncUserSetActiveClose = Boolean.valueOf(configMap.get(PlatformGlobalConfigKeyEnum.SET_USER_ACTIVE_CLOSE.getKey()));
            customUserNameSuffix = configMap.get(PlatformGlobalConfigKeyEnum.USER_NAME_SUFFIX.getKey());
        }


        for (PlatformUser platformUser : platformUserList) {
            User user = userMap.get(platformUser.getUserTicket());
            // 亿方云存在，客户数据源不存在，修改名称后缀,放入待分配,修改成未激活
            if (!customUserIdList.contains(platformUser.getUserTicket())) {
                YfyUser yfyUser = new YfyUser();
                yfyUser.setId(platformUser.getUserTicket());
                log.info("need delete userTicket is :{}", platformUser.getUserTicket());
                String redisKey = DELETE_PUBLIC_USER_PREFIX + platformUser.getUserId();
                //累计删除超过3次，执行特殊处理操作
                if (checkNeedDeleteCount(redisKey)) {
                    PublicUserBean syncPublicUserBean = new PublicUserBean();
                    if (user == null && !user.isActive()) {
                        continue;
                    }
                    if (StringUtils.isNotBlank(customUserNameSuffix)) {
                        syncPublicUserBean.setName(user.getFullName() + customUserNameSuffix);
                        UserResult userResult = v2ClientHelper.editUser(syncPublicUserBean, enterpriseInfo.getAdminUserId(), platformUser.getUserId());
                        if (StringUtils.isNotBlank(userResult.getErrorMessage())) {
                            recordErrorLog(enterpriseInfo, yfyUser, null, userResult.getErrorMessage(), taskId);
                            errorRows.getAndAdd(1);
                        }
                    }
                    if (customSyncUserSetDepartmentIdNullEnable) {
                        List<Long> deleteDepartmentList = new ArrayList<>();
                        List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(platformUser.getUserId());
                        if (!CollectionUtils.isEmpty(departmentsUsers)) {
                            for (DepartmentsUsers departmentsUser : departmentsUsers) {
                                deleteDepartmentList.add(departmentsUser.getDepartmentId());
                            }
                        }
                        List<Long> userIds = new ArrayList<>();
                        userIds.add(platformUser.getUserId());
                        // 循环删除部门成员
                        if (!CollectionUtils.isEmpty(deleteDepartmentList)) {
                            for (Long aLong : deleteDepartmentList) {
                                EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                                editDepartmentUserBean.setDeleteUserIds(userIds);
                                EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, enterpriseInfo.getAdminUserId(), aLong);
                                if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                                    log.info("department delete user success! user id :{}, department id :{}", platformUser.getUserId(), aLong);
                                } else {
                                    recordErrorLog(enterpriseInfo, yfyUser, null, editDepartmentResult.getErrorMessage(), taskId);
                                    errorRows.addAndGet(1);
                                }
                            }
                        }
                    }
                    if (customSyncUserSetActiveClose) {
                        SyncActiveUsersBatchBean syncActiveUsersBatchBean = new SyncActiveUsersBatchBean();
                        syncActiveUsersBatchBean.setUserIds(Arrays.asList(platformUser.getUserId()));
                        syncActiveUsersBatchBean.setStatus("disable");
                        SyncActiveUsersBatchOutput syncActiveUsersBatchOutput = v2ClientHelper.active_users_batch(syncActiveUsersBatchBean, enterpriseInfo.getAdminUserId());
                        if(!Objects.isNull(syncActiveUsersBatchOutput) || !CollectionUtils.isEmpty(syncActiveUsersBatchOutput.getFailUserIds())) {
                            List<SyncActiveFailUserId> failUserIds = syncActiveUsersBatchOutput.getFailUserIds();
                            recordErrorLog(enterpriseInfo, yfyUser, null, failUserIds.get(0).getMsg(), taskId);
                            errorRows.getAndAdd(1);
                        }
                    }
                }
            }
        }
    }

    /**
     * 校验需要删除次数
     *
     * @param redisKey
     * @return
     */
    public boolean checkNeedDeleteCount(String redisKey) {
        String deleteCountStr = redisStringManager.get(redisKey);
        Integer deleteCount = StringUtils.isEmpty(deleteCountStr) ? 1 : Integer.valueOf(deleteCountStr);
        if (deleteCount >= 3) {
            log.info("data need delete, redisKey :{}, deleteCount :{}", redisKey, deleteCount);
            return true;
        } else {
            redisStringManager.increment(redisKey, 1);
            log.info("data do not need delete, redisKey :{}, deleteCount :{}", redisKey, deleteCount);
            return false;
        }

    }

}
