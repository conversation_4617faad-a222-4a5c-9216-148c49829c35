package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.EnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.EnterpriseDto;
import com.fangcloud.thirdpartplatform.helper.ChameleonClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.*;

@Component
@Slf4j
public class WmpiServiceImpl implements ApiExecuteService {

    @Value("${custom.base.url}")
    private String baseHost;

    @Autowired
    private EnterpriseMapper enterpriseMapper;

    @Resource
    private ChameleonClientHelper chameleonClientHelper;

    //SSO地址
    private static final String SSO_URL = "%s/v2/sso/oauth/postlogin";

    public EnterpriseDto getEnterprises() {
        List<Enterprise> enterpriseList =  enterpriseMapper.query();
        if (enterpriseList != null && enterpriseList.size() > 0) {
            EnterpriseDto enterpriseDto = new EnterpriseDto();

            List<EnterpriseDto.EnterpriseInfo> enterpriseInfo = new ArrayList<>();
            String redirectUri=String.format(SSO_URL,baseHost);
            for (Enterprise enterprise : enterpriseList) {
                if (enterprise.getId() == 114 || enterprise.isForbiddenLogin()) {
                    continue;
                }
                EnterpriseDto.EnterpriseInfo enterpriseInfoData =  new EnterpriseDto.EnterpriseInfo();
                enterpriseInfoData.setIdAlias(Base64Utils.encode(String.valueOf(enterprise.getId())));
                enterpriseInfoData.setId(enterprise.getId());
                enterpriseInfoData.setName(enterprise.getName());
                enterpriseInfoData.setProductId(chameleonClientHelper.getProductIdByPlatformId(enterprise.getPlatformId()));
                log.info("current enterprise_info: {}",enterprise);
                //取钉钉三方应用appid
                JSONObject additionalInfo =JSON.parseObject(enterprise.getAdditionalInfo());
                if (additionalInfo!=null&&additionalInfo.getJSONObject("platform_config")!=null&&additionalInfo.getJSONObject("platform_config").getJSONObject("dingTalk")!=null){
                    enterpriseInfoData.setAppId(additionalInfo.getJSONObject("platform_config").getJSONObject("dingTalk").getJSONObject("appKey").getString("value"));
                }
                enterpriseInfoData.setRedirectUri(redirectUri);
                enterpriseInfo.add(enterpriseInfoData);
            }
            enterpriseDto.setEnterprises(enterpriseInfo);
            return enterpriseDto;
        }
        return null;

    }

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        return null;
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.WMPI.getDesc();
    }
}
