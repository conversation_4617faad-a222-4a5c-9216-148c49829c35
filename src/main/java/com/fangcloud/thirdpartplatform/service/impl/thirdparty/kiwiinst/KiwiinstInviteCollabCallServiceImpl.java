package com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.kiwiinst.*;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.v2.CreateCommentBean;
import com.fangcloud.thirdpartplatform.helper.DateHelper;
import com.fangcloud.thirdpartplatform.helper.KiwiinstClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.kiwiinst.KiwiinstInviteCollabCallbackServiceImpl.*;

@Service
@Slf4j
public class KiwiinstInviteCollabCallServiceImpl implements ThirdpartyExecuteService {


    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private GroupsUsersMapper groupsUsersMapper;

    @Autowired
    private KiwiinstClientHelper kiwiinstClientHelper;

    @Resource
    private V2ClientHelper v2ClientHelper;

    static final String DPSQ_WORK_FLOW_FLAG = "DPSQ";


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        KiwiinstInviteCollabCallValueBox kiwiinstInviteCollabCallValueBox = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getValueBox()), KiwiinstInviteCollabCallValueBox.class);

        long inviteUserId = kiwiinstInviteCollabCallValueBox.getInviteUserId();
        User user = userMapper.queryById(inviteUserId);

        if(Objects.isNull(user)){
            throw new ParamException("inviteUser is null !");
        }
        long enterpriseId = user.getEnterpriseId();
        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);

        if(Objects.isNull(platformEnterprises)){
            throw new ParamException("platformEnterprises is null !");
        }

        int platformId = platformEnterprises.getPlatformId();
        PlatformUser invitePlatformUser = platformUserMapper.queryByPlatformIdUserId(platformId, inviteUserId);
        if(Objects.isNull(invitePlatformUser)){
            throw new ParamException("invitePlatformUser is null!");
        }

        CreateWorkflow createWorkflow = new CreateWorkflow();

        createWorkflow.setBase(buildCreateWorkflowBase(invitePlatformUser));
        createWorkflow.setMain(buildCreateWorkflowMain(kiwiinstInviteCollabCallValueBox, invitePlatformUser, enterpriseId));
        createWorkflow.setDt1(buildCreateWorkflowDt1(kiwiinstInviteCollabCallValueBox, platformId));
        List<CreateWorkflow> list = new ArrayList<>();
        list.add(createWorkflow);
        // 调用创建流程接口
        String result = kiwiinstClientHelper.createWorkflow(JSON.toJSONString(list), enterpriseId);

        String status = (String) JSONPath.extract(result, "$.resultlist[0].status");
        if(!"0".equals(status)){
            throw new ParamException("createWorkflow error!");
        }

        try {
            String msg = (String) JSONPath.extract(result, "$.resultlist[0].msg");
            if(StringUtils.isNotEmpty(msg)){
                // 新增评论
                CreateCommentBean createCommentBean = new CreateCommentBean("folder", msg,kiwiinstInviteCollabCallValueBox.getFolderId());
                v2ClientHelper.createComment(createCommentBean, kiwiinstInviteCollabCallValueBox.getFolderOwnerId());
            }

        }catch (Exception e){
            log.info("createWorkflow create comment error !");
        }

        return  ResultUtils.getSuccessResult(true);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {
        return null;
    }

    /**
     * 构建邀请协作对象列表
     * @param kiwiinstInviteCollabCallValueBox
     * @param platformId
     * @return
     */
    private List<CreateWorkflowDt1> buildCreateWorkflowDt1(KiwiinstInviteCollabCallValueBox kiwiinstInviteCollabCallValueBox, int platformId) {
        List<CreateWorkflowDt1> dt1List = new ArrayList<>();

        String invitedUsers = kiwiinstInviteCollabCallValueBox.getInvitedUsers();
        String invitedGroups = kiwiinstInviteCollabCallValueBox.getInvitedGroups();

        if(StringUtils.isEmpty(invitedGroups) && StringUtils.isEmpty(invitedUsers)){
            throw new ParamException("invited object is null!");
        }
        if(StringUtils.isNotEmpty(invitedUsers)){
            List<Long> userIdList = new ArrayList<>();
            Map<Long, String> dataMap = new HashMap<>();
            String[] split = invitedUsers.split(";");
            for (String s : split) {
                String[] split1 = s.split(":");
                dataMap.put(Long.valueOf(split1[0]), split1[1]);
                userIdList.add(Long.valueOf(split1[0]));
            }
            List<PlatformUser> platformUsers = platformUserMapper.queryByPlatformIdUserIds(platformId, userIdList);
            for (PlatformUser platformUser : platformUsers) {
                CreateWorkflowDt1 createWorkflowDt1 = new CreateWorkflowDt1();
                createWorkflowDt1.setBdr(new CreateWorkflowValue(platformUser.getUserTicket()));
                createWorkflowDt1.setBdlx(new CreateWorkflowValue(BDR_TYPE));
                createWorkflowDt1.setXzlx(new CreateWorkflowValue(dataMap.get(platformUser.getUserId())));
                dt1List.add(createWorkflowDt1);
            }
        }

        if(StringUtils.isNotEmpty(invitedGroups)){
            List<Long> groupIdList = new ArrayList<>();
            Map<Long, String> dataMap = new HashMap<>();
            String[] split = invitedGroups.split(";");
            for (String s : split) {
                String[] split1 = s.split(":");
                dataMap.put(Long.valueOf(split1[0]), split1[1]);
                groupIdList.add(Long.valueOf(split1[0]));
            }

            List<Group> groups = groupMapper.queryByIds(groupIdList);

            for (Group group : groups) {
                CreateWorkflowDt1 createWorkflowDt1 = new CreateWorkflowDt1();
                long departmentId = group.getDepartmentId();
                if(departmentId>0){ // 群组有对应的部门
                    PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentId(platformId, group.getDepartmentId());
                    if(Objects.isNull(platformDepartment)){
                        throw new ParamException("platformDepartment is null !");
                    }
                    createWorkflowDt1.setBm(new CreateWorkflowValue(platformDepartment.getDepartmentId()));
                    createWorkflowDt1.setBdlx(new CreateWorkflowValue(BM_TYPE));
                    createWorkflowDt1.setXzlx(new CreateWorkflowValue(dataMap.get(group.getId())));
                    createWorkflowDt1.setQz(new CreateWorkflowValue(String.valueOf(group.getId())));
                    dt1List.add(createWorkflowDt1);

                }else {// 群组没有对应的部门
                    List<GroupsUser> groupsUsers = groupsUsersMapper.queryByGroupId(group.getId());
                    List<Long> userIdList = new ArrayList<>();
                    for (GroupsUser groupsUser : groupsUsers) {
                        userIdList.add(groupsUser.getUserId());
                    }

                    List<PlatformUser> platformUsers = platformUserMapper.queryByPlatformIdUserIds(platformId, userIdList);
                    List<String> collect = platformUsers.stream().map(p -> p.getUserTicket()).collect(Collectors.toList());
                    String join = String.join(",", collect);

                    createWorkflowDt1.setQz(new CreateWorkflowValue(String.valueOf(group.getId())));
                    createWorkflowDt1.setBdlx(new CreateWorkflowValue(QZ_TYPE));
                    createWorkflowDt1.setBdr(new CreateWorkflowValue(join));
                    createWorkflowDt1.setXzlx(new CreateWorkflowValue(dataMap.get(group.getId())));
                    dt1List.add(createWorkflowDt1);
                }
            }
        }
        return dt1List;
    }

    private CreateWorkflowMain buildCreateWorkflowMain(KiwiinstInviteCollabCallValueBox kiwiinstInviteCollabCallValueBox, PlatformUser invitePlatformUser, long enterpriseId) {
        CreateCollabWorkflowMain createWorkflowMain = new CreateCollabWorkflowMain();
        createWorkflowMain.setSqr(new CreateWorkflowValue(invitePlatformUser.getUserTicket()));
        createWorkflowMain.setStartdate(new CreateWorkflowValue(DateHelper.transformTimeStampToDate(System.currentTimeMillis()/1000)));
        createWorkflowMain.setFolderId(new CreateWorkflowValue(kiwiinstInviteCollabCallValueBox.getFolderId()));
        createWorkflowMain.setFolderName(new CreateWorkflowValue(kiwiinstInviteCollabCallValueBox.getFolderName()));

        PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentId(invitePlatformUser.getPlatformId(), kiwiinstInviteCollabCallValueBox.getFolderDepartmentId());
        if(Objects.isNull(platformDepartment)){
            throw new ParamException("folderDepartment is null!");
        }
        createWorkflowMain.setFolderDeptId(new CreateWorkflowValue(platformDepartment.getDepartmentId()));


        PlatformUser ownPlatformUser = platformUserMapper.queryByPlatformIdUserId(invitePlatformUser.getPlatformId(), kiwiinstInviteCollabCallValueBox.getFolderOwnerId());
        if(Objects.isNull(ownPlatformUser)){
            throw new ParamException("ownPlatformUser error!");
        }
        createWorkflowMain.setOwnerId(new CreateWorkflowValue(ownPlatformUser.getUserTicket()));
        createWorkflowMain.setEnterpriseId(new CreateWorkflowValue(String.valueOf(enterpriseId)));
        createWorkflowMain.setInvitationMessage(new CreateWorkflowValue(kiwiinstInviteCollabCallValueBox.getInvitationMessage()));

        return createWorkflowMain;
    }

    private CreateWorkflowBase buildCreateWorkflowBase(PlatformUser invitePlatformUser) {
        CreateWorkflowBase createWorkflowBase = new CreateWorkflowBase();
        createWorkflowBase.setCreator(new CreateWorkflowValue(invitePlatformUser.getUserTicket()));
        createWorkflowBase.setFpkid(DPSQ_WORK_FLOW_FLAG);
        createWorkflowBase.setWorkflowflag(DPSQ_WORK_FLOW_FLAG);
        return createWorkflowBase;
    }


    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.KIWIINST_INVITE_COLLAB_CALL.getDesc();
    }
}
