package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.check.ThirdpartyControllerCheck;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.service.ThirdpartyService;
import com.fangcloud.thirdpartplatform.service.factory.ThirdpartyExecuteServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import static com.fangcloud.thirdpartplatform.service.impl.thirdparty.ThirdpartyInvokeServiceImpl.*;

@Service
@Slf4j
public class ThirdpartyServiceImpl implements ThirdpartyService {


    @Autowired
    private ThirdpartyExecuteServiceFactory thirdpartyExecuteServiceFactory;

    @Autowired
    private ThirdpartyControllerCheck thirdpartyControllerCheck;

    @Override
    public Result<Object> callback(ThirdpartyCallbackParams thirdpartyCallbackParams) {

        thirdpartyControllerCheck.checkCallbackParams(thirdpartyCallbackParams);


        if(thirdpartyCallbackParams.getType().startsWith("kiwiinst")){ // 必易微企业对回调逻辑

            ThirdpartyExecuteService handler = thirdpartyExecuteServiceFactory.getHandler(thirdpartyCallbackParams.getType());
            return handler.execute(thirdpartyCallbackParams);
        }else {// 通用模式回调逻辑，场景内部还要区分类型

            String reviewType = (String) JSONPath.extract(JSON.toJSONString(thirdpartyCallbackParams.getData()), "$.reviewType");
            ThirdpartyExecuteService handler = thirdpartyExecuteServiceFactory.getHandler(reviewType);
            return handler.executeCallback(thirdpartyCallbackParams);
        }
    }

    public static void main(String[] args) {

    }

    @Override
    public Result<Object> call(ThirdpartyParams thirdpartyParams) {
        thirdpartyControllerCheck.checkCallParams(thirdpartyParams);
        ThirdpartyExecuteService handler = thirdpartyExecuteServiceFactory.getHandler(thirdpartyParams.getType());

        return handler.execute(thirdpartyParams);
    }

    @Override
    public Result<Object> invoke(ThirdpartyParams thirdpartyParams) {
        if(!ThirdpartyCallTypeEnum.THIRDPARTY_INVOKE.getDesc().equals(thirdpartyParams.getType())){
            throw new ParamException("type is wrong! ");
        }
        ThirdpartyExecuteService handler = thirdpartyExecuteServiceFactory.getHandler(ThirdpartyCallTypeEnum.THIRDPARTY_INVOKE.getDesc());

        return handler.execute(thirdpartyParams);
    }


    @Override
    public Object invokeGet(HttpServletRequest request, HttpServletResponse response) {
        String enterpriseId = request.getParameter(VALUE_BOX_KEY_ENTERPRISE_ID);
        String thirdpartyInvokeType = request.getParameter(VALUE_BOX_KEY_THIRDPARTY_INVOKE_TYPE);
        // 若传参数没有企业id，使用默认企业id
        if(StringUtils.isEmpty(enterpriseId)){
            enterpriseId = "115";
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(VALUE_BOX_KEY_ENTERPRISE_ID, enterpriseId);
        jsonObject.put(VALUE_BOX_KEY_THIRDPARTY_INVOKE_TYPE, thirdpartyInvokeType);
        jsonObject.put(VALUE_BOX_KEY_REQUEST, request);
        jsonObject.put(VALUE_BOX_KEY_RESPONSE, response);

        ThirdpartyParams thirdpartyParams = new ThirdpartyParams();
        thirdpartyParams.setData(jsonObject);
        ThirdpartyExecuteService handler = thirdpartyExecuteServiceFactory.getHandler(ThirdpartyCallTypeEnum.THIRDPARTY_INVOKE.getDesc());

        return handler.execute(thirdpartyParams);
    }

    @Override
    public Object invokePost(HttpServletRequest request, JSONObject jsonObject, HttpServletResponse response) {

        String enterpriseId = request.getParameter(VALUE_BOX_KEY_ENTERPRISE_ID);
        String thirdpartyInvokeType = request.getParameter(VALUE_BOX_KEY_THIRDPARTY_INVOKE_TYPE);
        // 若传参数没有企业id，使用默认企业id
        if(StringUtils.isEmpty(enterpriseId)){
            enterpriseId = "115";
        }

        JSONObject jsonObjectNew = JSON.parseObject(JSON.toJSONString(jsonObject));
        jsonObjectNew.put(VALUE_BOX_KEY_THIRDPARTY_INVOKE_TYPE, thirdpartyInvokeType);
        jsonObjectNew.put(VALUE_BOX_KEY_REQUEST, request);
        jsonObjectNew.put(VALUE_BOX_KEY_RESPONSE, response);
        jsonObjectNew.put(VALUE_BOX_KEY_ENTERPRISE_ID, enterpriseId);

        ThirdpartyParams thirdpartyParams = new ThirdpartyParams();
        thirdpartyParams.setData(jsonObjectNew);
        ThirdpartyExecuteService handler = thirdpartyExecuteServiceFactory.getHandler(ThirdpartyCallTypeEnum.THIRDPARTY_INVOKE.getDesc());

        return handler.execute(thirdpartyParams);
    }
}
