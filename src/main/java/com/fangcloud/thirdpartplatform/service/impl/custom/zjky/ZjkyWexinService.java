package com.fangcloud.thirdpartplatform.service.impl.custom.zjky;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ZjkyWexinService {
    
    private final Map<String, String> uidToWeixinMap = new HashMap<>();
    private final Map<String, String> weixinToUidMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        try {
            loadUserMappings();
        } catch (IOException e) {
            log.error("Failed to load user mappings", e);
        }
    }
    
    private void loadUserMappings() throws IOException {
        ClassPathResource resource = new ClassPathResource("com/fangcloud/thirdpartplatform/service/impl/custom/zjky/users.txt");
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()))) {
            String line;
            boolean isFirstLine = true;
            
            while ((line = reader.readLine()) != null) {
                // Skip the header line
                if (isFirstLine) {
                    isFirstLine = false;
                    continue;
                }
                
                String[] parts = line.split("\\s+");
                if (parts.length >= 2) {
                    String uid = parts[0];
                    String weixinId = parts[1];
                    
                    uidToWeixinMap.put(uid, weixinId);
                    weixinToUidMap.put(weixinId, uid);
                }
            }
            
            log.info("Loaded {} user mappings", uidToWeixinMap.size());
        }
    }
    
    /**
     * Convert a UID to Weixin ID
     * 
     * @param uid the user's UID
     * @return the corresponding Weixin ID, or null if not found
     */
    public String getWeixinIdByUid(String uid) {
        return uidToWeixinMap.get(uid);
    }
    
    /**
     * Convert a Weixin ID to UID
     * 
     * @param weixinId the user's Weixin ID
     * @return the corresponding UID, or null if not found
     */
    public String getUidByWeixinId(String weixinId) {
        return weixinToUidMap.get(weixinId);
    }
}
