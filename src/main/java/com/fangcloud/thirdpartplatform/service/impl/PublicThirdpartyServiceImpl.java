package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import com.fangcloud.thirdpartplatform.service.PublicThirdpartyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Service
@Slf4j
public class PublicThirdpartyServiceImpl implements PublicThirdpartyService {

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private GroupMapper groupMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Override
    public JSONObject buildShareInviteInfo(JSONObject jsonObject) {
        JSONObject InviteInfo = new JSONObject();

        Long userId = jsonObject.getLong("user_id");
        String access = jsonObject.getString("access");
        Long enterpriseId = jsonObject.getLong("enterpriseId");


        PlatformEnterprises platformEnterprise = platformEnterpriseMapper.queryByEnterpriseId(Long.valueOf(enterpriseId));

        if(Objects.isNull(platformEnterprise)){
            throw new ParamException("platformEnterprises is null !");
        }

        int platformId = platformEnterprise.getPlatformId();
        PlatformUser createPlatformUser = platformUserMapper.queryByPlatformIdUserId(platformId, userId);
        User user = userMapper.queryById(userId);
        ThirdpartyCustom createThirdpartyCustom = new ThirdpartyCustom();
        createThirdpartyCustom.setId(userId);
        createThirdpartyCustom.setName(user.getFullName());
        if(!Objects.isNull(createPlatformUser)){
            createThirdpartyCustom.setCustomId(createPlatformUser.getUserTicket());
        }

        InviteInfo.put("createUser", createThirdpartyCustom);


        // 只有分享类型为指定类型才需要组装邀请对对象
        if("collaborators".equals(access)){

            JSONArray invitedUserIds = jsonObject.getJSONArray("invited_user_ids");
            // 若邀请用户不为空，则组装CustomUser
            if(!CollectionUtils.isEmpty(invitedUserIds)){
                List<Long> invitedUserIdList = new ArrayList<>();

                for (Object invitedUserId : invitedUserIds) {
                    invitedUserIdList.add((Long) invitedUserId);
                }

                Map<Long, User> userMap = new HashMap<>();
                List<User> users = userMapper.queryByIds(invitedUserIdList);
                for (User user1 : users) {
                    userMap.put(user1.getId(), user1);
                }
                List<PlatformUser> platformUsers = platformUserMapper.queryByPlatformIdUserIds(platformId, invitedUserIdList);
                Map<Long, String> platformUserMap= new HashMap<>();
                for (PlatformUser platformUser : platformUsers) {
                    platformUserMap.put(platformUser.getUserId(), platformUser.getUserTicket());
                }
                List<ThirdpartyCustom> customUserList = new ArrayList<>();
                for (Long invitedUserId : invitedUserIdList) {
                    ThirdpartyCustom customUser = new ThirdpartyCustom();
                    customUser.setId(invitedUserId);
                    customUser.setCustomId(platformUserMap.get(invitedUserId));
                    User invitedUser = userMap.get(invitedUserId);
                    if(!Objects.isNull(invitedUser)){
                        customUser.setName(invitedUser.getFullName());
                    }
                    customUserList.add(customUser);
                }
                InviteInfo.put("userList",customUserList);
            }


            JSONArray groupIds = jsonObject.getJSONArray("group_ids");
            List<Long> departmentIds = new ArrayList<>();
            Map<Long, Group> groupMap = new HashMap<>();
            // 若邀请群组不为空，则组装CustomGroup

            if(!CollectionUtils.isEmpty(groupIds)){
                List<Long> groupIdList = new ArrayList<>();
                for (Object groupId : groupIds) {
                    groupIdList.add((Long) groupId);
                }


                List<Group> groups = groupMapper.queryByIds(groupIdList);

                List<ThirdpartyCustom> customGroupList = new ArrayList<>();

                for (Group group : groups) {
                    long departmentId = group.getDepartmentId();
                    if(departmentId > 0){// 群组有对应的部门
                        departmentIds.add(departmentId);
                        groupMap.put(departmentId, group);
                    }else {
                        ThirdpartyCustom customGroup = new ThirdpartyCustom();
                        customGroup.setId(group.getId());
                        customGroup.setName(group.getName());
                        customGroupList.add(customGroup);
                    }
                }
                InviteInfo.put("groupList",customGroupList);
            }

            // 若邀请部门不为空，则组装CustomDepartment
            if(!CollectionUtils.isEmpty(departmentIds)){
                List<PlatformDepartment> platformDepartmentList = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentIds(platformId, departmentIds);
                Map<Long, String> platformDepartmentMap= new HashMap<>();
                for (PlatformDepartment platformDepartment : platformDepartmentList) {
                    platformDepartmentMap.put(platformDepartment.getYfyDepartmentId(), platformDepartment.getDepartmentId());
                }
                List<ThirdpartyCustom> customDepartments = new ArrayList<>();
                for (Long departmentId : departmentIds) {
                    ThirdpartyCustom customDepartment = new ThirdpartyCustom();
                    customDepartment.setId(departmentId);
                    customDepartment.setCustomId(platformDepartmentMap.get(departmentId));
                    Group group = groupMap.get(departmentId);
                    if(!Objects.isNull(group)){
                        customDepartment.setName(group.getName());
                    }
                    customDepartments.add(customDepartment);
                }
                InviteInfo.put("deptList",customDepartments);
            }
        }
        return InviteInfo;
    }
}
