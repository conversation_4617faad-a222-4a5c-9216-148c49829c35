package com.fangcloud.thirdpartplatform.service.impl.thirdparty.worlflow;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformEnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.dao.UserMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.db.model.User;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdPartyCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.ThirdpartyCustom;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.CopyWorkflowCall;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.FerryWorkflow;
import com.fangcloud.thirdpartplatform.entity.dto.thirdParty.workflow.FerryWorkflowCall;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.entity.request.ark.WorkflowApprovalBean;
import com.fangcloud.thirdpartplatform.helper.ArkClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyCommonService;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@Slf4j
public class WorkflowFerryCallServiceImpl implements ThirdpartyExecuteService {

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private ArkClientHelper arkClientHelper;

    @Autowired
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Autowired
    private ThirdpartyCommonService thirdpartyCommonService;


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        FerryWorkflow ferryWorkflow = JSON.parseObject(JSON.toJSONString(thirdpartyParams.getData()), FerryWorkflow.class);

        FerryWorkflowCall ferryWorkflowCall = ferryWorkflowConvertToFerryWorkflowCall(ferryWorkflow);

        ThirdPartyCall thirdPartyCall = ThirdPartyCall.builder()
                .type(ThirdpartyCallbackTypeEnum.FERRY_CALLBACK.getDesc())
                .data(ferryWorkflowCall)
                .build();

        return ResultUtils.getSuccessResult(thirdPartyCall);
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {

        CopyWorkflowCall copyWorkflowCall = JSON.parseObject(JSON.toJSONString(thirdpartyCallbackParams.getData()), CopyWorkflowCall.class);

        // 校验回调记录
        thirdpartyCommonService.checkCallbackResult(
                copyWorkflowCall.getUniqueId(),
                copyWorkflowCall.getEnterpriseId(),
                copyWorkflowCall.getReviewType(),
                thirdpartyCallbackParams.isReviewResult());

        WorkflowApprovalBean workflowApprovalBean = new WorkflowApprovalBean();
        workflowApprovalBean.setPass(thirdpartyCallbackParams.isReviewResult());
        workflowApprovalBean.setTaskId(Long.parseLong(copyWorkflowCall.getTaskId()));

        boolean workflowApprovalResult = arkClientHelper.workflowApproval(workflowApprovalBean, copyWorkflowCall.getReceiverId());

        return ResultUtils.getSuccessResult(workflowApprovalResult);
    }

    /**
     * 转化对应字段
     * @param ferryWorkflow
     * @return
     */
    private FerryWorkflowCall ferryWorkflowConvertToFerryWorkflowCall(FerryWorkflow ferryWorkflow) {
        FerryWorkflowCall ferryWorkflowCall = new FerryWorkflowCall();

        Long enterpriseId = ferryWorkflow.getEnterpriseId();
        ferryWorkflowCall.setEnterpriseId(enterpriseId);
        ferryWorkflowCall.setReceiverId(ferryWorkflow.getReceiverId());
        ferryWorkflowCall.setTaskId(ferryWorkflow.getTaskId());
        ferryWorkflowCall.setUniqueId(ferryWorkflow.getUniqueId());
        ferryWorkflowCall.setFerryId(ferryWorkflow.getFerryId());
        ferryWorkflowCall.setSourcePath(ferryWorkflow.getSourcePath());
        ferryWorkflowCall.setTargetPath(ferryWorkflow.getTargetPath());
        ferryWorkflowCall.setItemId(ferryWorkflow.getItemId());
        ferryWorkflowCall.setItemName(ferryWorkflow.getItemName());
        ferryWorkflowCall.setItemType(ferryWorkflow.getItemType());

        Long authorId = ferryWorkflow.getAuthorId();
        ThirdpartyCustom thirdpartyCustom = new ThirdpartyCustom();
        thirdpartyCustom.setId(authorId);

        PlatformEnterprises platformEnterprises = platformEnterpriseMapper.queryByEnterpriseId(enterpriseId);
        if(!Objects.isNull(platformEnterprises)){
            PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserId(platformEnterprises.getPlatformId(), authorId);
            if(!Objects.isNull(platformUser)){
                thirdpartyCustom.setCustomId(platformUser.getUserTicket());
            }
        }
        User authorUser = userMapper.queryById(authorId);
        if(!Objects.isNull(authorUser)){
            thirdpartyCustom.setName(authorUser.getFullName());
        }
        ferryWorkflowCall.setCreateUser(thirdpartyCustom);
        ferryWorkflowCall.setReviewType(ThirdpartyCallTypeEnum.WOEKFLOW_FERRY_CALL.getDesc());
        return ferryWorkflowCall;
    }

    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.WOEKFLOW_FERRY_CALL.getDesc();
    }
}
