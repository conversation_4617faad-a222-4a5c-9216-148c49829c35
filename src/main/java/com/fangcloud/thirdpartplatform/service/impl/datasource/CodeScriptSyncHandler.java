package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.CodeScriptValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.LoginSourceCodeScriptConfigDto;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.service.factory.ApiExecuteServiceFactory;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class CodeScriptSyncHandler extends AbstractDataSourceSyncHandler {
    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private APIHelper apiHelper;

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private ApiExecuteServiceFactory apiExecuteServiceFactory;

    public static void main(String[] args) {

    }

    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return null;
    }

    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        List<YfyUser> users = new ArrayList<>();
        CodeScriptValueBox codeScriptValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), CodeScriptValueBox.class);
        log.info("syncUser-codeScriptValueBox:{}", codeScriptValueBox);
        getYfyUsersByConfigValueBox(users, codeScriptValueBox);
        log.info("apiSyncHandler-syncUser, users:{}, taskId:{}", JSON.toJSONString(users), taskId);
        if (CollectionUtils.isEmpty(users)) {
            return executeResult;
        }
        return syncUserList(platformSyncConfig, users, taskId);
    }

    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        List<YfyDepartment> departments = new ArrayList<>();
        CodeScriptValueBox codeScriptValueBox = JSON.parseObject(platformSyncConfig.getValueBox(), CodeScriptValueBox.class);
        getYfyDepartmentsByConfigValueBox(departments, codeScriptValueBox);
        log.info("apiSyncHandler-syncDepartment, department:{}, taskId:{}", JSON.toJSONString(departments), taskId);
        if (CollectionUtils.isEmpty(departments)) {
            return executeResult;
        }
        return syncDepartmentList(platformSyncConfig, departments, taskId);
    }

    public void getYfyUsersByConfigValueBox(List<YfyUser> users, CodeScriptValueBox codeScriptValueBox) {
        List<YfyUser> yfyUsers = null;
        try {
            yfyUsers = (List<YfyUser>) apiHelper.executeContext(URLDecoder.decode(codeScriptValueBox.getCodeScript(), "UTF-8"), null);
        } catch (UnsupportedEncodingException e) {
            throw new ParamException(SyncTaskConstants.CODE_SCRIPT_URL_DECODE_ERROR);
        }
        log.info("syncUser-codeScriptConfigData:{}", JSON.toJSONString(yfyUsers));
        if (CollectionUtils.isEmpty(yfyUsers)) {
            throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
        }
        users.addAll(yfyUsers);
    }

    public void getYfyDepartmentsByConfigValueBox(List<YfyDepartment> departments,  CodeScriptValueBox codeScriptValueBox) {

        List<YfyDepartment> yfyDepartments = null;
        try {
            yfyDepartments = (List<YfyDepartment>) apiHelper.executeContext(URLDecoder.decode(codeScriptValueBox.getCodeScript(), "UTF-8"), null);
        } catch (UnsupportedEncodingException e) {
            throw new ParamException(SyncTaskConstants.CODE_SCRIPT_URL_DECODE_ERROR);
        }
        log.info("codeScriptConfigData:{}", JSON.toJSONString(yfyDepartments));
        if (CollectionUtils.isEmpty(yfyDepartments)) {
            throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
        }
        departments.addAll(yfyDepartments);
    }

    /**
     * 构建用户对象
     * @param dataMap
     * @param loginSourceCodeScriptConfigDto
     * @return
     */
    public YfyUser getYfyUserByConfigValueBox(Map<String, String> dataMap, LoginSourceCodeScriptConfigDto loginSourceCodeScriptConfigDto) {
        try {
            YfyUser yfyUser = (YfyUser) apiHelper.executeContext(URLDecoder.decode(loginSourceCodeScriptConfigDto.getCodeScript(), "UTF-8"), dataMap);
            log.info("login-codeScriptConfigData:{}", JSON.toJSONString(yfyUser));
            return yfyUser;
        } catch (UnsupportedEncodingException e) {
            throw new ParamException(SyncTaskConstants.CODE_SCRIPT_URL_DECODE_ERROR);
        }
    }

    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.CODE_SCRIPT.getDesc();
    }

    public Object testConnectData(CodeScriptValueBox codeScriptValueBox){
        Map<String, Object> map = new HashMap<>();
        try {
            Object obj = apiHelper.executeContext(URLDecoder.decode(codeScriptValueBox.getCodeScript(), "UTF-8"), null);
            map.put("codeScriptResultData", JSON.toJSONString(obj));
        } catch (Exception e) {
            map.put("codeScriptResultData", "error");
        }
        return map;
    }

}
