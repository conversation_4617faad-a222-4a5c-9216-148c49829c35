package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.EnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.EnterpriseDto;
import com.fangcloud.thirdpartplatform.entity.dto.YfyGroup;
import com.fangcloud.thirdpartplatform.helper.ChameleonClientHelper;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.service.impl.SyncServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.fangcloud.thirdpartplatform.utils.sm4.SM4Utils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.*;

@Component
@Slf4j
public class H3cServiceImpl implements ApiExecuteService {

    // 接口类型
    private static String H3C_TYPE = "type";

    final public static String H3C_TYPE_LOGIN= "login";
    final private static String H3C_TYPE_LOGIN_RESULT = "login_result";
    final private static String H3C_TYPE_USER_INFO = "sync_user";
    final private static String H3C_TYPE_DEPT_INFO = "sync_dept";


    // 调用地址
    private static String H3C_PARAMETER_URL = "url";

    private static String H3C_LOGIN_PATH= "/vdi/rest/cloud/disk/yf/check/login";
    private static String H3C_USER_PATH= "/vdi/rest/cloud/disk/yf/sync/user";
    private static String H3C_DEPT_PATH= "/vdi/rest/cloud/disk/yf/sync/user/group";
    private static String H3C_GROUP_PATH= "/vdi/rest/cloud/disk/yf/sync/user/lessonGroup";


    // 摘要认证账号
    private static String H3C_PARAMETER_AUTH_USERNAME = "authUsername";
    // 摘要认证密码
    private static String H3C_PARAMETER_AUTH_PASSWORD = "authPassword";


    // 登录账号
    private static String H3C_PARAMETER_USERNAME = "username";
    // 登录密码
    private static String H3C_PARAMETER_PASSWORD = "password";

    private static String H3C_GROUP_INFO = "$.data";

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    private SyncServiceImpl syncService;

    @Resource
    private HttpClientHelper httpClientHelper;

    @Autowired
    private EnterpriseMapper enterpriseMapper;

    @Resource
    private ChameleonClientHelper chameleonClientHelper;

    public EnterpriseDto getEnterprises() {
        List<Enterprise> enterpriseList =  enterpriseMapper.query();
        if (enterpriseList != null && enterpriseList.size() > 0) {
            EnterpriseDto enterpriseDto = new EnterpriseDto();

            List<EnterpriseDto.EnterpriseInfo> enterpriseInfo = new ArrayList<>();
            for (Enterprise enterprise : enterpriseList) {
                if (enterprise.getId() == 114 || enterprise.isForbiddenLogin()) {
                    continue;
                }
                EnterpriseDto.EnterpriseInfo enterpriseInfoData =  new EnterpriseDto.EnterpriseInfo();
                enterpriseInfoData.setIdAlias(Base64Utils.encode(String.valueOf(enterprise.getId())));
                enterpriseInfoData.setId(enterprise.getId());
                enterpriseInfoData.setName(enterprise.getName());
                enterpriseInfoData.setProductId(chameleonClientHelper.getProductIdByPlatformId(enterprise.getPlatformId()));
                enterpriseInfo.add(enterpriseInfoData);
            }
            enterpriseDto.setEnterprises(enterpriseInfo);
            return enterpriseDto;
        }
        return null;

    }

    public String getLoginResult( String oauthResult) {
        return oauthResult;
    }


    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {

        String type = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), H3C_TYPE);

        String url = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), H3C_PARAMETER_URL);

        // 摘要认证账号
        String authUsername = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), H3C_PARAMETER_AUTH_USERNAME);
        // 摘要认证密码
        String authPassword = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), H3C_PARAMETER_AUTH_PASSWORD);
        switch (type){
            case H3C_TYPE_LOGIN:
                return getLoginUserInfo(apiConfig, url, authUsername, authPassword, enterpriseId);
            case H3C_TYPE_LOGIN_RESULT:
                return getLoginResult(oauthResult);
            case H3C_TYPE_USER_INFO:
                return getUserInfo(url, authUsername, authPassword, enterpriseId);
            case H3C_TYPE_DEPT_INFO:
                return getDeptInfo(url, authUsername, authPassword, enterpriseId);
            default:
                return null;
        }
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    private String getUserInfo(String url, String authUsername, String authPassword, Integer enterpriseId) {

        String getUserInfoUrl = url + H3C_USER_PATH;

        try {
            log.info("get h3c user info url:{}", getUserInfoUrl);
            Response response = httpClientHelper.getDigestAuth(getUserInfoUrl, authUsername, authPassword);
            String userInfoJson = Objects.requireNonNull(response.body()).string();
            return buildUserResult(userInfoJson, enterpriseId);
        } catch (IOException e) {
            log.error("get h3c user info error url {}", url, e);
            return null;
        }
    }

    private String getDeptInfo(String url, String authUsername, String authPassword, Integer enterpriseId) {
        String getDeptInfoUrl = url + H3C_DEPT_PATH;

        String getGroupInfoUrl = url + H3C_GROUP_PATH;

        // 同步群组数据
        syncGroups(getGroupInfoUrl, authUsername, authPassword, enterpriseId);

        try {
            log.info("get h3c dept info url:{}", getDeptInfoUrl);
            Response response = httpClientHelper.getDigestAuth(getDeptInfoUrl, authUsername, authPassword);
            return Objects.requireNonNull(response.body()).string();
        } catch (IOException e) {
            log.error("get h3c dept info error url {}", url, e);
            return null;
        }
    }

    /**
     * 同步群组数据
     * @param getGroupInfoUrl
     * @param authUsername
     * @param authPassword
     * @param enterpriseId
     */
    private void syncGroups(String getGroupInfoUrl, String authUsername, String authPassword, Integer enterpriseId) {

        String groupInfo = null;
        try {
            log.info("get h3c group info url:{}", getGroupInfoUrl);
            Response response = httpClientHelper.getDigestAuth(getGroupInfoUrl, authUsername, authPassword);
            groupInfo = Objects.requireNonNull(response.body()).string();
            log.info("get h3c group info result:{}", groupInfo);
        } catch (IOException e) {
            log.error("get h3c group info error url {}", getGroupInfoUrl, e);
        }
        Enterprise enterprise = enterpriseMapper.queryById(enterpriseId);
        if(StringUtils.isNotEmpty(groupInfo) && !Objects.isNull(enterprise)){
            syncService.doSyncGroup( buildGroupList(groupInfo), enterprise);
        }

    }

    private List<YfyGroup> buildGroupList(String groupInfo) {
        List<YfyGroup> yfyGroupList = new ArrayList<>();
        JSONArray yfyGroupJSONArray = (JSONArray) JSONPath.extract(groupInfo, H3C_GROUP_INFO);
        for (Object yfyGroupJSON : yfyGroupJSONArray) {
            String id =  ((JSONObject) yfyGroupJSON).get("id") + "";
            String name = (String) ((JSONObject) yfyGroupJSON).get("name");
            YfyGroup yfyGroup = new YfyGroup();
            yfyGroup.setStatus("1");
            yfyGroup.setName(name);
            yfyGroup.setId(id);
            yfyGroupList.add(yfyGroup);
        }
        return yfyGroupList;
    }

    private String getLoginUserInfo(APIConfigValueBox.APIConfig apiConfig, String url, String authUsername, String authPassword, Integer enterpriseId) {
        // 登录账号
        String username = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), H3C_PARAMETER_USERNAME);
        // 登录密码
        String password = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), H3C_PARAMETER_PASSWORD);

        InetAddress address = null;
        try {
            address = InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            e.printStackTrace();
            log.info("get ip error !");
        }
        String ip = address.getHostAddress();
        Map postData = new HashMap<>();
        postData.put("loginName",username);
        postData.put("password", SM4Utils.webEncryptText(password));
        postData.put("ip",ip);
        postData.put("enterpriseId", enterpriseId);

        String loginUrl = url + H3C_LOGIN_PATH;
        log.info("h3c login url:{}, postData:{}", loginUrl, postData);

        try {
            Response response = httpClientHelper.postDigestAuth(loginUrl, JSON.toJSONString(postData), authUsername, authPassword);
            String loginUserJson = Objects.requireNonNull(response.body()).string();
            return buildLoginUserResult(loginUserJson,enterpriseId);
        } catch (Exception e) {
            log.error("get h3c login error url {}", url, e);
            return null;
        }
    }

    private String buildLoginUserResult(String loginUserJson, Integer enterpriseId) {
        JSONObject jSONObject = (JSONObject) JSONPath.extract(loginUserJson, "$.data.user");
        Object email = jSONObject.get("email");
        buildEmail(enterpriseId, jSONObject, email);

        JSONObject jSONUser = new JSONObject();
        jSONUser.put("user", jSONObject);

        JSONObject jSONData= new JSONObject();
        jSONData.put("data", jSONUser);
        return JSON.toJSONString(jSONData);
    }

    private String buildUserResult(String userInfoJson, Integer enterpriseId) {
        List<JSONObject> list = new ArrayList<>();
        JSONArray jsonArray = (JSONArray) JSONPath.extract(userInfoJson, "$.data");
        for (Object object : jsonArray) {
            JSONObject jSONObject = (JSONObject)object;
            Object email = jSONObject.get("email");

            buildEmail(enterpriseId, jSONObject, email);
            list.add(jSONObject);
        }
        JSONObject jSONData = new JSONObject();
        jSONData.put("data", list);
        return JSON.toJSONString(jSONData);
    }

    private void buildEmail(Integer enterpriseId, JSONObject jSONObject, Object email) {
        if(Objects.isNull(email) || StringUtils.isEmpty((String) email)){
            String uuid = UUID.randomUUID().toString();
            jSONObject.put("email", uuid + "@" + enterpriseId + ".com");
        }
    }


    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.H3C.getDesc();
    }
}
