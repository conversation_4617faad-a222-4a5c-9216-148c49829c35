package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.entity.input.CcWorkInitParam;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
public class CcWorKSassSyncHandler {

    private static String CCWORK_CORP_ID_PATH = "$.platform_config.ccWork.corpId.value";

    private static String ARR_PLATFORM_CONFIG_PATH = "$.platform_config_arr";
    private static String ARR_CCWORK_CORP_ID_PATH = "$.platform_config_arr[0].ccWork.corpId.value";

    @Resource
    private EnterpriseServiceImpl enterpriseService;

    public CcWorkInitParam getCcWorkInfo(Integer enterpriseId) {
        try {
            CcWorkInitParam ccWorkInitParam = new CcWorkInitParam();
            Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
            String additionalInfo = enterpriseInfo.getAdditionalInfo();
            if (enterpriseInfo == null || additionalInfo == null) {
                log.info("enterprise id {}, info is null ! {}", enterpriseId, enterpriseInfo);
                throw new ParamException("enterpriseInfo is null !");
            }

            String corpId = null;
            Object platformConfigArr = JSONPath.extract(additionalInfo, ARR_PLATFORM_CONFIG_PATH);
            if (Objects.isNull(platformConfigArr)) {
                corpId = JSONPath.extract(additionalInfo, CCWORK_CORP_ID_PATH).toString();
            } else {
                corpId = JSONPath.extract(additionalInfo, ARR_CCWORK_CORP_ID_PATH).toString();
            }

            log.info("result: corpId:{} ", corpId);
            if (corpId == null) {
                throw new ParamException("appId or ssoAppId is null !");
            }
            ccWorkInitParam.setCorpId(corpId);
            // 添加企业超级管理员信息
            EnterpriseParams enterpriseParams = new EnterpriseParams();
            enterpriseParams.setAdminUserId(enterpriseInfo.getAdminUserId());
            enterpriseParams.setPlatformId(enterpriseInfo.getPlatformId() + "");
            enterpriseParams.setEnterpriseId(enterpriseId + "");
            ccWorkInitParam.setEnterpriseParams(enterpriseParams);
            return ccWorkInitParam;
        } catch (Exception e) {
            log.error("getCcWorkInfo error {}", e);
            throw new ParamException("get enterpriseInfo error !");
        }
    }

    private Long getOrder(String order) {
        if (StringUtils.isNotBlank(order)) {
            return 0L;
        }
        return 1000000000L - Long.parseLong(order);
    }
}
