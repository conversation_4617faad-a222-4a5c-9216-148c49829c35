package com.fangcloud.thirdpartplatform.service.impl.custom;

import cn.hutool.core.lang.UUID;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.MessageUtilEnum;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.common.UserEnterpriseInfo;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.APIParamConfigDto;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.entity.input.FeiShuInitParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.fangcloud.thirdpartplatform.service.impl.thirdPart.BaseServiceImpl;
import com.sync.common.entity.dto.YfyMessage;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.enums.DepartmentStatusEnum;
import com.sync.common.enums.UserStatusEnum;
import com.sync.common.enums.YfyMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

import static com.sync.common.contants.SyncConstant.GB;

@Component
@Slf4j
public class FeiShuServiceImpl extends BaseServiceImpl implements ApiExecuteService {

    private static final String HOST = "https://open.feishu.cn";

    private static final String TENANT_ACCESS_TOKEN_URI = "%s/open-apis/auth/v3/tenant_access_token/internal";

    private static final String GET_DEPARTMENT_URI = "%s/open-apis/contact/v3/departments/%s/children?user_id_type=open_id&department_id_type=open_department_id&fetch_child=true&page_size=50&page_token=%s";

    private static final String GET_USER_URI = "%s/open-apis/contact/v3/users/find_by_department?user_id_type=open_id&department_id_type=open_department_id&department_id=%s&page_size=50&page_token=%s";

    private static final String EXTERNAL_INSTANCES = "%s/open-apis/approval/v4/external_instances";

    private static final String GET_DEPARTMENT_ONE = "%s/open-apis/contact/v3/departments/%s?user_id_type=open_id&department_id_type=open_department_id";

    private static final String SEND_MESSAGE = "%s/open-apis/im/v1/messages?receive_id_type=open_id";

    private static final String GET_APP_ACCESS_TOKEN_URI = "%s/open-apis/auth/v3/app_access_token/internal";

    private static final String GET_USER_ACCESS_TOKEN_URI = "%s/open-apis/authen/v1/oidc/access_token";

    private static final String GET_USER_INFO_URI = "%s/open-apis/authen/v1/user_info";

    private static final String BATCH_GET_ID = "%s/open-apis/contact/v3/users/batch_get_id?user_id_type=open_id";

    private static String FEI_SHU_APP_ID_PATH = "$.platform_config.feishu.appId.value";
    private static String FEI_SHU_APP_SECRET_PATH = "$.platform_config.feishu.appSecret.value";
    private static String FEI_SHU_HOST_PATH = "$.platform_config.feishu.host.value";

    private static String ARR_PLATFORM_CONFIG_PATH = "$.platform_config_arr";
    private static String ARR_FEI_SHU_APP_ID_PATH = "$.platform_config_arr[0].feishu.appId.value";
    private static String ARR_FEI_SHU_APP_SECRET_PATH = "$.platform_config_arr[0].feishu.appSecret.value";
    private static String ARR_FEI_SHU_HOST_PATH = "$.platform_config_arr[0].feishu.host.value";

    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Resource
    private EnterpriseServiceImpl enterpriseService;

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        String syncType = headMap.get("syncType");
        String accessToken = getAccessToken(headMap.get("app_id"), headMap.get("app_secret"), HOST);

        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId + "");
        String departmentId = configMap.get(PlatformGlobalConfigKeyEnum.FEI_SHU_DEPARTMENT_ID.getKey());

        if (StringUtils.isBlank(accessToken)) {
            return new JSONObject().fluentPut("code", 500).toJSONString();
        }
        JSONArray jsonArray = "user".equals(syncType) ? getUsersInfo(accessToken, departmentId, HOST) : getDepartmentInfo(accessToken, departmentId, HOST);

        return new JSONObject().fluentPut("code", CollectionUtils.isEmpty(jsonArray) ? 500 : 200).fluentPut("data", jsonArray).toJSONString();
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.FEISHU.getDesc();
    }

    public String getAccessToken(String appId, String appSecret, String host) {
        if (StringUtils.isAnyBlank(appId, appSecret)) {
            log.error("appId or appSecret is null!!");
            return null;
        }
        String getTokenUrl = String.format(TENANT_ACCESS_TOKEN_URI, StringUtils.isNotBlank(host) ? host : HOST);
        String tokenPostData = getTokenPostData(appId, appSecret);
        try {
            log.info("getAccessToken url {}, postData {}", getTokenUrl, tokenPostData);
            Response response = httpClientHelper.postResponse(getTokenUrl, tokenPostData);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("getAccessToken result {}", result);
            String token = (String) JSONPath.extract(result, "$.tenant_access_token");
            return token;
        } catch (Exception e) {
            log.error("getAccessToken error ", e);
        }
        return null;
    }

    private JSONArray getDepartmentInfo(String accessToken, String departmentId, String host) {
        if (accessToken == null) {
            return null;
        }
        if (StringUtils.isBlank(host)) {
            host = HOST;
        }
        JSONArray jsonArray = new JSONArray();
        getDepartmentOne(departmentId, accessToken, jsonArray, host);
        String pageToken = StringUtils.EMPTY;
        while (true) {
            String departmentUrl = String.format(GET_DEPARTMENT_URI, host, departmentId, pageToken);
            try {
                log.info("getDepartmentInfo url {}, accessToken {}", departmentUrl, accessToken);
                httpClientHelper.setHeaders(getHeaders(accessToken));
                Response response = httpClientHelper.getResponse(departmentUrl);
                String result = Objects.requireNonNull(response.body()).string();
                log.info("getDepartmentInfo result {}", result);
                int code = (int) JSONPath.extract(result, "$.code");
                if (code != 0) {
                    break;
                }
                JSONArray items = (JSONArray) JSONPath.extract(result, "$.data.items");
                if (CollectionUtils.isEmpty(items)) {
                    break;
                }
                jsonArray.addAll(items);
                Boolean hasMore = (Boolean) JSONPath.extract(result, "$.data.has_more");
                if (!hasMore) {
                    break;
                }
                pageToken = (String) JSONPath.extract(result, "$.data.page_token");
            } catch (Exception e) {
                log.error("getDepartmentInfo error ", e);
                break;
            }
        }
        return jsonArray;
    }

    private void getDepartmentOne(String departmentId, String accessToken, JSONArray jsonArray, String host) {
        if (departmentId.equals("0")) {
            return;
        }
        if (StringUtils.isBlank(host)) {
            host = HOST;
        }
        String url = String.format(GET_DEPARTMENT_ONE, host, departmentId);
        try {
            log.info("getDepartmentOne url {}", url);
            httpClientHelper.setHeaders(getHeaders(accessToken));
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("getDepartmentOne result {}", result);
            int code = (int) JSONPath.extract(result, "$.code");
            if (code != 0) {
                return;
            }
            JSONObject jsonObject = (JSONObject) JSONPath.extract(result, "$.data.department");
            if (CollectionUtils.isEmpty(jsonObject)) {
                return;
            }
            jsonArray.add(jsonObject);
        } catch (Exception e) {
            log.error("getDepartmentOne error ", e);
        }
    }

    private List<String> getDepartmentIds(String accessToken, String departmentId, String host) {
        JSONArray departmentInfo = getDepartmentInfo(accessToken, departmentId, host);
        return (List<String>) JSONPath.extract(departmentInfo.toJSONString(), "$.open_department_id");
    }

    private JSONArray getUsersInfo(String accessToken, String departmentId, String host) {
        if (StringUtils.isBlank(host)) {
            host = HOST;
        }
        List<String> departmentIds = getDepartmentIds(accessToken, departmentId, host);
        JSONArray jsonArray = new JSONArray();
        String finalHost = host;
        departmentIds.forEach(s -> {
            String pageToken = StringUtils.EMPTY;
            while (true) {
                String getUsersUrl = String.format(GET_USER_URI, finalHost, s, pageToken);
                try {
                    httpClientHelper.setHeaders(getHeaders(accessToken));
                    Response response = httpClientHelper.getResponse(getUsersUrl);
                    String result = Objects.requireNonNull(response.body()).string();
                    log.info("getUsersInfo result {}", result);
                    int code = (int) JSONPath.extract(result, "$.code");
                    if (code != 0) {
                        break;
                    }
                    JSONArray items = (JSONArray) JSONPath.extract(result, "$.data.items");
                    if (CollectionUtils.isEmpty(items)) {
                        break;
                    }
                    jsonArray.addAll(items);
                    Boolean hasMore = (Boolean) JSONPath.extract(result, "$.data.has_more");
                    if (!hasMore) {
                        break;
                    }
                    pageToken = (String) JSONPath.extract(result, "$.data.page_token");
                } catch (Exception e) {
                    log.error("getUsersInfo error ", e);
                    break;
                }
            }
        });
        return jsonArray;
    }


    private String getTokenPostData(String appId, String appSecret) {
        return "{\"app_id\":\"" + appId + "\",\"app_secret\":\"" + appSecret + "\"}";
    }

    private String getUserTokenPostData(String code) {
        return "{\"grant_type\":\"authorization_code\",\"code\":\"" + code + "\"}";
    }

    private Map<String, String> getHeaders(String accessToken) {
        Map<String, String> map = new HashMap<>();
        map.put("Authorization", "Bearer " + accessToken);
        return map;
    }

    public boolean sendMessage(Map<String, String> yfyMessage, PlatformSyncConfig platformSyncConfig) {
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        List<APIParamConfigDto> paramConfig = apiConfigValueBox.getApiConfig().getParamConfig();
        String type = yfyMessage.get(MessageUtilEnum.MESSAGE_TYPE.getKey());
        String title = YfyMessageTypeEnum.getEnum(type).getDesc();
        String messagePushTitle = yfyMessage.get(MessageUtilEnum.MESSAGE_PUSH_TITLE.getKey());
        if (StringUtils.isBlank(messagePushTitle)) {
            yfyMessage.put(MessageUtilEnum.MESSAGE_PUSH_TITLE.getKey(), title);
        }
        String accessToken = getAccessToken(apiSyncHandler.getValueByParamConfig(paramConfig, "app_id"), apiSyncHandler.getValueByParamConfig(paramConfig, "app_secret"), HOST);
        if ((YfyMessageTypeEnum.BEHAVIORREVIEW.getType().equals(type))) {
            return behaviorReviewMessage(yfyMessage, paramConfig, accessToken);
        } else {
            return sendRoBotMessages(HOST, buildRoBotToPost(yfyMessage, paramConfig), accessToken);
        }
    }

    private boolean sendRoBotMessages(String host, String postData, String accessToken) {
        String url = String.format(SEND_MESSAGE, host);
        log.info("sendRoBotMessages url: {}, postData: {}", url, postData);
        try {
            return toPost(url, postData, accessToken);
        } catch (Exception e) {
            log.error("sendRoBotMessages error, postData: {}", postData, e);
        }
        return false;
    }

    private String buildRoBotToPost(Map<String, String> yfyMessage, List<APIParamConfigDto> paramConfig) {
        String panHost = apiSyncHandler.getValueByParamConfig(paramConfig, "panHost");
        if (StringUtils.isBlank(panHost)) {
            //单域名可以用这个url
            panHost = customNacosConfig.getBaseUrl();
        }
        String receivers = yfyMessage.get(MessageUtilEnum.MESSAGE_RECEIVERS.getKey());
        String type = yfyMessage.get(MessageUtilEnum.MESSAGE_TYPE.getKey());
        String title = YfyMessageTypeEnum.getEnum(type).getDesc();
        String messagePushTitle = yfyMessage.get(MessageUtilEnum.MESSAGE_PUSH_TITLE.getKey());
        if (StringUtils.isNotBlank(messagePushTitle)) {
            title = title.replace("云盘", messagePushTitle);
        }
        String pcLink = panHost + yfyMessage.get(MessageUtilEnum.MESSAGE_WEB_URL.getKey());
        String content = yfyMessage.get(MessageUtilEnum.MESSAGE_CONTENT.getKey()) + ":" + yfyMessage.get(MessageUtilEnum.MESSAGE_TITLE.getKey());
        return String.format("{\"receive_id\": \"%s\"," +
                "\"content\": \"{\\\"zh_cn\\\":{\\\"title\\\":\\\"%s\\\",\\\"content\\\":[[{\\\"tag\\\":\\\"a\\\",\\\"href\\\":\\\"%s\\\",\\\"text\\\":\\\"%s\\\"}]]}}\"," +
                "\"msg_type\": \"post\",\"uuid\": \"%s\"}", receivers, title, pcLink, content, UUID.randomUUID());

    }


    private boolean behaviorReviewMessage(Map<String, String> yfyMessage, List<APIParamConfigDto> paramConfig, String accessToken) {
        String content = yfyMessage.get(MessageUtilEnum.MESSAGE_CONTENT.getKey());
        if (content.contains("需要你审批")) {
            return createInstanceToPost(yfyMessage, paramConfig, accessToken);
        }
        if (content.contains("通过") || content.contains("关闭")) {
            return updateInstanceToPost(yfyMessage, paramConfig, accessToken);
        }
        return false;
    }

    private boolean updateInstanceToPost(Map<String, String> yfyMessage, List<APIParamConfigDto> paramConfig, String accessToken) {
        String postData = buildUpdateInstanceToPost(yfyMessage, paramConfig);
        String url = String.format(EXTERNAL_INSTANCES, HOST);
        log.info("updateInstanceToPost url: {}, postData: {}", url, postData);
        try {
            return toPost(url, postData, accessToken);
        } catch (Exception e) {
            log.error("updateInstanceToPost error, postData: {}", postData, e);
        }
        return false;
    }

    private String buildUpdateInstanceToPost(Map<String, String> yfyMessage, List<APIParamConfigDto> paramConfig) {
        String panHost = apiSyncHandler.getValueByParamConfig(paramConfig, "panHost");
        if (StringUtils.isBlank(panHost)) {
            //单域名可以用这个url
            panHost = customNacosConfig.getBaseUrl();
        }
        String instanceId = "instance_review_id_" + yfyMessage.get(MessageUtilEnum.MESSAGE_PRICESS_ID.getKey());
        String pcLink = panHost + yfyMessage.get(MessageUtilEnum.MESSAGE_WEB_URL.getKey());
        String mobileLink = panHost + "/h5" + yfyMessage.get(MessageUtilEnum.MESSAGE_H5_URL.getKey());
        String receivers = yfyMessage.get(MessageUtilEnum.MESSAGE_RECEIVERS.getKey());
        String reviewUser = yfyMessage.get(MessageUtilEnum.MESSAGE_REVIEW_USER.getKey());
        String review = yfyMessage.get(MessageUtilEnum.MESSAGE_REVIEWER.getKey());
        String content = yfyMessage.get(MessageUtilEnum.MESSAGE_CONTENT.getKey());
        long currentTime = System.currentTimeMillis();
        String status = "APPROVED";
        String taskStatus = StringUtils.EMPTY;
        if (content.contains("不通过")) {
            status = "REJECTED";
        }
        if (content.contains("关闭")) {
            status = "CANCELED";
            taskStatus = "REJECTED";
        }
        //如果发送人和接收人是同一个，说明是单条审批
        if (reviewUser.equals(receivers)) {
            receivers = review;
        }
        String taskId = "task_" + "review_id_" + yfyMessage.get(MessageUtilEnum.MESSAGE_REVIEW_ID.getKey()) + "_" + receivers;


        String title = yfyMessage.get(MessageUtilEnum.MESSAGE_TITLE.getKey());
        return String.format("{\"approval_code\":\"%s\",\"status\":\"%s\",\"instance_id\":\"%s\"," +
                        "\"links\":{\"pc_link\":\"%s\",\"mobile_link\":\"%s\"},\"open_id\":\"%s\"," +
                        "\"start_time\":\"%s\",\"end_time\":\"%s\",\"update_time\":\"%s\",\"display_method\":\"SIDEBAR\"," +
                        "\"update_mode\":\"UPDATE\",\"task_list\":[{\"task_id\":\"%s\",\"open_id\":\"%s\",\"title\":\"%s\",\"links\":{\"pc_link\":\"%s\"," +
                        "\"mobile_link\":\"%s\"},\"status\":\"%s\",\"create_time\":\"%s\",\"end_time\":\"%s\"," +
                        "\"update_time\":\"%s\",\"display_method\":\"SIDEBAR\",\"exclude_statistics\":false}],\"i18n_resources\":[{\"locale\":\"zh-CN\"," +
                        "\"texts\":[{\"key\":\"@i18n@1\",\"value\":\"%s\"}],\"is_default\":true},{\"locale\":\"en-US\",\"texts\":[{\"key\":\"@i18n@2\"," +
                        "\"value\":\"%s\"}],\"is_default\":false}]}",
                apiSyncHandler.getValueByParamConfig(paramConfig, "approval_code"), status, instanceId,
                pcLink, mobileLink, reviewUser,
                currentTime, currentTime, currentTime,
                taskId, receivers, title, pcLink,
                mobileLink, StringUtils.isNotBlank(taskStatus) ? taskStatus : status, currentTime, currentTime,
                currentTime, "行为审批消息", "Behavior Approval Message");
    }


    private boolean createInstanceToPost(Map<String, String> yfyMessage, List<APIParamConfigDto> paramConfig, String accessToken) {
        String postData = buildCreatInstancesPostData(yfyMessage, paramConfig);
        String url = String.format(EXTERNAL_INSTANCES, HOST);
        log.info("createInstance url: {}, postData: {}", url, postData);
        try {
            if (!toPost(url, postData, accessToken)) {
                Thread.sleep(1000);
                //在产生多条消息时候，可能会调用接口频繁
                return toPost(url, postData, accessToken);
            }
            return true;
        } catch (Exception e) {
            log.error("createInstance error, postData: {}", postData, e);
        }
        return false;
    }

    private String buildCreatInstancesPostData(Map<String, String> yfyMessage, List<APIParamConfigDto> paramConfig) {
        String panHost = apiSyncHandler.getValueByParamConfig(paramConfig, "panHost");
        if (StringUtils.isBlank(panHost)) {
            //单域名可以用这个url
            panHost = customNacosConfig.getBaseUrl();
        }
        String instanceId = "instance_review_id_" + yfyMessage.get(MessageUtilEnum.MESSAGE_PRICESS_ID.getKey());
        String pcLink = panHost + yfyMessage.get(MessageUtilEnum.MESSAGE_WEB_URL.getKey());
        String mobileLink = panHost + "/h5" + yfyMessage.get(MessageUtilEnum.MESSAGE_H5_URL.getKey());
        String receivers = yfyMessage.get(MessageUtilEnum.MESSAGE_RECEIVERS.getKey());
        String title = yfyMessage.get(MessageUtilEnum.MESSAGE_TITLE.getKey());
        long currentTime = System.currentTimeMillis();
        String reviewUser = yfyMessage.get(MessageUtilEnum.MESSAGE_REVIEW_USER.getKey());
        String content = yfyMessage.get(MessageUtilEnum.MESSAGE_CONTENT.getKey());
        String taskId = "task_" + "review_id_" + yfyMessage.get(MessageUtilEnum.MESSAGE_REVIEW_ID.getKey()) + "_" + receivers;
        String messagePushTitle = yfyMessage.get(MessageUtilEnum.MESSAGE_PUSH_TITLE.getKey());
        return String.format("{\"approval_code\":\"%s\",\"status\":\"PENDING\",\"instance_id\":\"%s\"," +
                        "\"links\":{\"pc_link\":\"%s\",\"mobile_link\":\"%s\"},\"title\":\"%s\"," +
                        "\"form\":[{\"name\":\"%s\",\"value\":\"%s\"}],\"open_id\":\"%s\",\"start_time\":\"%s\"," +
                        "\"end_time\":\"0\",\"update_time\":\"%s\",\"display_method\":\"SIDEBAR\",\"update_mode\":\"UPDATE\"," +
                        "\"task_list\":[{\"task_id\":\"%s\",\"open_id\":\"%s\",\"title\":\"%s\",\"links\":{\"pc_link\":\"%s\"," +
                        "\"mobile_link\":\"%s\"},\"status\":\"PENDING\",\"create_time\":\"%s\",\"end_time\":\"0\",\"update_time\":\"%s\"," +
                        "\"display_method\":\"SIDEBAR\",\"exclude_statistics\":false}],\"i18n_resources\":[{\"locale\":\"zh-CN\"," +
                        "\"texts\":[{\"key\":\"@i18n@1\",\"value\":\"%s\"}],\"is_default\":true},{\"locale\":\"en-US\",\"texts\":[{\"key\":\"@i18n@2\"," +
                        "\"value\":\"%s\"}],\"is_default\":false}]}",
                apiSyncHandler.getValueByParamConfig(paramConfig, "approval_code"), instanceId,
                pcLink, mobileLink, title,
                messagePushTitle, content, reviewUser, currentTime,
                currentTime,
                taskId, receivers, title, pcLink,
                mobileLink, currentTime, currentTime,
                "行为审批消息", "Behavior Approval Message");
    }


    private Boolean toPost(String url, String param, String accessToken) throws IOException {
        httpClientHelper.setHeaders(getHeaders(accessToken));
        Response response = httpClientHelper.postResponse(url, param);
        if (response == null || response.body() == null) {
            return false;
        }
        String result = response.body().string();
        log.info("result {}", result);
        int code = (int) JSONPath.extract(result, "$.code");
        if (code == 0) {
            return true;
        }
        return false;
    }


    private String getAppAccessToken(String host, String appId, String appSecret) {
        if (StringUtils.isAnyBlank(appId, appSecret)) {
            log.error("appId or appSecret is null!!");
            return null;
        }
        String url = String.format(GET_APP_ACCESS_TOKEN_URI, host);
        String tokenPostData = getTokenPostData(appId, appSecret);
        try {
            log.info("getAppAccessToken url {}, postData {}", url, tokenPostData);
            Response response = httpClientHelper.postResponse(url, tokenPostData);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("getAppAccessToken result {}", result);
            String token = (String) JSONPath.extract(result, "$.app_access_token");
            return token;
        } catch (Exception e) {
            log.error("getAppAccessToken error ", e);
        }
        return null;
    }

    private String getUserAccessToken(String host, String code, String appAccessToken) {
        if (StringUtils.isBlank(appAccessToken)) {
            log.error("appAccessToken is null");
            return null;
        }
        String url = String.format(GET_USER_ACCESS_TOKEN_URI, host);
        String tokenPostData = getUserTokenPostData(code);
        try {
            log.info("getUserAccessToken url {}, postData {}", url, tokenPostData);
            httpClientHelper.setHeaders(getHeaders(appAccessToken));
            Response response = httpClientHelper.postResponse(url, tokenPostData);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("getUserAccessToken result {}", result);
            String token = (String) JSONPath.extract(result, "$.data.access_token");
            return token;
        } catch (Exception e) {
            log.error("getUserAccessToken error ", e);
        }
        return null;
    }

    public String getAutoLoginUrl(FeiShuInitParams params) {
        YfyUser yfyUser = getUserInfoByCode(params);
        if (yfyUser == null) {
            log.error("getUserInfoByCode is null");
            return null;
        }
        String idInfo = null;
        if (CommonConstants.ENTERPRISE_LOGIN_TYPE_USER_ID.equals(params.getLoginType())) {
            idInfo = yfyUser.getId();
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
        } else if (CommonConstants.ENTERPRISE_LOGIN_TYPE_PHONE.equals(params.getLoginType())) {
            idInfo = yfyUser.getPhone();
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
        } else if (CommonConstants.ENTERPRISE_LOGIN_TYPE_EMAIL.equals(params.getLoginType())) {
            idInfo = yfyUser.getEmail();
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
        } else {
            log.error("未知的类型 loginType {}", params.getLoginType());
        }

        params.getEnterpriseParams().setIdentifier(idInfo);
        String loginUrl = getLoginUrl(params.getSyncUserFlag(), params.getEnterpriseParams(), yfyUser);
        if (loginUrl != null) {
            return loginUrl;
        }
        return CommonConstants.URL_ACCOUNT_NOT_OPEN;
    }


    public YfyUser getUserInfoByCode(FeiShuInitParams params) {
        String host = StringUtils.isNotBlank(params.getHost()) ? params.getHost() : HOST;
        String appAccessToken = getAppAccessToken(host, params.getAppId(), params.getAppSecret());
        String userAccessToken = getUserAccessToken(host, params.getCode(), appAccessToken);
        JSONObject userInfo = getUserInfoByUserAccessToken(host, userAccessToken);
        if (CollectionUtils.isEmpty(userInfo)) {
            log.error("userInfo is null");
            return null;
        }
        YfyUser yfyUser = new YfyUser();
        yfyUser.setId(userInfo.getString("open_id"));
        yfyUser.setFullName(userInfo.getString("name"));
        yfyUser.setEmail(userInfo.getString("email"));
        String mobile = userInfo.getString("mobile");
        if (mobile.contains(SyncTaskConstants.USER_PHONE_CN)) {
            mobile = mobile.substring(Math.max(0, mobile.length() - 11));
        }
        yfyUser.setPhone(mobile);
        yfyUser.setSpaceTotal(20 * GB);
        yfyUser.setCreateTime(new Date());
        yfyUser.setStatus(UserStatusEnum.SAVE.getCode());
        return yfyUser;
    }

    private JSONObject getUserInfoByUserAccessToken(String host, String userAccessToken) {
        if (StringUtils.isBlank(userAccessToken)) {
            log.error("userAccessToken is null");
            return null;
        }
        String url = String.format(GET_USER_INFO_URI, host);
        try {
            log.info("getUserInfoByUserAccessToken url {}", url);
            httpClientHelper.setHeaders(getHeaders(userAccessToken));
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("getUserInfoByUserAccessToken result {}", result);
            JSONObject userInfo = (JSONObject) JSONPath.extract(result, "$.data");
            return userInfo;
        } catch (Exception e) {
            log.error("getUserInfoByUserAccessToken error ", e);
        }
        return null;
    }

    public boolean sendMessageV2(MessageParams params) {
        // 登陆类型为mobile的时候需要转化消息接受者
        if (CommonConstants.ENTERPRISE_LOGIN_TYPE_PHONE.equals(params.getFeiShuInitParams().getLoginType())) {
            EnterpriseParams enterpriseParams = params.getFeiShuInitParams().getEnterpriseParams();
            UserEnterpriseInfo userEnterpriseInfo = UserEnterpriseInfo.builder()
                    .enterpriseId(enterpriseParams.getEnterpriseId())
                    .platformId(enterpriseParams.getPlatformId())
                    .userId(params.getReceivers()).build();
            JSONObject userInfoJSONObject = v2ClientHelper.getUserInfoFromV2(userEnterpriseInfo);
            if (!Objects.isNull(userInfoJSONObject)) {
                String phone = userInfoJSONObject.getString("phone");
                if (StringUtils.isEmpty(phone)) {
                    return false;
                }
                // 根据手机号获取获取钉钉用户id
                String userId = getUserIdByPhone(phone, params.getFeiShuInitParams());
                if (StringUtils.isEmpty(userId)) {
                    return false;
                }
                params.setReceivers(userId);
            }
        }
        String host = params.getFeiShuInitParams().getHost();
        if (StringUtils.isBlank(host)) {
            host = HOST;
        }
        // 发送消息
        return sendRoBotMessages(host, sendBotMessagePostData(getYfyMessage(params)), getAccessToken(params.getFeiShuInitParams().getAppId(), params.getFeiShuInitParams().getAppSecret(), host));
    }

    private String getUserIdByPhone(String phone, FeiShuInitParams feishuInitParams) {
        String url = String.format(BATCH_GET_ID, StringUtils.isNotBlank(feishuInitParams.getHost()) ? feishuInitParams.getHost() : HOST);
        String accessToken = getAccessToken(feishuInitParams.getAppId(), feishuInitParams.getAppSecret(), HOST);
        if (StringUtils.isBlank(accessToken)) {
            log.error("getUserIdByPhone accessToken is null feishuInitParams {}", feishuInitParams);
            return null;
        }
        String postData = getUserIdByPhonePostData(phone);
        try {
            log.info("getUserIdByPhone url:{}, postData:{}, params:{}", url, postData, JSON.toJSONString(feishuInitParams));
            httpClientHelper.setHeaders(getHeaders(accessToken));
            Response response = httpClientHelper.postResponse(url, postData);
            String result = response.body().string();
            log.info("getUserIdByPhone result:{} ",result);
            return (String) JSONPath.extract(result, "$.data.user_list[0].user_id");
        } catch (Exception e) {
            log.error("getUserIdByPhone error url:{}, postData:{}, params:{} ", url, postData, JSON.toJSONString(feishuInitParams), e);
        }
        return null;
    }

    private String getUserIdByPhonePostData(String phone) {
        return "{\"mobiles\":[\"" + phone + "\"],\"include_resigned\":true}";
    }


    private String sendBotMessagePostData(YfyMessage yfyMessage) {
        return  String.format("{\"receive_id\": \"%s\"," +
                "\"content\": \"{\\\"zh_cn\\\":{\\\"title\\\":\\\"%s\\\",\\\"content\\\":[[{\\\"tag\\\":\\\"a\\\",\\\"href\\\":\\\"%s\\\",\\\"text\\\":\\\"%s\\\"}]]}}\"," +
                "\"msg_type\": \"post\",\"uuid\": \"%s\"}", yfyMessage.getReceivers(), yfyMessage.getTitle(), yfyMessage.getH5Url(), yfyMessage.getContent(), UUID.randomUUID());

    }

    public FeiShuInitParams getInitParams(Integer enterpriseId) {
        try {
            FeiShuInitParams params = new FeiShuInitParams();
            log.info("enterpriseId:{}", enterpriseId);
            Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
            String additionalInfo = enterpriseInfo.getAdditionalInfo();
            if (enterpriseInfo == null || additionalInfo == null) {
                log.info("enterprise id {}, info is null ! {}", enterpriseId, enterpriseInfo);
                throw new ParamException("enterpriseInfo is null !");
            }

            String appId = null;
            String appSecret = null;
            String host = null;
            Object platformConfigArr = JSONPath.extract(additionalInfo, ARR_PLATFORM_CONFIG_PATH);
            if(Objects.isNull(platformConfigArr)){
                appId = JSONPath.extract(additionalInfo, FEI_SHU_APP_ID_PATH).toString();
                appSecret = JSONPath.extract(additionalInfo, FEI_SHU_APP_SECRET_PATH).toString();
                host = JSONPath.extract(additionalInfo, FEI_SHU_HOST_PATH).toString();
            }else {
                appId = JSONPath.extract(additionalInfo, ARR_FEI_SHU_APP_ID_PATH).toString();
                appSecret = JSONPath.extract(additionalInfo, ARR_FEI_SHU_APP_SECRET_PATH).toString();
                host = JSONPath.extract(additionalInfo, ARR_FEI_SHU_HOST_PATH).toString();
            }

            log.info("result: appId:{} , appSecret:{} , host:{}", appId, appSecret, host);
            if (appId == null || appSecret == null) {
                throw new ParamException("appId or appSecret is null !");
            }
            params.setAppId(appId);
            params.setAppSecret(appSecret);
            params.setHost(host);
            // 添加企业超级管理员信息
            EnterpriseParams enterpriseParams = new EnterpriseParams();
            enterpriseParams.setAdminUserId(enterpriseInfo.getAdminUserId());
            enterpriseParams.setPlatformId(enterpriseInfo.getPlatformId() + "");
            enterpriseParams.setEnterpriseId(enterpriseId + "");
            params.setEnterpriseParams(enterpriseParams);
            return params;
        } catch (Exception e) {
            log.error("get fei shu info error {}", e);
            throw new ParamException("get enterpriseInfo error !");
        }

    }


    public List<YfyDepartment> getYfyDepartmentByFeiShu(String appId, String appSecret, String host) {
        List<YfyDepartment> yfyDepartments = new ArrayList<>();
        String accessToken = getAccessToken(appId, appSecret, host);
        JSONArray departmentInfo = getDepartmentInfo(accessToken, "0", host);
        if (CollectionUtils.isEmpty(departmentInfo)) {
            return Collections.emptyList();
        }
        for (int i = 0; i < departmentInfo.size(); i++) {
            YfyDepartment yfyDepartment = new YfyDepartment();
            JSONObject jsonObject = departmentInfo.getJSONObject(i);
            if ((Boolean) JSONPath.extract(jsonObject.toJSONString(), "$.status.is_deleted")) {
                continue;
            }
            yfyDepartment.setId(jsonObject.getString("open_department_id"));
            yfyDepartment.setName(jsonObject.getString("name"));
            yfyDepartment.setParentId(jsonObject.getString("parent_department_id").equals("0") ? "" : jsonObject.getString("parent_department_id"));
            yfyDepartment.setStatus(DepartmentStatusEnum.SAVE.getCode());
            yfyDepartment.setDirectorId(StringUtils.isNotBlank(jsonObject.getString("leader_user_id")) ? jsonObject.getString("leader_user_id") : null);
            yfyDepartment.setPublicFolder(true);
            yfyDepartment.setCollabAutoAccepted(true);
            yfyDepartment.setOrder(getOrder(jsonObject.getString("order")));
            yfyDepartments.add(yfyDepartment);
        }
        return yfyDepartments;
    }

    public List<YfyUser> getYfyUsersByFeiShu(String appId, String appSecret, String host) {
        List<YfyUser> yfyUsers = new ArrayList<>();
        String accessToken = getAccessToken(appId, appSecret, host);
        JSONArray usersInfo = getUsersInfo(accessToken, "0", host);
        for (int i = 0; i < usersInfo.size(); i++) {
            YfyUser yfyUser = new YfyUser();
            JSONObject jsonObject = usersInfo.getJSONObject(i);
            if ((Boolean) JSONPath.extract(jsonObject.toJSONString(), "$.status.is_resigned")) {
                continue;
            }
            String mobile = jsonObject.getString("mobile");
            if (mobile.contains(SyncTaskConstants.USER_PHONE_CN)) {
                mobile = mobile.substring(Math.max(0, mobile.length() - 11));
            }
            yfyUser.setId(jsonObject.getString("open_id"));
            yfyUser.setFullName(jsonObject.getString("name"));
            yfyUser.setEmail(jsonObject.getString("email"));
            yfyUser.setPhone(mobile);
            yfyUser.setDepartmentIds((List<String>) jsonObject.get("department_ids"));
            yfyUsers.add(yfyUser);
        }
        return yfyUsers;
    }

    private Long getOrder(String order) {
        if (StringUtils.isBlank(order)) {
            return 0L;
        }
        try {
            return 1000000000L - Long.parseLong(order);
        } catch (Exception e) {
            log.error("getOrder error order:{}", order, e);
            return 0L;
        }
    }
}
