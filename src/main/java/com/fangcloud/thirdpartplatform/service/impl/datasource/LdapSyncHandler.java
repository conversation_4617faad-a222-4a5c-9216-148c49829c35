package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ADFilterDto;
import com.fangcloud.thirdpartplatform.entity.dto.ADValueBoxDto;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.source.ldap.LdapConfigProperties;
import com.fangcloud.thirdpartplatform.source.ldap.LdapTemplateFactory;
import com.fangcloud.thirdpartplatform.utils.SyncStringUtils;
import com.google.common.collect.Lists;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ldap.control.PagedResultsDirContextProcessor;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.ldap.core.support.SingleContextSource;
import org.springframework.ldap.query.LdapQuery;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import javax.naming.directory.SearchControls;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.sync.common.contants.SyncConstant.GB;
import static org.springframework.ldap.query.LdapQueryBuilder.query;

@Component
@Slf4j
public class LdapSyncHandler extends AbstractDataSourceSyncHandler {

    @Resource
    private CustomNacosConfig customNacosConfig;
    private static String objectGUID = "objectGUID";
    private static String DN_KEY = "distinguishedName";
    private static String MANAGER_BY = "managedBy";
    private static String SPLIT_SEMICOLON = ";";
    private static String SPLIT_COLON = ":";
    private static String SPLIT_COMMA = ",";
    //一人多岗标识
    private static String MULTIPLE_DEPARTMENTS_KEY = "multiple";
    private static Integer PAGE_SIZE = 999;

    private static String USER_ACCOUNT_CONTROL = "userAccountControl";

    private static Integer ACCOUNT_DISABLE = 2;


    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();
        if (platformSyncConfig == null) {
            return executeResult;
        }
        String valueBox = platformSyncConfig.getValueBox();
        ADValueBoxDto adValueBoxDto = JSON.parseObject(valueBox, ADValueBoxDto.class);

        List<YfyUser> users= new ArrayList<>();
        PagedResultsDirContextProcessor processor = new PagedResultsDirContextProcessor(PAGE_SIZE);
        SearchControls searchControls = new SearchControls();
        searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
        ADFilterDto filter = adValueBoxDto.getFilter();
        String groupName = filter.getGroupName();
        if (StringUtils.isBlank(adValueBoxDto.getSearch().getBaseOu()) || adValueBoxDto.getSearch().getBaseOu().equals("null")) {
            adValueBoxDto.getSearch().setBaseOu("");
        }
        if (StringUtils.isNotBlank(groupName)) {
            String[] split = groupName.split(SPLIT_SEMICOLON);
            LdapTemplate template = getLdapTemplate(adValueBoxDto);
            template.setIgnorePartialResultException(true);
            for (String group : split) {
                do {
                    List<YfyUser> search = template.search(adValueBoxDto.getSearch().getBaseOu(), "(&(objectCategory=user)(memberOf="+ group +"))", searchControls, new UserAttributesMapper(adValueBoxDto, template), processor);
                    users.addAll(search);
                } while (processor.hasMore());
            }
        } else {
            List<YfyUser> yfyUsers = pageSearchUser(adValueBoxDto, processor, searchControls);
            users.addAll(yfyUsers);
        }
        List<YfyUser> yfyUsers = users.stream().filter(s -> s != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(yfyUsers)) {
            return executeResult;
        }
        return syncUserList(platformSyncConfig, yfyUsers, taskId);
    }



    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        if (platformSyncConfig == null) {
            return new ExecuteResult();
        }
        String valueBox = platformSyncConfig.getValueBox();
        ADValueBoxDto adValueBoxDto = JSON.parseObject(valueBox, ADValueBoxDto.class);
        PagedResultsDirContextProcessor processor = new PagedResultsDirContextProcessor(PAGE_SIZE);
        SearchControls searchControls = new SearchControls();
        searchControls.setSearchScope(SearchControls.SUBTREE_SCOPE);
        if (StringUtils.isBlank(adValueBoxDto.getSearch().getBaseOu()) || adValueBoxDto.getSearch().getBaseOu().equals("null")) {
            adValueBoxDto.getSearch().setBaseOu("");
        }
        List<YfyDepartment> departmentList = pageSearchDepartments(adValueBoxDto, processor, searchControls);
        List<YfyDepartment> notNullList = departmentList.stream().filter(s -> s != null).collect(Collectors.toList());
        filterIsSyncParent(adValueBoxDto, notNullList,platformSyncConfig.getEnterpriseId());
        return syncDepartmentList(platformSyncConfig, notNullList, taskId);
    }

    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return null;
    }

    private void filterIsSyncParent(ADValueBoxDto adValueBoxDto, List<YfyDepartment> notNullList, Integer enterpriseId) {
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId+ "");
        String config = configMap.get(PlatformGlobalConfigKeyEnum.LDAP_SYNC_PARENT_STEP.getKey());
        Integer ldapSyncParentStep = 0;
        if (StringUtils.isNotBlank(config)) {
            ldapSyncParentStep = Integer.valueOf(config);
        }
        int syncParentStep = adValueBoxDto.getSearch().getSyncParentStep();
        if (syncParentStep == 0) {
            syncParentStep = ldapSyncParentStep;
        }
        if (syncParentStep > 0) {
            YfyDepartment yfyDepartment = notNullList.get(0);
            String distinguishedName = yfyDepartment.getDistinguishedName();
            LdapContextSource source = getSource(adValueBoxDto);
            LdapTemplate template = new LdapTemplate(source);
            for (int i = 0; i < syncParentStep; i++) {
                distinguishedName = getDepartmentDn(distinguishedName);
                if (distinguishedName.contains("/")){
                    distinguishedName = distinguishedName.replace("/","\\2f");
                }
                try {
                    YfyDepartment departmentOne = getDepartmentOne(template, distinguishedName, adValueBoxDto);
                    if (departmentOne == null) {
                        return;
                    }
                    if (StringUtils.isBlank(yfyDepartment.getParentId())) {
                        yfyDepartment.setParentId(departmentOne.getId());
                    }
                    notNullList.add(0, departmentOne);
                } catch (Exception e) {
                    log.error("filterIsSyncParent error ", e);
                }
            }
            //不管循环几次取出的数据要保障最顶层的parent为空
            notNullList.get(0).setParentId("");
        }
    }

    public static class DepartmentAttributesMapper implements AttributesMapper<YfyDepartment> {

        private ADValueBoxDto adValueBoxDto;
        private LdapTemplate ldapTemplate;

        public DepartmentAttributesMapper(ADValueBoxDto adValueBoxDto, LdapTemplate ldapTemplate) {
            this.adValueBoxDto = adValueBoxDto;
            this.ldapTemplate = ldapTemplate;
        }

        @Override
        public YfyDepartment mapFromAttributes(Attributes attributes) throws NamingException {
            ADFilterDto filter = adValueBoxDto.getFilter();

            YfyDepartment ldapDepartment = new YfyDepartment();
            ADValueBoxDto.ADField field = adValueBoxDto.getField();
            String dn = getLdapAttribute(DN_KEY, attributes);
            String id = field.getId();
            if (dn.contains("Domain Controllers")) {
                return null;
            }
            id = StringUtils.isBlank(id) ? objectGUID : field.getId();
            String departmentId = stringGUID(id, attributes);
            if (StringUtils.isBlank(departmentId)) {
                log.error("departmentId is not null  attributes{}",attributes);
                return null;
            }
            ldapDepartment.setId(departmentId);
            String name = field.getName();
            name = StringUtils.isBlank(name) ? "name" : name;
            ldapDepartment.setName(SyncStringUtils.formatString(getLdapAttribute(name, attributes)));
            ldapDepartment.setDistinguishedName(dn);
            String baseOu = adValueBoxDto.getSearch().getBaseOu();
            ADValueBoxDto.SyncRule syncRule = adValueBoxDto.getSyncRule();
            String parentId = field.getParentId();
            String parentIdStr = null;
            if (!StringUtils.isBlank(parentId)) {
                parentIdStr = getLdapAttribute(parentId, attributes);
            } else {
                parentIdStr = getDepartmentParentId(dn, ldapTemplate, baseOu);
            }
            String directorId = field.getDirectorId();
            if (StringUtils.isNotBlank(directorId)) {
                String ldapAttribute = getLdapAttribute(directorId, attributes);
                if (MANAGER_BY.equals(directorId) && StringUtils.isNotBlank(ldapAttribute)) {
                    String[] split = ldapAttribute.split(",");
                    if (split != null && split.length > 1) {
                        String cn = split[0];
                        String[] cnName = cn.split("=");
                        if (cnName != null && cnName.length > 1) {
                            ldapAttribute = cnName[1];
                        }
                    }
                }
                ldapDepartment.setDirectorId(ldapAttribute);
            }
            String depts = filter.getDepts();
            if (StringUtils.isNotBlank(depts)) {
                if (!deptFilter(dn, depts)) {
                    log.info("该部门不在同步范围内,请检查部门过滤配置 dn:{}", dn);
                    return null;
                }
            }
            ldapDepartment.setParentId(parentIdStr);
            String topDept = filter.getTopDept();
            if (StringUtils.isNotBlank(topDept) && StringUtils.isNotBlank(ldapDepartment.getId())) {
                if (ldapDepartment.getId().equals(topDept)) {
                    return null;
                }
            }
            if (StringUtils.isNotBlank(topDept) && StringUtils.isNotBlank(ldapDepartment.getParentId())) {
                if (ldapDepartment.getParentId().equals(topDept)) {
                    ldapDepartment.setParentId("");
                }
            }
            String orderStr = getLdapAttribute(field.getOrder(), attributes);
            ldapDepartment.setOrder(orderStr == null ? null : Long.valueOf(Integer.MAX_VALUE)-Long.valueOf(orderStr));
            if (syncRule != null) {
                ldapDepartment.setCollabAutoAccepted(syncRule.isCollabAutoAccepted());
                ldapDepartment.setPublicFolder(syncRule.isPublicFolder());
                if (!syncRule.isPositiveOrder()) {
                    ldapDepartment.setOrder(orderStr == null ? null : Long.valueOf(orderStr));
                }
            }
            ldapDepartment.setSpaceTotal(adValueBoxDto.getDeptSpace().longValue());
            ldapDepartment.setCreateTime(new Date());
            ldapDepartment.setUpdateTime(new Date());
            ldapDepartment.setStatus("1");
            log.info("syncDepartment, department:{}", JSON.toJSONString(ldapDepartment));
            return ldapDepartment;
        }
    }

    private static boolean deptFilter(String dn, String depts) {
        String[] split = depts.split(SPLIT_SEMICOLON);
        for (String path : split) {
            if (dn.contains(path)) {
                return true;
            }
        }
        return false;
    }

    public List<YfyUser> pageSearchUser(ADValueBoxDto adValueBoxDto, PagedResultsDirContextProcessor processor, SearchControls searchControls) {
        LdapContextSource source = getSource(adValueBoxDto);
        LdapTemplate template = new LdapTemplate(source);
        List<YfyUser> yfyUsers = SingleContextSource.doWithSingleContext(
                source, operations -> {
                    List<YfyUser> result = new LinkedList<>();
                    do {
                        List<YfyUser> oneResult = operations.search(
                                adValueBoxDto.getSearch().getBaseOu(),
                                "(&(objectclass=person))",
                                searchControls,
                                new UserAttributesMapper(adValueBoxDto, template),
                                processor);
                        result.addAll(oneResult);
                    } while (processor.hasMore());

                    return result;
                }, false, true, false);
        return yfyUsers;
    }

    public List<YfyDepartment> pageSearchDepartments(ADValueBoxDto adValueBoxDto, PagedResultsDirContextProcessor processor, SearchControls searchControls) {
        LdapContextSource source = getSource(adValueBoxDto);
        LdapTemplate template = new LdapTemplate(source);
        List<YfyDepartment> yfyDepartments = SingleContextSource.doWithSingleContext(
                source, operations -> {
                    List<YfyDepartment> result = new LinkedList<>();
                    do {
                        List<YfyDepartment> oneResult = operations.search(
                                adValueBoxDto.getSearch().getBaseOu(),
                                "(&(objectclass=organizationalUnit))",
                                searchControls,
                                new DepartmentAttributesMapper(adValueBoxDto, template),
                                processor);
                        result.addAll(oneResult);
                    } while (processor.hasMore());

                    return result;
                }, false, true, false);
        return yfyDepartments;
    }

    public static class UserAttributesMapper implements AttributesMapper<YfyUser> {
        private ADValueBoxDto adValueBoxDto;
        private LdapTemplate ldapTemplate;

        public UserAttributesMapper(ADValueBoxDto adValueBoxDto, LdapTemplate ldapTemplate) {
            this.adValueBoxDto = adValueBoxDto;
            this.ldapTemplate = ldapTemplate;
        }

        @Override
        public YfyUser mapFromAttributes(Attributes attributes) throws NamingException {
            ADValueBoxDto.ADField field = adValueBoxDto.getField();
            String dn = getLdapAttribute(DN_KEY, attributes);
            ADFilterDto filter = adValueBoxDto.getFilter();
            String depts = filter.getDepts();
            if (StringUtils.isNotBlank(depts)) {
                if (!deptFilter(dn, depts)) {
                    log.info("该用户不在同步范围内,请检查部门过滤配置 dn:{}", dn);
                    return null;
                }
            }
            YfyUser yfyUser = new YfyUser();
            String fullName = getLdapAttribute(field.getName(), attributes);
            yfyUser.setFullName(SyncStringUtils.formatString(fullName));
            String users = filter.getUsers();
            if (StringUtils.isNotBlank(users)) {
                String[] split = users.split(SPLIT_SEMICOLON);
                if (split != null && !Lists.newArrayList(split).contains(fullName)) {
                    return null;
                }
            }

            if (StringUtils.isNotBlank(field.getFilterRules())) {
                String[] split = field.getFilterRules().split("=");
                String status = getLdapAttribute(split[0], attributes);
                if (status.equals(split[1])) {
                    return null;
                }
            } else {
                if (getStatusIsDisable(getLdapAttribute(USER_ACCOUNT_CONTROL, attributes))) {
                    return null;
                }
            }

            if (StringUtils.isNotBlank(field.getIsDisableRules())) {
                String[] rules = field.getIsDisableRules().split(",");
                for (String rule : rules) {
                    String[] split = rule.split("=");
                    String status = getLdapAttribute(split[0], attributes);
                    if (status.equals(split[1])) {
                        yfyUser.setDisable(true);
                    }
                }
            }

            yfyUser.setCreateTime(new Date());
            yfyUser.setDistinguishedName(dn);
            String userId = getLdapAttribute(field.getId(), attributes);
            if (StringUtils.isBlank(userId)) {
                log.error("userId is not null  attributes{}",attributes);
                return null;
            }
            yfyUser.setId(userId);
            List<String> deptList = new ArrayList<>();
            String baseOu = adValueBoxDto.getSearch().getBaseOu();
            String defaultId = getDepartmentParentId(dn, ldapTemplate, baseOu);
            if (field.getDeptId().startsWith(MULTIPLE_DEPARTMENTS_KEY)){
                deptList.add(defaultId);
            }
            String deptId = getLdapAttribute(field.getDeptId().contains(SPLIT_COLON)
                    ? field.getDeptId().split(SPLIT_COLON)[1] : field.getDeptId(), attributes);
            if (StringUtils.isEmpty(deptId)) {
                deptId = defaultId;
            }
            deptList.addAll(Arrays.asList(deptId.split(SPLIT_COMMA)));
            yfyUser.setDepartmentIds(new ArrayList<>(new HashSet<>(deptList)));
            String email = getLdapAttribute(field.getMail(), attributes);
            String orderStr = getLdapAttribute(field.getOrder(), attributes);
            yfyUser.setOrder(orderStr == null ? null : Long.valueOf(Integer.MAX_VALUE)-Long.valueOf(orderStr));
            ADValueBoxDto.SyncRule syncRule = adValueBoxDto.getSyncRule();
            if (syncRule != null) {
                String emailSuffix = syncRule.getEmailSuffix();
                if (StringUtils.isBlank(email) && !StringUtils.isEmpty(emailSuffix)) {
                    email = yfyUser.getId() + "@" + emailSuffix;
                }
                if (!syncRule.isPositiveOrder()) {
                    yfyUser.setOrder(orderStr == null ? null : Long.valueOf(orderStr));
                }
            }
            yfyUser.setEmail(email);
            yfyUser.setPhone(getLdapAttribute(field.getPhone(), attributes));
            yfyUser.setSpaceTotal(Long.valueOf(adValueBoxDto.getUserSpace()) * GB);
            yfyUser.setUpdateTime(new Date());
            String expireTime = getLdapAttribute(field.getExpireTime(), attributes);
            if (StringUtils.isNotBlank(expireTime)) {
                expireTime = expireTime.replace("-", "");
                if (expireTime.matches("\\d{8}")) {
                    DateTimeFormatter pattern1 = DateTimeFormatter.ofPattern("yyyyMMdd");
                    LocalDate expirationDate = LocalDate.parse(expireTime, pattern1);
                    if (expirationDate.isBefore(LocalDate.now())) {
                        yfyUser.setDisable(true);
                        log.info("need disable user info: {}", JSON.toJSONString(yfyUser));
                    }
                } else {
                    log.warn("user expireTime is error: {}", expireTime);
                }
            }

            if (adValueBoxDto.getSyncRule().isActivate()) {
                yfyUser.setStatus("1");
            } else {
                yfyUser.setStatus("3");
            }
            if (adValueBoxDto.getSyncRule().isDmz()) {
                yfyUser.setDmz(true);
            }
            yfyUser.setForced(true);
            log.info("syncUser, users:{}", JSON.toJSONString(yfyUser));
            return yfyUser;
        }
    }

    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.AD.getDesc();
    }

    public static String getDepartmentParentId(String dn, LdapTemplate ldapTemplate, String base){
        String deptDn = getDepartmentDn(dn);
        if (deptDn == null) return null;
        return getDepartmentId(ldapTemplate, deptDn, base);
    }

    public static String getDepartmentDn(String dn) {
        String[] ss = dn.split(",");
        StringBuilder sb = new StringBuilder();
        for (int i=1; i<ss.length; i++){
            String s = ss[i].trim();
            sb.append(s);
            sb.append(",");
        }
        String deptDn = sb.toString();
        if (deptDn.length() == 0) {
            return null;
        }
        deptDn = deptDn.substring(0, sb.length() - 1);
        return deptDn;
    }

    public LdapContextSource getSource(ADValueBoxDto adValueBoxDto ) {
        LdapConfigProperties properties = buildProperties(adValueBoxDto);
        return LdapTemplateFactory.getSource(properties);
    }

    public LdapTemplate getLdapTemplate(ADValueBoxDto adValueBoxDto ) {
        LdapConfigProperties properties = buildProperties(adValueBoxDto);
        LdapTemplate template = LdapTemplateFactory.getTemplate(properties);
        return template;
    }

    public LdapConfigProperties buildProperties(ADValueBoxDto adValueBoxDto) {
        LdapConfigProperties properties = LdapConfigProperties.builder()
                .password(adValueBoxDto.getPassword())
                .base(adValueBoxDto.getBaseOu())
                .url(adValueBoxDto.getUrl())
                .userName(adValueBoxDto.getUserName())
                .isSasl(customNacosConfig.getCustomSyncUserSetLdapCheckSasl())
                .build();
        return properties;
    }

    public static String getDepartmentId(LdapTemplate ldapTemplate, String simpleDN, String base){
        if (simpleDN.contains("/")){
            simpleDN = simpleDN.replace("/","\\2f");
        }

        LdapQuery ldapQuery = query().base(base)
                .where("objectclass").is("organizationalUnit")
                .and(DN_KEY).is(simpleDN);
        ldapTemplate.setIgnorePartialResultException(true);
        List<String> search = ldapTemplate.search(ldapQuery, new AttributesMapper<String>() {
            @Override
            public String mapFromAttributes(Attributes attributes) throws NamingException {
                return stringGUID(objectGUID, attributes);
            }
        });
        if (CollectionUtils.isEmpty(search)) {
            return "";
        }
        return search.get(0);
    }

    public YfyDepartment getDepartmentOne(LdapTemplate ldapTemplate, String simpleDN,ADValueBoxDto adValueBoxDto){
        LdapQuery ldapQuery = query()
                .where("objectclass").is("organizationalUnit")
                .and(DN_KEY).is(simpleDN);
        ldapTemplate.setIgnorePartialResultException(true);
        List<YfyDepartment> departments = ldapTemplate.search(ldapQuery, new DepartmentAttributesMapper(adValueBoxDto,ldapTemplate));
        if (CollectionUtils.isEmpty(departments)) {
            return null;
        }
        return departments.get(0);
    }

    public YfyUser findOneUser(String customId, ADValueBoxDto adValueBoxDto) {
        LdapContextSource source = getSource(adValueBoxDto);
        ADValueBoxDto.ADField field = adValueBoxDto.getField();
        LdapTemplate ldapTemplate = new LdapTemplate(source);
        LdapQuery ldapQuery = query().
                where("objectclass").is("person").
                and(field.getId()).is(customId);
        ldapTemplate.setIgnorePartialResultException(true);
        List<YfyUser> yfyUsers = ldapTemplate.search(ldapQuery, new UserAttributesMapper(adValueBoxDto, ldapTemplate));
        if (!CollectionUtils.isEmpty(yfyUsers)){
            return yfyUsers.get(0);
        }
        return null;
    }

    public static String stringGUID(String key, Attributes attributes) {
        Attribute attribute = attributes.get(key);
        Object o = null;
        try {
            if (attribute == null) {
                return null;
            }
            o = attribute.get();
        } catch (NamingException e) {

        }
        byte[] GUID = (byte[]) o;
        StringBuilder strGUID = new StringBuilder();
        strGUID.append( AddLeadingZero((int) GUID[3] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[2] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[1] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[0] & 0xFF));
        strGUID.append( "-" );
        strGUID.append( AddLeadingZero((int) GUID[5] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[4] & 0xFF));
        strGUID.append( "-" );
        strGUID.append( AddLeadingZero((int) GUID[7] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[6] & 0xFF));
        strGUID.append( "-");
        strGUID.append( AddLeadingZero((int) GUID[8] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[9] & 0xFF));
        strGUID.append( "-" );
        strGUID.append( AddLeadingZero((int) GUID[10] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[11] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[12] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[13] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[14] & 0xFF));
        strGUID.append( AddLeadingZero((int) GUID[15] & 0xFF));

        return strGUID.toString();
    }

    public static String AddLeadingZero(int k) {
        return (k <= 0xF) ? "0" + Integer.toHexString(k) : Integer.toHexString(k);
    }

    public static String getLdapAttribute(String name, Attributes attributes) throws NamingException {
        if (StringUtils.isEmpty(name)) {
            return null;
        }
        if (null != attributes) {
            Attribute attribute = attributes.get(name);

            return attribute == null ? null : attribute.get().toString().trim();
        }
        return null;
    }

    public static Boolean getStatusIsDisable(String userAccountControl){
        return userAccountControl != null && (Integer.parseInt(userAccountControl) & ACCOUNT_DISABLE) == ACCOUNT_DISABLE;
    }

    public static void main(String[] args) {
        //需要同步路径
        String demo = "OU=北京华联(SKP)百货有限公司,DC=skp,DC=bj;OU=北京华联时尚百货有限公司,DC=skp,DC=bj";
        //当前部门路径
        String dn = "OU=北京厨房1,OU=北京厨房,OU=北京华联(SKP)百货有限公司,DC=skp,DC=bj";
        String[] split = demo.split(SPLIT_SEMICOLON);
        for (String s : split) {
            if (dn.contains(s)) {
                System.out.printf("同步");
            }
        }
        if (!Lists.newArrayList(split).contains(dn)) {
            System.out.printf("不同步");
        }
    }
}
