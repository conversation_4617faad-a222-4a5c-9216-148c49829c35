package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncResultStatusEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncTaskRecordMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.helper.SyncTaskHelper;
import com.fangcloud.thirdpartplatform.service.PlatformSyncConfigExecutor;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskExecutor;
import com.fangcloud.thirdpartplatform.service.PlatformSyncTaskService;
import com.fangcloud.thirdpartplatform.service.impl.datasource.PushSyncHandler;
import com.fangcloud.thirdpartplatform.utils.NamedThreadPools;
import com.fangcloud.thirdpartplatform.utils.thread.TaskThreadContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

@Service
@Slf4j
public class PlatformSyncConfigExecutorImpl implements PlatformSyncConfigExecutor {
    private ExecutorService executorService = NamedThreadPools.newFixedThreadPool(50, "platform-syncConfig-executor");

    @Resource
    private PlatformSyncTaskExecutor platformSyncTaskExecutor;
    @Resource
    private PlatformSyncTaskRecordMapper platformSyncTaskRecordMapper;
    @Resource
    private PlatformSyncTaskService platformSyncTaskService;
    @Resource
    private SyncTaskHelper syncTaskHelper;
    @Resource
    private PushSyncHandler pushSyncHandler;
    @Override
    public Integer execute(PlatformSyncConfig platformSyncConfig, boolean isSync) {
        try {
            Integer id = platformSyncConfig.getId();
            Integer enterpriseId = platformSyncConfig.getEnterpriseId();
            PlatformSyncTaskRecord platformSyncTaskRecord = new PlatformSyncTaskRecord();
            platformSyncTaskRecord.setSyncConfigId(id);
            platformSyncTaskRecord.setSyncTaskType(platformSyncConfig.getSyncType());
            platformSyncTaskRecord.setSyncTaskStatus(SyncResultStatusEnum.PROGRESSING.getStatus());
            platformSyncTaskRecord.setEnterpriseId(enterpriseId);
            long time = new Date().getTime();
            platformSyncTaskRecord.setSyncStartat(time);
            platformSyncTaskRecord.setCreated(time);
            platformSyncTaskRecord.setSyncResult(1);
            platformSyncTaskRecord.setUpdated(time);
            int i = platformSyncTaskRecordMapper.insertSelective(platformSyncTaskRecord);
            int taskId = platformSyncTaskRecord.getId();
            if (i > 0) {
                String sourceType = platformSyncConfig.getSourceType();
                if (!isSync) {
                    HttpServletRequest request = getRequest(sourceType);
                    executorService.submit(() -> {
                        setThreadContext(sourceType, request);
                        sync(platformSyncConfig, taskId, platformSyncTaskRecord);
                        removeThreadContext(sourceType);
                    });
                } else {
                    setThreadContext(sourceType, getRequest(sourceType));
                    sync(platformSyncConfig, taskId, platformSyncTaskRecord);
                    removeThreadContext(sourceType);
                }
            }
            return taskId;
        }catch (Throwable e) {
            log.error("PlatformSyncConfigExecutorImpl-error, param:{}", JSON.toJSON(platformSyncConfig),e);
        }
        return null;
    }

    private void removeThreadContext(String sourceType) {
        if (SourceTypeEnum.HTTP_REQUEST_SOURCE_TYPES.contains(SourceTypeEnum.getByDesc(sourceType))) {
            TaskThreadContextHolder.remove();
        }
    }

    private HttpServletRequest getRequest(String sourceType) {
        if (SourceTypeEnum.HTTP_REQUEST_SOURCE_TYPES.contains(SourceTypeEnum.getByDesc(sourceType))) {
            ServletRequestAttributes attrs = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            return attrs.getRequest();
        }
        return null;
    }

    private void setThreadContext(String sourceType, HttpServletRequest request) {
        if (SourceTypeEnum.HTTP_REQUEST_SOURCE_TYPES.contains(SourceTypeEnum.getByDesc(sourceType))) {
            Map<String, String> headsMap = pushSyncHandler.getHeadsByRequest(request);
            Map<String, Object> paramsMap = pushSyncHandler.getParamsByRequest(request);
            String bodyParam = pushSyncHandler.getBodyParam(request);
            TaskThreadContextHolder.set(new TaskThreadContextHolder.TaskThreadContext(headsMap, paramsMap, bodyParam));
        }
    }

    private void sync(PlatformSyncConfig platformSyncConfig, int taskId ,PlatformSyncTaskRecord platformSyncTaskRecord) {
        try {
            platformSyncTaskService.scheduledDeleteSyncTaskFailLogs(platformSyncTaskRecord.getEnterpriseId());
            ExecuteResult execute = platformSyncTaskExecutor.execute(platformSyncConfig, taskId);
            platformSyncTaskRecord.setSyncTaskStatus(SyncResultStatusEnum.FINISH.getStatus());
            if (execute != null && execute.getErrorRows() <= 0) {
                platformSyncTaskRecord.setSyncResult(SyncResultStatusEnum.FINISH.getStatus());
            }
            if (execute == null || execute.getErrorRows() > 0) {
                if (syncTaskHelper.checkMultiTask(platformSyncConfig)) {
                    return;
                }
                platformSyncTaskRecord.setSyncResult(SyncResultStatusEnum.ERROR.getStatus());
            }
            platformSyncTaskRecord.setValueBox(JSON.toJSONString(execute));
            long endTime = new Date().getTime();
            platformSyncTaskRecord.setUpdated(endTime);
            platformSyncTaskRecord.setSyncEndat(endTime);
            platformSyncTaskRecordMapper.updateByPrimaryKeySelective(platformSyncTaskRecord);
        } catch (Exception e) {
            PlatformSyncTaskFailLogs platformSyncTaskFailLogs = new PlatformSyncTaskFailLogs();
            platformSyncTaskFailLogs.setCreated(new Date().getTime());
            platformSyncTaskFailLogs.setSyncTaskId(taskId);
            platformSyncTaskFailLogs.setEnterpriseId(platformSyncConfig.getEnterpriseId());
            StackTraceElement[] stackTrace = e.getStackTrace();
            String jsonString = JSON.toJSONString(stackTrace);
            String s = jsonString == null ? "请查看日志" : jsonString;
            platformSyncTaskFailLogs.setReason(StringUtils.isBlank(e.getMessage()) ? s.substring(0, 150) : e.getMessage());
            List<PlatformSyncTaskFailLogs> records = Arrays.asList(platformSyncTaskFailLogs);
            platformSyncTaskService.batchInsertLogs(records);
            log.error("platformSyncTaskExecutor-error, param:{}", JSON.toJSON(platformSyncConfig), e);
            platformSyncTaskRecord.setSyncTaskStatus(SyncResultStatusEnum.FINISH.getStatus());
            platformSyncTaskRecord.setSyncResult(SyncResultStatusEnum.ERROR.getStatus());
            long endTime = new Date().getTime();
            platformSyncTaskRecord.setUpdated(endTime);
            platformSyncTaskRecord.setSyncEndat(endTime);
            platformSyncTaskRecordMapper.updateByPrimaryKeySelective(platformSyncTaskRecord);
        }
    }
}
