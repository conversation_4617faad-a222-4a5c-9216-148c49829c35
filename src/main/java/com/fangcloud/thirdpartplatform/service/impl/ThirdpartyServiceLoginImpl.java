package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.helper.ChameleonClientHelper;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyLoginService;
import com.fangcloud.thirdpartplatform.utils.CookieUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.Objects;

@Service
@Slf4j
public class ThirdpartyServiceLoginImpl implements ThirdpartyLoginService {

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private ChameleonClientHelper chameleonClientHelper;

    static final String TYPE_ZDJS = "zdjs";
    static final String TYPE_ZDJS_COOKIE_KEY_CESTC_TOKEN= "yfyCestcToken";


    @Override
    public Object logout(HttpServletRequest request, HttpServletResponse response) throws Exception {
        String type = request.getParameter("type");
        switch (type){
            case TYPE_ZDJS:
                return zdjsExecuteLogout(request, response);
            default:
                return null;
        }
    }

    @Override
    public Object loginCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String type = request.getParameter("type");
        // 部分系统回调只能传一个参数，多个参数使用_隔开
        String[] split = type.split("_");
        switch (split[0]){
            case TYPE_ZDJS:
                return zdjsExecuteLogin(request, response, split);
            default:
                return null;
        }
    }


    /**
     * 中电建设登陆写入登陆token
     * @param request
     * @param response
     * @param split
     * @return
     * @throws IOException
     */
    private Object zdjsExecuteLogin(HttpServletRequest request, HttpServletResponse response, String[] split) throws IOException {
        String cestcToken = request.getParameter("cestcToken");
        String baseUrl = chameleonClientHelper.getBaseUrl(Integer.parseInt(split[1]));

        log.info("baseUrl is :{}, cestcToken is :{}", baseUrl, cestcToken);
        // 在cookie中写入登陆的token
        Cookie cookie = new Cookie(TYPE_ZDJS_COOKIE_KEY_CESTC_TOKEN, cestcToken);
        cookie.setPath("/");
        cookie.setDomain(customNacosConfig.getBaseDomain());
        response.addCookie(cookie);
        response.sendRedirect(baseUrl + "sso/oauth/postlogin?code=" +cestcToken);
        return null;
    }

    /**
     * 中电建设执行登出
     * @param request
     * @param response
     * @return
     * @throws IOException
     */
    private Object zdjsExecuteLogout(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String logoutUrlEncode = request.getParameter("logoutUrl");
        String logoutUrl = URLDecoder.decode(logoutUrlEncode,"UTF-8");
        String productCode = request.getParameter("productCode");

        // 从cookie中获取登陆token，调用登出接口，清除登陆态
        String cestcToken = CookieUtils.getValueFromCookie(request.getCookies(), TYPE_ZDJS_COOKIE_KEY_CESTC_TOKEN);
        String bodyFormat = "{\"productCode\": \"%s\",\"cestcToken\": \"%s\"}";
        String body = String.format(bodyFormat, productCode, cestcToken);
        try {
            Response logoutResponse = httpClientHelper.postResponse(logoutUrl, body);
            if (null != logoutResponse.body() && logoutResponse.code() == 200) {
                String responseResult = Objects.requireNonNull(logoutResponse.body()).string();
                log.info("zdjs logout result {}", responseResult);
            }
        }catch (Exception e){
            log.info("zdjs logout error!", e);
        }
        // 清除cookie
        CookieUtils.deleteAllCookie(response, request.getCookies());

        // 重定向到云盘首页
        String serviceEncode = request.getParameter("service");
        String service = URLDecoder.decode(serviceEncode,"UTF-8");
        log.info("redirect url {}", service);
        response.sendRedirect(service);
        return null;
    }

}
