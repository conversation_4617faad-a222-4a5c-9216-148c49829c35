package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.ResponseCodeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncConfigStatusEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.factory.PlatformSyncConfigFactory;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncConfigParams;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.fangcloud.thirdpartplatform.helper.SyncTaskHelper;
import com.fangcloud.thirdpartplatform.service.PlatFormSyncConfigService;
import com.fangcloud.thirdpartplatform.service.PlatformSyncConfigExecutor;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.support.CronSequenceGenerator;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class PlatformSyncConfigServiceImpl implements PlatFormSyncConfigService {
    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;
    @Resource
    private PlatformSyncConfigExecutor platformSyncConfigExecutor;
    @Resource
    private SyncTaskHelper syncTaskHelper;
    @Override
    public boolean update(PlatformSyncConfigParams platformSyncConfigParams) {
        PlatformSyncConfig platformSyncConfig = PlatformSyncConfigFactory.build(platformSyncConfigParams);
        platformSyncConfig.setUpdated(new Date().getTime());
        int i = platformSyncConfigMapper.updateByPrimaryKeySelective(platformSyncConfig);
        return i > 0;
    }

    @Override
    public boolean save(PlatformSyncConfigParams platformSyncConfigParams) {
        PlatformSyncConfig platformSyncConfig = PlatformSyncConfigFactory.build(platformSyncConfigParams);
        long time = new Date().getTime();
        platformSyncConfig.setCreated(time);
        platformSyncConfig.setUpdated(time);
        int i = platformSyncConfigMapper.insertSelective(platformSyncConfig);
        return i > 0;
    }

    @Override
    public List<PlatformSyncConfigResponse> list(Integer enterpriseId, Integer syncType) {
        List<PlatformSyncConfig> query = platformSyncConfigMapper.queryByEnterpriseId(enterpriseId, syncType);
        return PlatformSyncConfigFactory.build(query);
    }

    @Override
    public PlatformSyncConfigResponse get(Integer id) {
        if (id == null || id <= 0) {
            PlatformSyncConfig platformSyncConfig = new PlatformSyncConfig();
            platformSyncConfig.setSourceType(SourceTypeEnum.API.getDesc());
            return PlatformSyncConfigFactory.build(platformSyncConfig);
        }
        PlatformSyncConfig config = platformSyncConfigMapper.selectByPrimaryKey(id);
        if (config == null || config.getDeleted() == 1) {
            return null;
        }
        return PlatformSyncConfigFactory.build(config);
    }

    @Override
    public boolean deleted(Integer id) {
        int i = platformSyncConfigMapper.updateDelete(id, 1);
        return i > 0;
    }

    @Override
    public boolean open(Integer id, Boolean open) {
        if (id == null || open == null) {
            return false;
        }
        PlatformSyncConfig platformSyncConfig = platformSyncConfigMapper.selectByPrimaryKey(id);
        CronSequenceGenerator generator = new CronSequenceGenerator(platformSyncConfig.getCron());
        PlatformSyncConfig newPlatformSyncConfig = new PlatformSyncConfig();
        newPlatformSyncConfig.setId(platformSyncConfig.getId());
        try {
            long time = generator.next(new Date()).getTime();
            newPlatformSyncConfig.setNextExecuteTime(time);
        } catch (Exception e) {
            log.error("同步时间设置格式异常");
        }
        platformSyncConfigMapper.updateByPrimaryKeySelective(newPlatformSyncConfig);
        PlatformSyncConfig config = platformSyncConfigMapper.selectByPrimaryKey(id);
        if (config.getSyncType().equals(SyncTypeEnum.USERS.getSyncType()) && syncTaskHelper.checkMultiTask(config)) {
            if (!open || syncTaskHelper.checkMainTask(config)) {
                return updateStatus(id, open);
            }
        } else {
            return updateStatus(id, open);
        }
        return false;
    }

    @Override
    public Result<Boolean> sync(Integer id) {
        PlatformSyncConfig config = platformSyncConfigMapper.selectByPrimaryKey(id);
        if (config == null || config.getDeleted() != 0) {
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR,"sync config not found");
        }
        if (config.getSyncType().equals(SyncTypeEnum.USERS.getSyncType()) && syncTaskHelper.checkMultiTask(config) &&
                !syncTaskHelper.checkMainTask(config)) {
            return ResultUtils.getFailedResult(ResponseCodeEnum.ERROR,"is not main task, task_binding:"
                    + syncTaskHelper.getTaskBinding(config));
        }
        platformSyncConfigExecutor.execute(config,false);
        return ResultUtils.getSuccessResult();
    }

    private boolean updateStatus(Integer id, Boolean open) {
        int i = platformSyncConfigMapper.updateStatus(id,
                open ? SyncConfigStatusEnum.START.getStatus() : SyncConfigStatusEnum.STOP.getStatus());
        return i > 0;
    }

}
