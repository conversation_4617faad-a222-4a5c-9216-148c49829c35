package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.service.PushSyncService;
import com.fangcloud.thirdpartplatform.service.impl.datasource.PushSyncHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class PushSyncServiceImpl implements PushSyncService {

    @Resource
    private PushSyncHandler pushSyncHandler;

    @Async
    public void asyncExceptionRetry(Integer taskId) {
        pushSyncHandler.asyncExceptionRetry(taskId);
    }
}
