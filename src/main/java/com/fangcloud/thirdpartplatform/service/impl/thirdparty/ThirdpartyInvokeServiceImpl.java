package com.fangcloud.thirdpartplatform.service.impl.thirdparty;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTypeEnum;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.dto.CodeScriptValueBox;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ThirdpartyInvokeServiceImpl implements ThirdpartyExecuteService {

    @Resource
    private APIHelper apiHelper;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    public static String VALUE_BOX_KEY_ENTERPRISE_ID = "enterpriseId";
    public static String VALUE_BOX_KEY_THIRDPARTY_INVOKE_TYPE = "thirdpartyInvokeType";

    // 通用请求进来的request，里面可以获取请求的各种信息
    public static String VALUE_BOX_KEY_REQUEST = "request";
    // 通用请求进来的response
    public static String VALUE_BOX_KEY_RESPONSE = "response";


    @Override
    public Result<Object> execute(ThirdpartyParams thirdpartyParams) {

        JSONObject jsonObject = new JSONObject();
        try {
            jsonObject =JSONObject.parseObject(JSON.toJSONString(thirdpartyParams.getData()), JSONObject.class);
        }catch (Exception e){
            jsonObject = (JSONObject) thirdpartyParams.getData();
        }

        String enterpriseId = jsonObject.getString(VALUE_BOX_KEY_ENTERPRISE_ID);
        String thirdpartyInvokeType = jsonObject.getString(VALUE_BOX_KEY_THIRDPARTY_INVOKE_TYPE);

        CodeScriptValueBox codeScriptValueBox = getCodeScriptValueBox(Integer.valueOf(enterpriseId), thirdpartyInvokeType);

        if(Objects.isNull(codeScriptValueBox)){
            throw new ParamException("code is null! thirdpartyInvokeType:" + thirdpartyInvokeType);
        }
        try {
            Object result = apiHelper.executeContext(URLDecoder.decode(codeScriptValueBox.getCodeScript(), "UTF-8"), jsonObject);
            return ResultUtils.getSuccessResult(result);
        } catch (UnsupportedEncodingException e) {
            throw new ParamException(SyncTaskConstants.CODE_SCRIPT_URL_DECODE_ERROR);
        }

    }

    /**
     * 获取需要执行的代码
     * @param enterpriseId
     * @param thirdpartyInvokeType
     * @return
     */
    public CodeScriptValueBox getCodeScriptValueBox(Integer enterpriseId, String thirdpartyInvokeType) {
        List<PlatformSyncConfig> platformSyncConfigs = platformSyncConfigMapper.queryByEnterpriseId(enterpriseId, SyncTypeEnum.THIRDPARTY_INVOKE.getSyncType());


        List<PlatformSyncConfig> PlatformSyncConfigList = platformSyncConfigs.stream().
                filter(platformSyncConfig -> platformSyncConfig.getSourceType().equals(thirdpartyInvokeType))
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(PlatformSyncConfigList)){
            return null;
        }
        CodeScriptValueBox codeScriptValueBox = JSONObject.parseObject(PlatformSyncConfigList.get(0).getValueBox(), CodeScriptValueBox.class);
        return codeScriptValueBox;
    }

    @Override
    public Result<Object> executeCallback(ThirdpartyCallbackParams thirdpartyCallbackParams) {
      return null;
    }


    @Override
    public String getThirdpartyExecuteType() {
        return ThirdpartyCallTypeEnum.THIRDPARTY_INVOKE.getDesc();
    }
}
