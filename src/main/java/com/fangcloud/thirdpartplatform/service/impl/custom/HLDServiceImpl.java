package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 葫芦岛免密登录
 */
@Component
@Slf4j
public class HLDServiceImpl {


    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private OpenClientHelper openClientHelper;

    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        String localIp = getIpAddress(request);
        log.info("HLD 获取客户端请求IP 为 {}", localIp);
        String apiHost = request.getParameter("api_host");
        if (StringUtils.isEmpty(apiHost)) {
            apiHost = "**********:36105";
        }
        String tokeUrl = String.format("http://%s/user/mvc/api/getToken?loginIp=%s", apiHost, localIp);
        String userInfoUrl = String.format("http://%s/user/mvc/api/getUserInfoByToken", apiHost);

//        String userInfoUrl = "https://www.fastmock.site/mock/db4bf316e6551c244abbea602f2ed8e8/prefession/user/mvc/api/getUserInfoByToken";
//        String tokeUrl = "https://www.fastmock.site/mock/db4bf316e6551c244abbea602f2ed8e8/prefession/user/mvc/api/getToken";
        String clientId = configInfo.getClientId();
        String enterpriseId = configInfo.getEnterpriseId();
        String platformId = configInfo.getPlatformId();

        try {
            Response response = httpClientHelper.getResponse(tokeUrl);
            if (null == response.body()) {
                // 重试一次
                response = httpClientHelper.getResponse(tokeUrl);
            }

            if (null == response.body()) {
                log.error("获取HLD用户token 异常为空 url {}", tokeUrl);
                return null;
            }
            String result = Objects.requireNonNull(response.body()).string();
            log.info("HLD getToken接口返回 数据 {}", result);
            Object token = JSONPath.extract(result, "$.data.token");
            if (token != null) {
                String params = String.format("{\"token\":\"%s\"}", token);

                response =  httpClientHelper.postResponse(userInfoUrl, params);

                if (null == response.body()) {
                    // 重试一次
                    response = httpClientHelper.getResponse(tokeUrl);
                }

                if (null == response.body()) {
                    log.error("获取HLD用户信息 异常为空 url {} params {}", userInfoUrl, params);
                    return null;
                }

                result = Objects.requireNonNull(response.body()).string();
                log.info("HLD getUserInfo 接口返回 数据 {}", result);
                Object loginName = JSONPath.extract(result, "$.data.loginName");

                if (loginName != null) {
                    EnterpriseParams enterpriseParams = new EnterpriseParams();
                    enterpriseParams.setEnterpriseId(enterpriseId);
                    enterpriseParams.setIdentifier((String)loginName);
                    enterpriseParams.setPlatformId(platformId);
                    enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
                    enterpriseParams.setClientId(clientId);
                    String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
                    log.info("HLD 免密登录url {}", loginUrl);
                    return loginUrl;
                }
            }
        } catch (Exception e) {
            log.error("HLD 异常", e);
        }
        return null;
    }

    /**
     * 获取客户端的IP。
     * @param request
     * @return
     */
     public String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("X-Real-IP");
        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getRemoteAddr();
        }

        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("X-Forwarded-For");
        }

        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("X-Cluster-Client-Ip");
        }

        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }

        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }

        if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase("unknown")) {
            ip = request.getRemoteAddr();
        }

        return ip;
    }
}
