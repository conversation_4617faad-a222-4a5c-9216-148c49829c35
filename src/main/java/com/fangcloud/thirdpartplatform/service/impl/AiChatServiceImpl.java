package com.fangcloud.thirdpartplatform.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.AiChatConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.ai.AiPubDocYunEventSourceListener;
import com.fangcloud.thirdpartplatform.helper.ai.AiSseEventSourceListener;
import com.fangcloud.thirdpartplatform.helper.ai.ChatGPTStream;
import com.fangcloud.thirdpartplatform.helper.ai.SseEmitterUtil;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.AiChatService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

@Component
@Slf4j
public class AiChatServiceImpl implements AiChatService {

    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private RedisStringManager redisStringManager;

    @Autowired
    private PlatformUserMapper platformUserMapper;



    private static final List<String> chatTypeList = Arrays.asList("single_chat","group_chat");
    private static final String SEND_ROBOT_MESSAGE = "%s/message/custom/send?appid=%s&secret=%s";
    private static final String ERRCODE_PATH = "$.errcode";


    @Override
    public SseEmitter chatStream(HttpServletRequest request, HttpServletResponse response, JSONObject jSONObject) {

        try {

            String chatType = jSONObject.getString("chatType");
            jSONObject.remove("chatType");
            switch (chatType){
                case AiChatConstants.CHAT_TYPE_KNOWLEDGE_GPT:
                    return knowledgeAssistantChat(request, jSONObject);
                case AiChatConstants.CHAT_TYPE_KNOWLEDGE_ZSH_GPT:
                    return knowledgeZshChat(request, jSONObject);
                case AiChatConstants.CHAT_TYPE_KNOWLEDGE_360_AI_SEARCH:
                    return knowledgeAiSearch(request, jSONObject);
                case AiChatConstants.CHAT_TYPE_KNOWLEDGE_AI_FILE_CHAT:
                    return knowledgeAiFileChat(request, jSONObject);
                case AiChatConstants.CHAT_TYPE_KNOWLEDGE_AI_FILE_EXTRACT:
                    return knowledgeAiFileExtract(request, jSONObject);
                default:
                    throw new ParamException("chatType error!");
            }

        }catch (Exception e){
            SseEmitter sseEmitter = new SseEmitter(-1L);
            SseEmitterUtil.sendError(sseEmitter,e.getMessage());
            return sseEmitter;
        }

    }

    private SseEmitter knowledgeAiFileChat(HttpServletRequest request, JSONObject jSONObject) throws IOException{
        SseEmitter sseEmitter = new SseEmitter(-1L);

        String userId = getUserId(request, sseEmitter);
        if(StringUtils.isEmpty(userId)) {
            return sseEmitter;
        }else {

            JSONObject jsonObjectParam = jSONObject.getJSONObject("data");
            buildFilesParam(jsonObjectParam);

            jsonObjectParam.put("sessionType", AiChatConstants.CHAT_AI_FILE_CHAT);
            jsonObjectParam.put("sessionId", jSONObject.getString("sessionId"));
            jsonObjectParam.put("messages", jSONObject.getJSONArray("messages"));

            String chatUrl = customNacosConfig.getAiapiHostUrl() + customNacosConfig.getAiapiAiFileChat();

            log.info("chatUrl : {}, jsonObjectParam : {}", chatUrl, JSON.toJSONString(jsonObjectParam));

            ChatGPTStream chatGPTStream = ChatGPTStream.builder()
                    .timeout(50)
                    .apiHost(chatUrl)
                    .build()
                    .init();

            AiPubDocYunEventSourceListener listener = new AiPubDocYunEventSourceListener(sseEmitter);
            chatGPTStream.streamChatCompletion(JSONObject.toJSONString(jsonObjectParam), listener, userId);
        }
        return sseEmitter;
    }

    private SseEmitter knowledgeAiFileExtract(HttpServletRequest request, JSONObject jSONObject) throws IOException{
        SseEmitter sseEmitter = new SseEmitter(-1L);

        String userId = getUserId(request, sseEmitter);
        if(StringUtils.isEmpty(userId)) {
            return sseEmitter;
        }else {

            JSONObject dataJsonObject = jSONObject.getJSONObject("data");

            JSONObject paramJson = new JSONObject();

            paramJson.put("sessionType", AiChatConstants.CHAT_AI_FILE_CHAT);
            paramJson.put("sessionId", UUID.randomUUID().toString() + System.currentTimeMillis());
            paramJson.put("intent", AiChatConstants.AI_FILE_EXTRACT_INTENT);


            JSONArray fields = dataJsonObject.getJSONArray("fields");
            if(!CollectionUtil.isEmpty(fields)){
                paramJson.put("fields", fields);
            }

            List<String> fileIds = new ArrayList<>();
            fileIds.add(dataJsonObject.getString("fileId"));
            paramJson.put("fileIds", fileIds);


            String chatUrl = customNacosConfig.getAiapiHostUrl() + customNacosConfig.getAiapiAiFileExtract();

            log.info("chatUrl : {}, paramJson : {}", chatUrl, JSON.toJSONString(paramJson));

            ChatGPTStream chatGPTStream = ChatGPTStream.builder()
                    .timeout(50)
                    .apiHost(chatUrl)
                    .build()
                    .init();

            AiPubDocYunEventSourceListener listener = new AiPubDocYunEventSourceListener(sseEmitter);
            chatGPTStream.streamChatCompletion(JSONObject.toJSONString(paramJson), listener, userId);
        }
        return sseEmitter;
    }

    private void buildFilesParam(JSONObject jsonObjectParam) {
        JSONArray files = jsonObjectParam.getJSONArray("files");

        if(!Objects.isNull(files)){
            List<JSONObject> list = new ArrayList<>();
            for (Object file : files) {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(file));

                JSONObject jsonObjectFile = new JSONObject();
                jsonObjectFile.put("file_id", jsonObject.getString("fileId"));
                jsonObjectFile.put("url", jsonObject.getString("url"));
                jsonObjectFile.put("file_name", jsonObject.getString("fileName"));

                list.add(jsonObjectFile);
            }
            jsonObjectParam.put("files", list);
        }
    }

    private SseEmitter knowledgeAiSearch(HttpServletRequest request, JSONObject jSONObject) throws IOException {
        SseEmitter sseEmitter = new SseEmitter(-1L);


        String userId = getUserId(request, sseEmitter);
        if(StringUtils.isEmpty(userId)) {
            return sseEmitter;
        }else {
            JSONObject jsonObjectParam = jSONObject.getJSONObject("data");

            jsonObjectParam.put("sessionType", AiChatConstants.CHAT_AI_SEARCH);
            jsonObjectParam.put("sessionId", jSONObject.getString("sessionId"));

            String chatUrl = customNacosConfig.getAiapiHostUrl() + customNacosConfig.getAiapiAiSearch();

            log.info("chatUrl : {}, jsonObjectParam : {}", chatUrl, JSON.toJSONString(jsonObjectParam));

            ChatGPTStream chatGPTStream = ChatGPTStream.builder()
                    .timeout(50)
                    .apiHost(chatUrl)
                    .build()
                    .init();

            AiPubDocYunEventSourceListener listener = new AiPubDocYunEventSourceListener(sseEmitter);
            chatGPTStream.streamChatCompletion(JSONObject.toJSONString(jsonObjectParam), listener, userId);
        }
        return sseEmitter;
    }

    private SseEmitter knowledgeAssistantChat(HttpServletRequest request, JSONObject jSONObject) throws IOException {
        SseEmitter sseEmitter = new SseEmitter(-1L);

        String userId = getUserId(request, sseEmitter);
        if(StringUtils.isEmpty(userId)) {
            return sseEmitter;
        }else {

            jSONObject.put("sessionType", AiChatConstants.CHAT_SCENE_OPEN);
            String chatUrl = customNacosConfig.getAiapiHostUrl() + customNacosConfig.getAiapiAssistantChatChatStream();

            ChatGPTStream chatGPTStream = ChatGPTStream.builder()
                    .timeout(50)
                    .apiHost(chatUrl)
                    .build()
                    .init();

            AiPubDocYunEventSourceListener listener = new AiPubDocYunEventSourceListener(sseEmitter);
            chatGPTStream.streamChatCompletion(JSONObject.toJSONString(jSONObject), listener, userId);
        }
        return sseEmitter;
    }

    private SseEmitter knowledgeZshChat(HttpServletRequest request, JSONObject jSONObject) throws IOException {
        SseEmitter sseEmitter = new SseEmitter(-1L);

        String userId = getUserId(request, sseEmitter);
        if(StringUtils.isEmpty(userId)){
            return sseEmitter;
        }else {

            String token = getKnowledgeGptChatToken(jSONObject, userId);

            jSONObject.put("token", token);
            jSONObject.put("sessionType", "ai_zsh");
            jSONObject.put("chatSource", "client");
            String chatUrl = customNacosConfig.getAiapiHostUrl() + customNacosConfig.getAiapiKnowledgeChatStreamV1();

            ChatGPTStream chatGPTStream = ChatGPTStream.builder()
                    .timeout(50)
                    .apiHost(chatUrl)
                    .build()
                    .init();

            AiPubDocYunEventSourceListener listener = new AiPubDocYunEventSourceListener(sseEmitter);
            chatGPTStream.streamChatCompletion(JSONObject.toJSONString(jSONObject), listener, userId);
        }

        return sseEmitter;
    }

    /**
     * 获取知识号对话token
     * @param jSONObject
     * @param userId
     * @return
     */
    private String getKnowledgeGptChatToken(JSONObject jSONObject, String userId) throws IOException {

        String url = customNacosConfig.getAiapiHostUrl() + customNacosConfig.getAiapiGetKnowledgeChatToken();

        String knowledgeGptId = jSONObject.getString("knowledgeGptId");

        Boolean resetChatToken = jSONObject.getBoolean("resetChatToken");

        String chatRedisTokenKey = AiChatConstants.CHAT_REDIS_TOKEN_PREFIX + knowledgeGptId + "_" + userId;


        if(Objects.isNull(resetChatToken) || !resetChatToken){
            String chatRedisToken = redisStringManager.get(chatRedisTokenKey);

            if(StringUtils.isNotEmpty(chatRedisToken)){
                return chatRedisToken;
            }
        }

        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("knowledgeGptId", knowledgeGptId);
        bodyMap.put("scene", AiChatConstants.CHAT_SCENE_OPEN);

        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("User-Id", userId);
        httpClientHelper.setHeaders(headerMap);

        Response Response = httpClientHelper.postResponse(url, JSON.toJSONString(bodyMap));
        String result = Objects.requireNonNull(Response.body()).string();
        log.info("getKnowledgeGptChatToken result: {}", result);

        JSONObject jsonObject = JSONObject.parseObject(result, JSONObject.class);
        String data = jsonObject.getString("data");
        if(StringUtils.isEmpty(data)){
          throw new ParamException("getKnowledgeGptChatToken error");
        }
        redisStringManager.set(chatRedisTokenKey, data, 60 * 60L);
        return data;

    }

    public String getUserId(HttpServletRequest request, SseEmitter sseEmitter) throws IOException {
        String userId = "";
        String authorization = request.getHeader("Authorization");
        String url = customNacosConfig.getOpenHostUrl() + customNacosConfig.getOpenUriGetUserInfo();

        if(StringUtils.isEmpty(authorization)){
            authorization = "";
        }
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", authorization);
        httpClientHelper.setHeaders(headerMap);
        log.info("get user info url: {}", url);
        log.info("get user info header: {}", JSON.toJSONString(headerMap));
        Response Response = httpClientHelper.getResponse(url);

        String result = Objects.requireNonNull(Response.body()).string();
        log.info("get user info result: {}", result);
        if (result.contains("errors")){
            SseEmitterUtil.sendError(sseEmitter, result);
        }else {
            JSONObject jsonObject = JSONObject.parseObject(result, JSONObject.class);
            userId = jsonObject.getString("id");
            if(StringUtils.isEmpty(userId)){
                SseEmitterUtil.sendError(sseEmitter, result);
            }
        }


        return userId;
    }

    @Override
    public String chatAiToString(HttpServletRequest request,JSONObject jSONObject) {
        //请求头校验
        if (!checkHeader(request,jSONObject)){
            log.error("认证信息校验失败: {}",request);
            return null;
        }
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String currentDay = sdf.format(new  Date());
            String uid= jSONObject.getString("uid");
            String account= jSONObject.getString("user_account");
            String event= jSONObject.getString("event");
            JSONObject msgData = jSONObject.getJSONObject("data");
            String groupid="";
            String question="";
            if (chatTypeList.contains(event)&&!Objects.isNull(msgData)){
                if ("group_chat".equals(event)) {
                    if (!msgData.getBoolean("at_me"))  {
                        // 如果群聊没有@xxx机器人则直接退出
                        return null;
                    }
                    groupid = msgData.getString("group_id");
                }
                if (msgData.getString("msg_type").equals("text")){
                    question=msgData.getString("text");
                }
                //1.根据account换取云盘user_id
                PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserTicket(2,account);
                if (!Objects.isNull(platformUser)){
                    String userId = String.valueOf(platformUser.getUserId());
                    //添加并更新历史对话上下文(最多10条，字数在5000-8000)
                    List<String> msg = getForUpdateMsgByUid(groupid, uid, question);
                    JSONObject aiChatJson = new JSONObject();
                    aiChatJson.put("knowledgeGptId",jSONObject.getString("knowledgeId"));
                    String aiToken=getKnowledgeGptChatToken(aiChatJson,userId);
                    //List<String> msg =Arrays.asList(question);
                    aiChatJson.put("msg",msg);
                    aiChatJson.put("token",aiToken);
                    aiChatJson.put("sessionId",uid+"_"+currentDay);
                    aiChatJson.put("subSessionId",UUID.randomUUID().toString());
                    aiChatJson.put("sessionType", "ai_zsh");
                    aiChatJson.put("chatSource", "client");
                    aiChatJson.put("gptType","deepseek");
                    aiChatJson.put("search",false);
                    //调Ai对话接口
                    return stringChatGptAi(aiChatJson,userId);
                }
            }

        }catch (Exception e){
            log.error("zsh ai chat has failure beacuse of:{}",e.getMessage());
        }
        return null;

    }

    @Override
    public void sendMsgToRobot(JSONObject jSONObject,String aiAnswer) {
        String uid= jSONObject.getString("uid");
        String account= jSONObject.getString("user_account");
        String event= jSONObject.getString("event");
        JSONObject msgData = jSONObject.getJSONObject("data");
        String groupid="";
        String msgId="";
        if (StringUtils.isNotEmpty(aiAnswer)&&!Objects.isNull(msgData)){
            msgId=msgData.getString("msgid");
            if ("group_chat".equals(event)&&msgData.getBoolean("at_me")){
                groupid=msgData.getString("group_id");
            }
            //1.将AI回答更新到历史对话记录
            getForUpdateMsgByUid(groupid,uid,aiAnswer);
            //2.发消息给机器人
            if (sendMessage(getSendMessagePostData(groupid,account,aiAnswer,msgId),jSONObject.getString("robotAppid"),jSONObject.getString("robotAppSecret"))){
                log.info("send robot message success");
            }else {
                log.error("send robot message failure");
            }
        }

    }

    private boolean sendMessage(String postData,String appid,String secret) {

        String url = String.format(SEND_ROBOT_MESSAGE, customNacosConfig.getCustomTuiMessageHost(), appid, secret);
        log.info("sendRobotMessage url {}, postData {}", url, postData);
        try {
            Response response = httpClientHelper.postResponse(url, postData);
            String result = Objects.requireNonNull(response.body().string());
            log.info("sendRobotMessage result {}", result);
            Integer errCode = (Integer) JSONPath.extract(result, ERRCODE_PATH);
            if (errCode == 0) {
                return true;
            }
        } catch (Exception e) {
            log.error("sendRobotMessage error url {}", url, e);
        }
        return false;
    }

    private String getSendMessagePostData(String groupId,String acccount,String content,String refMsgId) {
        JSONObject json = new JSONObject();
        json.put("msgtype","text");
        JSONObject text = new JSONObject();
        text.put("content",content);
        text.put("reference_msgid",refMsgId);
        json.put("text",text);

        //单聊
        if (StringUtils.isEmpty(groupId)){
            json.put("tousers",Arrays.asList(acccount));
            return json.toString();
        }
        //群聊
        json.put("togroups",Arrays.asList(groupId));
        json.put("at",Arrays.asList(acccount));
        return json.toString();
    }

    private List<String> getForUpdateMsgByUid(String groupid, String uid, String context) {
        String msgKey=uid;
        if (StringUtils.isNotEmpty(groupid)){
            msgKey=groupid+"_"+uid;
        }
        List<String> msg;
        String result = redisStringManager.get(msgKey);
        if (StringUtils.isEmpty(result)){
            //第一次提问
            msg = new ArrayList<>(Arrays.asList(context));
            //保留三天
            redisStringManager.set(msgKey,JSONObject.toJSONString(msg),3*24*60*60L);
            return msg;
        }
        try {
            // 创建可修改的ArrayList
            msg = new ArrayList<>(JSONArray.parseArray(result,  String.class));

            if (msg.size()  >= 10) {
                msg.remove(0);
            }

            if (context != null) {
                msg.add(context);
            }
            redisStringManager.set(msgKey,  JSONObject.toJSONString(msg),  3 * 24 * 60 * 60L);
            return msg;
        } catch (Exception e) {
            log.error("get redis context has faliured:{}",e.getMessage());
            return new ArrayList<>();
        }
    }

    private String stringChatGptAi(JSONObject aiChatJson,String userId) throws ExecutionException, InterruptedException, TimeoutException {
        String chatUrl = customNacosConfig.getAiapiHostUrl() + customNacosConfig.getAiapiKnowledgeChatStreamV1();

        ChatGPTStream chatGPTStream = ChatGPTStream.builder()
                .timeout(50)
                .apiHost(chatUrl)
                .build()
                .init();

        AiSseEventSourceListener listener = new AiSseEventSourceListener();
        chatGPTStream.streamChatCompletion(JSONObject.toJSONString(aiChatJson), listener, userId);
        return listener.getResultFuture().get(120, TimeUnit.SECONDS);
    }

    private boolean checkHeader(HttpServletRequest request,JSONObject jSONObject) {
        String robotAppid=request.getHeader("X-Tuitui-Robot-Appid");
        if (StringUtils.isNotEmpty(robotAppid)){
            List<JSONObject> idsJson = JSON.parseArray(customNacosConfig.getAiapiKnowedgeGptIds(),JSONObject.class);
            if (CollectionUtil.isNotEmpty(idsJson)){
                Map<String,JSONObject> idsMap = idsJson.stream().collect(Collectors.toMap(obj->obj.getString("robotAppid"),obj->obj));
                JSONObject idObject = idsMap.get(robotAppid);
                if (idObject!=null){
                    jSONObject.put("knowledgeId",idObject.getString("knowledgeId"));
                    jSONObject.put("robotAppid",idObject.getString("robotAppid"));
                    jSONObject.put("robotAppSecret",idObject.getString("robotAppSecret"));
                }
            }
            return true;
        }
        return false;
    }
}
