package com.fangcloud.thirdpartplatform.service.impl.datasource;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.dingtalk.api.response.OapiV2DepartmentGetResponse;
import com.dingtalk.api.response.OapiV2UserListResponse;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SourceTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.ThirdPartyValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.entity.input.DingTalkInitParams;
import com.fangcloud.thirdpartplatform.helper.DingTalkClientHelper;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.service.impl.EnterpriseServiceImpl;
import com.fangcloud.thirdpartplatform.utils.SyncStringUtils;
import com.sync.common.entity.dto.DingTalkDepartment;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.utils.StreamUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.sync.common.contants.SyncConstant.GB;

@Component
@Slf4j
public class DingdingSyncHandler extends AbstractDataSourceSyncHandler {

    @Autowired
    private EnterpriseServiceImpl enterpriseService;

    @Resource
    private RedisStringManager redisStringManager;

    private static String DINGTALK_APP_KEY_PATH = "$.platform_config.dingTalk.appKey.value";
    private static String DINGTALK_APP_SECRET_PATH = "$.platform_config.dingTalk.appSecret.value";
    private static String DINGTALK_HOST_PATH = "$.platform_config.dingTalk.host.value";

    private static String DINGTALK_AGENTID_PATH = "$.platform_config.dingTalk.agentId.value";


    private static String ARR_PLATFORM_CONFIG_PATH = "$.platform_config_arr";
    private static String ARR_DINGTALK_APP_KEY_PATH = "$.platform_config_arr[%s].dingTalk.appKey.value";
    private static String ARR_DINGTALK_APP_SECRET_PATH = "$.platform_config_arr[%s].dingTalk.appSecret.value";
    private static String ARR_DINGTALK_HOST_PATH = "$.platform_config_arr[%s].dingTalk.host.value";

    private static String ARR_AGENTID_PATH = "$.platform_config_arr[%s].dingTalk.agentId.value";

    private static final int ROOT_DEPT_ID = 1;

    @Override
    public ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();

        Integer enterpriseId = platformSyncConfig.getEnterpriseId();
        log.info("enterpriseId:{}", enterpriseId);
        Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);
        List<OapiV2UserListResponse.ListUserResponse> userResponseList = new ArrayList<>();

        String valueBox = platformSyncConfig.getValueBox();
        ThirdPartyValueBox thirdPartyValueBox = JSON.parseObject(valueBox, ThirdPartyValueBox.class);
        String cacheUserKey = thirdPartyValueBox.getUserConfigDto().getCacheUserKey();

        //获取appKey和appSecret
        List<DingTalkInitParams> dingTalkParamsList = getDingdingInfo(enterpriseInfo);
        for (DingTalkInitParams params : dingTalkParamsList) {
            List<OapiV2UserListResponse.ListUserResponse> dingTalkUsers = new DingTalkClientHelper(params).getAllDingTalkUsers(String.valueOf(enterpriseId));
            if (dingTalkUsers.isEmpty()) {
                log.error("ding_talk_users  is empty agent_id：{}", params.getAgentId());
                continue;
            }
            //判断是否需要将用户数据存入缓存。如果需要就不执行同步
            if (StringUtils.isNotBlank(cacheUserKey)) {
                if(cacheUserKey.equals("jobNumber")) {
                    //如果缓存的key是工号，就将工号和亿方云信息存入缓存
                    Map<String, YfyUser> dingTalkYfyUserByJobNumber = new HashMap<>();
                    buildUserByRedis(dingTalkUsers, thirdPartyValueBox, dingTalkYfyUserByJobNumber);
                    redisStringManager.set(SyncTaskConstants.DING_TALK_JOB_NUMBER + params.getAgentId(), JSONObject.toJSONString(dingTalkYfyUserByJobNumber), 60 * 60 * 24L);
                } else if (cacheUserKey.equals("userId")) {
                    redisStringManager.set(SyncTaskConstants.DING_TALK_USER_ID + params.getAgentId(), "1", 60 * 60 * 24L);
                }
            }
            userResponseList.addAll(dingTalkUsers);
        }
        //用户数据要存入缓存的情况下，就不执行此次同步
        if (StringUtils.isNotBlank(cacheUserKey)) {
            return executeResult;
        }
        if (CollectionUtils.isEmpty(userResponseList)) {
            throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
        }
        List<YfyUser> users = buildUser(userResponseList,thirdPartyValueBox,enterpriseId);
        log.info("apiSyncHandler-syncUser, user:{}, taskId:{}", JSON.toJSONString(users), taskId);
        if (CollectionUtils.isEmpty(users)) {
            return executeResult;
        }
        return syncUserList(platformSyncConfig, users, taskId);
    }

    @Override
    public ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        ExecuteResult executeResult = new ExecuteResult();

        Integer enterpriseId = platformSyncConfig.getEnterpriseId();
        log.info("enterpriseId:{}", enterpriseId);
        Enterprise enterpriseInfo = enterpriseService.getEnterpriseById(enterpriseId);

        List<DingTalkInitParams> dingTalkParamsList = getDingdingInfo(enterpriseInfo);
        List<YfyDepartment> yfyDepartments = new ArrayList<>();
        for (DingTalkInitParams params : dingTalkParamsList) {
            List<DingTalkDepartment> dingTalkDepartments = new DingTalkClientHelper(params).getAllDingTalkDepartments(String.valueOf(enterpriseId));
            yfyDepartments.addAll(buildDepartments(dingTalkDepartments,platformSyncConfig,params, enterpriseId));
        }
        if (CollectionUtils.isEmpty(yfyDepartments)) {
            throw new ParamException(SyncTaskConstants.SYNC_DEPARTMENT_DATA_IS_NULL);
        }
        log.info("apiSyncHandler-syncDepartment, department:{}, taskId:{}", JSON.toJSONString(yfyDepartments), taskId);
        if (CollectionUtils.isEmpty(yfyDepartments)) {
            return executeResult;
        }
        cacheDeptIds(yfyDepartments, enterpriseId);
        return syncDepartmentList(platformSyncConfig, yfyDepartments, taskId);
    }

    @Override
    public ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId) {
        return null;
    }

    @Override
    public String getDataSourceType() {
        return SourceTypeEnum.DING_TALK.getDesc();
    }
    public List<DingTalkInitParams> getDingdingInfo(Enterprise enterpriseInfo) {
        if (enterpriseInfo == null || enterpriseInfo.getAdditionalInfo() == null) {
            log.info("enterpriseInfo or additionalInfo is null! {}", enterpriseInfo);
            throw new ParamException("enterpriseInfo is null!");
        }

        String additionalInfo = enterpriseInfo.getAdditionalInfo();
        List<DingTalkInitParams> dingTalkInitParams = new ArrayList<>();
        Object platformConfigArr = JSONPath.extract(additionalInfo, ARR_PLATFORM_CONFIG_PATH);

        if (Objects.isNull(platformConfigArr)) {
            dingTalkInitParams.add(createDingTalkParams(additionalInfo, ""));
        } else {
            JSONArray objects = JSONArray.parseArray(platformConfigArr.toString());
            for (int i = 0; i < objects.size(); i++) {
                dingTalkInitParams.add(createDingTalkParams(additionalInfo, String.valueOf(i)));
            }
        }
        if (dingTalkInitParams.isEmpty()) {
            throw new ParamException("dingTalkInitParams is null!");
        }

        return dingTalkInitParams;
    }

    private DingTalkInitParams createDingTalkParams(String additionalInfo, String index) {
        DingTalkInitParams dingTalkParams = new DingTalkInitParams();
        String appId, appSecret, host, agentId;

        if (StringUtils.isBlank(index)) {
            appId = JSONPath.extract(additionalInfo, DINGTALK_APP_KEY_PATH).toString();
            appSecret = JSONPath.extract(additionalInfo, DINGTALK_APP_SECRET_PATH).toString();
            host = JSONPath.extract(additionalInfo, DINGTALK_HOST_PATH).toString();
            agentId = JSONPath.extract(additionalInfo, DINGTALK_AGENTID_PATH).toString();
        } else {
            appId = JSONPath.extract(additionalInfo, String.format(ARR_DINGTALK_APP_KEY_PATH, index)).toString();
            appSecret = JSONPath.extract(additionalInfo, String.format(ARR_DINGTALK_APP_SECRET_PATH, index)).toString();
            host = JSONPath.extract(additionalInfo, String.format(ARR_DINGTALK_HOST_PATH, index)).toString();
            agentId = JSONPath.extract(additionalInfo, String.format(ARR_AGENTID_PATH, index)).toString();
        }

        dingTalkParams.setAppId(appId);
        dingTalkParams.setAppSecret(appSecret);
        dingTalkParams.setHost(host == null ? "" : host);
        dingTalkParams.setAgentId(agentId);
        return dingTalkParams;
    }

    private List<YfyUser> buildUser(List<OapiV2UserListResponse.ListUserResponse> userResponseList, ThirdPartyValueBox thirdPartyValueBox, Integer enterpriseId) {
        boolean syncPhone = thirdPartyValueBox.getUserConfigDto().isSyncPhone();
        List<YfyUser> yfyUsers = StreamUtils.map(userResponseList, user -> {
            String email = null;
            if(StringUtils.isBlank(user.getEmail())){
                String emailSuffix = thirdPartyValueBox.getUserConfigDto().getEmailSuffix();
                if(StringUtils.isNotEmpty(emailSuffix)){
                    email = user.getUserid() + "@" + emailSuffix;
                } else {
                    log.info("email is null! user info {}", JSON.toJSONString(user));
                }
            }else {
                email = user.getEmail();
            }
            List<String> deptIds = StreamUtils.map(user.getDeptIdList(), String::valueOf);
            // 从根部门移除
            deptIds.remove("1");
            //同步范围内的部门id数组
            String syncDeptIds = redisStringManager.get(SyncTaskConstants.DING_TALK_DEPT_IDS + enterpriseId);
            List<String> newDeptList = new ArrayList<>();
            if (syncDeptIds != null) {
                List<String> syncDeptIdList = Arrays.asList(syncDeptIds.split(","));
                if (!CollectionUtils.isEmpty(deptIds) && !CollectionUtils.isEmpty(syncDeptIdList)) {
                    deptIds.forEach(deptId -> {
                        if (syncDeptIdList.contains(deptId)) {
                            newDeptList.add(deptId);
                        }
                    });
                }
            }
            return YfyUser.builder()
                    .id(user.getUserid())
                    .fullName(SyncStringUtils.formatString(user.getName()))
                    .spaceTotal(thirdPartyValueBox.getUserSpace() * GB)
                    .forced(true)
                    //.email(Objects.isNull(user.getOrgEmail()) ? (user.getMobile() + customNacosConfig.getCustomUserEmailSuffix()) : user.getOrgEmail())
                    .email(email)
                    .createTime(new Date())
                    .status(thirdPartyValueBox.getUserConfigDto().isActivate() ? "1" : "3")
                    .departmentIds(newDeptList)
                    .phone(syncPhone ? user.getMobile() : null)
                    .build();
        });

        log.info("从钉钉获取并转换的用户总数为{}, 转换后用户为 {}", yfyUsers.size(), yfyUsers);
        return yfyUsers;
    }

    private List<YfyDepartment> buildDepartments(List<DingTalkDepartment> dingTalkDepartments, PlatformSyncConfig platformSyncConfig ,DingTalkInitParams params ,Integer enterpriseId) {
        String valueBox = platformSyncConfig.getValueBox();
        ThirdPartyValueBox thirdPartyValueBox = JSON.parseObject(valueBox, ThirdPartyValueBox.class);
        return StreamUtils.map(dingTalkDepartments, dingTalkDepartment -> {
            OapiV2DepartmentGetResponse.DeptGetResponse departmentDetails = new DingTalkClientHelper(params).getDingTalkDepartmentById(dingTalkDepartment.getDeptId());
            if (departmentDetails == null) {
                return null;
            }
            YfyDepartment yfyDepartment = new YfyDepartment();
            yfyDepartment.setId(String.valueOf(dingTalkDepartment.getDeptId()));
            yfyDepartment.setName(SyncStringUtils.formatString(dingTalkDepartment.getName()));
            if (isRoot(dingTalkDepartment.getParentId())) {
                yfyDepartment.setParentId(null);
            } else {
                yfyDepartment.setParentId(String.valueOf(dingTalkDepartment.getParentId()));
            }
            yfyDepartment.setDirectorId(!CollectionUtils.isEmpty(departmentDetails.getDeptManagerUseridList()) ? departmentDetails.getDeptManagerUseridList().get(0) : null);
            yfyDepartment.setSpaceTotal(thirdPartyValueBox.getDeptSpace().longValue());
            yfyDepartment.setPublicFolder(thirdPartyValueBox.getDepConfigDto().isPublicFolder());
            yfyDepartment.setCollabAutoAccepted(thirdPartyValueBox.getDepConfigDto().isCollabAutoAccepted());
            yfyDepartment.setOrder(1000000000L - departmentDetails.getOrder());
            yfyDepartment.setStatus("1");
            log.info("从钉钉获取部门数据并且重新组装后的 yfyDepartment {} department {}", yfyDepartment, dingTalkDepartment);
            return yfyDepartment;
        }).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private void buildUserByRedis(List<OapiV2UserListResponse.ListUserResponse> userResponseList, ThirdPartyValueBox thirdPartyValueBox, Map<String, YfyUser> map) {
        boolean syncPhone = thirdPartyValueBox.getUserConfigDto().isSyncPhone();
        String emailSuffix = thirdPartyValueBox.getUserConfigDto().getEmailSuffix();
        for (OapiV2UserListResponse.ListUserResponse user : userResponseList) {
            if (StringUtils.isBlank(user.getJobNumber())) {
                continue;
            }
            String email = null;
            if (StringUtils.isNotBlank(emailSuffix)) {
                email = user.getJobNumber() + "@" + emailSuffix;
            }else {
                email = user.getEmail();
            }

            YfyUser yfyUser = YfyUser.builder()
                    .id(user.getUserid())
                    .fullName(SyncStringUtils.formatString(user.getName()))
                    .forced(true)
                    .email(email)
                    .createTime(new Date())
                    .status("1")
                    .spaceTotal(thirdPartyValueBox.getUserSpace() * GB)
                    .phone(syncPhone ? user.getMobile() : null)
                    .build();
            map.put(user.getJobNumber(), yfyUser);
        }
        log.info("从钉钉获取并存入缓存用户总数为{}, 存入缓存的用户为 {}", map.size(), map);
    }

    private boolean isRoot(long deptId) {
        return deptId == ROOT_DEPT_ID;
    }


    public List<YfyUser> getYfyUsersByDingTalk(DingTalkInitParams params) {
        List<YfyUser> yfyUsers = new ArrayList<>();
        List<OapiV2UserListResponse.ListUserResponse> userResponseList = new DingTalkClientHelper(params).getAllDingTalkUsers(params.getEnterpriseParams().getEnterpriseId());
        userResponseList.forEach(user -> {
            String mobile = user.getMobile();
            String email = user.getEmail();
            List<String> deptIds = StreamUtils.map(user.getDeptIdList(), String::valueOf);
            deptIds.remove("1");
            YfyUser yfyUser = YfyUser.builder()
                    .id(user.getUserid())
                    .fullName(SyncStringUtils.formatString(user.getName()))
                    .status("1")
                    .departmentIds(deptIds)
                    .build();
            if (StringUtils.isNotBlank(mobile)) {
                yfyUser.setPhone(mobile);
            } else {
                yfyUser.setEmail(email);
            }
            yfyUsers.add(yfyUser);
        });
        return yfyUsers;
    }

    public List<YfyDepartment> getYfyDepartmentsByDingTalk(DingTalkInitParams params) {
        List<YfyDepartment> yfyDepartments = new ArrayList<>();
        List<DingTalkDepartment> dingTalkDepartments = new DingTalkClientHelper(params).getAllDingTalkDepartments(params.getEnterpriseParams().getEnterpriseId());
        dingTalkDepartments.forEach(dingTalkDepartment -> {
            OapiV2DepartmentGetResponse.DeptGetResponse departmentDetails = new DingTalkClientHelper(params).getDingTalkDepartmentById(dingTalkDepartment.getDeptId());
            YfyDepartment yfyDepartment = new YfyDepartment();
            yfyDepartment.setId(String.valueOf(dingTalkDepartment.getDeptId()));
            yfyDepartment.setName(SyncStringUtils.formatString(dingTalkDepartment.getName()));
            if (isRoot(dingTalkDepartment.getParentId())) {
                yfyDepartment.setParentId("");
            } else {
                yfyDepartment.setParentId(String.valueOf(dingTalkDepartment.getParentId()));
            }
            yfyDepartment.setDirectorId(!CollectionUtils.isEmpty(departmentDetails.getDeptManagerUseridList()) ? departmentDetails.getDeptManagerUseridList().get(0) : null);
            yfyDepartment.setOrder(1000000000L - departmentDetails.getOrder());
            yfyDepartments.add(yfyDepartment);
        });
        return yfyDepartments;
    }

    private void cacheDeptIds(List<YfyDepartment> yfyDepartments, Integer enterpriseId) {
        List<String> yfyDepartmentIdList = yfyDepartments.stream().map(YfyDepartment::getId).collect(Collectors.toList());
        //将同步范围内的部门id存入缓存
        redisStringManager.set(SyncTaskConstants.DING_TALK_DEPT_IDS + enterpriseId, String.join(",", yfyDepartmentIdList), 0L);
    }
}
