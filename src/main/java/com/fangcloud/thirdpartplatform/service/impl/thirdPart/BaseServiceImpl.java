package com.fangcloud.thirdpartplatform.service.impl.thirdPart;

import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.entity.sync.SyncPlatformResult;
import com.fangcloud.thirdpartplatform.entity.sync.SyncUserBean;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.runtime.Executor;
import com.fangcloud.thirdpartplatform.runtime.ExecutorContext;
import com.google.common.collect.Lists;
import com.sync.common.entity.dto.YfyMessage;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.enums.YfyMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
public class BaseServiceImpl {

    @Autowired
    private Executor executor;
    @Resource
    private OpenClientHelper openClientHelper;

    @Resource
    private V2ClientHelper v2ClientHelper;

    private static String PUBLIC_BASE_URL_OLD = "https://www.fangcloud.com";
    private static String PUBLIC_BASE_URL_NEW = "https://v2.fangcloud.com";

    @Value("${custom.autologin.base.ip}")
    private String autologinBaseIp;
    @Value("${custom.autologin.base.host}")
    private String autologinBaseHost;

    /**
     * 消息体构造
     * @param params
     * @return
     */
    protected YfyMessage getYfyMessage(MessageParams params)
    {
        YfyMessage yfyMessage = YfyMessage.builder()
                .content(params.getContent())
                .customerId(params.getCustomerId())
                .enterpriseId(params.getEnterpriseId())
                .h5Url(params.getH5Url())
                .receivers(params.getReceivers())
                .time(params.getTime())
                .title(params.getTitle())
                .type(params.getType())
                .webUrl(params.getWebUrl())
                .picUrl(params.getPicUrl())
                .build();

        String title = YfyMessageTypeEnum.getEnum(params.getType()).getDesc();
        if (StringUtils.isNotBlank(params.getMessagePushTitle())) {
            title  = title.replace("云盘", params.getMessagePushTitle());
        }
        if(!YfyMessageTypeEnum.FOLLOW.getType().equals(params.getType())){
            title = title + "：" + params.getTitle();
        }

        String content = yfyMessage.getContent().replaceAll("\\[\\d+:", "[").replaceAll("\\[g-\\d+:","[");
        yfyMessage.setTitle(title);
        yfyMessage.setContent(content);

        return yfyMessage;
    }

    /**
     * 获取登录链接
     * @param enterpriseParams
     * @return
     */
    protected String getLoginUrl(Boolean syncUserFlag, EnterpriseParams enterpriseParams, YfyUser yfyUser){
        // 调开放平台获取登录url
        String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
        log.info("syncUserFlag :{}, loginUrl :{}", syncUserFlag, loginUrl);
        // 若开放平台获取登录url为null，同步用户后就再次调用开放平台获取登录url
        if(syncUserFlag && StringUtils.isEmpty(loginUrl)){
            List<YfyUser> yfyUsers = new ArrayList<>();
            yfyUsers.add(yfyUser);
            SyncUserBean syncUserBean = new SyncUserBean();
            syncUserBean.setRequestId(MDC.get("uuid"));
            syncUserBean.setUsers(yfyUsers);
            syncUserBean.setEnterpriseId(Long.parseLong(enterpriseParams.getEnterpriseId()));
            syncUserBean.setPlatformId(Long.parseLong(enterpriseParams.getPlatformId()));
            SyncPlatformResult syncPlatformResult = v2ClientHelper.syncUser(syncUserBean);

            if(!Objects.isNull(syncPlatformResult)){
                loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
            }
        }

        // 公有云企业获取的免密地址是www.fangcloud.com的域名，替换成v2.fangcloud.com
        if(StringUtils.isNotEmpty(loginUrl)){
            if(loginUrl.startsWith(autologinBaseIp)){
                log.info("before loginUrl:{}", loginUrl);
                loginUrl = loginUrl.replace(autologinBaseIp, autologinBaseHost);
                log.info("after loginUrl:{}", loginUrl);
            }
        }
        return loginUrl;
    }

    protected Map<String, Object> executeContext(String code, Object params) throws Exception {
        ExecutorContext executeContext = new ExecutorContext();
        executeContext.setCode(code);
        executeContext.setMethodName("execute");
        executeContext.setParams(Lists.newArrayList(params));
        executeContext.setDebug(true);
        Object obj = executor.execute(executeContext);
        if (obj instanceof HashMap) {
            return (Map<String, Object>) obj;
        } else {
            throw new Exception("脚本返回类型应为map");
        }
    }
}
