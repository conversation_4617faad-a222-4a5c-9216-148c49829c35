package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.db.dao.PlatformEnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import com.fangcloud.thirdpartplatform.service.PlatformEnterprisesService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PlatformEnterprisesServiceImpl implements PlatformEnterprisesService {

    @Resource
    private PlatformEnterpriseMapper platformEnterpriseMapper;

    @Override
    public PlatformEnterprises queryByEnterpriseTicket(String enterpriseTicket) {
        return platformEnterpriseMapper.queryByEnterpriseTicket(enterpriseTicket);
    }
}
