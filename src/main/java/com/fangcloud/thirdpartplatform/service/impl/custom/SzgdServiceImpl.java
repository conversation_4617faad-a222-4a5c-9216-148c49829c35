package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.fangcloud.thirdpartplatform.utils.sm4.SM4Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class SzgdServiceImpl{

    @Resource
    private RedisStringManager redisStringManager;

    private final static String taskKeyPrefix = "SYNC_TASK_ID_";

    private final static String userKeyPrefix = "SYNC_USER_ID_";

    public String processApiResult(String body,String type,Integer taskId){
        log.info("need decrypt body:{}",body);
        String decryBody = SM4Utils.szgdDecryptText(body);
        JSONObject object = null;
        if (StringUtils.isNotEmpty(decryBody)){
            object = JSON.parseObject(decryBody);
            String serialNum = object.getString("serialNum");
            String uid=null;
            String loginName = null;
            if ("USER".equals(type)){
                uid = object.containsKey("userId")?object.getString("userId"):object.getString("uid");
                loginName = object.getString("loginName");
            }else if ("DEPT".equals(type)){
                uid = object.getString("baseOrgId");
            }
            if (StringUtils.isNotEmpty(uid)){
                //设置临时缓存与taskId绑定 用于返回
                String taskKey = taskKeyPrefix + taskId;
                String value = uid + "_" + serialNum;
                redisStringManager.set(taskKey,value,3*60L);
            }
            if (StringUtils.isNotEmpty(loginName)){
                //设置永久缓存，再执行用户删除操作时也一并删除
                String userKey = userKeyPrefix + uid;
                redisStringManager.set(userKey,JSON.toJSONString(object),0L);
            }
        }
        return JSON.toJSONString(object);
    }

    public void buildResultData(Integer taskId,Map<String, Object> resultMap,String failLogs){
        //根据taskId取出当前推送序列号
        //设置临时缓存与taskId绑定
        String taskKey = taskKeyPrefix + taskId;
        String currentSync=redisStringManager.get(taskKey);
        resultMap.put("code",200);
        resultMap.put("message","success");
        if (currentSync!=null){
            redisStringManager.delete(taskKey);
            String[] split = currentSync.split("_");
            resultMap.put("uid",split[0]);
            resultMap.put("serialNum",split[1]);
        }
        if (failLogs!=null){
            resultMap.put("code",500);
            resultMap.put("message",failLogs);
        }

    }

}
