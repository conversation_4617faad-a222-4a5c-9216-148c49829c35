package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.DepartmentsUsers;
import com.fangcloud.thirdpartplatform.db.model.PlatformDepartment;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.db.model.User;
import com.fangcloud.thirdpartplatform.entity.sync.CustomUser;
import com.fangcloud.thirdpartplatform.entity.sync.DepartmentIds;
import com.fangcloud.thirdpartplatform.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class UserServiceImpl implements UserService {

    final static int USER_PAGE_CAPACITY = 500;

    @Autowired
    private EnterpriseMapper enterpriseMapper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Override
    public List<CustomUser> findAll(int platformId) {
        log.info("get all users startTime:{}",System.currentTimeMillis());
        List<PlatformUser> platformUserList = getPlatformUser(platformId);

        List<CustomUser> customUserList = getCustomUser(platformUserList);
        log.info("get all users endTime:{}",System.currentTimeMillis());
        log.info("customUserList info {}", JSON.toJSONString(customUserList));

        return customUserList;
    }

    /**
     * 获取用户信息
     * @param platformId
     * @return
     */
    public List<PlatformUser> getPlatformUser(int platformId) {

        Map<String, Integer> paramMap = new HashMap<>();
        paramMap.put("platformId", platformId);
        paramMap.put("pageSize", USER_PAGE_CAPACITY);

        int totalCount = (int) platformUserMapper.countByPlatformId(platformId);
        // 部门列表
        List<PlatformUser> platformUserList = new ArrayList<>();
        int pageId = 0;

        // 循环分页查询用户信息
        while (platformUserList.size() < totalCount){
            paramMap.put("start", pageId * USER_PAGE_CAPACITY);
            List<PlatformUser> platformDepartments = platformUserMapper.queryByPlatformIdWithPage(paramMap);

            platformUserList.addAll(platformDepartments);

            ++pageId;
        }
        return platformUserList;
    }

    /**
     * 拼装用户信息
     * @param platformUserList
     * @return
     */
    private List<CustomUser> getCustomUser(List<PlatformUser> platformUserList) {
        if (CollectionUtils.isEmpty(platformUserList)) {
            return new ArrayList<>();
        }

        List<CustomUser> customUsersList = new ArrayList<>();
        Map<Long, PlatformUser> platformUserMap = new HashMap<>();
        platformUserList.forEach(platformUser -> {
            platformUserMap.put(platformUser.getUserId(), platformUser);
        });
        List<Long> yfyUserIds = platformUserList.stream().map(PlatformUser::getUserId).collect(Collectors.toList());
        List<User> userList = userMapper.queryByIds(yfyUserIds);


        int platformId = (int) platformUserList.get(0).getPlatformId();
        userList.forEach(user -> {
            String userGroup = user.getUserGroup();
            if (userGroup.equals(SyncTaskConstants.USER_GROUP_ADMIN)) {
                return;
            }
            CustomUser customUser = new CustomUser();
            customUser.setCustomId(platformUserMap.get(user.getId()).getUserTicket());
            customUser.setEmail(user.getEmail());
            customUser.setId(user.getId());
            customUser.setName(user.getName());
            customUser.setPhone(user.getPhone());
            customUser.setDepartmentIds(getDepartmentIds(user.getId(), platformId));
            customUser.setActive(user.isActive());
            customUser.setOrder(getOrder(user.getFullNamePinyin()));
            customUser.setValueBox(platformUserMap.get(user.getId()).getValueBox());
            customUsersList.add(customUser);
        });

        return customUsersList;
    }

    /**
     * 拼装用户部门信息
     * @param userId
     * @param platformId
     * @return
     */
    private List<DepartmentIds> getDepartmentIds(long userId, int platformId) {
        List<DepartmentIds> departmentIdsList = new ArrayList<>();
        List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(userId);
        if (departmentsUsers == null || departmentsUsers.size() < 1) {
            return departmentIdsList;
        }
        List<PlatformDepartment> platformDepartments = platformDepartmentMapper.getByPlatformIdAndYfyDepartmentIds(
                platformId, departmentsUsers.stream().map(DepartmentsUsers::getDepartmentId).collect(Collectors.toList()));

        platformDepartments.forEach(platformDepartment -> {
            DepartmentIds departmentIds =  new DepartmentIds();
            departmentIds.setDepartmentId(platformDepartment.getYfyDepartmentId());
            departmentIds.setCustomDepartmentId(platformDepartment.getDepartmentId());
            departmentIds.setName(platformDepartment.getName());
            departmentIdsList.add(departmentIds);
        });
        return departmentIdsList;
    }



    private Long getOrder(String fullNamePinyin) {
        if (Strings.isBlank(fullNamePinyin)) {
            return  null;
        }
        String order = fullNamePinyin.length() > 10 ? fullNamePinyin.substring(0, 10) : null;
        if (order !=null && order.matches("\\d{10}") && Integer.MAX_VALUE > Long.valueOf(order)) {
            return Integer.MAX_VALUE-Long.valueOf(order);
        }
        return null;
    }

    public List<CustomUser> findUsersByIds(int platformId,List<String> userTickets){
        List<PlatformUser> platformUserList = platformUserMapper.queryByPlatformIdUserTicketIds(platformId,userTickets);
        if (CollectionUtils.isEmpty(platformUserList)){
            return new ArrayList<>();
        }
        List<CustomUser> customUserList = getCustomUser(platformUserList);
        log.info("get all users endTime:{}",System.currentTimeMillis());
        log.info("customUserList info {}", JSON.toJSONString(customUserList));
        return customUserList;
    }
}
