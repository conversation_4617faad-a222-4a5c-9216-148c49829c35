package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.APIParamConfigDto;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.tomcat.util.codec.binary.Base64;
import org.bouncycastle.crypto.AsymmetricBlockCipher;
import org.bouncycastle.crypto.encodings.PKCS1Encoding;
import org.bouncycastle.crypto.engines.RSAEngine;
import org.bouncycastle.crypto.params.AsymmetricKeyParameter;
import org.bouncycastle.crypto.params.RSAKeyParameters;
import org.bouncycastle.crypto.util.PrivateKeyFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.List;

@Component
@Slf4j
public class HhsdnServiceImpl {

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    private HttpClientHelper httpClientHelper;

    private String getUserInfo(List<APIParamConfigDto> paramConfig, String accessToken) {
        String host = apiSyncHandler.getValueByParamConfig(paramConfig, "host");
        String userInfoUri = apiSyncHandler.getValueByParamConfig(paramConfig, "user_info");
        String decrypto = apiSyncHandler.getValueByParamConfig(paramConfig, "decrypto");
        String resultPath = apiSyncHandler.getValueByParamConfig(paramConfig, "result_path");
        String userIdPath = apiSyncHandler.getValueByParamConfig(paramConfig, "user_id_path");
        String UiaSKey = apiSyncHandler.getValueByParamConfig(paramConfig, "uias_key");
        try {
            String url = String.format(userInfoUri, host, accessToken, decrypto);
            log.info("get_user_info url:{}", url);
            Response response = httpClientHelper.getResponse(url);
            String result = response.body().string();
            log.info("get_user_info result:{}", result);
            String extract = (String) JSONPath.extract(result, resultPath);
            String decryption = decryption(UiaSKey, extract);
            String userId = JSONPath.extract(decryption, userIdPath).toString();
            return userId;
        } catch (Exception e) {
            log.error("getUserInfo error ", e);
        }
        return null;
    }

    public Object query(PlatformSyncConfigResponse platformSyncConfig, String accessToken) {
        String valueBox = platformSyncConfig.getValueBox();
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(valueBox, APIConfigValueBox.class);
        List<APIParamConfigDto> paramConfig = apiConfigValueBox.getApiConfig().getParamConfig();
        return ResultUtils.getSuccessResult(getUserInfo(paramConfig, accessToken));
    }

    private String getUserInfoParams(String accessToken, String decrypto) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("access_token", accessToken);
        jsonObject.put("decrypto", decrypto);
        return jsonObject.toJSONString();
    }

    private String decryption(String UiaSKey, String result) throws Exception {
        String pkey = "";
        InputStream is = new ByteArrayInputStream(UiaSKey.getBytes());
        byte[] b = new byte[is.available()];

        is.read(b);
        is.close();
        pkey = new String(b, "UTF-8");
        pkey = bin2str(pkey);
        result = bin2str(result);
        result = new String(decryptByPriKey2(result, pkey), "UTF-8");
        log.info("decryption result:{}", result);
        return result;
    }

    public static String bin2str(String binStr) {
        String[] tempStr = b2s1(binStr);
        char[] tempChar = new char[tempStr.length];
        for (int i = 0; i < tempStr.length; i++) {
            tempChar[i] = b2s2(tempStr[i]);
        }
        return String.valueOf(tempChar);
    }

    private static String[] b2s1(String str) {
        int len = str.length() / 8;
        String[] arr = new String[len];
        for (int i = 0; i < len; i++) {
            arr[i] = str.substring(8 * i, 8 * (i + 1));
        }
        return arr;
    }

    private static char b2s2(String binStr) {
        int[] temp = b2s3(binStr);
        int sum = 0;
        for (int i = 0; i < temp.length; i++) {
            sum += temp[temp.length - 1 - i] << i;
        }
        return (char) sum;
    }

    private static int[] b2s3(String binStr) {
        char[] temp = binStr.toCharArray();
        int[] result = new int[temp.length];
        for (int i = 0; i < temp.length; i++) {
            result[i] = temp[i] - 48;
        }
        return result;
    }

    public static byte[] decryptByPriKey2(String text, String pkey) throws Exception {
        byte[] data = null;
        try {
            byte[] textBytes = Base64.decodeBase64(text);
            byte[] pkeyBytes = Base64.decodeBase64(pkey);
            AsymmetricKeyParameter params = PrivateKeyFactory.createKey(pkeyBytes);
            RSAKeyParameters rsaAKp = (RSAKeyParameters) params;

            AsymmetricBlockCipher cipher = new RSAEngine();
            cipher = new PKCS1Encoding(cipher);
            cipher.init(false, rsaAKp);

            data = segmentCrypt2(textBytes, cipher);
        } catch (Exception e) {
            throw e;
        }
        return data;
    }

    private static byte[] segmentCrypt2(byte[] text, AsymmetricBlockCipher cipher) throws Exception {
        byte[] result = null;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int l1 = text.length, j = 0, l2 = 0, l3 = l1;

        try {
            while (l3 > 0) {
                int tlen = l3 > 128 ? 128 : l3;
                byte[] d1 = cipher.processBlock(text, l2, tlen);
                out.write(d1);
                j++;

                l2 = j * 128;
                l3 = l1 - l2;
            }
            result = out.toByteArray();
        } catch (Exception e) {
            throw e;
        } finally {
            out = null;
        }
        return result;
    }
}
