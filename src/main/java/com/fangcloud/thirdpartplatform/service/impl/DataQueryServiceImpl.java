package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.constant.login.ProductIdEnum;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;
import com.fangcloud.thirdpartplatform.service.DataQueryService;
import com.fangcloud.thirdpartplatform.service.impl.custom.GzlabServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.custom.HhsdnServiceImpl;
import com.fangcloud.thirdpartplatform.service.impl.custom.LinMaServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Service
@Slf4j
public class DataQueryServiceImpl implements DataQueryService {

    @Resource
    private LinMaServiceImpl linMaService;

    @Resource
    private GzlabServiceImpl gzlabServiceImpl;

    @Resource
    private HhsdnServiceImpl hhsdnServiceImpl;


    @Override
    public Object customQuery(String productId, PlatformSyncConfigResponse platformSyncConfig, String params) {
        if (productId.equals(ProductIdEnum.LINMA.getProductId())) {
            return linMaService.query(platformSyncConfig, params);
        }
        if (productId.equals(ProductIdEnum.GZLAB.getProductId())) {
            return gzlabServiceImpl.query(platformSyncConfig, params);
        }
        if (productId.equals(ProductIdEnum.HHSDN.getProductId())) {
            return hhsdnServiceImpl.query(platformSyncConfig, params);
        }
        return null;
    }


}
