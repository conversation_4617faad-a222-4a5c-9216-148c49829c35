package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.db.dao.PlatformLoginConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.factory.PlatformLoginConfigFactory;
import com.fangcloud.thirdpartplatform.entity.input.PlatformLoginConfigParams;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginConfigResponse;
import com.fangcloud.thirdpartplatform.service.PlatformLoginConfigService;
import com.fangcloud.thirdpartplatform.utils.ResultUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PlatformLoginConfigServiceImpl implements PlatformLoginConfigService {

    @Resource
    private PlatformLoginConfigMapper platformLoginConfigMapper;

    @Resource
    private CustomNacosConfig customNacosConfig;


    @Override
    public Result<Boolean> saveConfig(PlatformLoginConfigParams platformLoginConfigParams) {

        PlatformLoginConfig platformLoginConfig = PlatformLoginConfigFactory.build(platformLoginConfigParams);

        platformLoginConfigMapper.updateByPrimaryKeySelective(platformLoginConfig);

        return ResultUtils.getSuccessResult();
    }

    @Override
    public Result<PlatformLoginConfigResponse> getConfig(Integer enterpriseId) {
        List<PlatformLoginConfig> platformLoginConfigs = platformLoginConfigMapper.selectByEnterpriseId(enterpriseId);
        PlatformLoginConfig platformLoginConfig;
        // 第一次进入没有配置，创建配置
        if(CollectionUtils.isEmpty(platformLoginConfigs)){

            platformLoginConfig = PlatformLoginConfigFactory.buildDefaultPlatformLoginConfig(enterpriseId);

            platformLoginConfigMapper.insert(platformLoginConfig);
        }else {
            platformLoginConfig = platformLoginConfigs.get(0);
        }
        return ResultUtils.getSuccessResult(PlatformLoginConfigFactory.buildPlatformLoginConfigResponse(platformLoginConfig, customNacosConfig.getBaseUrl()));
    }
}
