package com.fangcloud.thirdpartplatform.service.impl.custom;

import com.alibaba.cloudapi.sdk.enums.HttpMethod;
import com.alibaba.cloudapi.sdk.enums.Scheme;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.input.AliGateWayInitParams;
import com.fangcloud.thirdpartplatform.helper.AliGateWayClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;

@Component
@Slf4j
public class AliGateWayServiceImpl implements ApiExecuteService {


    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        AliGateWayInitParams params = new AliGateWayInitParams();
        buildClientParams(params, protocol, apiConfig, paramMap);
        log.info("AliGateWay param : {}", params);
        return new AliGateWayClientHelper(params).ResultData();
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    private void buildClientParams(AliGateWayInitParams params, String protocol, APIConfigValueBox.APIConfig apiConfig, Map<String, Object> paramMap) {

        if ("HTTPS".equals(protocol)){
            params.setScheme(Scheme.HTTPS);
        } else if ("HTTP".equals(protocol)) {
            params.setScheme(Scheme.HTTP);
        }

        if (RequestMethod.POST.name().equals(apiConfig.getRequestMethod())) {
            params.setMethod(HttpMethod.POST_FORM);
        } else if (RequestMethod.GET.name().equals(apiConfig.getRequestMethod())) {
            params.setMethod(HttpMethod.GET);
        }

        params.setHost(apiConfig.getApi());

        if (!Objects.isNull(paramMap.get("appKey"))) {
            params.setAppKey(paramMap.get("appKey").toString());
        }

        if (!Objects.isNull(paramMap.get("appSecret"))) {
            params.setAppSecret(paramMap.get("appSecret").toString());
        }

        if (!Objects.isNull(paramMap.get("path"))) {
            params.setPath(paramMap.get("path").toString());
        }

        if (!Objects.isNull(paramMap.get("pageNum"))) {
            params.setPageNum(paramMap.get("pageNum").toString());
        }

        if (!Objects.isNull(paramMap.get("pageSize"))) {
            params.setPageSize(paramMap.get("pageSize").toString());
        }
    }



    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.ALIGATEWAY.getDesc();
    }
}
