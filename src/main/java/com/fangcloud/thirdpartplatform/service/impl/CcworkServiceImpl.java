package com.fangcloud.thirdpartplatform.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.ccwork.AESEncryptUtil;
import com.fangcloud.thirdpartplatform.constant.ApiExecuteTypeEnum;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncResultStatusEnum;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncConfigMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformSyncTaskRecordMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord;
import com.fangcloud.thirdpartplatform.entity.CcworkEvent;
import com.fangcloud.thirdpartplatform.entity.common.ConfigInfo;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.helper.APIHelper;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.helper.OpenClientHelper;
import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import com.fangcloud.thirdpartplatform.service.impl.datasource.ApiSyncHandler;
import com.fangcloud.thirdpartplatform.utils.CcworkUtils;
import com.fangcloud.thirdpartplatform.utils.SyncStringUtils;
import com.google.common.collect.Lists;
import com.sync.common.entity.dto.YfyMessage;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.enums.YfyMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLEncoder;
import java.util.*;

@Component
@Slf4j
public class CcworkServiceImpl implements ApiExecuteService {

    @Resource
    private HttpClientHelper httpClientHelper;

    @Resource
    private OpenClientHelper openClientHelper;

    @Resource
    private PlatformSyncConfigMapper platformSyncConfigMapper;

    @Resource
    private SyncServiceImpl syncService;

    @Resource
    private EnterpriseServiceImpl enterpriseService;

    @Resource
    private PlatformSyncTaskRecordMapper platformSyncTaskRecordMapper;

    @Resource
    private ApiSyncHandler apiSyncHandler;

    @Resource
    private APIHelper apiHelper;

    /**
     * 递归获取所有部门ID接口
     */
    private static final String DEPT_RELATION = "%s/v2/dept/relation?access_token=%s&did=%s&page=%s";
    /**
     * 获取部门详情接口
     */
    private static final String DEPT_GET = "%s/v2/dept/get?access_token=%s&did=%s";

    private static final String DEPT_GET_V1 = "%s/v1/dept/get?access_token=%s&did=%s";
    /**
     * 获取所有用户id
     */
    private static final String DEPT_UIDS = "%s/v2/dept/uids?access_token=%s";
    /**
     * 批量获取用户详情
     */
    private static final String USER_BATH_GET = "%s/v2/user/batch/get?access_token=%s";

    private static final String USER_BATH_GET_V1 = "%s/v1/user/batch/get?access_token=%s";
    /**
     * 免密登录
     */
    private static final String SSO_CHECK_SIGN = "%s/v2/sso/check_sign";

    private static final String GET_ACCESS_TOKEN = "%s/v2/gettoken?corpid=%s&appid=%s";

    private static final String SEND_MESSAGE = "%s/v2/message/send";

    private static final String DEPT_RELATION_PTH = "$.datas";

    private static final String DEPT_PAGE_COUNT_PTH = "$.pageCount";
    private static final String ACCOUNT_PATH = "$.account";

    private static final String TICKET_PATH = "$.ticket";

    private static final String TOKEN_PATH = "$.access_token";

    private static final String DEPT_DATA = "deptData";

    private static final String SECRET_KEY = "secretKey";
    private static final Integer MAX_SIZE = 100;
    private  PlatformSyncConfig platformSyncDeptConfig;
    private  PlatformSyncConfig platformSyncUserConfig;

    @Override
    public String getApiResult(Map<String, String> headMap, Map<String, Object> paramMap, String oauthResult, String protocol, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId, String apiResult) {
        String host = getHostByConfig(apiConfig, protocol);
        String secretKey = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), SECRET_KEY);
        String syncType = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), "syncType");
        String accessToken = getAccessToken(apiConfig, host);
        List<String> departmentIds = getDepartmentIds(host, secretKey, accessToken);
        if ("dept".equals(syncType)) {
            return getDepartmentInfo(host, secretKey, departmentIds, accessToken, null);
        } else {
            List<String> usersIds = getUsersIds(host, secretKey, accessToken, departmentIds);
            return getUserInfo(host, secretKey, accessToken, usersIds, null);
        }
    }

    @Override
    public String processApiResult(String apiResult, Map<String, String> headMap, Map<String, Object> bodyMap, Map<String, Object> paramMap, APIConfigValueBox.APIConfig apiConfig, Integer enterpriseId) {
        return null;
    }

    @Override
    public String getApiExecuteType() {
        return ApiExecuteTypeEnum.CCWORK.getDesc();
    }

    public List<String> getDepartmentIds(String host, String secretKey, String accessToken) {
        int page = 1;
        List<String> ids = new ArrayList<>();
        while (true) {
            String url = String.format(DEPT_RELATION, host, accessToken, 0, page);
            log.info("get departmentIds url {}", url);
            try {
                page++;
                Map<String, String> headersMap = CcworkUtils.getSignatureHeader(JSONObject.parseObject(""), getUrlParams(url), secretKey);
                httpClientHelper.setHeaders(headersMap);
                Response response = httpClientHelper.getResponse(url);
                if (null == response || null == response.body()) {
                    break;
                }
                String result = Objects.requireNonNull(response.body()).string();
                log.info("get departmentIds url {} heder {} result {}", url, headersMap, result);
                JSONArray dataList = (JSONArray) JSONPath.extract(result, DEPT_RELATION_PTH);
                for (int i = 0; i < dataList.size(); i++) {
                    JSONObject jsonObject = dataList.getJSONObject(i);
                    String did = jsonObject.getString("did");
                    ids.add(did);
                }
                String pageCount = (String) JSONPath.extract(result, DEPT_PAGE_COUNT_PTH);
                if (page > Integer.parseInt(pageCount)) {
                    break;
                }
            } catch (Exception e) {
                log.error("get departmentIds error url {}", url, e);
                break;
            }
        }
        return ids;
    }

    public String getDepartmentInfo(String host, String secretKey, List<String> departmentIds, String accessToken, String interfaceVersion) {
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (String id : departmentIds) {
            String urlPath = DEPT_GET;
            if (interfaceVersion != null &&interfaceVersion.equals("v1")) {
                urlPath = DEPT_GET_V1;
            }
            String url = String.format(urlPath, host, accessToken, id);
            log.info("get departmentInfo url {}", url);
            try {
                Map<String, String> headersMap = CcworkUtils.getSignatureHeader(JSONObject.parseObject(""), getUrlParams(url), secretKey);
                httpClientHelper.setHeaders(headersMap);
                Response response = httpClientHelper.getResponse(url);
                String result = Objects.requireNonNull(response.body()).string();
                log.info("get departmentInfo url {} header {} result {}", url, headersMap, result);
                jsonArray.add(JSON.parseObject(result));
            } catch (Exception e) {
                log.error("get departmentInfo error url {}", url, e);
            }
        }
        jsonObject.put("data", jsonArray);
        return jsonObject.toString();
    }

    public List<String> getUsersIds(String host, String secretKey, String accessToken, List<String> ids) {
        String url = String.format(DEPT_UIDS, host, accessToken);
        log.info("get usersIds url {}", url);
        try {
            String postData = getPostDataByDepartmentIds(ids);
            Map<String, String> headersMap = CcworkUtils.getSignatureHeader(JSONObject.parseObject(postData), getUrlParams(url), secretKey);
            httpClientHelper.setHeaders(headersMap);
            Response response = httpClientHelper.postResponse(url, postData);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("get usersIds url {} header {} result {}", url, headersMap, result);
            return (List<String>) JSONPath.extract(result, DEPT_RELATION_PTH);
        } catch (Exception e) {
            log.error("get usersIds error url {}", url, e);
        }
        return null;
    }

    public String getUserInfo(String host, String secretKey, String accessToken, List<String> userIds, String interfaceVersion) {
        List<List<String>> partition = Lists.partition(userIds, MAX_SIZE);
        if (!host.contains(":8888")) {
            host = host + ":8888";
        }
        String urlPath = USER_BATH_GET;
        if (interfaceVersion != null &&interfaceVersion.equals("v1")) {
            urlPath = USER_BATH_GET_V1;
        }
        String url = String.format(urlPath, host, accessToken);
        log.info("get userInfo url {}", url);
        JSONObject userInfoResult = new JSONObject();
        JSONArray newJsonArray = new JSONArray();
        try {
            for (List<String> userIdList : partition) {
                String postData = getPostDataByUserIds(userIdList);
                Map<String, String> headersMap = CcworkUtils.getSignatureHeader(JSONObject.parseObject(postData), getUrlParams(url), secretKey);
                httpClientHelper.setHeaders(headersMap);
                Response response = httpClientHelper.postResponse(url, postData);
                String result = Objects.requireNonNull(response.body().string());
                log.info("get userInfo url {} header {} result {}", url, headersMap, result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                JSONArray jsonArray = (JSONArray) jsonObject.get("datas");
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject userInfo = (JSONObject) jsonArray.get(i);
                    Object deptData = userInfo.get(DEPT_DATA);
                    List<String> extract = (List<String>) JSONPath.extract(deptData.toString(), "$..did");
                    userInfo.put(DEPT_DATA, extract);
                }
                newJsonArray.addAll(jsonArray);
            }
            userInfoResult.put("datas", newJsonArray);
        } catch (Exception e) {
            log.error("get userInfo error url {}", url, e);
        }
        return userInfoResult.toJSONString();
    }

    public String getAccessToken(APIConfigValueBox.APIConfig apiConfig, String host) {
        String corpId = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), "corpid");
        String appId = apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), "appid");
        String url = String.format(GET_ACCESS_TOKEN, host, corpId, appId);
        log.info("get access_token url {}", url);
        try {
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body().string());
            log.info("get access_token result {}", result);
            return (String) JSONPath.extract(result, TOKEN_PATH);
        } catch (Exception e) {
            log.error("get access_token error url {}", url, e);
        }
        return null;
    }

    public Map<String, Object> getUrlParams(String url) {
        Map<String, Object> urlParams = new LinkedHashMap<>();
        String[] split = url.split("\\?");
        String[] split1 = split[1].split("&");
        for (String s : split1) {
            String[] split2 = s.split("=");
            urlParams.put(split2[0], split2[1]);
        }
        return urlParams;
    }

    public String getLoginUrl(HttpServletRequest request, ConfigInfo configInfo) {
        String msgSignature = request.getParameter("msgSignature");
        String timeStamp = request.getParameter("timeStamp");
        String nonce = request.getParameter("nonce");
        String encrypt = request.getParameter("encrypt");
        String yfyRedirectUrl = request.getParameter("yfyRedirectUrl");
        String enterpriseId = configInfo.getEnterpriseId();
        String platformId = configInfo.getPlatformId();
        String clientId = configInfo.getClientId();
        String config_id = configInfo.getUserConfigId();
        String redirectUrl = StringUtils.isNotEmpty(yfyRedirectUrl) ? yfyRedirectUrl : configInfo.getRedirectUrl();
        PlatformSyncConfig platformSyncConfig = platformSyncConfigMapper.selectByPrimaryKey(Integer.parseInt(config_id));
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(platformSyncConfig.getValueBox(), APIConfigValueBox.class);
        APIConfigValueBox.APIConfig apiConfig = apiConfigValueBox.getApiConfig();
        String str = AESEncryptUtil.decrypt(apiSyncHandler.getValueByParamConfig(apiConfig.getParamConfig(), SECRET_KEY), "", msgSignature, timeStamp, nonce, encrypt);
        String ticket = (String) JSONPath.extract(str, TICKET_PATH);
        String account = getAccountByTicket(getHostByConfig(apiConfig, apiConfigValueBox.getProtocol()), nonce, ticket);
        if (account == null) {
            return null;
        }
        try {
            EnterpriseParams enterpriseParams = new EnterpriseParams();
            enterpriseParams.setEnterpriseId(enterpriseId);
            enterpriseParams.setIdentifier(account);
            enterpriseParams.setPlatformId(platformId);
            enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
            enterpriseParams.setClientId(clientId);
            String loginUrl = openClientHelper.getLoginUrlFromOpenApi(enterpriseParams);
            if (StringUtils.isNotEmpty(redirectUrl)) {
                loginUrl = loginUrl + "&redirect=" + URLEncoder.encode(redirectUrl);
            }
            log.info("ccwork getLoginUrl  {}", loginUrl);
            return loginUrl;
        } catch (Exception e) {
            log.error("ccwork error", e);
        }
        return null;
    }

    private String getAccountByTicket(String host, String nonce, String ticket) {
        String url = String.format(SSO_CHECK_SIGN, host);
        log.info("get accountByTicket url {}", url);
        try {
            Response response = httpClientHelper.postResponse(url, getPostDataByTicket(nonce, ticket));
            String result = Objects.requireNonNull(response.body().string());
            log.info("get accountByTicket result {}", result);
            return (String) JSONPath.extract(result, ACCOUNT_PATH);
        } catch (Exception e) {
            log.error("get accountByTicket error url {}", url, e);
        }
        return null;
    }

    private String getPostDataByDepartmentIds(List<String> ids) {
        return "{\"dids\": " + JSON.toJSONString(ids) + "}";
    }

    private String getPostDataByUserIds(List<String> ids) {
        return "{\"uids\": " + JSON.toJSONString(ids) + "}";
    }

    private String getPostDataByTicket(String nonce, String ticket) {
        return "{\"nonce\":\"" + nonce + "\",\"ticket\":\"" + ticket + "\"}";
    }

    public String getHostByConfig(APIConfigValueBox.APIConfig apiConfig, String protocol) {
        String url = apiConfig.getApi();
        String[] split = url.split("/");
        return protocol + SyncTaskConstants.URL_PREFIX + split[0];
    }


    public boolean messageSend(YfyMessage yfyMessage, String userConfigId, String redirectHost) {
        if (yfyMessage == null) {
            log.error("消息实体为空 yfyMessage {}", yfyMessage);
            return false;
        }
        if (redirectHost == null) {
            redirectHost = "";
        }
        String title = YfyMessageTypeEnum.getEnum(yfyMessage.getType()).getDesc();
        String content = yfyMessage.getContent().replaceAll("\\[\\d+:", "[");
        String webUrl = redirectHost + yfyMessage.getWebUrl();
        webUrl = getRealUrl(webUrl, "target=new&open_window=hidden");
        if (yfyMessage.getMessagePushTitle() != null) {
            title = title.replaceAll("云盘", yfyMessage.getMessagePushTitle());
        }
        // 处理消息内容
        String messageContent = getMessageContent(content, yfyMessage.getTitle());
        if (!isSendLinkMessage(yfyMessage)) {
            //云盘h5消息不支持的类型,给出错误提示
            messageContent += " [该功能不支持织语客户端,请在云盘中打开] ";
            webUrl = redirectHost +  "/assets/app/images/h5error.png?target=new&open_window=hidden";
        }

        return sendMessage(yfyMessage.getReceivers(), messageContent, userConfigId, webUrl, title);
    }

    private boolean sendMessage(String receivers, String messageContent, String userConfigId, String h5Url, String title) {
        platformSyncUserConfig = platformSyncConfigMapper.selectByPrimaryKey(Integer.parseInt(userConfigId));
        APIConfigValueBox apiConfigValueBox = JSON.parseObject(platformSyncUserConfig.getValueBox(), APIConfigValueBox.class);
        String host = getHostByConfig(apiConfigValueBox.getApiConfig(), apiConfigValueBox.getProtocol());
        String secretKey = apiSyncHandler.getValueByParamConfig(apiConfigValueBox.getApiConfig().getParamConfig(), "secretKey");
        String accessToken = getAccessToken(apiConfigValueBox.getApiConfig(), host);
        String url = String.format(SEND_MESSAGE, host);

        try {
            String autoLoginUrl = getAutoLoginUrl(apiSyncHandler.getValueByParamConfig(apiConfigValueBox.getApiConfig().getParamConfig(), "mesageTemplateUrl"),
                    apiSyncHandler.getValueByParamConfig(apiConfigValueBox.getApiConfig().getParamConfig(), "autoLoginUrl"), h5Url);
            String message = buildTextMessage(receivers, messageContent, accessToken, autoLoginUrl, title);
            Map<String, String> headersMap = CcworkUtils.getSignatureHeader(JSONObject.parseObject(message), null, secretKey);
            httpClientHelper.setHeaders(headersMap);
            Response response = httpClientHelper.postResponse(url, message);
            String result = Objects.requireNonNull(response.body().string());
            log.info("send messages url {} header {} result {}", url, headersMap, result);
        } catch (Exception e) {
            log.error("send ccwork message error", e);
            return false;
        }
        return true;
    }

    private String getAutoLoginUrl(String urlTemplate, String autoLoginUrl, String h5Url) throws Exception {

        //urlTemplate  http://10.216.222.82:8282/fed/app-web-workbench/transfer.html?appId=117340***********&redirect_uri=%s

        // 处理免密登录url, 组装回调url https://ysso.ccwork.com.cn/platform/special/autologin?yfyRedirectUrl=

        String realAutoLoginUrl = autoLoginUrl + URLEncoder.encode(h5Url, "UTF-8");

        return String.format(urlTemplate, URLEncoder.encode(realAutoLoginUrl, "UTF-8"));

    }

    public String buildTextMessage(String userId, String messageContent, String accessToken, String url, String title) {
        //return String.format("{\"access_token\":\"%s\",\"accounts\":[\"%s\"],\"type\":\"%s\",\"message\":{\"content\":\"%s\"}}",accessToken, userId, "text", messageContent);

        return String.format("{\"access_token\": \"%s\",\"accounts\": [\"%s\"],\"message\": {\"head\": {\"text\": \"%s\"}," +
                        "\"avatartype\": 1,\"body\": {\"content\": \"%s\"},\"url\": \"%s\"},\"type\": \"attachment\"}",
                accessToken, userId, title, messageContent, url);

    }

    private Boolean isSendLinkMessage(YfyMessage yfyMessage) {
        if (YfyMessageTypeEnum.COMMENT.getType().equals(yfyMessage.getType())
                || YfyMessageTypeEnum.SHARELINK.getType().equals(yfyMessage.getType())
                || YfyMessageTypeEnum.COLLAB.getType().equals(yfyMessage.getType())
                || YfyMessageTypeEnum.FOLLOW.getType().equals(yfyMessage.getType())) {
            return true;
        }
        return false;
    }

    private String getMessageContent(String content, String title) {
        return content + ":" + title;
    }

    private String getRealUrl(String h5Url, String format) {
        if (h5Url.contains("?")) {
            h5Url = h5Url + "&" + format;
        } else {
            h5Url = h5Url + "?" + format;
        }
        return h5Url;
    }


    public Object eventCallback(String message, String userConfigId, String deptConfigId) {
        log.info("回调参数 event {}",message);

        JSONObject jsonObject = JSONObject.parseObject(message);
        String msgSignature = jsonObject.getString("msgSignature");
        String encrypt = jsonObject.getString("encrypt");
        String timeStamp = jsonObject.getString("timeStamp");
        String nonce = jsonObject.getString("nonce");

        setPlatformConfig(deptConfigId, userConfigId);
        List<YfyUser> yfyUserList = new ArrayList<>();
        List<YfyDepartment> yfyDepartments = new ArrayList<>();

        APIConfigValueBox baseConfigValueBox = JSONObject.parseObject(platformSyncUserConfig.getValueBox(), APIConfigValueBox.class);
        String host = getHostByConfig(baseConfigValueBox.getApiConfig(), baseConfigValueBox.getProtocol());
        String secretKey = apiSyncHandler.getValueByParamConfig(baseConfigValueBox.getApiConfig().getParamConfig(), SECRET_KEY);
        String accessToken = getAccessToken(baseConfigValueBox.getApiConfig(), host);
        String str = AESEncryptUtil.decrypt(secretKey, "", msgSignature, timeStamp, nonce, encrypt);
        log.info("事件回调解析数据为： {}", str);
        CcworkEvent event = JSONObject.parseObject(str, CcworkEvent.class);

        Boolean isCreateDefaultPassword = false;
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(platformSyncUserConfig.getEnterpriseId()+ "");
        String isCreateDefaultPasswordConfig = configMap.get(PlatformGlobalConfigKeyEnum.CREATE_DEFAULT_PASSWORD.getKey());
        if (StringUtils.isNotBlank(isCreateDefaultPasswordConfig)){
            isCreateDefaultPassword = Boolean.valueOf(isCreateDefaultPasswordConfig);
        }
        String customPassword = configMap.get(PlatformGlobalConfigKeyEnum.CUSTOM_PASSWORD.getKey());
        if (event.getEvent_type().equals("check_url")) {
            return CcworkUtils.CBCencrypt(secretKey, "success");
        }


        /**
         * 账号删除
         */
        if (event.getEvent_type().equals("user_delete") && event.getIds_type().equals("1")){
            List<String> accounts = event.getAccounts();
            List<YfyUser> finalYfyUserList = yfyUserList;
            accounts.forEach(user -> {
                YfyUser yfyUser = buildDeleteYfyUser(user);
                if (yfyUser == null) {
                    return;
                }
                finalYfyUserList.add(yfyUser);
            });
            Integer taskId = getTaskId(platformSyncUserConfig);
            ExecuteResult result = syncService.syncUsers(new ArrayList<>(), finalYfyUserList, taskId,
                    getEnterprise(), platformSyncUserConfig, false, null);
            updateTask(taskId, result);

            log.info("ccwork event user_delete sync result {} users {}", result, finalYfyUserList);
            return true;
        }

        /**
         * 账号新增 / 更新
         */
        if (event.getEvent_type().equals("user_add") || event.getEvent_type().equals("user_update")){
            APIConfigValueBox apiConfigValueBox = JSON.parseObject(platformSyncUserConfig.getValueBox(), APIConfigValueBox.class);
            String userResult = getUserInfo(host,secretKey,accessToken, event.getIds(), null);
            JSONArray apiResultData = apiSyncHandler.checkAndGet(apiConfigValueBox.getApiConfig(), userResult);

            log.info("syncUser-apiConfigData:{}", apiResultData);
            if (apiResultData.isEmpty()) {
                throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
            }
            yfyUserList = apiHelper.buildUser(apiResultData, apiConfigValueBox);
            log.info("ccwork event {} yfyUserList {}", event.getEvent_type(), yfyUserList);
            Integer taskId = getTaskId(platformSyncUserConfig);
            ExecuteResult result = syncService.syncUsers(new ArrayList<>(), yfyUserList, taskId,
                    getEnterprise(), platformSyncUserConfig, isCreateDefaultPassword, customPassword);
            updateTask(taskId, result);
            log.info("ccwork event {}} sync result {} users {}", event.getEvent_type(), result, yfyUserList);
            return true;
        }

        /**
         * 部门创建 / 更新
         */
        if (event.getEvent_type().equals("dept_add") || event.getEvent_type().equals("dept_update")){
            APIConfigValueBox apiConfigValueBox = JSON.parseObject(platformSyncDeptConfig.getValueBox(), APIConfigValueBox.class);
            String departmentResult = getDepartmentInfo(host,secretKey, event.getIds(), accessToken, null);
            JSONArray apiResultData = apiSyncHandler.checkAndGet(apiConfigValueBox.getApiConfig(), departmentResult);

            log.info("syncDepartment-apiConfigData:{}", apiResultData);
            if (apiResultData.isEmpty()) {
                throw new ParamException(SyncTaskConstants.SYNC_USER_DATA_IS_NULL);
            }
            yfyDepartments = apiHelper.buildDept(apiResultData, apiConfigValueBox);
            log.info("ccwork event {} yfyUserList {}", event.getEvent_type(), yfyDepartments);
            Integer taskId = getTaskId(platformSyncDeptConfig);
            ExecuteResult result = syncService.syncDepartments(new ArrayList<>(), yfyDepartments, taskId,
                    getEnterprise(), platformSyncDeptConfig);
            updateTask(taskId, result);
            log.info("ccwork event {} sync result {} users {}", event.getEvent_type(), result, yfyUserList);

            return true;
        }
        if (event.getEvent_type().equals("dept_delete")){
            List<String> ids = event.getIds();
            List<YfyDepartment> finalYfyDepartments = yfyDepartments;
            ids.forEach(deptId -> {
                YfyDepartment yfyDepartment = buildDeleteYfyDepartment(deptId);
                finalYfyDepartments.add(yfyDepartment);
            });
            Integer taskId = getTaskId(platformSyncUserConfig);
            ExecuteResult result = syncService.syncDepartments(new ArrayList<>(), finalYfyDepartments, taskId,
                    getEnterprise(), platformSyncUserConfig);
            updateTask(taskId, result);
            log.info("ccwork event user_delete sync result {} users {}", result, finalYfyDepartments);
            return true;
        }
        return false;

    }

    private void updateTask(Integer taskId, ExecuteResult execute) {

        PlatformSyncTaskRecord platformSyncTaskRecord = new PlatformSyncTaskRecord();
        platformSyncTaskRecord.setId(taskId);
        platformSyncTaskRecord.setSyncTaskStatus(SyncResultStatusEnum.FINISH.getStatus());
        if (execute != null && execute.getErrorRows() <= 0) {
            platformSyncTaskRecord.setSyncResult(SyncResultStatusEnum.FINISH.getStatus());
        }
        if (execute == null || execute.getErrorRows() > 0) {
            platformSyncTaskRecord.setSyncResult(SyncResultStatusEnum.ERROR.getStatus());
        }
        platformSyncTaskRecord.setValueBox(JSON.toJSONString(execute));
        long endTime = new Date().getTime();
        platformSyncTaskRecord.setUpdated(endTime);
        platformSyncTaskRecord.setSyncEndat(endTime);
        platformSyncTaskRecordMapper.updateByPrimaryKeySelective(platformSyncTaskRecord);
    }

    private Integer getTaskId(PlatformSyncConfig platformSyncConfig) {
        Integer id = platformSyncConfig.getId();
        Integer enterpriseId = platformSyncConfig.getEnterpriseId();
        PlatformSyncTaskRecord platformSyncTaskRecord = new PlatformSyncTaskRecord();
        platformSyncTaskRecord.setSyncConfigId(id);
        platformSyncTaskRecord.setSyncTaskType(platformSyncConfig.getSyncType());
        platformSyncTaskRecord.setSyncTaskStatus(SyncResultStatusEnum.PROGRESSING.getStatus());
        platformSyncTaskRecord.setEnterpriseId(enterpriseId);
        long time = new Date().getTime();
        platformSyncTaskRecord.setSyncStartat(time);
        platformSyncTaskRecord.setCreated(time);
        platformSyncTaskRecord.setSyncResult(1);
        platformSyncTaskRecord.setUpdated(time);
        int i = platformSyncTaskRecordMapper.insertSelective(platformSyncTaskRecord);
        return platformSyncTaskRecord.getId();
    }

    private Enterprise getEnterprise() {
        // 1. 获取企业信息
        Enterprise enterprise = enterpriseService.getEnterpriseById(platformSyncUserConfig.getEnterpriseId());
        if(Objects.isNull(enterprise)){
            log.info("enterprise id {}, info is null !",platformSyncUserConfig.getEnterpriseId());
            throw new ParamException("enterprise is null !");
        }

        return enterprise;
    }

    private void setPlatformConfig(String deptConfigId, String userConfigId) {
        platformSyncDeptConfig = platformSyncConfigMapper.selectByPrimaryKey(Integer.parseInt(deptConfigId));
        platformSyncUserConfig = platformSyncConfigMapper.selectByPrimaryKey(Integer.parseInt(userConfigId));
    }

    private YfyUser buildDeleteYfyUser(String user) {
        APIConfigValueBox apiConfigValueBox = JSONObject.parseObject(platformSyncUserConfig.getValueBox(), APIConfigValueBox.class);
        YfyUser yfyUser = YfyUser.builder()
                .fullName(SyncStringUtils.formatString(user))
                .id(user)
                .email(user + apiConfigValueBox.getUserConfigDto().getEmailSuffix())
                .status("2")
                .forced(true)
                .build();

        log.info("从第三方删除用户数据并且重新组装后的 YfyUser {} 原数据 {}", yfyUser, user);

        return yfyUser;
    }

    private YfyDepartment buildDeleteYfyDepartment(String deptId) {
        YfyDepartment yfyDepartment = YfyDepartment.builder()
                .id(deptId)
                .name(deptId)
                .status("0")
                .build();
        log.info("从第三方删除部门数据并且重新组装后的 yfyDepartment {} department {}", yfyDepartment, deptId);
        return yfyDepartment;
    }

}
