package com.fangcloud.thirdpartplatform.service.impl;

import com.fangcloud.thirdpartplatform.entity.input.RadiusCheckLoginParams;
import com.fangcloud.thirdpartplatform.service.RadiusService;
import com.fangcloud.thirdpartplatform.utils.RadiusUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RadiusServiceImpl implements RadiusService {

    @Override
    public Boolean checkLogin(RadiusCheckLoginParams params) {
        return RadiusUtils.checkLogin(params);
    }
}
