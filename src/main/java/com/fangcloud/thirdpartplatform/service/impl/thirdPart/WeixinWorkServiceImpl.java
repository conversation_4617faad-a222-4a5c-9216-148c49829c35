package com.fangcloud.thirdpartplatform.service.impl.thirdPart;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.entity.common.UserEnterpriseInfo;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.entity.input.WeixinWorkInitParams;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.helper.WeixinWorkClientHelper;
import com.fangcloud.thirdpartplatform.service.WeixinWorkService;
import com.fangcloud.thirdpartplatform.service.impl.custom.zjky.ZjkyWexinService;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;


@Service
@Slf4j
public class WeixinWorkServiceImpl extends BaseServiceImpl implements WeixinWorkService {

    private WeixinWorkClientHelper weixinWorkClientHelper;

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private ZjkyWexinService zjkyWexinService;

    @Override
    public String getAutoLoginUrl(WeixinWorkInitParams params)  {
        // 调微信接口获取用户ID
        String idInfo = null;
        YfyUser yfyUser = new WeixinWorkClientHelper(params).getYfyUser();

        //zjky的企业id与云盘同步的不一致需要做替换
        if (CommonConstants.ZJKY_WEINXIN_CORPID.equals(params.getCorpId())) {
            String newYfyUserId = zjkyWexinService.getUidByWeixinId(yfyUser.getId());
            log.info("zjky newYfyUserId: {} weixinId: {}", newYfyUserId, yfyUser.getId());
            if (StringUtils.isNotBlank(newYfyUserId)) {
                yfyUser.setId(newYfyUserId);
            }
            log.info("zjky newYfyUserId: {} weixinId: {} yfyUser: {}", newYfyUserId, yfyUser.getId(), yfyUser);
        }

        if (CommonConstants.ENTERPRISE_LOGIN_TYPE_USER_ID.equals(params.getLoginType())) {
            idInfo = yfyUser.getId();
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
        } else if (CommonConstants.ENTERPRISE_LOGIN_TYPE_PHONE.equals(params.getLoginType())) {
            String phone = yfyUser.getPhone();
            if (StringUtils.isNotBlank(phone) && phone.contains("+")) {
                if (phone.contains(SyncTaskConstants.USER_PHONE_CN)) {
                    phone = phone.substring(Math.max(0, phone.length() - 11));
                } else {
                    phone = null;
                }
            }
            idInfo = phone;
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
        }else if (CommonConstants.ENTERPRISE_LOGIN_TYPE_EMAIL.equals(params.getLoginType())) {
            idInfo = yfyUser.getEmail();
            params.getEnterpriseParams().setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
        } else {
            log.error("未知的类型 loginType {}", params.getLoginType());
        }

        if (StringUtils.isEmpty(idInfo)) {
            log.warn("从微信获取用户信息为空 入参 params {}", params);
        }else {
            params.getEnterpriseParams().setIdentifier(idInfo);
            if (params.getSyncUserFlag()  && StringUtils.isEmpty(yfyUser.getFullName())){
                String userDetailJson = new WeixinWorkClientHelper(params).getUserDetail(yfyUser.getId());
                Object name = JSON.parseObject(userDetailJson).get("name");
                if (Objects.isNull(name)){
                    log.info("get name is null !  yfyUser:{}",yfyUser);
                } else {
                    yfyUser.setFullName(name.toString());
                }
            }
            // 获取登录url
            String loginUrl = getLoginUrl(params.getSyncUserFlag(), params.getEnterpriseParams(), yfyUser);
            if (loginUrl != null) {
                return loginUrl;
            }

        }

        dropWeixinWorkClient();
        return CommonConstants.URL_ACCOUNT_NOT_OPEN;
    }

    @Override
    public Boolean sendMessage(MessageParams params) {
        WeixinWorkClientHelper weixinWorkClientHelper = new WeixinWorkClientHelper(params.getWeixinWorkInitParams());
        boolean sendFlag = false;

        // 登陆类型为mobile的时候需要转化消息接受者
        if (CommonConstants.ENTERPRISE_LOGIN_TYPE_PHONE.equals(params.getWeixinWorkInitParams().getLoginType())) {
            EnterpriseParams enterpriseParams = params.getWeixinWorkInitParams().getEnterpriseParams();
            UserEnterpriseInfo userEnterpriseInfo = UserEnterpriseInfo.builder()
                    .enterpriseId(enterpriseParams.getEnterpriseId())
                    .platformId(enterpriseParams.getPlatformId())
                    .userId(params.getReceivers()).build();
            JSONObject userInfoJSONObject = v2ClientHelper.getUserInfoFromV2(userEnterpriseInfo);

            if (Objects.isNull(userInfoJSONObject)) {
                // 兼容公有云企业微信去 72下再查一次
                userEnterpriseInfo = UserEnterpriseInfo.builder()
                        .enterpriseId(enterpriseParams.getEnterpriseId())
                        .platformId("72")
                        .yfyUserId(params.getReceivers()).build();
                userInfoJSONObject = v2ClientHelper.getUserInfoFromV2(userEnterpriseInfo);
            }
            // 通过userid 去查用户信息
            if (Objects.isNull(userInfoJSONObject)) {
                userInfoJSONObject = v2ClientHelper.getUserInfoByUserId(params.getReceivers());
            }

            if(!Objects.isNull(userInfoJSONObject)){
                String phone = userInfoJSONObject.getString("phone");
                if(StringUtils.isEmpty(phone)){
                    return sendFlag;
                }
                // 根据手机号获取获取用户id
                String weiXinUserId = weixinWorkClientHelper.getUserIdByPhone(phone);
                if(StringUtils.isEmpty(weiXinUserId)){
                    return sendFlag;
                }
                params.setReceivers(weiXinUserId);
            }
        }else if(CommonConstants.ENTERPRISE_LOGIN_TYPE_YFY_USER_ID.equals(params.getWeixinWorkInitParams().getLoginType())){
            // 公有云企业只有接受者为亿方云用户id，需要转化为企业微信的id

        }else if(CommonConstants.ENTERPRISE_LOGIN_TYPE_EMAIL.equals(params.getWeixinWorkInitParams().getLoginType())){
            //登录类型为email的时候需要转化消息接受者
            EnterpriseParams enterpriseParams = params.getWeixinWorkInitParams().getEnterpriseParams();
            UserEnterpriseInfo userEnterpriseInfo = UserEnterpriseInfo.builder()
                    .enterpriseId(enterpriseParams.getEnterpriseId())
                    .platformId(enterpriseParams.getPlatformId())
                    .userId(params.getReceivers()).build();
            JSONObject userInfoJSONObject = v2ClientHelper.getUserInfoFromV2(userEnterpriseInfo);
            if(!Objects.isNull(userInfoJSONObject)){
                String email = userInfoJSONObject.getString("email");
                if(StringUtils.isEmpty(email)){
                    return sendFlag;
                }
                // 根据手机号获取获取钉钉用户id
                String dingTalkUserId = weixinWorkClientHelper.getUserIdByEmail(email);
                if(StringUtils.isEmpty(dingTalkUserId)){
                    return sendFlag;
                }
                params.setReceivers(dingTalkUserId);
            }
        }


        // zjky的企业id与云盘同步的不一致需要做替换
        if (CommonConstants.ZJKY_WEINXIN_CORPID.equals(params.getWeixinWorkInitParams().getCorpId())) {
            String newWexinId = zjkyWexinService.getWeixinIdByUid(params.getReceivers());
            log.info("zjky newWeixinId: {} userId: {}", newWexinId, params.getReceivers());
            if (StringUtils.isNotBlank(newWexinId)) {
                params.setReceivers(newWexinId);
            }
            log.info("zjky newWeixinId: {} userId: {} params: {}", newWexinId, params.getReceivers(), params);
        } 

        String baseUrl = params.getWeixinWorkInitParams().getBaseUrl();

        //TODO kaika公司临时解决方案，后续下线
        String h5Url = params.getH5Url();
        if(StringUtils.isEmpty(baseUrl) && h5Url.contains("appid=wwc43c12428f29677c")){
            log.info("baseUrl is null!");
            log.info("kaika message autoLogin update , before h5Url:{}", params.getH5Url());
            String[] split = h5Url.split("redirect_uri=");
            h5Url = split[0] + "redirect_uri=" + "https%3A%2F%2Fpan.kactusbio.cn%2F" + split[1];
            params.setH5Url(h5Url);
            log.info("kaika message autoLogin update , after h5Url:{}", params.getH5Url());
        }

        dropWeixinWorkClient();

        return weixinWorkClientHelper.sendMessage(getYfyMessage(params));
    }

    private void dropWeixinWorkClient()
    {
        weixinWorkClientHelper = null;
    }
}
