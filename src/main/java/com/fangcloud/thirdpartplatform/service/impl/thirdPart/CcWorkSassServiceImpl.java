package com.fangcloud.thirdpartplatform.service.impl.thirdPart;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.OpenApiConstants;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.CcworkEvent;
import com.fangcloud.thirdpartplatform.entity.dto.APIConfigValueBox;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.input.CcWorkInitParam;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.entity.sync.*;
import com.fangcloud.thirdpartplatform.helper.CcWorkClientHelper;
import com.fangcloud.thirdpartplatform.helper.OauthClientHelper;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.service.CcWorkSassService;
import com.fangcloud.thirdpartplatform.service.EnterpriseService;
import com.fangcloud.thirdpartplatform.service.PlatformEnterprisesService;
import com.fangcloud.thirdpartplatform.service.impl.CcworkServiceImpl;
import com.fangcloud.thirdpartplatform.utils.CcworkUtils;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.utils.SyncStringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CcWorkSassServiceImpl extends BaseServiceImpl implements CcWorkSassService {

    @Resource
    private PlatformEnterprisesService platformEnterprisesService;

    @Resource
    private EnterpriseService enterpriseService;

    @Resource
    private OauthClientHelper oauthClientHelper;

    private CcWorkClientHelper ccWorkClientHelper;

    @Resource
    private CustomNacosConfig customNacosConfig;


    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    @Resource
    private V2ClientHelper v2ClientHelper;

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private CcworkServiceImpl ccworkService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PlatformDepartmentMapper platformDepartmentMapper;

    private static Long USER_DEFAULT_SPACE_TOTAL = 5L;
    private static Long DEPARTMENT_DEFAULT_SPACE_TOTAL = 50L;

    private static Long DEPARTMENT_POSITION_STAFF = 0L;
    private static Long DEPARTMENT_POSITION_DIRECTOR = 2L;
    @Override
    public String getAutoLoginUrl(CcWorkInitParam ccWorkInitParam) {
        String idInfo = null;
        YfyUser yfyUser = new CcWorkClientHelper(ccWorkInitParam).getUserInfoByTicket();
        if (yfyUser == null) {
            return CommonConstants.URL_ACCOUNT_NOT_OPEN;
        }
        if (ccWorkInitParam.getAccountType().equals(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET)) {
            idInfo = yfyUser.getId();
        } else if (ccWorkInitParam.getAccountType().equals(OpenApiConstants.IDENTIFIER_TYPE_EMAIL)) {
            idInfo = yfyUser.getEmail();
        } else {
            idInfo = yfyUser.getPhone();
        }
        ccWorkInitParam.setEnterpriseParams(getEnterpriseParams(yfyUser.getTuituiTenantsID(), idInfo, ccWorkInitParam.getAccountType()));
        if (StringUtils.isEmpty(idInfo)) {
            log.error("idInfo is null ccWorkInitParam {}", ccWorkInitParam);
        } else {
            // 获取登录url
            String loginUrl = getLoginUrl(false, ccWorkInitParam.getEnterpriseParams(), yfyUser);
            if (loginUrl != null) {
                log.info("loginUrl {}", loginUrl);
                return loginUrl;
            }
        }
        dropCcWorkClient();
        return CommonConstants.URL_ACCOUNT_NOT_OPEN;
    }

    @Override
    public Object syncCallBack(String message) {
        log.info("syncCallBack message {}", message);
        JSONObject jsonObject = JSONObject.parseObject(message);
        String msgSignature = jsonObject.getString("msgSignature");
        String encrypt = jsonObject.getString("encrypt");
        String timeStamp = jsonObject.getString("timeStamp");
        String nonce = jsonObject.getString("nonce");

        CcWorkInitParam ccWorkInitParam = new CcWorkInitParam();
        ccWorkInitParam.setMsgSignature(msgSignature);
        ccWorkInitParam.setEncrypt(encrypt);
        ccWorkInitParam.setTimeStamp(timeStamp);
        ccWorkInitParam.setNonce(nonce);

        String idInfo = null;
        CcworkEvent event = new CcWorkClientHelper(ccWorkInitParam).getEventObject();
        if (event == null) {
            log.error("event is null ccWorkInitParam {}", ccWorkInitParam);
            return false;
        }

        if (event.getEvent_type().equals("check_url")) {
            return CcworkUtils.CBCencrypt(customNacosConfig.getCustomTuiSecretKey(), "success");
        }

        ccWorkInitParam.setCorpId(event.getCorp_id());
        EnterpriseParams enterpriseParams =getEnterpriseParams(event.getCorp_id(), "", "");
        if (enterpriseParams == null) {
            log.info("enterpriseParams is null, event {}", event);
            return false;
        }

        ccWorkInitParam.setEnterpriseParams(enterpriseParams);

        /**
         * 账号删除
         */
        if (event.getEvent_type().equals("user_delete") && event.getIds_type().equals("1")){
            eventDeleteUser(event, ccWorkInitParam);
            return true;
        }

        /**
         * 账号新增
         */
        if (event.getEvent_type().equals("user_add")){
            eventAddUser(event, ccWorkInitParam);
            return true;
        }

        /**
         * 账号更新
         */
        if (event.getEvent_type().equals("user_update")) {
            eventUpdateUser(event, ccWorkInitParam);
            return true;
        }


        /**
         * 部门删除
         */
        if (event.getEvent_type().equals("dept_delete")){
            eventDeleteDept(event, ccWorkInitParam);
            return true;
        }

        /**
         * 部门新增
         */
        if (event.getEvent_type().equals("dept_add")){
            eventAddDept(event, ccWorkInitParam);
            return true;
        }

        /**
         * 部门更新
         */
        if (event.getEvent_type().equals("dept_update")) {
            eventUpdateDept(event, ccWorkInitParam);
            return true;
        }



        dropCcWorkClient();
        return false;
    }

    private void eventUpdateDept(CcworkEvent event, CcWorkInitParam ccWorkInitParam) {
        List<String> ccworkDeptIds = event.getIds();
        String accessToken = new CcWorkClientHelper(ccWorkInitParam).getAccessToken();
        String result = ccworkService.getDepartmentInfo(customNacosConfig.getCustomTuiHost(), customNacosConfig.getCustomTuiSecretKey(),
                ccworkDeptIds, accessToken, "v1");
        log.info("ccwork-saas eventUpdateDept result {}", result);
        cn.hutool.json.JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("data");

        for (int i = 0; i< jsonArray.size(); i++) {
            cn.hutool.json.JSONObject item = jsonArray.getJSONObject(i);
            PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
            // 1. 设置部门名称
            syncPublicDepartmentBean.setDepartmentName(item.getStr("name"));

            // 2. 设置父部门id
            if (item.getStr("pid").equals("0")) { // 次顶级部门，父部门设置为0
                log.info("parent is is top!");
                syncPublicDepartmentBean.setParentId(0L);
            } else {// 父部门需要换取真正的部门id
                String platformParentId = ccWorkInitParam.getCorpId() + CommonConstants.SPLIT_UNDERLINE + item.getStr("pid");
                PlatformDepartment platformParentDepartment = platformDepartmentMapper.getByPlatformIdAndDepartmentId(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()), platformParentId);
                if (!Objects.isNull(platformParentDepartment)) {
                    syncPublicDepartmentBean.setParentId(platformParentDepartment.getYfyDepartmentId());
                }
            }

            // 4. 设置排序
            syncPublicDepartmentBean.setOrder(Long.valueOf(item.getStr("order")));

            PlatformDepartment localPlatformDepartment = platformDepartmentMapper.getByPlatformIdAndDepartmentId(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()),
                    ccWorkInitParam.getCorpId() + CommonConstants.SPLIT_UNDERLINE + item.getStr("id"));

            //  修改部门
            PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.editDepartment(syncPublicDepartmentBean,
                    localPlatformDepartment.getYfyDepartmentId(), ccWorkInitParam.getEnterpriseParams().getAdminUserId());

            // 若部门修改成功，维护部门映射关系
            if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {

                // 若部门的name发生改变，更新platformDepartment的name
                if (!syncPublicDepartmentResult.getName().equals(localPlatformDepartment.getName())) {
                    localPlatformDepartment.setName(syncPublicDepartmentResult.getName());
                    platformDepartmentMapper.updateNameById(localPlatformDepartment);
                }
            }

        }
    }

    private void eventAddDept(CcworkEvent event, CcWorkInitParam ccWorkInitParam) {

        List<String> ccworkDeptIds = event.getIds();
        String accessToken = new CcWorkClientHelper(ccWorkInitParam).getAccessToken();
        String result = ccworkService.getDepartmentInfo(customNacosConfig.getCustomTuiHost(), customNacosConfig.getCustomTuiSecretKey(),
                ccworkDeptIds, accessToken, "v1");
        log.info("ccwork-saas eventAddDept result {}", result);
        cn.hutool.json.JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("data");

        for (int i = 0; i< jsonArray.size(); i++) {
            cn.hutool.json.JSONObject item = jsonArray.getJSONObject(i);
            PublicDepartmentBean syncPublicDepartmentBean = new PublicDepartmentBean();
            // 2.1 设置部门名称
            syncPublicDepartmentBean.setDepartmentName(SyncStringUtils.formatString(item.getStr("name")));

            // 2.2 设置父部门id
            if (item.getStr("pid").equals("0")) {  // 次顶级部门，父部门设置为null
                syncPublicDepartmentBean.setParentId(0L);
            } else {// 父部门需要换取真正的部门id
                String platformParentId = ccWorkInitParam.getCorpId() + CommonConstants.SPLIT_UNDERLINE + item.getStr("pid");
                PlatformDepartment platformParentDepartment = platformDepartmentMapper.getByPlatformIdAndDepartmentId(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()), platformParentId);
                if (!Objects.isNull(platformParentDepartment)) {
                    syncPublicDepartmentBean.setParentId(platformParentDepartment.getYfyDepartmentId());
                }
            }

            // 2.4 设置部门空间，默认设置100G
            syncPublicDepartmentBean.setSpaceTotal(DEPARTMENT_DEFAULT_SPACE_TOTAL);
            // 2.5 设置是否创建公共资料库，默认为true
            syncPublicDepartmentBean.setCreateCommonFolder(true);
            // 2.6 设置部门是否自动接受协作，默认为true
            syncPublicDepartmentBean.setCollabAutoAccepted(true);
            // 2.7 设置排序
            syncPublicDepartmentBean.setOrder(Long.valueOf(item.getStr("order")));


            PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.createDepartment(syncPublicDepartmentBean, ccWorkInitParam.getEnterpriseParams().getAdminUserId());

            // 若部门创建成功，插入部门信息
            if (!Objects.isNull(syncPublicDepartmentResult) && StringUtils.isBlank(syncPublicDepartmentResult.getErrorMessage())) {
                // 创建部门映射关系
                PlatformDepartment platformDepartment = new PlatformDepartment();
                platformDepartment.setPlatformId(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()));
                platformDepartment.setEnterpriseTicket(ccWorkInitParam.getCorpId());
                platformDepartment.setDepartmentId(ccWorkInitParam.getCorpId() + CommonConstants.SPLIT_UNDERLINE + item.getStr("did"));
                platformDepartment.setYfyDepartmentId(syncPublicDepartmentResult.getId());
                platformDepartment.setName(syncPublicDepartmentResult.getName());

                platformDepartmentMapper.insert(platformDepartment);
            }

        }
    }

    private void eventDeleteDept(CcworkEvent event, CcWorkInitParam ccWorkInitParam) {
        List<String> ccworkDeptIds = event.getIds();
        ccworkDeptIds.forEach(ccworkDeptId -> {
            String platformDepartmentId = ccWorkInitParam.getCorpId() + CommonConstants.SPLIT_UNDERLINE + ccworkDeptId;
            PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndDepartmentId(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()), platformDepartmentId);
            boolean deleteResult = v2ClientHelper.deleteDepartment(ccWorkInitParam.getEnterpriseParams().getAdminUserId(), platformDepartment.getYfyDepartmentId());
            if (deleteResult) {
                platformDepartmentMapper.delete(platformDepartment.getId());
            }
        });

    }

    private void eventUpdateUser(CcworkEvent event, CcWorkInitParam ccWorkInitParam) {

        List<String> ccworkUserIds = event.getIds();

        String accessToken = new CcWorkClientHelper(ccWorkInitParam).getAccessToken();
        String result = ccworkService.getUserInfo(customNacosConfig.getCustomTuiHost(), customNacosConfig.getCustomTuiSecretKey(),
                accessToken, ccworkUserIds, "v1");
        log.info("ccwork-saas eventUpdateUser result {}", result);
        cn.hutool.json.JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("datas");

        for (int i = 0; i< jsonArray.size(); i++) {
            cn.hutool.json.JSONObject item = jsonArray.getJSONObject(i);
            PublicUserBean syncPublicUserBean = new PublicUserBean();

            List<String> identifiers = new ArrayList<>();
            if (item.getStr("account").contains("@")) {
                identifiers.add(item.getStr("account"));
            } else {
                identifiers.add(item.getStr("email"));
            }
            syncPublicUserBean.setIdentifiers(identifiers);
            String name;
            if (item.getStr("name").contains("@")) {
                name = item.getStr("name").split("@")[0];

            } else {
                name = item.getStr("name");
            }
            syncPublicUserBean.setName(name);

            PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserTicket(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()), item.getStr("account"));
            log.info("CcWorkUser info :{}, user info:{}", jsonArray.getJSONObject(i), platformUser);

            v2ClientHelper.editUser(syncPublicUserBean, ccWorkInitParam.getEnterpriseParams().getAdminUserId(), platformUser.getUserId());


            // 2. 对比部门列表
            List<Long> yfyDepartmentIdList = new ArrayList<>();
            List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(platformUser.getUserId());
            if (!CollectionUtils.isEmpty(departmentsUsers)) {
                for (DepartmentsUsers departmentsUser : departmentsUsers) {
                    int position = departmentsUser.getPosition();
                    // 当用户职位是员工或者主管的时候，对比部门信息
                    if (position == DEPARTMENT_POSITION_STAFF || position == DEPARTMENT_POSITION_DIRECTOR) {
                        yfyDepartmentIdList.add(departmentsUser.getDepartmentId());
                    }
                }
            }

            String departmentId = ccWorkInitParam.getCorpId() + CommonConstants.SPLIT_UNDERLINE +item.getByPath("deptData.0").toString();
            List<Long> ccWorkDepartmentIdList = new ArrayList<>();
            // 将推推部门id转为亿方云id
            if (StringUtils.isNotBlank(departmentId)) {
                long yfyDepartmentId = 0;
                PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndDepartmentId(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()), departmentId);
                // 将用户添加到对应的部门中
                if (!Objects.isNull(platformDepartment)) {
                    yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                } else if (item.getJSONArray("deptData").isEmpty()) {
                    // 4. 获取企业的顶级部门id
                    Department rootDepartment = departmentMapper.queryRootDepartment(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getEnterpriseId()));
                    yfyDepartmentId = rootDepartment.getId();
                }
                ccWorkDepartmentIdList.add(yfyDepartmentId);
            }

            List<Long> addDepartmentList = new ArrayList<>();
            List<Long> deleteDepartmentList = new ArrayList<>();

            if (StringUtils.isNotBlank(departmentId) && CollectionUtils.isEmpty(departmentsUsers)) { // 推推和亿方云都是空的，没有发生变化

            } else if (StringUtils.isNotBlank(departmentId) && CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云为空的，推推不为空，发生改变
                // 将所有微信部门同步到亿方云
                for (Long yfyDepartmentId : ccWorkDepartmentIdList) {
                    addDepartmentList.add(yfyDepartmentId);
                }
            } else if (StringUtils.isBlank(departmentId) && !CollectionUtils.isEmpty(departmentsUsers)) {// 亿方云不为空，推推为空，发生改变
                // 将人员从部门中移除
                for (DepartmentsUsers departmentsUser : departmentsUsers) {
                    deleteDepartmentList.add(departmentsUser.getDepartmentId());
                }
            } else {
                // 找出需要删除的部门
                for (Long aLong : yfyDepartmentIdList) {
                    if (!ccWorkDepartmentIdList.contains(aLong)) {
                        deleteDepartmentList.add(aLong);
                    }
                }
                // 找出需要添加的部门
                for (Long aLong : ccWorkDepartmentIdList) {
                    if (!yfyDepartmentIdList.contains(aLong)) {
                        addDepartmentList.add(aLong);
                    }
                }
            }

            List<Long> userIds = new ArrayList<>();
            userIds.add(platformUser.getUserId());

            // 循环删除部门成员
            if (!CollectionUtils.isEmpty(deleteDepartmentList)) {
                for (Long aLong : deleteDepartmentList) {
                    EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                    editDepartmentUserBean.setDeleteUserIds(userIds);
                    EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, ccWorkInitParam.getEnterpriseParams().getAdminUserId(), aLong);
                    if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                        log.info("department delete user success! user id :{}, department id :{}", platformUser.getUserId(), aLong);
                    }
                }
            }

            // 循环添加部门成员
            if (!CollectionUtils.isEmpty(addDepartmentList)) {
                for (Long aLong : addDepartmentList) {
                    EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                    editDepartmentUserBean.setAddUserIds(userIds);
                    EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, ccWorkInitParam.getEnterpriseParams().getAdminUserId(), aLong);
                    if (!Objects.isNull(editDepartmentResult) && StringUtils.isBlank(editDepartmentResult.getErrorMessage())) {
                        log.info("department add user success! user id :{}, department id :{}", platformUser.getUserId(), aLong);
                    }
                }
            }
        }
    }

    private void eventAddUser(CcworkEvent event, CcWorkInitParam ccWorkInitParam) {

        List<String> ccworkUserIds = event.getIds();

        String accessToken = new CcWorkClientHelper(ccWorkInitParam).getAccessToken();
        String result = ccworkService.getUserInfo(customNacosConfig.getCustomTuiHost(), customNacosConfig.getCustomTuiSecretKey(),
                accessToken, ccworkUserIds, "v1");
        log.info("ccwork-saas eventAddUser result {}", result);
        cn.hutool.json.JSONArray jsonArray = JSONUtil.parseObj(result).getJSONArray("datas");

        for (int i = 0; i< jsonArray.size(); i++){
            cn.hutool.json.JSONObject item = jsonArray.getJSONObject(i);
            PublicUserBean syncPublicUserBean = new PublicUserBean();
            syncPublicUserBean.setPassword(customNacosConfig.getDefaultPassword());
            syncPublicUserBean.setSpaceTotal(USER_DEFAULT_SPACE_TOTAL);

            List<String> identifiers = new ArrayList<>();
            if (item.getStr("account").contains("@")) {
                identifiers.add(item.getStr("account"));
            } else {
                identifiers.add(item.getStr("email"));
            }
            syncPublicUserBean.setIdentifiers(identifiers);
            String name;
            if (item.getStr("name").contains("@")) {
                name = item.getStr("name").split("@")[0];

            } else {
                name = item.getStr("name");
            }
            syncPublicUserBean.setName(name);

            // 1.创建用户
            CreateUserResult createUserResult = v2ClientHelper.createUser(syncPublicUserBean, ccWorkInitParam.getEnterpriseParams().getAdminUserId());

            if (!Objects.isNull(createUserResult) && !org.springframework.util.CollectionUtils.isEmpty(createUserResult.getIdentifiersAlreadyInvited())) {
                log.info("ccwork-saas identifiers_already_invited, identifiers info :{}", JSON.toJSONString(syncPublicUserBean.getIdentifiers()));
                User userInvited = userMapper.queryByEmail(createUserResult.getIdentifiersAlreadyInvited().get(0), Long.valueOf(ccWorkInitParam.getEnterpriseParams().getEnterpriseId()));

                if (!Objects.isNull(userInvited)) {
                    UserResult userResult = new UserResult();
                    userResult.setId(userInvited.getId());
                    List<UserResult> list = new ArrayList<>();
                    list.add(userResult);
                    createUserResult.setUsers(list);
                    log.info("cccwork-sass identifiers_already_invited, userResult info :{}", JSON.toJSONString(userResult));
                }
            }
            if (!Objects.isNull(createUserResult) && !org.springframework.util.CollectionUtils.isEmpty(createUserResult.getUsers())) {
                // 2.创建用户成功后，维护用户的映射关系
                PlatformUser platformUser = new PlatformUser();
                String userTicket = item.getStr("account");
                platformUser.setUserTicket(userTicket);
                platformUser.setPlatformId(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()));
                platformUser.setEnterpriseTicket(ccWorkInitParam.getCorpId());
                platformUser.setUserInnerId(userTicket);
                platformUser.setUserId(createUserResult.getUsers().get(0).getId());
                platformUser.setPlatformUserAvatar("");
                platformUser.setValueBox("{}");
                platformUserMapper.insert(platformUser);
                // 3.修改用户名称
                UserResult userResult = v2ClientHelper.editUser(syncPublicUserBean, ccWorkInitParam.getEnterpriseParams().getAdminUserId(), createUserResult.getUsers().get(0).getId());

                // 3.修改将用户添加入对应部门中
                String departmentId = ccWorkInitParam.getCorpId() + CommonConstants.SPLIT_UNDERLINE +item.getByPath("deptData.0").toString();
                if (StringUtils.isNotBlank(departmentId)) {
                    PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndDepartmentId(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()), departmentId);
                    // 将用户添加到对应的部门中

                    EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
                    long yfyDepartmentId = 0;
                    if (!Objects.isNull(platformDepartment)) {
                        List<Long> addUserIds = new ArrayList<>();
                        addUserIds.add(createUserResult.getUsers().get(0).getId());
                        editDepartmentUserBean.setAddUserIds(addUserIds);
                        yfyDepartmentId = platformDepartment.getYfyDepartmentId();
                    } else if (item.getJSONArray("deptData").isEmpty()) {
                        // 4. 获取企业的顶级部门id
                        Department rootDepartment = departmentMapper.queryRootDepartment(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getEnterpriseId()));
                        List<Long> addUserIds = new ArrayList<>();
                        addUserIds.add(createUserResult.getUsers().get(0).getId());
                        editDepartmentUserBean.setAddUserIds(addUserIds);
                        yfyDepartmentId = rootDepartment.getId();
                    }
                    EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, ccWorkInitParam.getEnterpriseParams().getAdminUserId(), yfyDepartmentId);
                    if (!Objects.isNull(editDepartmentResult) && !org.springframework.util.CollectionUtils.isEmpty(editDepartmentResult.getAddUsers())) {
                        log.info("ccwork-saas department add user success! user id :{}, department id :{}", userResult.getId(), yfyDepartmentId);
                    }
                }

            }
        }
    }

    private void eventDeleteUser(CcworkEvent event, CcWorkInitParam ccWorkInitParam) {
        List<String> accounts = event.getAccounts();

        List<PlatformUser>  platformUserList = platformUserMapper.queryByPlatformIdUserTicketIds(Long.parseLong(ccWorkInitParam.getEnterpriseParams().getPlatformId()), accounts);

        platformUserList.forEach(platformUser -> {
            // 设置文件接受者
            List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(platformUser.getUserId());
            DeleteUserBean deleteUserBean = new DeleteUserBean();
            if (!org.springframework.util.CollectionUtils.isEmpty(departmentsUsers)) {
                List<Long> yfyDepartmentIdList = departmentsUsers.stream().map(DepartmentsUsers::getDepartmentId).collect(Collectors.toList());
                List<Department> departments = departmentMapper.queryByIds(yfyDepartmentIdList);
                for (Department department : departments) {
                    if (department.getDirectorId() != 0) {
                        deleteUserBean.setUserReceivedItems(department.getDirectorId());
                        break;
                    }
                }
            } else {
                deleteUserBean.setUserReceivedItems(ccWorkInitParam.getEnterpriseParams().getAdminUserId());
            }

            // 删除用户
            v2ClientHelper.deleteUser(ccWorkInitParam.getEnterpriseParams().getAdminUserId(), platformUser.getUserId(), deleteUserBean);
            log.info("ccwork-saas event user_delete sync platformUsers {}  ", platformUser);
        });
    }

    private EnterpriseParams getEnterpriseParams(String enterpriseTicket, String userTicket, String accountType) {
        PlatformEnterprises platformEnterprises = platformEnterprisesService.queryByEnterpriseTicket(enterpriseTicket);
        if (platformEnterprises == null) {
            return null;
        }
        Enterprise enterprise = enterpriseService.getEnterpriseById(platformEnterprises.getEnterpriseId());
        EnterpriseParams enterpriseParams = new EnterpriseParams();
        if (StringUtils.isNotEmpty(userTicket)) {
            enterpriseParams.setClientId(getOPenClientId(enterprise.getId()));
            enterpriseParams.setIdentifier(userTicket);
        }
        enterpriseParams.setEnterpriseId(String.valueOf(enterprise.getId()));
        enterpriseParams.setPlatformId(String.valueOf(enterprise.getPlatformId()));
        if (accountType.equals(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET)) {
            enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_USER_TICKET);
        } else {
            enterpriseParams.setType(OpenApiConstants.IDENTIFIER_TYPE_SIMPLE_PHONE_OR_EMAIL);
        }
        enterpriseParams.setAdminUserId(enterprise.getAdminUserId());
        return enterpriseParams;
    }

    @Override
    public Boolean sendMessage(MessageParams params) {
        CcWorkClientHelper ccWorkClientHelper = new CcWorkClientHelper(params.getCcWorkInitParams());
        dropCcWorkClient();
        return ccWorkClientHelper.sendLinkMessage(getYfyMessage(params));
    }


    private void dropCcWorkClient() {
        ccWorkClientHelper = null;
    }

    private String getOPenClientId(long enterpriseId) {
        // 获取clientId
        JSONArray jSONArray = oauthClientHelper.getClientList(enterpriseId);
        if(!CollectionUtils.isEmpty(jSONArray)){
            for (Object o : jSONArray) {
                JSONObject clientObj = (JSONObject) o;
                if(clientObj.getBoolean("enabled")){
                    return clientObj.getString("client_id");
                }
            }
        }
        return null;
    }
}
