package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginEnterpriseInfoResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginTokenResponse;
import com.sync.common.entity.dto.YfyUser;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 登陆相关服务
 */
public interface PlatformLoginService {

    public String loginCheck(HttpServletRequest request, HttpServletResponse response) throws IOException;

    public Result<PlatformLoginResponse> internalLogin(HttpServletRequest request, HttpServletResponse response) throws Exception;

    public Result<String> logout(HttpServletRequest request, HttpServletResponse response) throws Exception;

    public Result<PlatformLoginEnterpriseInfoResponse> getEnterpriseInfo(String enterpriseId);

    public Result<YfyUser> getUserInfo(String token);

    public String serviceValidate(String ticket);

    public Result<PlatformLoginTokenResponse> getToken(String enterpriseId, String clientId, String clientSecret, String ticket);

    public Result<PlatformLoginResponse> needCaptcha(String username, String enterpriseId);
}
