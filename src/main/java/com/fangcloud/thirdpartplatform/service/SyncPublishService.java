package com.fangcloud.thirdpartplatform.service;


import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;

import java.util.List;

public interface SyncPublishService {

    /**
     * 同步公有云组织架构
     * @param enterpriseId
     */
    void syncOrganization(Integer enterpriseId, String syncType, String requestId);

    void syncBatchyCcworkOrganization();

    ExecuteResult syncPublic(String productId, List<Integer> configIds, Integer enterpriseId, Integer taskId, String password, String requestId);

    String syncStatus(Integer enterpriseId, String syncType, String requestId);
}
