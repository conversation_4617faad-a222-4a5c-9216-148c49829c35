package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.input.PlatformGlobalConfigParam;
import com.fangcloud.thirdpartplatform.entity.response.PlatformGlobalConfigResponse;


public interface GlobalConfigService {

    /**
     * 读取配置
     * @param enterpriseId
     * @return
     */
    PlatformGlobalConfigResponse get(Integer enterpriseId);

    /**
     * 插入
     * @param platformGlobalConfigParam
     * @return
     */
    void save(PlatformGlobalConfigParam platformGlobalConfigParam);
}
