package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.input.CcWorkInitParam;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;

public interface CcWorkSassService {

    /**
     * 钉钉获取登陆链接
     *
     * @param params
     * @return
     */
    String getAutoLoginUrl(CcWorkInitParam params);

    /**
     * 账号，组织同步回调
     * @param params
     * @return
     */
    Object syncCallBack(String params);

    /**
     * 钉钉发送消息
     *
     * @param params
     * @return
     */
    Boolean sendMessage(MessageParams params);
}
