package com.fangcloud.thirdpartplatform.service;

import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public interface ThirdpartyService {

    Result<Object> callback(ThirdpartyCallbackParams thirdpartyCallbackParams);

    Result<Object> call(ThirdpartyParams thirdpartyParams);

    Result<Object> invoke(ThirdpartyParams thirdpartyParams);

    Object invokePost(HttpServletRequest request, JSONObject jsonObject, HttpServletResponse response);

    Object invokeGet(HttpServletRequest request, HttpServletResponse response);
}
