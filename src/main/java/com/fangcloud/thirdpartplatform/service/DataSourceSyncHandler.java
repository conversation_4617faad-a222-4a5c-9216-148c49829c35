package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;

public interface DataSourceSyncHandler {

    ExecuteResult syncUser(PlatformSyncConfig platformSyncConfig, Integer taskId);

    ExecuteResult syncDepartment(PlatformSyncConfig platformSyncConfig, Integer taskId);


    ExecuteResult sync(PlatformSyncConfig platformSyncConfig, Integer taskId);

    String getDataSourceType();
}
