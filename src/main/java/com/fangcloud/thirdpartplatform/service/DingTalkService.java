package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.input.DingTalkInitParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;

public interface DingTalkService {

    /**
     * 钉钉获取登陆链接
     * @param params
     * @return
     */
    String getAutoLoginUrl(DingTalkInitParams params);

    /**
     * 钉钉发送消息
     * @param params
     * @return
     */
    Boolean sendMessage(MessageParams params);
}
