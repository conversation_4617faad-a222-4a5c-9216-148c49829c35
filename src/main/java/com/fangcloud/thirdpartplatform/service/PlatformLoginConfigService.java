package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.PlatformLoginConfigParams;
import com.fangcloud.thirdpartplatform.entity.response.PlatformLoginConfigResponse;

/**
 * 登陆相关接口
 */
public interface PlatformLoginConfigService {

    /**
     * 保存登陆配置
     * @param platformLoginConfigParams
     * @return
     */
    public Result<Boolean> saveConfig(PlatformLoginConfigParams platformLoginConfigParams);

    /**
     * 获取登陆配置
     * @param enterpriseId
     * @return
     */
    public Result<PlatformLoginConfigResponse> getConfig(Integer enterpriseId);
}
