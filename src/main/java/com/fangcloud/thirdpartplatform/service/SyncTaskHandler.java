package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.sync.common.entity.dto.YfyUser;

import java.util.List;

public interface SyncTaskHandler {

    ExecuteResult executeResult(PlatformSyncConfig platformSyncConfig, List<YfyUser> yfyUserList, Integer taskId);

    String getSyncTaskType();
}
