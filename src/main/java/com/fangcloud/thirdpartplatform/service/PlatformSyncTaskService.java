package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncTaskLogListParam;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncTaskRecordListParam;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncTaskLogResponse;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncTaskRecordResponse;

import java.util.List;

public interface PlatformSyncTaskService {

    List<PlatformSyncTaskRecordResponse> list(PlatformSyncTaskRecordListParam param);

    Integer countPlatformSyncTaskRecord(PlatformSyncTaskRecordListParam param);

    List<PlatformSyncTaskLogResponse> listLogs(PlatformSyncTaskLogListParam param);

    Integer countPlatformSyncTaskLog(PlatformSyncTaskLogListParam param);

    Integer batchInsertLogs (List<PlatformSyncTaskFailLogs> records);

    List<PlatformSyncTaskFailLogs> selectByTaskId (Integer syncTaskId, String customeId);

    Integer updateByPrimaryKeySelective(PlatformSyncTaskFailLogs record);

    void scheduledDeleteSyncTaskFailLogs(Integer enterpriseId);
}
