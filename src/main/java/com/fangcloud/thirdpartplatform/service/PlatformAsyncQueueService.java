package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue;

import java.util.List;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-06-01
 **/
public interface PlatformAsyncQueueService {

    Integer batchInsertQueue(List<PlatformAsyncQueue> platformAsynQueues);

    List<PlatformAsyncQueue> selectByTaskId(Integer taskId);

    List<PlatformAsyncQueue> selectByEnterpriseIdAndStatus(Integer enterpriseId,Integer status);

    int deleteByPrimaryKey(Integer id);

    int insert(PlatformAsyncQueue platformAsynQueue);

    int updateByPrimaryKeySelective(PlatformAsyncQueue platformAsynQueue);
}
