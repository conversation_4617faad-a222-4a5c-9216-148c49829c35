package com.fangcloud.thirdpartplatform.service.factory;

import com.fangcloud.thirdpartplatform.service.SyncTaskHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class SyncTaskHandlerFactory {
    private Map<String, SyncTaskHandler> factory = new HashMap<>();

    @Autowired
    public void init (List<SyncTaskHandler> handlers) {
        if (handlers == null) {
            return;
        }
        for (SyncTaskHandler handler : handlers) {
            factory.put(handler.getSyncTaskType(), handler);
        }
    }

    public SyncTaskHandler getHandler(String taskType) {
        return factory.get(taskType);
    }
}
