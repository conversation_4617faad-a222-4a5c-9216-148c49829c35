package com.fangcloud.thirdpartplatform.service.factory;

import com.fangcloud.thirdpartplatform.service.ApiExecuteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ApiExecuteServiceFactory {
    private Map<String, ApiExecuteService> factory = new HashMap<>();

    @Autowired
    public void init (List<ApiExecuteService> services) {
        if (services == null) {
            return;
        }
        for (ApiExecuteService service : services) {
            factory.put(service.getApiExecuteType(), service);
        }
    }

    public ApiExecuteService getHandler(String ApiExecuteType) {
        return factory.get(ApiExecuteType);
    }
}
