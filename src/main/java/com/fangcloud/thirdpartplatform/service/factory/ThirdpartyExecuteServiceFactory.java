package com.fangcloud.thirdpartplatform.service.factory;

import com.fangcloud.thirdpartplatform.service.ThirdpartyExecuteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ThirdpartyExecuteServiceFactory {
    private Map<String, ThirdpartyExecuteService> factory = new HashMap<>();

    @Autowired
    public void init (List<ThirdpartyExecuteService> services) {
        if (services == null) {
            return;
        }
        for (ThirdpartyExecuteService service : services) {
            factory.put(service.getThirdpartyExecuteType(), service);
        }
    }

    public ThirdpartyExecuteService getHandler(String thirdpartyExecuteType) {
        return factory.get(thirdpartyExecuteType);
    }
}
