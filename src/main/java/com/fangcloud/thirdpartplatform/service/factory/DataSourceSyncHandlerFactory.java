package com.fangcloud.thirdpartplatform.service.factory;

import com.fangcloud.thirdpartplatform.service.DataSourceSyncHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class DataSourceSyncHandlerFactory {
    private Map<String, DataSourceSyncHandler> factory = new HashMap<>();

    @Autowired
    public void init (List<DataSourceSyncHandler> handlers) {
        if (handlers == null) {
            return;
        }
        for (DataSourceSyncHandler handler : handlers) {
            factory.put(handler.getDataSourceType(), handler);
        }
    }

    public DataSourceSyncHandler getHandler(String sourceType) {
        return factory.get(sourceType);
    }
}
