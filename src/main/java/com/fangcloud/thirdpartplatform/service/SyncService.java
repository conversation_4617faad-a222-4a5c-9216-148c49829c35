package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.dto.ExecuteResult;
import com.fangcloud.thirdpartplatform.entity.dto.YfyDepartment;
import com.fangcloud.thirdpartplatform.entity.sync.CustomDepartment;
import com.fangcloud.thirdpartplatform.entity.sync.CustomUser;
import com.sync.common.entity.dto.YfyUser;

import java.util.List;

public interface SyncService {
    ExecuteResult syncUsers(List<CustomUser> customUserList, List<YfyUser> yfyUserList, Integer taskId, Enterprise enterprise, PlatformSyncConfig platformSyncConfig, Boolean isCreateDefaultPassword, String customPassword);
    ExecuteResult syncDepartments(List<CustomDepartment> customDepartmentList, List<YfyDepartment> yfyDepartmentList, Integer taskId, Enterprise enterprise, PlatformSyncConfig platformSyncConfig);
}
