package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.entity.input.WeixinWorkInitParams;

public interface WeixinWorkService {

    /**
     * 企业微信获取自动登陆链接
     * @param params
     * @return
     */
    String getAutoLoginUrl(WeixinWorkInitParams params);

    /**
     * 企业微信微信发送消息
     * @param params
     * @return
     */
    Boolean sendMessage(MessageParams params);
}
