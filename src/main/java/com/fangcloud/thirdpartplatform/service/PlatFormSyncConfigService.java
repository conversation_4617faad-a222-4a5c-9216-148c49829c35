package com.fangcloud.thirdpartplatform.service;

import com.fangcloud.thirdpartplatform.entity.common.Result;
import com.fangcloud.thirdpartplatform.entity.input.PlatformSyncConfigParams;
import com.fangcloud.thirdpartplatform.entity.response.PlatformSyncConfigResponse;

import java.util.List;

public interface PlatFormSyncConfigService {

    /**
     * 更新
     * @param platformSyncConfigParams
     * @return
     */
    boolean update(PlatformSyncConfigParams platformSyncConfigParams);

    /**
     * 插入
     * @param platformSyncConfigParams
     * @return
     */
    boolean save(PlatformSyncConfigParams platformSyncConfigParams);

    /**
     * 列表查询
     * @param enterpriseId
     * @return
     */
    List<PlatformSyncConfigResponse> list(Integer enterpriseId, Integer syncType);

    /**
     * 列表查询
     * @param id
     * @return
     */
    PlatformSyncConfigResponse get(Integer id);

    /**
     * 逻辑删除
     * @param id
     * @return
     */
    boolean deleted(Integer id);

    /**
     * 是否开启
     * @param id
     * @return
     */
    boolean open(Integer id, Boolean open);

    Result<Boolean> sync(Integer id);
}
