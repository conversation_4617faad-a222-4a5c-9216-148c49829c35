package com.fangcloud.thirdpartplatform.service;


import com.alibaba.fastjson.JSONObject;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public interface AiChatService {

    SseEmitter chatStream(HttpServletRequest request, HttpServletResponse response, JSONObject jSONObject);

    String chatAiToString(HttpServletRequest request, JSONObject jSONObject);

    void sendMsgToRobot(JSONObject jSONObject,String aiAnswer);
}
