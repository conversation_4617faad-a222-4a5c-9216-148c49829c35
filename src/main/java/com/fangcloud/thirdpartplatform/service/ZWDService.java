package com.fangcloud.thirdpartplatform.service;

import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.entity.input.AutoLoginParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface ZWDService {

    /**
     * 政务钉获取登陆链接
     * @param params
     * @return
     */
    JSONObject getAutoLoginUrl(AutoLoginParams params);

    /**
     * 政务钉发送消息（链接消息）
     * @param params
     * @return
     */
    boolean sendMessage(MessageParams params);

    /**
     * 政务钉根据员工code获取登陆链接
     * @return
     */
    void getAutoLoginUrlByEmployeeCode(String employeeCode, String enterpriseId, HttpServletResponse response) throws IOException;

    /**
     * 政务钉发送文本消息，调用审批平台接口
     * @param params
     * @return
     */
    boolean sendTextMessage(MessageParams params);
}
