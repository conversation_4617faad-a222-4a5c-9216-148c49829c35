package com.fangcloud.thirdpartplatform;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.ldap.LdapHealthIndicatorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

@ServletComponentScan(basePackages = "com.fangcloud.thirdpartplatform.listener")
@SpringBootApplication(exclude = {LdapHealthIndicatorAutoConfiguration.class, RabbitAutoConfiguration.class} )
@MapperScan("com.fangcloud.thirdpartplatform.db.dao")
@EnableAsync
public class ThirdPartPlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(ThirdPartPlatformApplication.class, args);
    }
}
