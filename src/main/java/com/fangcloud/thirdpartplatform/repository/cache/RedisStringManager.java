package com.fangcloud.thirdpartplatform.repository.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * @Author: shenyt
 */
@Service
@Slf4j
public class RedisStringManager {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 获取String
     */
    public String get(String key) {
        String value = stringRedisTemplate.opsForValue().get(key);
        log.info("redisStringManager get key : {} , value : {}",key,value);
        return value;
    }

    /**
     * 设置string值, 时间参考TimeConstant
     */
    public void set(String key, String value, Long timeout) {
        if(timeout <= 0L){
            stringRedisTemplate.opsForValue().set(key, value);
        }else {
            stringRedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.SECONDS);
        }
        log.info("redisStringManager set key : {} , value : {} , timeout : {}", key, value, timeout);
    }

    /**
     * 删除
     */
    public void delete(String key) {
        stringRedisTemplate.delete(key);
        log.info("redisStringManager delete key : {} ", key);
    }


    /**
     *
     * @param key
     * @param num
     * @return
     */
    public long increment(String key, long num){
        Long numResult = stringRedisTemplate.opsForValue().increment(key, num);
        log.info("redisStringManager increment key : {} , num : {} , numResult : {}", key, num , numResult);
        return numResult;
    }

}