package com.fangcloud.thirdpartplatform.config;

import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2022-06-20
 */
@Configuration
@ConditionalOnProperty("spring.rabbitmq.enable")
public class AmqpConfig {

    @Bean
    DirectExchange exchange() {
        return new DirectExchange(CommonConstants.JOB_QUEUES_EXCHANGE);
    }

    @Bean
    Queue thirdPartyCallJobQueue() {
        return QueueBuilder.durable(CommonConstants.THIRD_PARTY_CALL_QUEUE).build();
    }

    @Bean
    Binding solrAsyncBinding() {
        return BindingBuilder.bind(thirdPartyCallJobQueue()).to(exchange()).with(CommonConstants.THIRD_PARTY_CALL_QUEUE);
    }

    /**
     * 手动ack的ContainerFactory
     */
    @Bean
    @ConditionalOnClass
    public SimpleRabbitListenerContainerFactory manualAckContainerFactory(CachingConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        return factory;
    }
}
