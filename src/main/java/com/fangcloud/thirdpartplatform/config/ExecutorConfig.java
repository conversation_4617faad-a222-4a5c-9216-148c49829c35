package com.fangcloud.thirdpartplatform.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
* <p>Title: ExecutorConfig</p>
* <p>Description: 自定义线程池配置</p>
* <p>Copyright: Copyright (c) 2019</p>
* <p>Company: www.fangcloud.com</p>
* <AUTHOR>
* date 2019-09-26
* @version 1.0
*/
@Configuration
@EnableAsync
public class ExecutorConfig {

    @Value("${task.pool.core-pool-size}")
    private Integer corePoolSize;
    @Value("${task.pool.max-pool-size}")
    private Integer maxPoolSize;
    @Value("${task.pool.keep-alive-seconds}")
    private Integer keepAliveSeconds;
    @Value("${task.pool.queue-capacity}")
    private Integer queueCapacity;


    @Bean
    public Executor mySimpleExecutor()
    {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("SimpleExecutor-");
        executor.initialize();

        return executor;
    }

    @Bean
    public Executor myExecutor()
    {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(maxPoolSize);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setQueueCapacity(queueCapacity);
        executor.setThreadNamePrefix("myExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        return executor;
    }
}
