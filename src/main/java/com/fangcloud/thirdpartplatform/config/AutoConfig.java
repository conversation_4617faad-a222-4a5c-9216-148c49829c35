package com.fangcloud.thirdpartplatform.config;

import com.sync.common.service.CaptchaCheckImpl;
import com.sync.common.service.LoginServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AutoConfig {
    /**
     * 获取登入request里的用户信息
     */
    @Bean
    public LoginServiceImpl loginServiceImpl() {
        return new LoginServiceImpl();
    }

    /**
     * 登入验证码相关类
     */
    @Bean
    public CaptchaCheckImpl captchaCheckImpl() {
        return new CaptchaCheckImpl();
    }
}
