package com.fangcloud.thirdpartplatform.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Component
public class CustomNacosConfig {

    @NacosValue(value = "${custom.v2.secret}", autoRefreshed = true)
    private String v2Secret;

    @NacosValue(value = "${custom.v2.version}", autoRefreshed = true)
    private String v2Version;

    @NacosValue(value = "${custom.v2.service_id}", autoRefreshed = true)
    private Integer v2ServiceId;

    @NacosValue(value = "${custom.v2.default_password}", autoRefreshed = true)
    private String  defaultPassword;

    @NacosValue(value = "${custom.v2.uri.url1}", autoRefreshed = true)
    private String v2UriUrl1;

    @NacosValue(value = "${custom.v2.uri.url2}", autoRefreshed = true)
    private String v2UriUrl2;

    @NacosValue(value = "${custom.v2.uri.url3}", autoRefreshed = true)
    private String v2UriUrl3;

    @NacosValue(value = "${custom.v2.uri.url4}", autoRefreshed = true)
    private String v2UriUrl4;

    @NacosValue(value = "${custom.v2.uri.url5}", autoRefreshed = true)
    private String v2UriUrl5;

    @NacosValue(value = "${custom.v2.uri.url6}", autoRefreshed = true)
    private String v2UriUrl6;

    @NacosValue(value = "${custom.v2.uri.url7}", autoRefreshed = true)
    private String v2UriUrl7;

    @NacosValue(value = "${custom.v2.uri.url8}", autoRefreshed = true)
    private String v2UriUrl8;

    @NacosValue(value = "${custom.v2.uri.url9}", autoRefreshed = true)
    private String v2UriUrl9;

    @NacosValue(value = "${custom.v2.uri.url10}", autoRefreshed = true)
    private String v2UriUrl10;

    @NacosValue(value = "${custom.v2.uri.url11}", autoRefreshed = true)
    private String v2UriUrl11;

    @NacosValue(value = "${custom.v2.uri.url12}", autoRefreshed = true)
    private String v2UriUrl12;

    @NacosValue(value = "${custom.v2.uri.url13}", autoRefreshed = true)
    private String v2UriUrl13;

    @NacosValue(value = "${custom.v2.uri.url14}", autoRefreshed = true)
    private String v2UriUrl14;

    @NacosValue(value = "${custom.v2.uri.url15}", autoRefreshed = true)
    private String v2UriUrl15;

    @NacosValue(value = "${custom.v2.uri.url16}", autoRefreshed = true)
    private String v2UriUrl16;

    @NacosValue(value = "${custom.v2.uri.url17}", autoRefreshed = true)
    private String v2UriUrl17;

    @NacosValue(value = "${custom.v2.uri.url18}", autoRefreshed = true)
    private String v2UriUrl18;

    @NacosValue(value = "${custom.v2.uri.url19}", autoRefreshed = true)
    private String v2UriUrl19;

    @NacosValue(value = "${custom.v2.uri.url20}", autoRefreshed = true)
    private String v2UriUrl20;
    
    @NacosValue(value = "${custom.v2.host.v2}", autoRefreshed = true)
    private String v2Host;

    @NacosValue(value = "${custom.v2.host.v2url}", autoRefreshed = true)
    private String v2HostUrl;

    @NacosValue(value = "${custom.simba.host.url}", autoRefreshed = true)
    private String simbaHostUrl;

    @NacosValue(value = "${custom.ark.host.url}", autoRefreshed = true)
    private String arkHostUrl;

    @NacosValue(value = "${custom.ark.uri.url1}", autoRefreshed = true)
    private String arkUriUrl1;

    @NacosValue(value = "${custom.open.secret}", autoRefreshed = true)
    private String openSecret;

    @Value("${custom.open.version}")
    private String openVersion;

    @NacosValue(value = "${custom.open.service_id}", autoRefreshed = true)
    private Integer openServiceId;

    @NacosValue(value = "${custom.open.uri.get_login_url}", autoRefreshed = true)
    private String openUriGetLoginUrl;

    @NacosValue(value = "${custom.open.uri.get_user_info}", autoRefreshed = true)
    private String openUriGetUserInfo;

    @NacosValue(value = "${custom.open.uri.sync_users}", autoRefreshed = true)
    private String openUriSync_Users;

    @NacosValue(value = "${custom.open.host.openurl}", autoRefreshed = true)
    private String  openHostUrl;

    @NacosValue(value = "${custom.aiapi.host.url}", autoRefreshed = true)
    private String  aiapiHostUrl;

    @NacosValue(value = "${custom.aiapi.uri.knowledge_chat_streamV1}", autoRefreshed = true)
    private String  aiapiKnowledgeChatStreamV1;

    @NacosValue(value = "${custom.aiapi.uri.get_knowledge_chat_token}", autoRefreshed = true)
    private String  aiapiGetKnowledgeChatToken;

    @NacosValue(value = "${custom.aiapi.uri.assistant_chat_chatStream}", autoRefreshed = true)
    private String  aiapiAssistantChatChatStream;

    @NacosValue(value = "${custom.aiapi.uri.ai_search}", autoRefreshed = true)
    private String  aiapiAiSearch;

    @NacosValue(value = "${custom.aiapi.uri.ai_file_chat}", autoRefreshed = true)
    private String  aiapiAiFileChat;

    @NacosValue(value = "${custom.aiapi.uri.ai_file_extract}", autoRefreshed = true)
    private String  aiapiAiFileExtract;

    @NacosValue(value = "${custom.aiapi.knowledgeGptIds}", autoRefreshed = true)
    private String  aiapiKnowedgeGptIds;

    @NacosValue(value = "${custom.open.host.open}", autoRefreshed = true)
    private String  hostOpen;

    @NacosValue(value = "${custom.base.url}", autoRefreshed = true)
    private String baseUrl;

    @NacosValue(value = "${custom.base.domain}", autoRefreshed = true)
    private String  baseDomain;

    @NacosValue(value = "${custom.http_client_type}", autoRefreshed = true)
    private String  httpClientType;

    @NacosValue(value = "${custom.dingding.debug}", autoRefreshed = true)
    private Boolean  customDingdingDebug;

    @NacosValue(value = "${custom.dingding.msg_token}", autoRefreshed = true)
    private String  customDingDingMsgToken;

    @NacosValue(value = "${custom.weixin.top_dept}", autoRefreshed = true)
    private Integer  customWeiXinTopDept;

    @NacosValue(value = "${custom.tuitui.host}", autoRefreshed = true)
    private String  customTuiHost;

    @NacosValue(value = "${custom.tuitui.sso_appid}", autoRefreshed = true)
    private String  customTuiSsoAppId;

    @NacosValue(value = "${custom.tuitui.secret_key}", autoRefreshed = true)
    private String  customTuiSecretKey;

    @NacosValue(value = "${custom.tuitui.message_host}", autoRefreshed = true)
    private String  customTuiMessageHost;

    @NacosValue(value = "${custom.tuitui.message_appid}", autoRefreshed = true)
    private String  customTuiMessageAppId;

    @NacosValue(value = "${custom.tuitui.message_secret}", autoRefreshed = true)
    private String  customTuiMessageSecret;

    @NacosValue(value = "${custom.public}", autoRefreshed = true)
    private Boolean  customPublic;

    @NacosValue(value = "${custom.sync_user.set_department_id_null_enable}", autoRefreshed = true)
    private Boolean  customSyncUserSetDepartmentIdNullEnable;

    @NacosValue(value = "${custom.sync_user.set_user_active_close}", autoRefreshed = true)
    private Boolean  customSyncUserSetActiveClose;

    @NacosValue(value = "${custom.sync_user.user_name_suffix}", autoRefreshed = true)
    private String  customUserNameSuffix;

    @NacosValue(value = "${custom.sync_user.disabled_user_name_suffix}", autoRefreshed = true)
    private String  customDisabledUserNameSuffix;

    @NacosValue(value = "${custom.sync_user.set_disabled_user_department_id_is_null}", autoRefreshed = true)
    private Boolean  customSetDisabledUserDepartmentIsNull;

    @NacosValue(value = "${custom.sync_user.set_ldap_check_sasl}", autoRefreshed = true)
    private Boolean customSyncUserSetLdapCheckSasl;

    
    @NacosValue(value = "${custom.oauth.uri.get_clients}", autoRefreshed = true)
    private String oauthGetClients;

    @NacosValue(value = "${custom.oauth.host.url}", autoRefreshed = true)
    private String oauthHostUrl;

    @NacosValue(value = "${custom.oauth.secret}", autoRefreshed = true)
    private String oauthSecret;

    @Value("${custom.oauth.version}")
    private String oauthVersion;

    @NacosValue(value = "${custom.oauth.service_id}", autoRefreshed = true)
    private Integer oauthServiceId;
}
