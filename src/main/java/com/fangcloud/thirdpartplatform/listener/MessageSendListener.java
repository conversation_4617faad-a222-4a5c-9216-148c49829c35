package com.fangcloud.thirdpartplatform.listener;

import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.service.MqService;
import com.google.common.base.Charsets;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@Slf4j
public class MessageSendListener {

    @Autowired
    private MqService mqService;


    @RabbitListener(queues = CommonConstants.THIRD_PARTY_CALL_QUEUE, concurrency = "50")
    @Async
    public void smsSend(@Payload Message messageRaw, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag, Channel channel)  {
        MDC.put("uuid", UUID.randomUUID().toString());
        log.info("start mq，queues: third_party_call_queue");
        String messageStr = new String(messageRaw.getBody(), Charsets.UTF_8);
        log.info("mq info :{}", messageStr);

        try {
            mqService.thirdPartyCall(messageStr);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("thirdPartyCall error :{}", e.getMessage());
        }

        log.info("end mq，queues: third_party_call_queue");
        MDC.remove("uuid");
    }

}
