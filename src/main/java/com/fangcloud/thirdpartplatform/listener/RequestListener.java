package com.fangcloud.thirdpartplatform.listener;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.util.StringUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletRequestEvent;
import javax.servlet.ServletRequestListener;
import javax.servlet.annotation.WebListener;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.UUID;

@Slf4j
@WebListener
public class RequestListener implements ServletRequestListener {

    @Override
    public void requestInitialized(ServletRequestEvent sre) {
        HttpServletRequest httpServletRequest = (HttpServletRequest)sre.getServletRequest();
        String request_id = httpServletRequest.getHeader("platform-request-id");
        if(StringUtils.isEmpty(request_id)){
            MDC.put("uuid", UUID.randomUUID().toString());
        }else{
            MDC.put("uuid", request_id );
        }

        log.info("Set up request id.");

    }

    @Override
    public void requestDestroyed(ServletRequestEvent sre) {

        log.info("Remove request id.");
        MDC.remove("uuid");
    }
}
