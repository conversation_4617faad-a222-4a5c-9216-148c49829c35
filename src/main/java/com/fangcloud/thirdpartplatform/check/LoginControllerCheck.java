package com.fangcloud.thirdpartplatform.check;

import com.fangcloud.thirdpartplatform.entity.input.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Objects;

/**
 * 登陆参数检验
 */
@Component
public class LoginControllerCheck {

    /**
     * 校验钉钉初始化参数
     * @param dingTalkInitParams
     */
    public void checkDingTalkInitParams(DingTalkInitParams dingTalkInitParams){
        Assert.isTrue(!Objects.isNull(dingTalkInitParams),"dingTalkInitParams is not null!");
        Assert.isTrue(!org.apache.commons.lang3.StringUtils.isAllBlank(dingTalkInitParams.getCode(), dingTalkInitParams.getAuthCode()),"code or auth_code is not null!");
        Assert.isTrue(!StringUtils.isEmpty(dingTalkInitParams.getAppId()),"appId is not null!");
        Assert.isTrue(!StringUtils.isEmpty(dingTalkInitParams.getAppSecret()),"appSecret is not null!");
        Assert.isTrue(!StringUtils.isEmpty(dingTalkInitParams.getAgentId()),"agentId is not null!");
        Assert.isTrue(!StringUtils.isEmpty(dingTalkInitParams.getCorpId()),"corpId is not null!");
        Assert.isTrue(!StringUtils.isEmpty(dingTalkInitParams.getCorpSecret()),"corpSecret is not null!");
        Assert.isTrue(!StringUtils.isEmpty(dingTalkInitParams.getLoginType()),"loginType is not null!");
        Assert.isTrue(!Objects.isNull(dingTalkInitParams.getSyncUserFlag()),"syncUserFlag is not null!");
        checkEnterpriseParamss(dingTalkInitParams.getEnterpriseParams());
    }

    /**
     * 校验企业微信初始化参数
     * @param weixinWorkInitParams
     */
    public void checkWeixinWorkInitParams(WeixinWorkInitParams weixinWorkInitParams){
        Assert.isTrue(!Objects.isNull(weixinWorkInitParams),"weixinWorkInitParams is not null!");
        Assert.isTrue(!StringUtils.isEmpty(weixinWorkInitParams.getCode()),"code is not null!");
        Assert.isTrue(!StringUtils.isEmpty(weixinWorkInitParams.getCorpId()),"corpId is not null!");
        Assert.isTrue(!StringUtils.isEmpty(weixinWorkInitParams.getCorpSecret()),"corpSecret is not null!");
        Assert.isTrue(!StringUtils.isEmpty(weixinWorkInitParams.getAgentId()),"agentId is not null!");
        Assert.isTrue(!StringUtils.isEmpty(weixinWorkInitParams.getLoginType()),"loginType is not null!");
        Assert.isTrue(!Objects.isNull(weixinWorkInitParams.getSyncUserFlag()),"syncUserFlag is not null!");
        checkEnterpriseParamss(weixinWorkInitParams.getEnterpriseParams());
    }

    /**
     * 校验飞书初始化参数
     * @param feiShuInitParams
     */
    public void checkFeiShuInitParams(FeiShuInitParams feiShuInitParams){
        Assert.isTrue(!Objects.isNull(feiShuInitParams),"feiShuInitParams is not null!");
        Assert.isTrue(!StringUtils.isEmpty(feiShuInitParams.getAppId()),"corpId is not null!");
        Assert.isTrue(!StringUtils.isEmpty(feiShuInitParams.getAppSecret()),"corpSecret is not null!");
        Assert.isTrue(!StringUtils.isEmpty(feiShuInitParams.getLoginType()),"loginType is not null!");
        Assert.isTrue(!Objects.isNull(feiShuInitParams.getSyncUserFlag()),"syncUserFlag is not null!");
        checkEnterpriseParamss(feiShuInitParams.getEnterpriseParams());
    }
    /**
     * 校验自动登录参数
     * @param params
     */
    public void checkAutoLoginParams(AutoLoginParams params){
        Assert.isTrue(!StringUtils.isEmpty(params.getCode()),"code is not null!");
        Assert.isTrue(!StringUtils.isEmpty(params.getEnterpriseId()),"enterpriseId is not null!");
    }

    /**
     * 校验企业信息
     * @param enterpriseParams
     */
    public void checkEnterpriseParamss(EnterpriseParams enterpriseParams){

        Assert.isTrue(!Objects.isNull(enterpriseParams),"enterpriseParams is not null !");
        Assert.isTrue(!StringUtils.isEmpty(enterpriseParams.getEnterpriseId()),"enterpriseId is not null !");
        Assert.isTrue(!StringUtils.isEmpty(enterpriseParams.getPlatformId()),"platformId is not null !");

    }

    /**
     * 判断request host是否跟目标url host一致;不一致将替换目标url的host;兼容单域名内外网场景
     */
    public String checkTargetHost(HttpServletRequest request, String url) throws MalformedURLException {
        String requestUrl = getRequestUrl(request);
        String requestHost = getHostFromUrl(requestUrl);
        URL targetURL = new URL(url);
        String targetHost = targetURL.getAuthority();
        if (!requestHost.equalsIgnoreCase(targetHost)) {
            url = url.replace(targetHost, requestHost);
        }
        return url;
    }

    /**
     * 校验推推初始化参数
     * @param ccWorkInitParam
     */
    public void checkCcWorkInitParams(CcWorkInitParam ccWorkInitParam){
        Assert.isTrue(!Objects.isNull(ccWorkInitParam),"ccWorkInitParam is not null!");
    }

    /**
     * 校验推推免登参数
     * @param ccWorkInitParam
     */
    public void checkCcWorkAutoLoginParams(CcWorkInitParam ccWorkInitParam){
        Assert.isTrue(!Objects.isNull(ccWorkInitParam),"ccWorkInitParam is not null!");
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(ccWorkInitParam.getEncrypt()),"encrypt is not null!");
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(ccWorkInitParam.getNonce()),"nonce is not null!");
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(ccWorkInitParam.getTimeStamp()),"timestamp is not null!");
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(ccWorkInitParam.getMsgSignature()),"msgSignature is not null!");
    }

    private String getRequestUrl(HttpServletRequest request) {
        StringBuffer url = request.getRequestURL();
        String queryString = request.getQueryString();
        if (queryString != null) {
            url.append('?').append(queryString);
        }
        return url.toString();
    }

    private String getHostFromUrl(String url) {
        try {
            URL parsedUrl = new URL(url);
            return parsedUrl.getAuthority();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static void checkAutoLoginByEmployeeCodeParams(String employeeCode, String enterpriseId) {
        Assert.isTrue(!StringUtils.isEmpty(enterpriseId),"enterpriseId is not null!");
        Assert.isTrue(!StringUtils.isEmpty(employeeCode),"employeeCode is not null!");
    }

    public void checkFeiShuAutoLoginParams(FeiShuInitParams params) {
        Assert.isTrue(!Objects.isNull(params),"FeiShuInitParams is not null!");
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(params.getCode()),"code is not null!");
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(params.getAppId()),"appId is not null!");
        Assert.isTrue(org.apache.commons.lang3.StringUtils.isNotBlank(params.getAppSecret()),"appSecret is not null!");
    }
}
