package com.fangcloud.thirdpartplatform.check;

import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallTypeEnum;
import com.fangcloud.thirdpartplatform.constant.thirdparty.ThirdpartyCallbackTypeEnum;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyCallbackParams;
import com.fangcloud.thirdpartplatform.entity.input.ThirdpartyParams;
import com.fangcloud.thirdpartplatform.utils.Md5Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * 对接第三方
 */
@Component
public class ThirdpartyControllerCheck {

    static long TWO_MINUTE_MILLIS = 2 * 60 * 1000L;



    public void checkCallParams(ThirdpartyParams thirdpartyParams){
        Assert.isTrue(!Objects.isNull(thirdpartyParams),"callbackParams is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(thirdpartyParams.getType()),"type is not null !");
        // 校验请求类型
        ThirdpartyCallTypeEnum callType = ThirdpartyCallTypeEnum.getByDesc(thirdpartyParams.getType());
        Assert.isTrue(!Objects.isNull(callType),"callType is wrong !");
    }

    /**
     * 校验回调参数
     * @param thirdpartyCallbackParams
     */
    public void checkCallbackParams(ThirdpartyCallbackParams thirdpartyCallbackParams) {

        Assert.isTrue(!Objects.isNull(thirdpartyCallbackParams),"callbackParams is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(thirdpartyCallbackParams.getType()),"type is not null !");
        // 校验回调类型
        ThirdpartyCallbackTypeEnum callbackType = ThirdpartyCallbackTypeEnum.getByDesc(thirdpartyCallbackParams.getType());
        Assert.isTrue(!Objects.isNull(callbackType),"callbackType is wrong !");

        Assert.isTrue(StringUtils.isNotEmpty(thirdpartyCallbackParams.getTimestamp()),"timestamp is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(thirdpartyCallbackParams.getSign()),"sign is not null !");

        // 校验sign
        checkSign(thirdpartyCallbackParams);

    }

    /**
     * 校验sign
     * @param thirdpartyCallbackParams
     */
    private void checkSign(ThirdpartyCallbackParams thirdpartyCallbackParams) {

        String md5OfStr = Md5Utils.getMD5OfStr(thirdpartyCallbackParams.getType() + thirdpartyCallbackParams.getTimestamp());

        Assert.isTrue(thirdpartyCallbackParams.getSign().equals(md5OfStr),"sign is wrong !");

        long differenceMillis = System.currentTimeMillis() - Long.parseLong(thirdpartyCallbackParams.getTimestamp());

        Assert.isTrue(!(differenceMillis < -TWO_MINUTE_MILLIS || differenceMillis > TWO_MINUTE_MILLIS),"timestamp is wrong !");
    }
}
