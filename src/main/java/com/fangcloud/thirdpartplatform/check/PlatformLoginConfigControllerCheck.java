package com.fangcloud.thirdpartplatform.check;

import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.login.LoginSourceTypeEnum;
import com.fangcloud.thirdpartplatform.db.dao.EnterpriseMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformLoginConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.Enterprise;
import com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.entity.input.PlatformLoginConfigParams;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 登陆参数检验
 */
@Component
public class PlatformLoginConfigControllerCheck {

    @Resource
    private EnterpriseMapper enterpriseMapper;

    @Resource
    private PlatformLoginConfigMapper platformLoginConfigMapper;

    /**
     * 校验企业id
     * @param enterpriseId
     */
    public void checkEnterpriseId(Integer enterpriseId){
        Enterprise enterprise = enterpriseMapper.queryById(enterpriseId);
        Assert.isTrue(!Objects.isNull(enterprise),"enterprise is not exist !");
    }
    /**
     * 校验企业登陆配置信息
     * @param platformLoginConfigParams
     */
    public void checkPlatformLoginConfig(PlatformLoginConfigParams platformLoginConfigParams) {
        Assert.isTrue(!Objects.isNull(platformLoginConfigParams),"platformLoginConfig is not null !");

        Assert.isTrue(!Objects.isNull(platformLoginConfigParams.getId()),"id is not null !");
        checkEnterpriseId(platformLoginConfigParams.getEnterpriseId());

        String sourceType = platformLoginConfigParams.getLoginSourceType();
        Assert.isTrue(!Objects.isNull(sourceType),"loginSourceType is not null !");
        String[] sourceTypeList = sourceType.split(",");
        for (int i= 0; i < sourceTypeList.length; i++){
            Assert.isTrue(!Objects.isNull(LoginSourceTypeEnum.getByDesc(sourceTypeList[i])),"loginSourceType is not exist !");
        }

        PlatformLoginConfigValueBox platformLoginConfigValueBox = platformLoginConfigParams.getPlatformLoginConfigValueBox();
        Assert.isTrue(!Objects.isNull(platformLoginConfigValueBox),"platformLoginConfigValueBox is not exist !");

        if(sourceType.contains(LoginSourceTypeEnum.LDAP.getDesc())){
            checkLoginSourceLDAPConfig(platformLoginConfigValueBox.getLoginSourceLDAPConfigDto());
        }else if(sourceType.contains(LoginSourceTypeEnum.OAUTH2.getDesc())){
            checkLoginSourceOAUTH2Config(platformLoginConfigValueBox.getLoginSourceOAUTH2ConfigDto());
        }else if(sourceType.contains(LoginSourceTypeEnum.WEIXIN.getDesc())){
            checkLoginThirdWeiXinConfig(platformLoginConfigValueBox.getLoginThirdWeiXinConfigDto());
        }else if(sourceType.contains(LoginSourceTypeEnum.DINGTALK.getDesc())){
            checkLoginThirdDingTalkConfig(platformLoginConfigValueBox.getLoginThirdDingTalkConfigDto());
        }else if(sourceType.contains(LoginSourceTypeEnum.CODE_SCRIPT.getDesc())){
            checkLoginSourceCodeScriptConfig(platformLoginConfigValueBox.getLoginSourceCodeScriptConfigDto());
        }

        // 校验登陆页面配置
        checkLoginPageConfig(platformLoginConfigValueBox.getLoginPageConfigDto());

        // 校验登陆验证配置
        checkLoginValidateConfig(platformLoginConfigValueBox.getLoginValidateConfigDto());


        // 获取数据库的client信息，不使用前端传入的数据
        PlatformLoginConfig platformLoginConfigOriginal = platformLoginConfigMapper.selectByPrimaryKey(platformLoginConfigParams.getId());
        PlatformLoginConfigValueBox platformLoginConfigValueBoxOriginal = JSONObject.parseObject(platformLoginConfigOriginal.getValueBox(), PlatformLoginConfigValueBox.class);

        platformLoginConfigValueBox.setLoginSecretConfigDto(platformLoginConfigValueBoxOriginal.getLoginSecretConfigDto());

        platformLoginConfigParams.setPlatformLoginConfigValueBox(platformLoginConfigValueBox);

    }

    /**
     * 校验OAUTH2类型数据源配置
     * @param loginSourceOAUTH2ConfigDto
     */
    private void checkLoginSourceOAUTH2Config(LoginSourceOAUTH2ConfigDto loginSourceOAUTH2ConfigDto) {
        Assert.isTrue(!Objects.isNull(loginSourceOAUTH2ConfigDto),"loginSourceOAUTH2Config is not null !");
        checkApiConfig(loginSourceOAUTH2ConfigDto.getOauth2Config());
        checkApiConfig(loginSourceOAUTH2ConfigDto.getApiConfig());
        // 校验api的resultPath，这个是必填的
        Assert.isTrue(StringUtils.isNotEmpty(loginSourceOAUTH2ConfigDto.getApiConfig().getResultPath()),"apiConfig resultPath is not null !");
    }

    /**
     * 校验WeiXin类型数据源配置
     * @param loginThirdWeiXinConfigDto
     */
    private void checkLoginThirdWeiXinConfig(LoginThirdWeiXinConfigDto loginThirdWeiXinConfigDto) {
        Assert.isTrue(!Objects.isNull(loginThirdWeiXinConfigDto),"loginThirdWeiXinConfig is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(loginThirdWeiXinConfigDto.getSsoUrl()),"ssoUrl is not null !");
    }

    /**
     * 校验DingTalk类型数据源配置
     * @param loginThirdDingTalkConfigDto
     */
    private void checkLoginThirdDingTalkConfig(LoginThirdDingTalkConfigDto loginThirdDingTalkConfigDto) {
        Assert.isTrue(!Objects.isNull(loginThirdDingTalkConfigDto),"loginThirdDingTalkConfig is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(loginThirdDingTalkConfigDto.getSsoUrl()),"ssoUrl  is not null !");
    }

    /**
     * 校验code_script类型数据源配置
     * @param loginSourceCodeScriptConfigDto
     */
    private void checkLoginSourceCodeScriptConfig(LoginSourceCodeScriptConfigDto loginSourceCodeScriptConfigDto) {
        Assert.isTrue(!Objects.isNull(loginSourceCodeScriptConfigDto),"loginSourceCodeScriptConfigDto is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(loginSourceCodeScriptConfigDto.getCodeScript()),"codeScript  is not null !");
    }

    /**
     * 校验Api配置
     * @param apiConfig
     */
    private void checkApiConfig(APIConfigValueBox.APIConfig apiConfig) {
        Assert.isTrue(!Objects.isNull(apiConfig),"apiConfig is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(apiConfig.getApi()),"apiConfig api is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(apiConfig.getRequestMethod()),"apiConfig requestMethod is not null !");


        List<APIParamConfigDto> apiParamConfigDtoList = apiConfig.getParamConfig();
        if(!CollectionUtils.isEmpty(apiParamConfigDtoList)){
            for (APIParamConfigDto apiParamConfigDto : apiParamConfigDtoList) {
                checkAPIParamConfigDto(apiParamConfigDto);
            }
        }

        List<APIResultConfigDto> apiResultConfigDtoList = apiConfig.getResultConfig();
        if(!CollectionUtils.isEmpty(apiResultConfigDtoList)){
            for (APIResultConfigDto apiResultConfigDto : apiResultConfigDtoList) {
                checkAPIResultConfigDto(apiResultConfigDto);
            }
        }


    }

    private void checkAPIResultConfigDto(APIResultConfigDto apiResultConfigDto) {
        Assert.isTrue(!Objects.isNull(apiResultConfigDto),"apiResultConfig is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(apiResultConfigDto.getName()),"apiResultConfig name is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(apiResultConfigDto.getMappingName()),"apiResultConfig mappingName is not null !");

    }

    private void checkAPIParamConfigDto(APIParamConfigDto apiParamConfigDto) {
        Assert.isTrue(!Objects.isNull(apiParamConfigDto),"apiParamConfig is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(apiParamConfigDto.getName()),"apiParamConfig name is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(apiParamConfigDto.getParamWay()),"apiParamConfig paramWay is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(apiParamConfigDto.getDataType()),"apiParamConfig dataType is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(apiParamConfigDto.getValue()),"apiParamConfig value is not null !");

    }

    /**
     * 校验LDAP类型数据源配置
     * @param loginSourceLDAPConfigDto
     */
    private void checkLoginSourceLDAPConfig(LoginSourceLDAPConfigDto loginSourceLDAPConfigDto) {
        Assert.isTrue(!Objects.isNull(loginSourceLDAPConfigDto),"loginSourceLDAPConfig is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(loginSourceLDAPConfigDto.getUserName()),"loginSourceLDAPConfig userName is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(loginSourceLDAPConfigDto.getPassword()),"loginSourceLDAPConfig password is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(loginSourceLDAPConfigDto.getUrl()),"loginSourceLDAPConfig url is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(loginSourceLDAPConfigDto.getBaseOu()),"loginSourceLDAPConfig baseOu is not null !");
    }


    /**
     * 校验登陆页面配置
     * @param loginPageConfigDto
     */
    private void checkLoginPageConfig(LoginPageConfigDto loginPageConfigDto) {
        Assert.isTrue(!Objects.isNull(loginPageConfigDto),"loginPageConfig is not null !");

        Assert.isTrue(StringUtils.isNotEmpty(loginPageConfigDto.getTitle()),"title is not null !");
//        Assert.isTrue(StringUtils.isNotEmpty(loginPageConfigDto.getLogoUrl()),"logoUrl is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(loginPageConfigDto.getClientBaseUrl()),"clientBaseUrl is not null !");
//        Assert.isTrue(StringUtils.isNotEmpty(loginPageConfigDto.getAccountPrompt()),"accountPrompt is not null !");

    }


    /**
     * 校验登陆验证配置
     * @param loginValidateConfigDto
     */
    private void checkLoginValidateConfig(LoginValidateConfigDto loginValidateConfigDto) {

        Assert.isTrue(!Objects.isNull(loginValidateConfigDto),"loginValidateConfig is not null !");
        Assert.isTrue(!Objects.isNull(loginValidateConfigDto.getNeedImgCaptcha()),"needImgCaptcha is not null !");
    }


}
