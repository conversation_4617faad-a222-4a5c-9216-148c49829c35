package com.fangcloud.thirdpartplatform.check;

import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.Objects;

/**
 * 发送消息参数校验
 */
@Component
@Slf4j
public class MessageControllerCheck {

    /**
     * 校验消息参数
     * @param messageParams
     */
    public void checkMessageParams(MessageParams messageParams){
        Assert.isTrue(!Objects.isNull(messageParams),"messageParams is not null!");
        Assert.isTrue(!StringUtils.isEmpty(messageParams.getReceivers()),"receivers is not null!");
        Assert.isTrue(!StringUtils.isEmpty(messageParams.getType()),"type is not null!");
        Assert.isTrue(!StringUtils.isEmpty(messageParams.getContent()),"content is not null!");
        Assert.isTrue(!StringUtils.isEmpty(messageParams.getTitle()),"title is not null!");
        Assert.isTrue(!StringUtils.isEmpty(messageParams.getWebUrl()),"webUrl is not null!");
        Assert.isTrue(!StringUtils.isEmpty(messageParams.getH5Url()),"h5Url is not null!");
        Assert.isTrue(!StringUtils.isEmpty(messageParams.getCustomerId()),"customerId is not null!");
        Assert.isTrue(!StringUtils.isEmpty(messageParams.getEnterpriseId()),"enterpriseId is not null!");
    }
}
