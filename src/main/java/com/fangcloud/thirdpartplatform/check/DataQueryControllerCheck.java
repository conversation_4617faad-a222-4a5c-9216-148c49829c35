package com.fangcloud.thirdpartplatform.check;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.Objects;

/**
 * 登陆参数检验
 */
@Component
public class DataQueryControllerCheck {
    public void checkCustomQuery(String productId, Integer configId, String params) {
        Assert.isTrue(StringUtils.isNotBlank(productId),"productId is not null !");
        Assert.isTrue(!Objects.isNull(configId),"configId is not null !");
    }
}
