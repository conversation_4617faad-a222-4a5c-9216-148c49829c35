package com.fangcloud.thirdpartplatform.check;

import com.fangcloud.thirdpartplatform.db.dao.PlatformLoginConfigMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 登陆参数检验
 */
@Component
public class PlatformLoginControllerCheck {

    @Resource
    private PlatformLoginConfigControllerCheck PlatformLoginConfigControllerCheck;

    @Resource
    private PlatformLoginConfigMapper platformLoginConfigMapper;

    /**
     * 登陆入参校验
     * @param request
     */
    public void checkInternalLogin(HttpServletRequest request) {
        String service = request.getParameter("service");
        String secureZone = request.getParameter("secure_zone");
        String enterpriseIdEncode = request.getParameter("enterprise_id");
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);

        Assert.isTrue(StringUtils.isNotEmpty(enterpriseId),"enterprise_id is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(service),"service is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(secureZone),"secureZone is not null !");

        checkPlatformLoginConfig(Integer.valueOf(enterpriseId));

    }

    /**
     * 校验企业的配置是否存在
     * @param enterpriseId
     */
    public void checkPlatformLoginConfig(Integer enterpriseId){
        PlatformLoginConfigControllerCheck.checkEnterpriseId(enterpriseId);

        List<PlatformLoginConfig> platformLoginConfigs = platformLoginConfigMapper.selectByEnterpriseId(enterpriseId);

        Assert.isTrue(!CollectionUtils.isEmpty(platformLoginConfigs),"platformLoginConfigs is not null !");

        Assert.isTrue(platformLoginConfigs.size() == 1,"platformLoginConfigs is only !");
    }

    public void checkServiceValidate(String ticket) {
        Assert.isTrue(StringUtils.isNotEmpty(ticket),"ticket is not null !");
    }

    public void checkLogout(HttpServletRequest request) {
        String enterpriseIdEncode = request.getParameter("enterprise_id");
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        String service = request.getParameter("service");


        Assert.isTrue(StringUtils.isNotEmpty(service),"service is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(enterpriseId),"enterprise_id is not null !");

        checkPlatformLoginConfig(Integer.valueOf(enterpriseId));

    }

    /**
     * 校验获取token
     * @param enterpriseIdEncode
     * @param clientId
     * @param clientSecret
     * @param ticket
     */
    public void checkGetToken(String enterpriseIdEncode, String clientId, String clientSecret, String ticket) {
        Assert.isTrue(StringUtils.isNotEmpty(clientId),"clientId is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(clientSecret),"clientSecret is not null !");
        Assert.isTrue(StringUtils.isNotEmpty(ticket),"ticket is not null !");

        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        Assert.isTrue(StringUtils.isNotEmpty(enterpriseId),"enterpriseId is not null !");

        checkPlatformLoginConfig(Integer.valueOf(enterpriseId));
    }

    public void checkNeedCaptcha(String username, String enterpriseIdEncode) {
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        Assert.isTrue(StringUtils.isNotEmpty(enterpriseId),"enterprise_id is not null !");
        checkPlatformLoginConfig(Integer.valueOf(enterpriseId));

        Assert.isTrue(StringUtils.isNotEmpty(username),"username is not null !");
    }

    public void checkGetEnterpriseInfo(String enterpriseIdEncode) {
        String enterpriseId = Base64Utils.decode(enterpriseIdEncode);
        Assert.isTrue(StringUtils.isNotEmpty(enterpriseId),"enterprise_id is not null !");

        checkPlatformLoginConfig(Integer.valueOf(enterpriseId));
    }
}
