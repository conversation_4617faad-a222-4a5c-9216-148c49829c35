package com.fangcloud.thirdpartplatform.constant.login;

/**
 * 企业登陆配置常类
 */
public class LoginConfigConstants {

    /**
     * cookie的key值
     */

    // uid
    public static final String PLATFORM_LOGIN_COOKIE_UID = "fangcloud_uid";

    public static final String PLATFORM_LOGIN_TICKET_PREFIX = "YFY_LOGIN_";



    /**
     *  redis key 相关
     */
    public static final String PLATFORM_LOGIN_REDIS_PREFIX_ERROR_COUNT = "PLATFORM_LOGIN_ERROR_COUNT";

    public static final String PLATFORM_LOGIN_REDIS_PREFIX_UID = "PLATFORM_LOGIN_UID";

    public static final String PLATFORM_LOGIN_REDIS_PREFIX_CAS_TICKET = "CAS_";


    /**
     * defaultLoginValidateConfig
     *
     */

    // 登陆图片验证码显示标识
    public static final Boolean DEFAULT_LOGIN_VALIDATE_NEED_PICTURE_CAPTCHA = true;

    /**
     * defaultLoginConfig
     */

    // 标准化登陆默认网址
    public static final String DEFAULT_AUTH_LOGIN_URL = "%s/platform/authentication/login?enterprise_id=%s";

    // 标准化登出默认网址
    public static final String DEFAULT_AUTH_LOGOUT_URL = "%s/platform/authentication/logout?enterprise_id=%s";

    /**
     * defaultLoginAuthOAUTH2Config
     */

    // OAUTH2登陆类型类型默认获取token网址
    public static final String DEFAULT_AUTH_OAUTH2_GET_TOKEN_URL = "%s/platform/authentication/oauth2/get_token?enterprise_id=%s&client_id=%s&client_secret=%s";

    // OAUTH2登陆类型类型默认获取用户信息网址
    public static final String DEFAULT_AUTH_OAUTH2_GET_USER_INFO_URL = "%s/platform/authentication/oauth2/get_user_info";

    public static void main(String[] args) {
        String format = String.format(DEFAULT_AUTH_OAUTH2_GET_TOKEN_URL, "http://123", "123", "sadad-dsaa", "dsadad-sdad");
        System.out.println(format);
    }
    /**
     * defaultLoginAuthCASConfig
     */

    // CAS登陆类型类型默认校验网址
    public static final String DEFAULT_AUTH_CAS_SERVICE_VALIDATE = "%s/platform/authentication/cas/service_validate";



    /**
     * defaultLoginAuthCASConfigDto
     *
     */

    // 登陆页面默认标题
    public static final String DEFAULT_LOGIN_PAGE_TITLE = "";

    // 登陆页面logo图片url
    public static final String DEFAULT_LOGIN_PAGE_LOGO_URL = "";

    // 登陆页面网页背景url
    public static final String DEFAULT_LOGIN_PAGE_WEB_BACKGROUND_URL = "";

    // 登陆页面手机背景url
    public static final String DEFAULT_LOGIN_PAGE_H5_BACKGROUND_URL = "";

    // 登陆默认url
    public static final String DEFAULT_LOGIN_PAGE_CLIENT_BASE_URL = "https://v2.fangcloud.com/";

    // 登陆账号默认提示语
    public static final String DEFAULT_LOGIN_PAGE_ACCOUNT_PROMPT = "请输入账号";

    // 登陆图标
    public static final String DEFAULT_LOGIN_PAGE_ICON_URL = "";

    /**
     * QIYEWEIXIN config
     */
    public static final String CORP_ID = "$.weixinWork.corpId.value";

    public static final String AGENT_ID = "$.weixinWork.agentId.value";

    public static String WEIXIN_HOST_PATH = "$.platform_config.weixinWork.host.value";

    public static final String WEIXIN_SSO_URL = "$.weixinWork.ssoUrl.value";

    //SSO地址
    public static final String SSO_URL = "%s/sso/oauth/postlogin";

    /**
     * DINGTALK config
     */

    public static final String APP_KEY = "$.dingTalk.appKey.value";

    public static final String APP_SECRET = "$.dingTalk.appSecret.value";

    public static final String HOST = "$.dingTalk.host.value";

    public static final String DINGTALK_SSO_URL = "$.dingTalk.ssoUrl.value";

}
