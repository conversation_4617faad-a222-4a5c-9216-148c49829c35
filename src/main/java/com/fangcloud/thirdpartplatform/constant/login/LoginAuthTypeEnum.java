package com.fangcloud.thirdpartplatform.constant.login;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @version 1.0
 * @author: liang<PERSON><PERSON><PERSON>
 * creat: 2022-04-20
 **/
public enum LoginAuthTypeEnum {
    CAS("CAS"),
    OAUTH2("OAUTH2");

    private String desc;

    LoginAuthTypeEnum(String desc) {
        this.desc = desc;
    }

    public static LoginAuthTypeEnum getByDesc(String desc) {
        for (LoginAuthTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public static List<String> names() {
        List<String> integers = Lists.newArrayList();
        for (LoginAuthTypeEnum en : values()) {
            integers.add(en.getDesc());
        }
        return integers;
    }

    public String getDesc() {
        return desc;
    }
}
