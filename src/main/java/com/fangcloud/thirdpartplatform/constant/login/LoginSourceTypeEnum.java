package com.fangcloud.thirdpartplatform.constant.login;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @version 1.0
 * @author: liang<PERSON><PERSON><PERSON>
 * creat: 2022-04-20
 **/
public enum LoginSourceTypeEnum {
    LDAP("LDAP"),
    OAUTH2("OAUTH2"),
    WEIXIN("WEIXIN"),
    DINGTALK("DINGTALK"),
    CCWORK("CCWORK"),
    // 政务钉钉
    ZWD("ZWD"),
    CODE_SCRIPT("CODE_SCRIPT");

    private String desc;

    LoginSourceTypeEnum(String desc) {
        this.desc = desc;
    }

    public static LoginSourceTypeEnum getByDesc(String desc) {
        for (LoginSourceTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public static List<String> names() {
        List<String> integers = Lists.newArrayList();
        for (LoginSourceTypeEnum en : values()) {
            integers.add(en.getDesc());
        }
        return integers;
    }

    public String getDesc() {
        return desc;
    }
}
