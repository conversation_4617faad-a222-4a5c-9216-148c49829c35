package com.fangcloud.thirdpartplatform.constant.login;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 内部登陆接口入参
 * @version 1.0
 * @author: lian<PERSON><PERSON><PERSON><PERSON>
 * creat: 2022-04-20
 **/
public enum LoginParamEnum {
    USER_NAME("${USER_NAME}","USER_NAME"),
    SMS_CODE("${SMS_CODE}","SMS_CODE"),
    PASSWORD("${PASSWORD}","PASSWORD");

    private String desc;
    private String type;

    LoginParamEnum(String desc, String type) {
        this.desc = desc;
        this.type = type;
    }

    public static LoginParamEnum getByDesc(String desc) {
        for (LoginParamEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public static List<String> names() {
        List<String> integers = Lists.newArrayList();
        for (LoginParamEnum en : values()) {
            integers.add(en.getDesc());
        }
        return integers;
    }

    public String getDesc() {
        return desc;
    }

    public String getType() {
        return type;
    }
}
