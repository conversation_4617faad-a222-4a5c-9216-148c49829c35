package com.fangcloud.thirdpartplatform.constant.login;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @version 1.0
 * @author: liang<PERSON><PERSON><PERSON>
 * creat: 2022-04-20
 **/
public enum ProductIdEnum {
    LINMA("LINMA"),
    GZLAB("GZLAB"),
    HHSDN("HHSDN");


    private String productId;

    ProductIdEnum(String productId) {
        this.productId = productId;
    }

    public static ProductIdEnum getByproductId(String productId) {
        for (ProductIdEnum en : values()) {
            if (en.productId.equals(productId)) {
                return en;
            }
        }
        return null;
    }

    public static List<String> names() {
        List<String> integers = Lists.newArrayList();
        for (ProductIdEnum en : values()) {
            integers.add(en.getProductId());
        }
        return integers;
    }

    public String getProductId() {
        return productId;
    }
}
