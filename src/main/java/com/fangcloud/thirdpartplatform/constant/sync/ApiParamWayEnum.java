package com.fangcloud.thirdpartplatform.constant.sync;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-04-14
 **/
public enum ApiParamWayEnum {
    HEAD("HEAD"),
    PARAMS("PARAMS"),
    BODY("BODY"),
    URLENCODED("URLENCODED");

    private String desc;

    ApiParamWayEnum(String desc) {
        this.desc = desc;
    }

    public static ApiParamWayEnum getByDesc(String desc) {
        for (ApiParamWayEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public static List<String> names() {
        List<String> integers = Lists.newArrayList();
        for (ApiParamWayEnum en : values()) {
            integers.add(en.getDesc());
        }
        return integers;
    }

    public String getDesc() {
        return desc;
    }
}
