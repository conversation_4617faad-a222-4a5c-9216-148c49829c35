package com.fangcloud.thirdpartplatform.constant.sync;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * API验证方式
 **/
public enum ApiVerificationModeEnum {
    OAUTH2("oAuth2"),
    OTHER("other");

    private String desc;

    ApiVerificationModeEnum(String desc) {
        this.desc = desc;
    }

    public static ApiVerificationModeEnum getByDesc(String desc) {
        for (ApiVerificationModeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }

    public static List<String> names() {
        List<String> integers = Lists.newArrayList();
        for (ApiVerificationModeEnum en : values()) {
            integers.add(en.getDesc());
        }
        return integers;
    }
}
