package com.fangcloud.thirdpartplatform.constant.sync;

/**
 * 同步表类型
 */
public enum SyncTableTypeEnum {

    TEACHER("TEACHER", "TEACHER-3333","教职工",3L),
    STUDENT("STUDENT", "STUDENT-2222","本科生",2L),
    GRADUATE("GRADUATE", "GRADUATE-1111","研究生",1L);

    private String status;

    private String name;

    private Long order;

    private String id;

    SyncTableTypeEnum(String status,String id, String name, Long order) {
        this.status = status;
        this.id = id;
        this.order = order;
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    public Long getOrder() {
        return order;
    }

    public String getId() {
        return id;
    }

    public static SyncTableTypeEnum get(String status) {
        for (SyncTableTypeEnum en : values()) {
            if (en.status.equals(status)) {
                return en;
            }
        }
        return null;
    }
}
