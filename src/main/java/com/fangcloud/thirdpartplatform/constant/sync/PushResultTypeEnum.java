package com.fangcloud.thirdpartplatform.constant.sync;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-05-23
 **/
public enum PushResultTypeEnum {
    JSON("JSON"),
    XML("XML"),
    SUCCESS("SUCCESS"),
    ERROR("ERROR");

    private String desc;

    PushResultTypeEnum(String desc) {
        this.desc = desc;
    }

    public static PushResultTypeEnum getByDesc(String desc) {
        for (PushResultTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public static List<String> names() {
        List<String> integers = Lists.newArrayList();
        for (PushResultTypeEnum en : values()) {
            integers.add(en.getDesc());
        }
        return integers;
    }

    public String getDesc() {
        return desc;
    }
}
