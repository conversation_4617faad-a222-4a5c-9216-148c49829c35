package com.fangcloud.thirdpartplatform.constant.sync;

public enum TaskTypeEnum {
    //单任务运行
    SINGLETASKING(1,"SINGLETASKING"),
    //多任务运行
    MULTITASKING(2, "MULTITASKING");

    private Integer type;


    private String desc;

    TaskTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static TaskTypeEnum getByDesc(String desc) {
        for (TaskTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
