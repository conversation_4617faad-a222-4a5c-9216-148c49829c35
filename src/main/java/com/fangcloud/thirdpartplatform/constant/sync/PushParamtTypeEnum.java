package com.fangcloud.thirdpartplatform.constant.sync;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-05-20
 **/
public enum PushParamtTypeEnum {
    LIST("LIST"),
    OBJECT("OBJECT");

    private String desc;

    PushParamtTypeEnum(String desc) {
        this.desc = desc;
    }

    public static PushParamtTypeEnum getByDesc(String desc) {
        for (PushParamtTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public static List<String> names() {
        List<String> integers = Lists.newArrayList();
        for (PushParamtTypeEnum en : values()) {
            integers.add(en.getDesc());
        }
        return integers;
    }

    public String getDesc() {
        return desc;
    }
}
