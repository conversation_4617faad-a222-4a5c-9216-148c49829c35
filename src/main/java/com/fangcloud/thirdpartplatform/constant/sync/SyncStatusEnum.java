package com.fangcloud.thirdpartplatform.constant.sync;

public enum SyncStatusEnum {
    PROGRESSING(1, "DOING"),
    STOP(2, "STOP");

    private Integer status;

    private String desc;

    SyncStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }


    public static SyncStatusEnum get(Integer status) {
        for (SyncStatusEnum en : values()) {
            if (en.status == status) {
                return en;
            }
        }
        return null;
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
