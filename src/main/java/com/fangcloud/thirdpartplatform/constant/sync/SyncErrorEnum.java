package com.fangcloud.thirdpartplatform.constant.sync;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-06-02
 **/
public enum SyncErrorEnum {

    PARENT_ID_NOT_EXIST("department id not exist"),
    DIRECTOR_ID_NOT_FOUND("director id not found"),
    LEAVE_DEPARTMENT_CAN_DELETE("leave_department_can_delete"),
    PLATFORM_DEPARTMENT_ID_NOT_EXISTED("platform_department_id not existed"),
    DEPARTMENT_NAME_EXIST("department name existed"),


    ;
    private String desc;


    SyncErrorEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public static Boolean check(String desc) {
        for (SyncErrorEnum en : values()) {
            if (desc.contains(en.desc)) {
                return true;
            }
        }
        return false;
    }
}
