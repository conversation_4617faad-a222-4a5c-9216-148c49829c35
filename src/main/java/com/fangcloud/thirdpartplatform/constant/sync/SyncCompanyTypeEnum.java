package com.fangcloud.thirdpartplatform.constant.sync;

/**
 * 同步企业类型
 */
public enum SyncCompanyTypeEnum {

    COLLEGE("COLLEGE", "高校企业"),
    COMPANY("COMPANY", "非高校企业");

    private String status;

    private String desc;

    SyncCompanyTypeEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static SyncCompanyTypeEnum get(String status) {
        for (SyncCompanyTypeEnum en : values()) {
            if (en.status.equals(status)) {
                return en;
            }
        }
        return null;
    }
}
