package com.fangcloud.thirdpartplatform.constant.sync;

public enum SyncTypeEnum {
    DEPARTMENT("DEPT", 1),
    USERS("USER",  2),
    <PERSON><PERSON><PERSON>("SY<PERSON>",  3),
    <PERSON><PERSON><PERSON><PERSON>("MESSAG<PERSON>",  4),
    THIRDPARTY_INVOKE("THIRDPARTY_INVOKE",  5);

    private String desc;

    private Integer syncType;

    SyncTypeEnum(String desc, Integer syncType) {
        this.desc = desc;
        this.syncType = syncType;
    }

    public static SyncTypeEnum getByDesc(String syncType) {
        for (SyncTypeEnum en : values()) {
            if (en.desc.equals(syncType)) {
                return en;
            }
        }
        return null;
    }

    public static SyncTypeEnum getBySyncType(Integer syncType) {
        for (SyncTypeEnum en : values()) {
            if (en.syncType.equals(syncType)) {
                return en;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }

    public Integer getSyncType() {
        return syncType;
    }
}
