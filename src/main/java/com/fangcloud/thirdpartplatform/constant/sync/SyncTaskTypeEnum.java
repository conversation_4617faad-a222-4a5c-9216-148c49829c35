package com.fangcloud.thirdpartplatform.constant.sync;

/**
 * 同步任务类型
 */
public enum SyncTaskTypeEnum {

    ADD("ADD", "添加数据"),
    DELETE("DELETE", "删除数据"),
    CHECK("CHECK", "校验数据");

    private String status;

    private String desc;

    SyncTaskTypeEnum(String status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public String getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static SyncTaskTypeEnum get(String status) {
        for (SyncTaskTypeEnum en : values()) {
            if (en.status.equals(status)) {
                return en;
            }
        }
        return null;
    }
}
