package com.fangcloud.thirdpartplatform.constant.sync;

public enum SyncResultStatusEnum {
    PROGRESSING(1, "DOING"),
    FINISH(2, "DONE"),
    ERROR(3, "STOP");

    private Integer status;

    private String desc;

    SyncResultStatusEnum(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    public static SyncResultStatusEnum get(Integer status) {
        for (SyncResultStatusEnum en : values()) {
            if (en.status == status) {
                return en;
            }
        }
        return null;
    }
}
