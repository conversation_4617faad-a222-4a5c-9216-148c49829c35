package com.fangcloud.thirdpartplatform.constant.sync;

public enum CustomIdentifierEnum {
    YFYPHONE(1,"yfyphone"),
    YFYEMAIL(2, "yfyemail");

    private Integer type;


    private String desc;

    CustomIdentifierEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static CustomIdentifierEnum getByDesc(String desc) {
        for (CustomIdentifierEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
