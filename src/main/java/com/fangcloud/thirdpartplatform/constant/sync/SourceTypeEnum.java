package com.fangcloud.thirdpartplatform.constant.sync;

import com.google.common.collect.Lists;

import java.util.List;

public enum SourceTypeEnum {
    MYSQL(1,"MYSQL"),
    SQLServer(2, "SQLSERVER"),
    <PERSON><PERSON>LE(3, "<PERSON><PERSON><PERSON>"),
    AD(4, "AD"),
    API(5, "API"),
    PUSH(6, "PUSH"),
    DING_TALK(7, "DING_TALK"),
    WEIXIN(8, "WEIXIN"),
    MESSAGE(9, "MESSAGE"),
    CODE_SCRIPT(10, "CODE_SCRIPT"),
    CCWORK(11, "CCWORK_SAAS"),
    ZWD(12, "ZWD"),
    DM(13,"DM"),
    FTP(14, "FTP");
    private Integer type;

    private String desc;

    public static List<SourceTypeEnum> HTTP_REQUEST_SOURCE_TYPES = Lists.newArrayList(PUSH);

    SourceTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static SourceTypeEnum getByDesc(String desc) {
        for (SourceTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }
}
