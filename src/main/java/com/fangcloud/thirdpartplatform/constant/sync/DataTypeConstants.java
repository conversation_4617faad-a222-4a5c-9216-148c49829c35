package com.fangcloud.thirdpartplatform.constant.sync;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.Date;
import java.util.List;
import java.util.Map;

public class DataTypeConstants {
    public static final Map<String, Class> dataTypes = Maps.newHashMap();

    static {
        dataTypes.put("String", String.class);
        dataTypes.put("int", Integer.class);
        dataTypes.put("date", Date.class);
        dataTypes.put("long", Long.class);
        dataTypes.put("boolean", Boolean.class);
        dataTypes.put("list", List.class);
        dataTypes.put("json", JSONObject.class);
        dataTypes.put("json_array", JSONArray.class);
        dataTypes.put("dynamic_date", String.class);
    }

    public static List<String> getDataTypes() {
        return Lists.newArrayList(dataTypes.keySet());
    }

    public static Class getClassByDataType(String dateType) {
        return dataTypes.get(dateType);
    }

    public static Object convert(Object obj, String dataType) {
        Class classByDataType = getClassByDataType(dataType);
        return convert(obj, classByDataType);
    }

    public static <T> T convert(Object obj, Class<T> cls) {
        if (obj == null || cls == null) {
            return null;
        }
        if (String.class.getName().equals(cls.getName())) {
            return cls.cast(String.valueOf(obj));
        } else if (Integer.class.getName().equals(cls.getName())) {
            return cls.cast(Integer.valueOf(obj.toString()));
        } else if (Long.class.getName().equals(cls.getName())) {
            return cls.cast(Long.valueOf(obj.toString()));
        } else if (Boolean.class.getName().equals(cls.getName())) {
            return cls.cast(Boolean.valueOf(obj.toString()));
        } else if (List.class.getName().equals(cls.getName())) {
            if (obj instanceof List) {
                return cls.cast(obj);
            }
            if (obj instanceof String[]) {
                return cls.cast(Lists.newArrayList((String[]) obj));
            }
            if (obj instanceof String) {
                return cls.cast(Lists.newArrayList((String) obj));
            }
        } else if (JSONObject.class.getName().equals(cls.getName())) {
            return cls.cast(JSONObject.parse(obj.toString()));
        } else if(JSONArray.class.getName().equals(cls.getName())){
            return cls.cast(JSONArray.parse(obj.toString()));
        }
        return cls.cast(obj);
    }
}
