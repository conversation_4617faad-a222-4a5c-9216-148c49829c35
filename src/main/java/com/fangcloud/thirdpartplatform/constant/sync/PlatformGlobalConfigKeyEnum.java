package com.fangcloud.thirdpartplatform.constant.sync;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-04-14
 **/
public enum PlatformGlobalConfigKeyEnum {
    HTTP_CLIENT_TYPE("http_client_type", "httpclient类型" ,""),
    SET_DEPARTMENT_ID_NULL_ENABLE("set_department_id_null_enable", "全量对比用户软删除：是否将用户放入待分配" ,"false"),
    SET_USER_ACTIVE_CLOSE("set_user_active_close", "全量对比用户软删除：是否将用户设为未激活" ,"false"),
    USER_NAME_SUFFIX("user_name_suffix", "全量对比用户软删除：用户后缀配置" ,""),
    DISABLED_USER_NAME_SUFFIX("disabled_user_name_suffix", "根据用户标识软删除：用户后缀配置" ,""),
    SET_DISABLED_USER_DEPARTMENT_ID_IS_NULL("set_disabled_user_department_id_is_null", "根据用户标识软删除：是否将用户放入待分配" ,"true"),
    LDAP_SYNC_PARENT_STEP("ldap_sync_parent_step", "ldap部门向上同步" ,"0"),

    CREATE_DEFAULT_PASSWORD("create_default_password", "创建默认密码" ,"false"),
    CUSTOM_PASSWORD("custom_password", "自定义密码" ,""),
    ZWD_AUTO_LOGIN_URL("zwd_auto_login_url", "政务钉钉免密地址" ,""),
    ZWD_AUTO_LOGIN_PAGE_URL("zwd_auto_login_page_url", "政务钉钉免密页面地址" ,""),
    ZWD_SEND_MESSAGE_URL("zwd_send_message_url", "政务钉钉发送消息地址" ,""),
    ZWD_IP("zwd_ip", "政务钉访问接口ip" ,""),
    ENCRYPTION_KEY("encryption_key", "加密密钥" ,""),
    IS_LEVEL_SYNC_DEPARTMENT("is_level_sync_department", "是否按层级同步部门" ,"false"),
    ADD_USERS_SET_TO_NULL_DEPARTMENT("add_users_set_to_null_department", "新增人员默认放待分配部门" ,"false"),
    FEI_SHU_DEPARTMENT_ID("fei_shu_department_id", "飞书同步默认id" ,"0"),
    DEPARTMENT_NAME_SUFFIX("department_name_suffix", "全量对比部门软删除：部门名称添加d_" ,"false"),
    KIWIINST_OA_HOST("kiwiinst_oa_host", "必易微OA服务地址" ,""),
    KIWIINST_OA_ACCESS_CODE("kiwiinst_oa_access_code", "必易微OA服务accessCode" ,""),
    THIRDPARTY_CALL_URL("thirdparty_call_url", "对接第三方推送地址" ,""),
    THIRDPARTY_CALL_AUTH_USERNAME("thirdparty_call_auth_username", "对接第三方推送鉴权账号" ,""),
    THIRDPARTY_CALL_AUTH_PASSWORD("thirdparty_call_auth_password", "对接第三方推送鉴权账号密码" ,""),
    THIRDPARTY_CALLBACK_URL("thirdparty_callback_url", "对接第三方推送回调地址" ,"http://127.0.0.1/thirdparty/callback"),
    THIRDPARTY_CHECK_LOGIN_MAPPING("thirdparty_check_login_mapping", "第三方登录二次验证映射关系，比如某些管理员使用account登录需要配置映射关系，示例admin=xiaoming;admin2=xiaohong" ,""),
    DING_TALK_FILTER_DEPT_IDS("ding_talk_filter_dept_ids", "钉钉同步过滤部门" ,""),
    INTERNAL_LOGIN_COOKIE("internal_login_cookie","是否set登录cookie","true");

    private String desc;

    private String key;

    private String defaultValue;

    PlatformGlobalConfigKeyEnum(String key, String desc, String defaultValue) {
        this.key = key;
        this.desc = desc;
        this.defaultValue = defaultValue;
    }

    public static PlatformGlobalConfigKeyEnum getByDesc(String desc) {
        for (PlatformGlobalConfigKeyEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public static PlatformGlobalConfigKeyEnum getByKey(String key) {
        for (PlatformGlobalConfigKeyEnum en : values()) {
            if (en.key.equals(key)) {
                return en;
            }
        }
        return null;
    }

    public static List<String> descs() {
        List<String> list = Lists.newArrayList();
        for (PlatformGlobalConfigKeyEnum en : values()) {
            list.add(en.getDesc());
        }
        return list;
    }

    public static List<String> keys() {
        List<String> list = Lists.newArrayList();
        for (PlatformGlobalConfigKeyEnum en : values()) {
            list.add(en.getKey());
        }
        return list;
    }

    public String getDesc() {
        return desc;
    }
    public String getKey() {
        return key;
    }
    public String getDefaultValue() {
        return defaultValue;
    }

}
