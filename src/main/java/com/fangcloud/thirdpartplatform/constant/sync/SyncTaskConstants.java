package com.fangcloud.thirdpartplatform.constant.sync;

import com.google.common.collect.Lists;

import java.util.List;

public class SyncTaskConstants {

    // mysql驱动
    public static final String DATABASE_MYSQL_DRIVER = "com.mysql.jdbc.Driver";

    // oracle驱动
    public static final String DATABASE_ORACLE_DRIVER = "oracle.jdbc.driver.OracleDriver";

    // sqlServer驱动
    public static final String DATABASE_SQL_SERVER_DRIVER = "com.microsoft.sqlserver.jdbc.SQLServerDriver";

    // 达梦数据库驱动
    public static final String DATABASE_DM_DRIVER = "dm.jdbc.driver.DmDriver";

    // 同步用户为空错误日志
    public static final String SYNC_USER_DATA_IS_NULL = "同步用户数据为空";

    // 同步部门数据为空错误日志
    public static final String SYNC_DEPARTMENT_DATA_IS_NULL = "同步部门数据为空";

    // mapper缓存key
    public static final String SYNC_BASE_MAPPER = "sync_base_mapper_";

    // 删除用户缓存key
    public static final String DELETE_DEPARTMENT = "delete_department_";

    // 删除用户缓存key
    public static final String DELETE_USER = "delete_user_";

    //多任务绑定缓存key
    public static final String TASK_BINDING = "task_binding";
    //钉钉用户工号缓存key
    public static final String DING_TALK_JOB_NUMBER = "ding_talk_job_number_";
    //钉钉用户id缓存key
    public static final String DING_TALK_USER_ID = "ding_talk_USER_ID_";
    // 钉钉部门id列表缓存key
    public static final String DING_TALK_DEPT_IDS = "ding_talk_DEPT_IDS_";
    //定时删除错误日志缓存key
    public static final String SCHEDULED_DELETE_SYNC_ERROR_LOGS = "scheduled_delete_sync_error_logs";
    //同步oauth2认证方式token缓存key
    public static final String OAUTH2_TOKEN = "OAUTH2_TOKEN_";

    //暂存用户列表
    public static final String STAGING_CUSTOM_USER_LIST = "stagingCustomUserList";

    //客户密码
    public static final String CUSTOM_USER_PASSWORD = "CUSTOM_USER_PASSWORD_";

    // 代码脚本url decode错误
    public static final String CODE_SCRIPT_URL_DECODE_ERROR = "代码脚本url decode错误";

    /**
     * 用户参数
     */

    // 邮箱
    public static final String USER_PARAMETER_EMAIL = "email";

    // 姓名
    public static final String USER_PARAMETER_FULL_NAME = "fullName";

    // id
    public static final String USER_PARAMETER_ID = "id";

    // 部门id
    public static final String USER_PARAMETER_DEPARTMENT_ID = "departmentId";

    public static final String USER_PARAMETER_DEPARTMENT_IDS = "departmentIds";

    // 群组id
    public static final String USER_PARAMETER_GROUP_ID = "groupId";

    public static final String USER_PARAMETER_GROUP_IDS = "groupIds";

    public static final String USER_IS_DELETE = "isDelete";

    public static final String USER_IS_DISABLE = "isDisable";

    public static final String USER_STATUS = "status";

    public static final String USER_FILTER = "filter";

    // 手机号
    public static final String USER_PARAMETER_PHONE = "phone";

    public static final String USER_CUSTOM_PASSWORD = "customPassword";

    public static final String USER_PHONE_CN = "+86";

    // 禁用用户名字前缀
    public static final String USER_DISABLE_PREFIX = "disable_";

    public static final String USER_PARAMETER_ORDER = "order";



    public static final List<String> USER_KEYS = Lists.newArrayList(USER_PARAMETER_ID, USER_PARAMETER_FULL_NAME,
            USER_PARAMETER_DEPARTMENT_ID, USER_PARAMETER_DEPARTMENT_IDS, USER_PARAMETER_EMAIL, USER_PARAMETER_PHONE, USER_IS_DELETE, USER_IS_DISABLE,
            USER_PARAMETER_GROUP_ID, USER_PARAMETER_GROUP_IDS, USER_FILTER, USER_PARAMETER_ORDER, USER_CUSTOM_PASSWORD);

    /**
     * 部门参数
     */

    // 名称
    public static final String DEPARTMENT_PARAMETER_NAME = "name";

    // 编号
    public static final String DEPARTMENT_PARAMETER_ID = "id";

    // 父部门id
    public static final String DEPARTMENT_PARAMETER_PARENT_ID = "parentId";

    // 主管id
    public static final String DEPARTMENT_PARAMETER_DIRECTOR_ID = "directorId";

    // 排序
    public static final String DEPARTMENT_PARAMETER_ORDER = "order";

    public static final String DEPARTMENT_PARAMETER_IS_TOP = "isTop";

    public static final String DEPARTMENT_PARAMETER_FILTER = "filter";

    //软删除：部门名前加d_前缀
    public static final String DEPARTMENT_PARAMETER_IS_DELETE = "isDelete";

    // 部门删除名称前缀
    public static final String DEPARTMENT_DISABLE_PREFIX = "d_";

    public static final List<String> DEPT_KEYS = Lists.newArrayList(DEPARTMENT_PARAMETER_ID, DEPARTMENT_PARAMETER_NAME, DEPARTMENT_PARAMETER_PARENT_ID, DEPARTMENT_PARAMETER_DIRECTOR_ID, DEPARTMENT_PARAMETER_ORDER, DEPARTMENT_PARAMETER_IS_TOP, DEPARTMENT_PARAMETER_FILTER, DEPARTMENT_PARAMETER_IS_DELETE);

    /**
     * 高校部门前缀  prefix
     */
    // 老师部门前缀
    public static final String COLLEGE_TEACHER_PREFIX = "TEACHER_";

    // 学生部门前缀
    public static final String COLLEGE_STUDENT_PREFIX = "STUDENT_";

    // 研究生部门前缀
    public static final String COLLEGE_GRADUATE_PREFIX = "GRADUATE_";


    /**
     * url参数
     */
    //url前缀
    public static final String URL_PREFIX = "://";

    public static final String SELF_INCREASING = "++";

    public static final String IS_FILTER_PLATFORM_USER_PATH = "$.is_filter_platform_user";

    public static final String USER_GROUP_ADMIN = "admin";

    public static final String PLATFORM_GLOBAL_CONFIG_JSON_PATH = "$.platform_global_config";
}
