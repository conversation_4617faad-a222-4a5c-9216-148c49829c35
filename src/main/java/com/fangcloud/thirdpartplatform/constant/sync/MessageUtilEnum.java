package com.fangcloud.thirdpartplatform.constant.sync;

public enum MessageUtilEnum {
    MESSAGE_TIME("time", "消息时间"),
    MESSAGE_RECEIVERS("receivers", "接收者id"),
    MESSAGE_TYPE("type", "消息类型"),
    MESSAGE_CONTENT("content", "消息内容"),
    MESSAGE_TITLE("title", "消息标题"),
    MESSAGE_WEB_URL("web_url", "webUrl"),
    MESSAGE_H5_URL("h5_url", "h5Url"),
    MESSAGE_CUSTOMER_ID("customer_id", "消息来源"),
    MESSAGE_ENTERPRISE_ID("enterprise_id", "企业id"),
    MESSAGE_ID("message_id", "消息id"),
    MESSAGE_DETAIL_URL("detail_url", "流程url"),
    MESSAGE_REVIEW_USER("review_user", "发送人id"),
    MESSAGE_PRICESS_ID("process_id", "流程id"),
    MESSAGE_FROM_REVIEW_ID("from_review_id", "上一个节点的reviewId"),
    MESSAGE_FROM_REVIEW_ACTION("from_review_action", "上一个节点的流程状态,1/通过,2/拒绝,3/回退"),
    MESSAGE_REVIEW_ID("review_id", "当前节点的reviewId"),
    MESSAGE_FROM_REVIEW_USERS("from_review_users", "上个节点的接收者id"),
    MESSAGE_REVIEW_USER_NAME("review_user_name", "申请人姓名"),
    MESSAGE_H5_DETAIL_URL("h5_detail_url", "h5详细地址"),
    MESSAGE_REVIEWER("reviewer", "审批人id"),
    MESSAGE_PUSH_TITLE("message_push_title", "消息标题");

    private String key;
    private String desc;

    MessageUtilEnum(String key, String desc) {
        this.key = key;
        this.desc = desc;
    }

    public String getKey() {
        return key;
    }

    public String getDesc() {
        return desc;
    }

}
