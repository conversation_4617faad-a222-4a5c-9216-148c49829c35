package com.fangcloud.thirdpartplatform.constant.sync;

public enum SyncConfigStatusEnum {
    START(1),
    STOP(2);

    private Integer status;

    public int getStatus() {
        return status;
    }

    SyncConfigStatusEnum(int status) {
        this.status = status;
    }


    public static boolean isStart(Integer status) {
        return START.getStatus() == status;
    }

    public static SyncConfigStatusEnum get(Integer status) {
        for (SyncConfigStatusEnum en : values()) {
            if (en.getStatus() == status) {
                return en;
            }
        }
        return null;
    }
}
