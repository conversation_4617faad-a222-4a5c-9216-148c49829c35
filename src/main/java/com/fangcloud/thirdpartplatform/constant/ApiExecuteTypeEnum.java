package com.fangcloud.thirdpartplatform.constant;

public enum ApiExecuteTypeEnum {
    H3C("H3C"),
    ALIGATEWAY("ALIGATEWAY"),
    CCWORK("CCWORK"),
    XUDC("XUDC"),

    SEEYON("SEEYON"),
    SXJCY("SXJCY"),
    OTYPE("OTYPE"),
    FEISHU("FEISHU"),
    LZLJ("LZLJ"),
    <PERSON><PERSON><PERSON><PERSON>("MORNSUN"),
    WMPI("WMPI"),
    FTP("FTP");

    private String desc;

    ApiExecuteTypeEnum(String desc) {
        this.desc = desc;
    }

    public static ApiExecuteTypeEnum getByDesc(String desc) {
        for (ApiExecuteTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }
}
