package com.fangcloud.thirdpartplatform.constant.thirdparty;

public enum ThirdpartyCallbackTypeEnum {

    KIWIINST_INVITE_COLLAB_CALLBACK("kiwiinst_invite_collab_callback"),
    KIWIINST_EDIT_COLLAB_CALLBACK("kiwiinst_edit_collab_callback"),
    KIWIINST_CREATE_SHARE_CALLBACK("kiwiinst_create_share_callback"),
    CREATE_SHARE_CALLBACK("create_share_review"),
    DOWNLOAD_CALLBACK("download_review"),
    MOVE_CALLBACK("move_review"),
    COPY_CALLBACK("copy_review"),
    FERRY_CALLBACK("ferry_review");

    private String desc;

    ThirdpartyCallbackTypeEnum(String desc) {
        this.desc = desc;
    }

    public static ThirdpartyCallbackTypeEnum getByDesc(String desc) {
        for (ThirdpartyCallbackTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }
}
