package com.fangcloud.thirdpartplatform.constant.thirdparty;

public enum ThirdpartyCallTypeEnum {

    KIWIINST_INVITE_COLLAB_CALL("kiwiinst_invite_collab_call"),
    KIWIINST_EDIT_COLLAB_CALL("kiwiinst_edit_collab_call"),
    KIWIINST_CREATE_SHARE_CALL("kiwiinst_create_share_call"),
    WOEKFLOW_SHARE_CALL("workflow_share"),
    WOEKFLOW_DOWNLOAD_CALL("workflow_download"),
    WOEKFLOW_FERRY_CALL("workflow_ferry"),
    WOEKFLOW_COPY_CALL("workflow_copy"),
    WOEKFLOW_MOVE_CALL("workflow_move"),
    THIRDPARTY_INVOKE("thirdparty_invoke"),
    THIRDPARTY_CALL_RESULT("thirdparty_call_result");

    private String desc;

    ThirdpartyCallTypeEnum(String desc) {
        this.desc = desc;
    }

    public static ThirdpartyCallTypeEnum getByDesc(String desc) {
        for (ThirdpartyCallTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }
}
