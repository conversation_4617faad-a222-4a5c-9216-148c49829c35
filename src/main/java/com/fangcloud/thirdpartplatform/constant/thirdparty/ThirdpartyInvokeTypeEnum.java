package com.fangcloud.thirdpartplatform.constant.thirdparty;

public enum ThirdpartyInvokeTypeEnum {

    THIRDPARTY_LOGIN_CHECK("kiwiinst_invite_collab_callback"),
    KIWIINST_EDIT_COLLAB_CALLBACK("kiwiinst_edit_collab_callback"),
    KIWIINST_CREATE_SHARE_CALLBACK("kiwiinst_create_share_callback"),
    CREATE_SHARE_CALLBACK("create_share_review"),
    DOWNLOAD_CALLBACK("download_review");

    private String desc;

    ThirdpartyInvokeTypeEnum(String desc) {
        this.desc = desc;
    }

    public static ThirdpartyInvokeTypeEnum getByDesc(String desc) {
        for (ThirdpartyInvokeTypeEnum en : values()) {
            if (en.desc.equals(desc)) {
                return en;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }
}
