package com.fangcloud.thirdpartplatform.constant;

public enum ResponseCodeEnum {
    SUCCESS("200", "成功"),
    ERROR("401", "失败"),
    DEFAULT("500", "未知异常"),


    /**
     * 登陆相关
     */
    CAPTCHA_ERROR("CaptchaError", "验证码错误"),
    INPUT_ERROR("InputError", "用户名或密码错误");


    private String type;
    private String desc;

    ResponseCodeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static ResponseCodeEnum getEnum (String type) {

        for(int i = 0; i < values().length; ++i) {
            if (values()[i].getType().equals(type)) {
                return values()[i];
            }
        }

        return DEFAULT;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
