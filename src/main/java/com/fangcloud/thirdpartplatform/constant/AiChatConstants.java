package com.fangcloud.thirdpartplatform.constant;

/**
 * ai对话相关常量
 */
public class AiChatConstants {

    // AI助手对话
    public static final String CHAT_TYPE_KNOWLEDGE_GPT = "ASSISTANT_CHAT";

    // 知识号对话
    public static final String CHAT_TYPE_KNOWLEDGE_ZSH_GPT = "ZSH_CHAT";

    // 知识号对话
    public static final String CHAT_TYPE_KNOWLEDGE_360_AI_SEARCH = "360_AI_SEARCH";

    // 知识号对话
    public static final String CHAT_TYPE_KNOWLEDGE_AI_FILE_CHAT = "AI_FILE_CHAT";

    // 文件提取
    public static final String CHAT_TYPE_KNOWLEDGE_AI_FILE_EXTRACT = "AI_FILE_EXTRACT";

    // 知识号对话场景：开放平台
    public static final String CHAT_SCENE_OPEN = "yfy_open_gpt";

    // 知识号对话场景：AI搜索
    public static final String CHAT_AI_SEARCH = "ai_web_search";

    // 知识号对话场景：开放平台
    public static final String CHAT_AI_FILE_CHAT = "ai_qxy";

    // 知识号token缓存前缀
    public static final String CHAT_REDIS_TOKEN_PREFIX = "CHAT_REDIS_TOKEN_PREFIX_";

    public static final String AI_FILE_EXTRACT_INTENT = "extract";



}