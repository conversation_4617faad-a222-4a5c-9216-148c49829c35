package com.fangcloud.thirdpartplatform.constant;

import com.google.common.collect.Lists;

import java.util.List;

/**
 * 常量类
 */
public class CommonConstants {

    public static final String SPLIT_UNDERLINE = "_";

    public static final String ACCESS_TOKEN = "access_token";

    // 用户不存在默认url
    public static final String URL_ACCOUNT_NOT_OPEN = "https://account.fangcloud.com/account_not_open.html";

    // 企业账号体系类型：手机号
    public static final String ENTERPRISE_LOGIN_TYPE_PHONE = "mobile";

    // 企业账号体系类型：邮箱
    public static final String ENTERPRISE_LOGIN_TYPE_EMAIL = "email";

    // 企业账号体系类型：第三方id
    public static final String ENTERPRISE_LOGIN_TYPE_USER_ID = "user_id";

    // 企业账号体系类型：亿方云id
    public static final String ENTERPRISE_LOGIN_TYPE_YFY_USER_ID = "yfy_user_id";

    // 企业微信免密登陆：手动授权
    public static final String SNSAPI_PRIVATEINFO = "snsapi_privateinfo";

    // 动态时间相关
    public static List<String> dynamicDateTypeList = Lists.newArrayList(
            CommonConstants.YEAR,
            CommonConstants.MONTH,
            CommonConstants.DAY,
            CommonConstants.HOUR,
            CommonConstants.MINUTE,
            CommonConstants.SECOND);
    public static final String TOKEN = "token";

    public static final String MESSAGE = "msg";

    public static final String SUCCESS = "成功";

    public static final String ERROR_REASON = "错误原因";

    public static final String PUSH_URL = "url";

    public static final String PUSH_REQUEST_MODE = "requestMode";

    public static final String PUSH_BODY = "body";

    public static final String DYNAMIC_DATE = "dynamic_date";
    public static final String PUSH_HEADER = "header";

    public static final String PUSH_RESULT = "result";

    public static final String PUSH_SUCCESS = "success";

    public static final String PUSH_ERROR = "error";

    public static final String PUSH_RANDOM_CHARACTER = "xxx";

    public static final String OPERATION_TYP_SAVE_OR_UPDATE = "SAVEORUPDATE";

    public static final String OPERATION_TYP_DELETE = "DELETE";

    public static final String STRING_NULL = "null";

    public static final String STRING_FORMAT = "%s";

    public static final String DATE_FORMAT = "yyyy-";

    public static final String MESSAGE_POST_DATA_SPLIT = "&";

    public static final String MESSAGE_URL_SPLIT = "=";

    public static final String MESSAGE_URL_ENCODE = "UTF-8";

    public static final String MESSAGE_TITLE = "云盘";

    public static final String NOW = "now";
    public static final String YEAR = "year";
    public static final String MONTH = "month";
    public static final String DAY = "day";
    public static final String HOUR = "hour";
    public static final String MINUTE = "minute";
    public static final String SECOND = "second";

    /**
     * 日期格式化
     */

    // 格式化成秒值
    public static final String DYNAMIC_DATE_FORMAT_SECOND = "second";
    // 格式化成毫秒值
    public static final String DYNAMIC_DATE_FORMAT_MILLISECOND = "millisecond";

    // http client 类型
    public static final String HTTP_CLIENT_TYPE_UNIREST = "unirest";

    /**
     * mq相关
     */

    public static final String JOB_QUEUES_EXCHANGE = "job_queues_exchange";

    /** 对接三方的queue */
    public static final String THIRD_PARTY_CALL_QUEUE = "third_party_call_queue";

    /** 网关中传递的 用户id */
    public static final String HEADER_USER_ID = "User-Id";

    /** url中带的token */
    public static final String PARAM_USER_TOKEN = "token";

    public static final String ZJKY_WEINXIN_CORPID = "wx9a845683e36a17e1";
}
