package com.fangcloud.thirdpartplatform.constant;

public enum PlatformTypeEnum {
    WEIXIN_WORK("weixinwork", "企业微信"),
    DING_TALK("dingtalk", "钉钉"),
    ZWD("<PERSON><PERSON>", "政务钉"),

    THIRD_PART_PLATFORM("thirdPartPlatform", "第三方平台"),
    CCWORK("ccWork", "推推Sass"),

    FEI_SHU("feishu", "飞书"),
    DEFAULT("other", "未知平台");


    private String type;
    private String desc;

    PlatformTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PlatformTypeEnum  getEnum (String type) {

        for(int i = 0; i < values().length; ++i) {
            if (values()[i].getType().equals(type)) {
                return values()[i];
            }
        }

        return DEFAULT;
    }


    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
