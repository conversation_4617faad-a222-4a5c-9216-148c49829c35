package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PlatformSyncConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PlatformSyncConfig record);

    int insertSelective(PlatformSyncConfig record);

    PlatformSyncConfig selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PlatformSyncConfig record);

    int updateByPrimaryKeyWithBLOBs(PlatformSyncConfig record);

    int updateByPrimaryKey(PlatformSyncConfig record);

    List<PlatformSyncConfig> query();

    List<PlatformSyncConfig> queryByIds(@Param("ids")List<Integer> ids);

    List<PlatformSyncConfig> queryByEnterpriseId(@Param("enterpriseId") Integer enterpriseId, @Param("syncType")Integer syncType);

    int updateNextExecuteTime(@Param("id") Integer id, @Param("nextExecuteTime") Long nextExecuteTime,
                              @Param("oldNextExecuteTime") Long oldNextExecuteTime);

    int updateStatus(@Param("id") Integer id, @Param("syncStatus") Integer syncStatus);

    int updateDelete(@Param("id") Integer id, @Param("deleted") Integer deleted);

    List<PlatformSyncConfig> queryByEnterpriseIdAndSourceType(Integer enterpriseId, String sourceType);
}