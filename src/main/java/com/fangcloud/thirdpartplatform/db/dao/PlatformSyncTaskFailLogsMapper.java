package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskFailLogs;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PlatformSyncTaskFailLogsMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PlatformSyncTaskFailLogs record);

    int batchInsert(List<PlatformSyncTaskFailLogs> records);

    int insertSelective(PlatformSyncTaskFailLogs record);

    PlatformSyncTaskFailLogs selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PlatformSyncTaskFailLogs record);

    int updateByPrimaryKeyWithBLOBs(PlatformSyncTaskFailLogs record);

    int updateByPrimaryKey(PlatformSyncTaskFailLogs record);

    List<PlatformSyncTaskFailLogs> queryByTaskId(@Param("syncTaskId") Integer syncTaskId, @Param("customId") String customId, @Param("limit") Integer limit,
                                                 @Param("pageSize") Integer pageSize);

    Integer countByTaskId(@Param("syncTaskId") Integer syncTaskId, @Param("customId") String customId);

    List<PlatformSyncTaskFailLogs> selectByTaskId(Integer syncTaskId, String customeId);

    int deleteByTimestamp(@Param("fifteenDaysAgoTimestamp") long fifteenDaysAgoTimestamp, @Param("enterpriseId") Integer enterpriseId, @Param("batchSize") int batchSize);

    int countDataBeforeFifteenDays(@Param("fifteenDaysAgoTimestamp") long fifteenDaysAgoTimestamp, @Param("enterpriseId") Integer enterpriseId);
}