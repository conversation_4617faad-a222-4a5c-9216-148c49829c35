package com.fangcloud.thirdpartplatform.db.dao;


import com.fangcloud.thirdpartplatform.db.model.DepartmentsUsers;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DepartmentsUsersMapper extends BaseMapper<DepartmentsUsers> {

    List<DepartmentsUsers> queryByUserId(@Param("userId") long userId);


    List<DepartmentsUsers> queryByUserIds(@Param("userIds") List<Long> userIds);

    List<DepartmentsUsers> queryByDepartmentId(@Param("departmentId") long departmentId);
}
