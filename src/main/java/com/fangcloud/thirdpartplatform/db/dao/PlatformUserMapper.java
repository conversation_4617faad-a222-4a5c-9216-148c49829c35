package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PlatformUserMapper extends BaseMapper<PlatformUser> {

    PlatformUser queryByPlatformIdUserTicket(
            @Param("platformId") long platformId,
            @Param("userTicket") String userTicket);

    PlatformUser queryByPlatformIdUserId(
            @Param("platformId") long platformId,
            @Param("userId") long userId);


    List<PlatformUser> queryByPlatformIdUserIds(
            @Param("platformId") long platformId,
            @Param("userIds") List<Long> userIds);

    List<PlatformUser> queryByPlatformIdUserTicketIds(
            @Param("platformId") long platformId,
            @Param("userTicketIds") List<String> userTicketIds);

    List<PlatformUser> queryByUserTicketIdsEnterpriseTicket(
            @Param("enterpriseTicket") String enterpriseTicket,
            @Param("userTicketIds") List<String> userTicketIds);


    List<PlatformUser> queryByEnterpriseTicket(
            @Param("enterpriseTicket") String enterpriseTicket);

    long countByPlatformId(@Param("platformId") long platformId);

    List<PlatformUser> queryByPlatformIdWithPage(Map queryParam);
}
