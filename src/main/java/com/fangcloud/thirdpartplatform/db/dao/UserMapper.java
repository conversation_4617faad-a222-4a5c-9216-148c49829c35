package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.User;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

public interface UserMapper extends BaseMapper<User> {

    User queryByEmail(
            @Param("email") String email,
            @Param("enterpriseId") Long enterpriseId
    );
    User queryByPhone(
            @Param("phone") String phone,
            @Param("enterpriseId") Long enterpriseId
    );

    User queryByVerifiedEmail(
            @Param("email")
            String email,
            @Param("isLoginIdentifier")
            boolean isLoginIdentifier);

    User queryByVerifiedPhone(
            @Param("phone")
            String phone,
            @Param("isLoginIdentifier")
            boolean isLoginIdentifier);

    void updateSpaceUsed(
            @Param("deltaSize")
            long deltaSize,
            @Param("userId")
            long userId);

    long queryUsersCountByUserIds(List<Long> userIds);


    List<User> searchUsers(
            @Param("queryWords")
            String queryWords,
            @Param("enterpriseId")
            long enterpriseId,
            RowBounds rowBounds);


    List<User> queryUsersByEnterpriseId(@Param("enterpriseId") long enterpriseId);


    List<User> queryDelUsersByEnterpriseId(@Param("enterpriseId") long enterpriseId);

    long searchUsersCount(
            @Param("queryWords")
            String queryWords,
            @Param("enterpriseId")
            long enterpriseId);

    void updateUserActive(long userId);
}
