package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformThirdpartyMessage;

import java.util.List;

public interface PlatformThirdpartyMessageMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(PlatformThirdpartyMessage row);

    PlatformThirdpartyMessage selectByPrimaryKey(Integer id);

    PlatformThirdpartyMessage selectByEnterpriseIdAndMessageTypeAndUniqueId(long enterpriseId, String messageType, String uniqueId);

    PlatformThirdpartyMessage selectByEnterpriseIdAndUniqueId(long enterpriseId, String uniqueId);

    List<PlatformThirdpartyMessage> selectByEnterpriseId(Integer enterpriseId);

    int updateByPrimaryKeyWithBLOBs(PlatformThirdpartyMessage row);

    int updateByPrimaryKeySelective(PlatformThirdpartyMessage row);

}