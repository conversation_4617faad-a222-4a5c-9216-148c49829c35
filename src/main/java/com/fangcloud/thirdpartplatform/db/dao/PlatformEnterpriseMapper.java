package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformEnterprises;
import org.springframework.data.repository.query.Param;

public interface PlatformEnterpriseMapper {

    PlatformEnterprises queryByEnterpriseTicket(@Param("enterpriseTicket") String enterpriseTicket);

    PlatformEnterprises queryByEnterpriseId(@Param("enterpriseId") long enterpriseId);
}
