package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformSyncTaskRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface PlatformSyncTaskRecordMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PlatformSyncTaskRecord record);

    int insertSelective(PlatformSyncTaskRecord record);

    PlatformSyncTaskRecord selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PlatformSyncTaskRecord record);

    int updateByPrimaryKeyWithBLOBs(PlatformSyncTaskRecord record);

    int updateByPrimaryKey(PlatformSyncTaskRecord record);

    List<PlatformSyncTaskRecord> pageByEnterpriseId(@Param("enterpriseId") Integer syncConfigId,
                                                    @Param("syncTaskType")Integer syncTaskType,
                                                    @Param("limit") Integer limit,
                                                    @Param("pageSize") Integer pageSize);

    Integer countByEnterpriseId(@Param("enterpriseId") Integer enterpriseId,
                        @Param("syncTaskType")Integer syncTaskType);
    List<PlatformSyncTaskRecord> selectByEnterpriseId(@Param("enterpriseId") Integer enterpriseId);
}