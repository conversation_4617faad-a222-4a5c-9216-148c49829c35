package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformLoginConfig;

import java.util.List;

public interface PlatformLoginConfigMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PlatformLoginConfig row);

    int insertSelective(PlatformLoginConfig row);

    PlatformLoginConfig selectByPrimaryKey(Integer id);

    List<PlatformLoginConfig> selectByEnterpriseId(Integer enterpriseId);

    int updateByPrimaryKeySelective(PlatformLoginConfig row);

    int updateByPrimaryKeyWithBLOBs(PlatformLoginConfig row);

    int updateByPrimaryKey(PlatformLoginConfig row);
}