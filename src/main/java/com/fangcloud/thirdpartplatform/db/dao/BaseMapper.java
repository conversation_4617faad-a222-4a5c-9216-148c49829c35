package com.fangcloud.thirdpartplatform.db.dao;

import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BaseMapper<T> {

    T queryById(Object id);

    List<T> queryByIds(
            @Param("ids")
            List<Long> ids);

    void insert(T t);

    void update(T t);

    void delete(Object id);

    void deleteByIds(
            @Param("ids")
            List<Long> ids);
}
