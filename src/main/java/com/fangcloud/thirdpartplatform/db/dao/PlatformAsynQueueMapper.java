package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformAsyncQueue;

import java.util.List;

public interface PlatformAsynQueueMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(PlatformAsyncQueue row);

    int insertSelective(PlatformAsyncQueue row);

    PlatformAsyncQueue selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PlatformAsyncQueue row);

    int updateByPrimaryKeyWithBLOBs(PlatformAsyncQueue row);

    int updateByPrimaryKey(PlatformAsyncQueue row);

    List<PlatformAsyncQueue> selectByEnterpriseIdAndStatus(Integer enterpriseId, Integer status);

    Integer batchInsertQueue(List<PlatformAsyncQueue> platformAsynQueues);

    List<PlatformAsyncQueue> selectByTaskId(Integer taskId);
}