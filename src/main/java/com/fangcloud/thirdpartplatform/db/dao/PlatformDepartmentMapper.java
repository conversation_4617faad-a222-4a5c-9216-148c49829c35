package com.fangcloud.thirdpartplatform.db.dao;

import com.fangcloud.thirdpartplatform.db.model.PlatformDepartment;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface PlatformDepartmentMapper extends BaseMapper<PlatformDepartment> {

    PlatformDepartment getByPlatformIdAndDepartmentId(
            @Param("platformId") long platformId,
            @Param("departmentId") String departmentId);

    PlatformDepartment getByPlatformIdAndYfyDepartmentId(
            @Param("platformId") long platformId,
            @Param("yfyDepartmentId") long yfyDepartmentId);

    List<PlatformDepartment> getByEnterpriseTicket(
            @Param("enterpriseTicket") String enterpriseTicket);

    List<PlatformDepartment> getByPlatformIdAndDepartmentIds(
            @Param("platformId") long platformId,
            @Param("departmentIds") List<String> departmentIds
    );

    void updateNameById(PlatformDepartment platformDepartment);

    List<PlatformDepartment> getByPlatformIdAndYfyDepartmentIds(
            @Param("platformId") long platformId,
            @Param("yfyDepartmentIds") List<Long> yfyDepartmentIds
    );
    Integer queryCountByPlatformId(@Param("platformId") long platformId);

    List<PlatformDepartment> queryByPlatformIdWithPage(Map paramMap);

}
