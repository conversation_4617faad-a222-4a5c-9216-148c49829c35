package com.fangcloud.thirdpartplatform.db.dao;


import com.fangcloud.thirdpartplatform.db.model.Enterprise;

import java.util.List;

public interface EnterpriseMapper extends BaseMapper<Enterprise> {

    long queryTrashPeriod(long enterpriseId);

    int queryFileVersionLimit(long enterpriseId);

    List<Enterprise> query();

    List<Enterprise> queryByAdditionalInfo(String searchText);

    int updateAdditionalById(Enterprise enterprise);
}
