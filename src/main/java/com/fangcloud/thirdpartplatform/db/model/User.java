package com.fangcloud.thirdpartplatform.db.model;

import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.helper.DBHelper;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class User extends BaseEntity {
    protected long enterpriseId;
    protected String fullName;
    protected String email;
    protected String userGroup;
    protected long settings;
    protected long deleted;
    protected long spaceUsed;
    protected long spaceTotal;
    protected String phone;
    protected String countryCode;
    protected boolean active;
    protected String profilePicPath;
    protected String fullNamePinyin;
    protected String pinyinFirstLetters;
    protected long operatEnterpriseId;
    protected String clientId;
    protected String valueBox;
    protected long userGuideSettings;


    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }


    public long getOperatEnterpriseId() {
        return operatEnterpriseId;
    }

    public void setOperatEnterpriseId(long operatEnterpriseId) {
        this.operatEnterpriseId = operatEnterpriseId;
    }



    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getProfilePicPath() {
        return profilePicPath;
    }

    public void setProfilePicPath(String profilePicPath) {
        this.profilePicPath = profilePicPath;
    }

    public long getSettings() {
        return settings;
    }

    public void setSettings(long settings) {
        this.settings = settings;
    }

    public long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserGroup() {
        return userGroup;
    }

    public void setUserGroup(String userGroup) {
        this.userGroup = userGroup;
    }

    public long getDeleted() {
        return deleted;
    }

    public void setDeleted(long deleted) {
        this.deleted = deleted;
    }

    public long getSpaceUsed() {
        return spaceUsed;
    }

    public void setSpaceUsed(long spaceUsed) {
        this.spaceUsed = spaceUsed;
    }

    public long getSpaceTotal() {
        return spaceTotal;
    }

    public void setSpaceTotal(long spaceTotal) {
        this.spaceTotal = spaceTotal;
    }

    public String getName() {
        return fullName;
    }

    public String getFullNamePinyin() {
        return fullNamePinyin;
    }

    public void setFullNamePinyin(String fullNamePinyin) {
        this.fullNamePinyin = fullNamePinyin;
    }

    public String getPinyinFirstLetters() {
        return pinyinFirstLetters;
    }

    public void setPinyinFirstLetters(String pinyinFirstLetters) {
        this.pinyinFirstLetters = pinyinFirstLetters;
    }

    public String getValueBox() {
        return valueBox;
    }

    public void setValueBox(String valueBox) {
        this.valueBox = valueBox;
    }

    public long getUserGuideSettings() {
        return userGuideSettings;
    }

    public void setUserGuideSettings(long userGuideSettings) {
        this.userGuideSettings = userGuideSettings;
    }

    /* ===================================== */

    public enum bitSettings {
        COLLAB_AUTO_ACCEPT_DISABLED(0),
        IDENTITY_TYPE_PHONE_AVAILABLE(19),
        IDENTITY_TYPE_EMAIL_AVAILABLE(20);

        private int bitIndex;

        bitSettings(int bitIndex) {
            this.bitIndex = bitIndex;
        }

        public int getBitIndex() {
            return this.bitIndex;
        }
    }
    public enum guideSettings {

        IS_SYSTEM_HOSTING(31);

        private int bitIndex;

        guideSettings(int bitIndex) {
            this.bitIndex = bitIndex;
        }

        public int getBitIndex() {
            return this.bitIndex;
        }
    }

    public static final long UNLIMITED_UPLOAD_SIZE_LIMIT = -1;
    public static final long USER_DELTA_INFO = -1;

    // personal user
    public static final int PERSONAL_FILE_VERSION_LIMIT = 10;
    public static final int PERSONAL_TRASH_PERIOD = 30;

    // user group
    public static final String ADMIN_GROUP = "admin";
    public static final String USER_GROUP = "user";
    public static final String UNION_ADMIN_GROUP = "union_admin";

    private final static String INTERNATIONAL_PHONE_FORMAT = "(+%s)%s";
    private final static String CHINESE_COUNTRY_CODE = "86";

    public static final String VALUE_BOX_IS_SYSTEM_HOSTING = "$.is_system_hosting";
    public static final String VALUE_BOX_IS_SYNC_USER = "$.is_sync_user";

    /**
     * 是否是个人用户
     *
     * @return
     */
    public boolean isPersonalUser() {
        return getEnterpriseId() == Enterprise.PERSONAL_USER_ENTERPRISE_ID;
    }

    /**
     * 是否自动接受协作
     *
     * @return
     */
    public boolean isCollabAutoAccepted() {
        return !DBHelper.getBooleanBitmask(settings, bitSettings.COLLAB_AUTO_ACCEPT_DISABLED.bitIndex);
    }


    private boolean isPhoneLoginAvailable() {
        return DBHelper.getBooleanBitmask(settings, bitSettings.IDENTITY_TYPE_PHONE_AVAILABLE.getBitIndex());
    }

    private boolean isEmailLoginAvailable() {
        return DBHelper.getBooleanBitmask(settings, bitSettings.IDENTITY_TYPE_EMAIL_AVAILABLE.getBitIndex());
    }

    /**
     * 是否已经被删除
     * // TODO 这里不能叫isDeleted,不然会报错,原因是不知道是用isDeleted还是用getDeleted
     *
     * @return
     */
    public boolean isHaveBeenDeleted() {
        return this.deleted > 0;
    }

    /**
     * 是否是管理员
     *
     * @return
     */
    public boolean isAdmin() {
        return Objects.equals(this.userGroup, ADMIN_GROUP);
    }

    /**
     * 获取用户用于登录的用户名
     *
     * @return
     */
    public String getLogin() {
        String login = null;
        if (isEmailLoginAvailable()) {
            login = getCheckedEmail();
        } else if (isPhoneLoginAvailable()) {
            login = getCheckedPhone();
        }
        return login;
    }

    public String getCheckedEmail() {
        String checkedEmail = email;
        if (email == null) {
            checkedEmail = "";
        } else if (email.startsWith("deleted")) {
            checkedEmail = email.split(":")[1];
        }
        return checkedEmail;
    }

    public String getCheckedPhone() {
        String checkedPhone = phone;
        if (phone == null) {
            return "";
        } else if (phone.startsWith("deleted")) {
            checkedPhone = phone.split(":")[1];
        }
        return CHINESE_COUNTRY_CODE.equals(countryCode) ? checkedPhone : String.format(INTERNATIONAL_PHONE_FORMAT, countryCode, phone);
    }


    /**
     * 获取同步用户是否是是系统托管用户
     * @return
     */
    public boolean getIsSystemHosting(){
        return !DBHelper.getBooleanBitmask(userGuideSettings, guideSettings.IS_SYSTEM_HOSTING.bitIndex);
    }

    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", created=" + created +
                ", updated=" + updated +
                ", enterpriseId=" + enterpriseId +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", userGroup='" + userGroup + '\'' +
                ", settings=" + settings +
                ", deleted=" + deleted +
                ", spaceUsed=" + spaceUsed +
                ", spaceTotal=" + spaceTotal +
                ", phone='" + phone + '\'' +
                ", countryCode='" + countryCode + '\'' +
                ", active=" + active +
                ", profilePicPath='" + profilePicPath + '\'' +
                ", fullNamePinyin='" + fullNamePinyin + '\'' +
                ", pinyinFirstLetters='" + pinyinFirstLetters + '\'' +
                '}';
    }
}
