package com.fangcloud.thirdpartplatform.db.model;


import com.fangcloud.thirdpartplatform.helper.DBHelper;
import com.fangcloud.thirdpartplatform.helper.DateHelper;

import java.util.HashSet;
import java.util.Set;

public class Enterprise extends BaseEntity {
    private String name;
    private int platformId;
    private long fileVersionLimit;
    private String additionalInfo;
    private long settings;
    private long settings2;
    private long adminUserId;
    private long spaceAmount;
    private long trialExpiresAt;
    private long planId;
    private long expiresAt;

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformIfd) {
        this.platformId = platformIfd;
    }

    public long getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(long adminUserId) {
        this.adminUserId = adminUserId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getFileVersionLimit() {
        return fileVersionLimit;
    }

    public void setFileVersionLimit(long fileVersionLimit) {
        this.fileVersionLimit = fileVersionLimit;
    }

    public long getSettings() {
        return settings;
    }

    public void setSettings(long settings) {
        this.settings = settings;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public long getSpaceAmount() {
        return spaceAmount;
    }

    public void setSpaceAmount(long spaceAmount) {
        this.spaceAmount = spaceAmount;
    }

    public long getTrialExpiresAt() {
        return trialExpiresAt;
    }

    public void setTrialExpiresAt(long trialExpiresAt) {
        this.trialExpiresAt = trialExpiresAt;
    }

    public long getPlanId() {
        return planId;
    }

    public void setPlanId(long planId) {
        this.planId = planId;
    }

    public long getExpiresAt() {
        return expiresAt;
    }

    public void setExpiresAt(long expiresAt) {
        this.expiresAt = expiresAt;
    }

    public long getSettings2() {
        return settings2;
    }

    public void setSettings2(long settings2) {
        this.settings2 = settings2;
    }

    /* ====================================== */

    public enum bitSettings {
        IS_PRIVATE_STORAGE_ENABLED(11), // 私有存储开关
        IS_OVERSEAS_ACCELERATION_ENABLED(31); // 是否开启海外加速

        private int bitIndex;

        bitSettings(int bitIndex) {
            this.bitIndex = bitIndex;
        }

        public int getBitIndex() {
            return this.bitIndex;
        }
    }

    public enum bitSettings2 {
        IS_FORBIDDEN_LOGIN(35); // 禁止企业成员登陆
        private int bitIndex;

        bitSettings2(int bitIndex) {
            this.bitIndex = bitIndex;
        }

        public int getBitIndex() {
            return this.bitIndex;
        }
    }

    public static final long PERSONAL_USER_ENTERPRISE_ID = 0;
    private static final long INFINITE_TRIAL_TIME = 0;
    private static final long PLAN_TYPE_EXPERIENCE = 1;
    private static final long PLAN_TYPE_FREE = 13;
    private static final long PLAN_TYPE_FREE_2017 = 23;
    private static final long PLAN_TYPE_FREE_2020 = 25;
    private static Set<Long> set;

    // additional info
    public static final String ADDITIONAL_INFO_SHARE_LINK_FIELD = "share_link";
    public static final String ADDITIONAL_INFO_SHARE_LINK_FIELD_TITLE = "title";

    static {
        set = new HashSet<>();
        set.add(PLAN_TYPE_EXPERIENCE);
        set.add(PLAN_TYPE_FREE);
        set.add(PLAN_TYPE_FREE_2017);
        set.add(PLAN_TYPE_FREE_2020);
    }

    /**
     * 是否开启海外加速
     *
     * @return
     */
    public boolean isOverseasAccEnabled() {
        return DBHelper.getBooleanBitmask(getSettings(), bitSettings.IS_OVERSEAS_ACCELERATION_ENABLED.bitIndex);
    }

    /**
     * 是否禁止企业成员登陆
     *
     * @return
     */
    public boolean isForbiddenLogin() {
        return DBHelper.getBooleanBitmask(getSettings2(), bitSettings2.IS_FORBIDDEN_LOGIN.bitIndex);
    }

    /**
     * 是否是个人用户企业
     *
     * @return
     */
    public boolean isPersonalEnterprise() {
        return this.id == PERSONAL_USER_ENTERPRISE_ID;
    }

    /**
     * 是否开启了私有存储
     * @return
     */
    public boolean isPrivateStorageEnabled() {
        return DBHelper.getBooleanBitmask(getSettings(), bitSettings.IS_PRIVATE_STORAGE_ENABLED.bitIndex);
    }

    /**
     * 企业是否已经过期
     * @return
     */
    public boolean isExpired() {
        return trialExpiresAt != INFINITE_TRIAL_TIME && !set.contains(planId) && DateHelper.getCurrentTimeStamp() > getRealExpired();
    }

    /**
     * 获取企业真正的过期时间戳
     * @return
     */
    private long getRealExpired() {
        return isTrialEnterprise() ? trialExpiresAt : expiresAt;
    }

    /**
     * 企业是否在试用期
     * @return
     */
    private boolean isTrialEnterprise() {
        return (trialExpiresAt == INFINITE_TRIAL_TIME || trialExpiresAt > expiresAt) && !set.contains(planId);
    }
}
