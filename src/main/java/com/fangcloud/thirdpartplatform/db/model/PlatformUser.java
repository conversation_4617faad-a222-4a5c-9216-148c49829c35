package com.fangcloud.thirdpartplatform.db.model;

public class PlatformUser extends BaseEntity {
    private long userId;
    private String userTicket;
    private long platformId;

    private long ticketType;
    private String platformUserAvatar;
    private String enterpriseTicket;
    private String UserInnerId;
    private long settings;
    private String valueBox;

    public long getUserId() {
        return userId;
    }

    public void setUserId(long userId) {
        this.userId = userId;
    }

    public String getUserTicket() {
        return userTicket;
    }

    public void setUserTicket(String userTicket) {
        this.userTicket = userTicket;
    }

    public long getPlatformId() {
        return platformId;
    }

    public void setPlatformId(long platformId) {
        this.platformId = platformId;
    }

    public long getTicketType() {
        return ticketType;
    }

    public void setTicketType(long ticketType) {
        this.ticketType = ticketType;
    }

    public String getPlatformUserAvatar() {
        return platformUserAvatar;
    }

    public void setPlatformUserAvatar(String platformUserAvatar) {
        this.platformUserAvatar = platformUserAvatar;
    }

    public String getEnterpriseTicket() {
        return enterpriseTicket;
    }

    public void setEnterpriseTicket(String enterpriseTicket) {
        this.enterpriseTicket = enterpriseTicket;
    }

    public String getUserInnerId() {
        return UserInnerId;
    }

    public void setUserInnerId(String userInnerId) {
        UserInnerId = userInnerId;
    }

    public long getSettings() {
        return settings;
    }

    public void setSettings(long settings) {
        this.settings = settings;
    }

    public String getValueBox() {
        return valueBox;
    }

    public void setValueBox(String valueBox) {
        this.valueBox = valueBox;
    }
}
