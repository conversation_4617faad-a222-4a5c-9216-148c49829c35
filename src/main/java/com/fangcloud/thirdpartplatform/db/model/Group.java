package com.fangcloud.thirdpartplatform.db.model;

public class Group extends BaseEntity {
    private String name;
    private String nameSort;
    private String description;
    private long adminUserId;
    private long enterpriseId;
    private long departmentId;
    private boolean visiable;
    private long settings;
    private long userCount;
    private long itemCount;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameSort() {
        return nameSort;
    }

    public void setNameSort(String nameSort) {
        this.nameSort = nameSort;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public long getAdminUserId() {
        return adminUserId;
    }

    public void setAdminUserId(long adminUserId) {
        this.adminUserId = adminUserId;
    }

    public long getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(long enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(long departmentId) {
        this.departmentId = departmentId;
    }

    public boolean isVisiable() {
        return visiable;
    }

    public void setVisiable(boolean visiable) {
        this.visiable = visiable;
    }

    public long getSettings() {
        return settings;
    }

    public void setSettings(long settings) {
        this.settings = settings;
    }

    public long getUserCount() {
        return userCount;
    }

    public void setUserCount(long userCount) {
        this.userCount = userCount;
    }

    public long getItemCount() {
        return itemCount;
    }

    public void setItemCount(long itemCount) {
        this.itemCount = itemCount;
    }

    /* ====================================================================== */

    /**
     * 判断是否是部门群组
     *
     * @return
     */
    public boolean isDepartmentGroup() {
        return departmentId > 0;
    }
}
