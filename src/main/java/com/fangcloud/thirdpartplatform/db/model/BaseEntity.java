package com.fangcloud.thirdpartplatform.db.model;

public abstract class BaseEntity {
    protected long id;
    protected long created;
    protected long updated;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public long getCreated() {
        return created;
    }

    public void setCreated(long created) {
        this.created = created;
    }

    public long getUpdated() {
        return updated;
    }

    public void setUpdated(long updated) {
        this.updated = updated;
    }

    /**
     * 判断id是否存在
     *
     * @return
     */
    public boolean isIdExisted() {
        return id > 0;
    }
}
