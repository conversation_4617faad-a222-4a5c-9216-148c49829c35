package com.fangcloud.thirdpartplatform.db.model;

public class PlatformDepartment extends BaseEntity {
    private String name;
    private String departmentId;
    private long yfyDepartmentId;
    private long platformId;
    private String enterpriseTicket;


    public String getEnterpriseTicket() {
        return enterpriseTicket;
    }

    public void setEnterpriseTicket(String enterpriseTicket) {
        this.enterpriseTicket = enterpriseTicket;
    }

    public long getPlatformId() {
        return platformId;
    }

    public void setPlatformId(long platformId) {
        this.platformId = platformId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId;
    }

    public long getYfyDepartmentId() {
        return yfyDepartmentId;
    }

    public void setYfyDepartmentId(long yfyDepartmentId) {
        this.yfyDepartmentId = yfyDepartmentId;
    }
}
