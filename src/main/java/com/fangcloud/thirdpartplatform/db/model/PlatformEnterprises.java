package com.fangcloud.thirdpartplatform.db.model;

public class PlatformEnterprises {
    private long id;
    private String alias;
    private String enterpriseTicket;
    private int enterpriseId;
    private int platformId;
    private String platformEnterpriseLogo;
    private long settings;
    private String valueBox;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getEnterpriseTicket() {
        return enterpriseTicket;
    }

    public void setEnterpriseTicket(String enterpriseTicket) {
        this.enterpriseTicket = enterpriseTicket;
    }

    public int getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(int enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public int getPlatformId() {
        return platformId;
    }

    public void setPlatformId(int platformId) {
        this.platformId = platformId;
    }

    public String getPlatformEnterpriseLogo() {
        return platformEnterpriseLogo;
    }

    public void setPlatformEnterpriseLogo(String platformEnterpriseLogo) {
        this.platformEnterpriseLogo = platformEnterpriseLogo;
    }

    public long getSettings() {
        return settings;
    }

    public void setSettings(long settings) {
        this.settings = settings;
    }

    public String getValueBox() {
        return valueBox;
    }

    public void setValueBox(String valueBox) {
        this.valueBox = valueBox;
    }
}
