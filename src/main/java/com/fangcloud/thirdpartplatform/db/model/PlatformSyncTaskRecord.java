package com.fangcloud.thirdpartplatform.db.model;

public class PlatformSyncTaskRecord {
    private Integer id;

    private Integer enterpriseId;

    private Integer syncConfigId;

    private Integer syncTaskType;

    private Integer syncTaskStatus;

    private Long syncStartat;

    private Long syncEndat;

    private Integer syncResult;

    private Long created;

    private Long updated;

    private String valueBox;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Integer enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public Integer getSyncConfigId() {
        return syncConfigId;
    }

    public void setSyncConfigId(Integer syncConfigId) {
        this.syncConfigId = syncConfigId;
    }

    public Integer getSyncTaskType() {
        return syncTaskType;
    }

    public void setSyncTaskType(Integer syncTaskType) {
        this.syncTaskType = syncTaskType;
    }

    public Integer getSyncTaskStatus() {
        return syncTaskStatus;
    }

    public void setSyncTaskStatus(Integer syncTaskStatus) {
        this.syncTaskStatus = syncTaskStatus;
    }

    public Long getSyncStartat() {
        return syncStartat;
    }

    public void setSyncStartat(Long syncStartat) {
        this.syncStartat = syncStartat;
    }

    public Long getSyncEndat() {
        return syncEndat;
    }

    public void setSyncEndat(Long syncEndat) {
        this.syncEndat = syncEndat;
    }

    public Integer getSyncResult() {
        return syncResult;
    }

    public void setSyncResult(Integer syncResult) {
        this.syncResult = syncResult;
    }

    public Long getCreated() {
        return created;
    }

    public void setCreated(Long created) {
        this.created = created;
    }

    public Long getUpdated() {
        return updated;
    }

    public void setUpdated(Long updated) {
        this.updated = updated;
    }

    public String getValueBox() {
        return valueBox;
    }

    public void setValueBox(String valueBox) {
        this.valueBox = valueBox == null ? null : valueBox.trim();
    }
}