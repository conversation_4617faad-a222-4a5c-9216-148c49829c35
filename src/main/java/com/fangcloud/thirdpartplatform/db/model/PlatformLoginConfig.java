package com.fangcloud.thirdpartplatform.db.model;

public class PlatformLoginConfig {
    private Integer id;

    private Integer enterpriseId;

    private String loginSourceType;

    private Long created;

    private Long updated;

    private Integer deleted;

    private String valueBox;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getEnterpriseId() {
        return enterpriseId;
    }

    public void setEnterpriseId(Integer enterpriseId) {
        this.enterpriseId = enterpriseId;
    }

    public String getLoginSourceType() {
        return loginSourceType;
    }

    public void setLoginSourceType(String loginSourceType) {
        this.loginSourceType = loginSourceType == null ? null : loginSourceType.trim();
    }

    public Long getCreated() {
        return created;
    }

    public void setCreated(Long created) {
        this.created = created;
    }

    public Long getUpdated() {
        return updated;
    }

    public void setUpdated(Long updated) {
        this.updated = updated;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public String getValueBox() {
        return valueBox;
    }

    public void setValueBox(String valueBox) {
        this.valueBox = valueBox == null ? null : valueBox.trim();
    }
}