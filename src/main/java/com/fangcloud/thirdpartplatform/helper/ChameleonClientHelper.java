package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;


@Slf4j
@Component
public class ChameleonClientHelper {
    private String GET_PRODUCT_ID_URL = "%s/service/simba/chameleon/sso_config/get_product_id?platform_id=%s";

    private String GET_DOMAIN_URL = "%s/service/simba/chameleon/domain_config/get/%s";


    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private HttpClientHelper httpClientHelper;

    public String getProductIdByPlatformId(int platformId) {
        String url = String.format(GET_PRODUCT_ID_URL, customNacosConfig.getSimbaHostUrl(), platformId);
        try {
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            return JSON.parseObject(result).getString("product_id");

        } catch (Exception e) {
            log.error("调用变色龙服务异常 url {}",url,e);
        }
        return null;
    }

    public String getDomainInfo(int platformId) {
        String productId = getProductIdByPlatformId(platformId);
        String url = String.format(GET_DOMAIN_URL, customNacosConfig.getSimbaHostUrl(), productId);
        try {
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            return JSON.parseObject(result).getString("domain_config");

        } catch (Exception e) {
            log.error("调用变色龙服务异常 url {}",url,e);
        }
        return null;
    }

    public String getBaseUrl(int platformId) {
        String productId = getProductIdByPlatformId(platformId);
        String url = String.format(GET_DOMAIN_URL, customNacosConfig.getSimbaHostUrl(), productId);
        try {
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            String domainConfig = JSON.parseObject(result).getString("domain_config");
            String baseUrl = JSONPath.extract(domainConfig, "$.base_url").toString();
            return baseUrl;
        } catch (Exception e) {
            log.error("调用变色龙服务异常 url {}",url,e);
        }
        return null;
    }
}
