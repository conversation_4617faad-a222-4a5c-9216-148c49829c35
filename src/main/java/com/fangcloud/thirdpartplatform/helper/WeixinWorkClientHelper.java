package com.fangcloud.thirdpartplatform.helper;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.PlatformTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.CustomIdentifierEnum;
import com.fangcloud.thirdpartplatform.entity.input.WeixinWorkInitParams;
import com.fangcloud.thirdpartplatform.entity.output.QiyeWeixinDepartment;
import com.fangcloud.thirdpartplatform.entity.output.QiyeWeixinUser;
import com.fangcloud.thirdpartplatform.service.impl.datasource.QiyeWeixinSyncHandler;
import com.sync.common.entity.dto.YfyMessage;
import com.sync.common.entity.dto.YfyUser;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.sync.common.contants.SyncConstant.GB;

@Slf4j
public class WeixinWorkClientHelper {

    private static final String DEFAULT_HOST = "https://qyapi.weixin.qq.com";
    private static final String GET_TOKEN = "gettoken?corpid=%s&corpsecret=%s";
    private static final String SEND_MESSAGE = "message/send?access_token=%s";
    private static final String GET_USER_INFO_BY_USER_ID = "user/get?access_token=%s&userid=%s";
    private static final String GET_USER_INFO_BY_CODE = "user/getuserinfo?access_token=%s&code=%s";
    private static final String GET_USER_INFO_BY_USER_TICKET = "user/getuserdetail?access_token=%s";
    private static final String GET_USER_INFO_BY_CODE_NEW = "auth/getuserinfo?access_token=%s&code=%s";
    private static final String GET_USER_INFO_BY_USER_TICKET_NEW = "auth/getuserdetail?access_token=%s";
    private static final String GET_USER_INFO_BY_DEPARTMENT_ID_IS_RECURSION = "user/list?access_token=%s&department_id=%d&fetch_child=1";
    private static final String GET_USER_INFO_BY_DEPARTMENT_ID = "user/list?access_token=%s&department_id=%d";
    private static final String GET_USER_ID_BY_PHONE = "/user/getuserid?access_token=%s";
    private static final String GET_USER_ID_BY_EMAIL = "/user/get_userid_by_email?access_token=%s";
    private static final String GET_ALL_DEPARTMENT = "department/list?access_token=%s";
    private static final String GET_CHILD_DEPARTMENT_BY_ID = "department/list?access_token=%s&id=%d";

    private String weixinHost = "%s/cgi-bin/%s";

    private String corpid;

    private String corpsecret;

    private String host;

    private String agentId;

    private String code;

    private String authorizationMethod;

    private HttpClientHelper httpClientHelper;

    private CacheHelper cacheHelper;

    public WeixinWorkClientHelper(WeixinWorkInitParams params) {
        corpid = params.getCorpId();
        corpsecret = params.getCorpSecret();
        host = StringUtils.isEmpty(params.getHost()) ? DEFAULT_HOST : params.getHost();
        agentId = params.getAgentId();
        code = params.getCode();
        authorizationMethod = params.getAuthorizationMethod();
        cacheHelper = SpringHelper.getBean(CacheHelper.class);
        httpClientHelper = SpringHelper.getBean(HttpClientHelper.class);

    }



    public String getUid() {
        String result = getUserInfo();
        if (result == null || Objects.isNull(JSON.parseObject(result).get("userid"))) {
            return null;
        }
        return JSON.parseObject(result).get("userid").toString();
    }

    /**
     * 组装亿方云用户
     * @return
     */
    public YfyUser getYfyUser() {
        String result = getUserInfo();
        if(Objects.isNull(result) || Objects.isNull(JSON.parseObject(result).get("userid"))){
            return null;
        }
        String mobile = null;
        String email = null;
        String userDetailJson = getInfoByUserTicket(result);
        if (Objects.isNull(userDetailJson)
                || Objects.isNull(JSON.parseObject(userDetailJson).get("mobile"))){
            String userId = JSON.parseObject(result).get("userid").toString();
            userDetailJson = getUserDetail(userId);
            Object mobileObj = JSON.parseObject(userDetailJson).get("mobile");
            if(!Objects.isNull(mobileObj)){
                mobile = mobileObj.toString();
            }else {
                JSONObject extattrJsonObj =  (JSONObject)JSON.parseObject(userDetailJson).get("extattr");
                JSONArray attrsJsonObj = extattrJsonObj.getJSONArray("attrs");
                for (Object attr : attrsJsonObj) {
                    JSONObject attrJSONObj = (JSONObject) attr;
                    String name = (String) attrJSONObj.get("name");
                    if("yfyphone".equals(name)){
                        mobile =  (String)  attrJSONObj.get("value");
                    }
                }
            }
            if(StringUtils.isEmpty(mobile)){
                Object telephone = JSON.parseObject(userDetailJson).get("telephone");
                if(!Objects.isNull(telephone)){
                    mobile =  telephone.toString();
                }
            }

            log.info("get userDetailJson by user ticket is null!");
        }else {
            mobile = JSON.parseObject(userDetailJson).get("mobile").toString();
            email = JSON.parseObject(userDetailJson).get("email").toString();
        }

        if(Objects.isNull(userDetailJson)){
            return null;
        }

        if (StringUtils.isBlank(email)) {
            QiyeWeixinUser qiyeWeixinUser = JSON.parseObject(userDetailJson, QiyeWeixinUser.class);
            QiyeWeixinSyncHandler qiyeWeixinSyncHandler = new QiyeWeixinSyncHandler();
            email = qiyeWeixinSyncHandler.getCustomIdentifierValue(qiyeWeixinUser.getExtAttributes(), CustomIdentifierEnum.YFYEMAIL.getDesc());
        }



        YfyUser build = YfyUser.builder()
                .id(JSON.parseObject(userDetailJson).get("userid").toString())
                .fullName(Objects.isNull(JSON.parseObject(userDetailJson).get("name"))? null :JSON.parseObject(userDetailJson).get("name").toString())
                .spaceTotal(20 * GB)
                .createTime(new Date())
                .phone(mobile)
                .email(email)
                .status("1")
                .build();
        log.info("yfyUser info :{}",JSON.toJSONString(build));
        return build;

    }


    public String getMobile() {
        String uid = getUid();
        String result = getUserDetail(uid);
        if (result == null || Objects.isNull(JSON.parseObject(result).get("mobile"))) {
            return null;
        }
        return JSON.parseObject(result).get("mobile").toString();
    }

    public String getUserDetail(String weixinUserId) {
        String urlInfo = String.format(GET_USER_INFO_BY_USER_ID, getToken(), weixinUserId);
        String url = String.format(weixinHost, host, urlInfo);
        log.info("获取微信用户详细信息 url {}", url);
        httpClientHelper.setDefaultHeaders();
        try {
            Response response = httpClientHelper.getResponse(url);
            if (null != response.body() && response.code() == 200) {
                String result = Objects.requireNonNull(response.body()).string();
                log.info("微信接口返回值 url {} result {}", url, result);
                return result;
            }
        } catch (Exception e) {
            log.error("企业微信获取详细信息异常 url {}", url, e);
        }
        return null;
    }

    /**
     * 根据user_ticket获取用户敏感信息
     * @param
     * @return
     */
    private String getInfoByUserTicket(String result) {
        String urlInfo = String.format(GET_USER_INFO_BY_USER_TICKET, getToken());
        if (isPrivateAuth()) {
            urlInfo = String.format(GET_USER_INFO_BY_USER_TICKET_NEW, getToken());
        }
        String url = String.format(weixinHost, host, urlInfo);
        log.info("通过userTicket获取微信用户详细信息 url {}", url);
        httpClientHelper.setDefaultHeaders();
        try {
            Object userTicketObj = JSON.parseObject(result).get("user_ticket");
            if(Objects.isNull(userTicketObj)){
                log.info("user ticket is null!");
                return null;
            }
            String userTicket = userTicketObj.toString();
            Map<String,String> ticketMap = new HashMap<>();
            ticketMap.put("user_ticket",userTicket);
            Response response = httpClientHelper.postResponse(url,JSON.toJSONString(ticketMap));
            if (null != response.body() && response.code() == 200) {
                String responseResult = Objects.requireNonNull(response.body()).string();
                log.info("微信接口返回值 result {}", responseResult);
                return responseResult;
            }
        } catch (Exception e) {
            log.error("企业微信获取详细信息异常 url {}", url, e);
        }
        return null;
    }

    /**
     * 根据手机号获取userId
     * @param phone
     * @return
     */
    public String getUserIdByPhone(String phone) {
        String url = String.format(weixinHost, host, String.format(GET_USER_ID_BY_PHONE, getToken()));
        httpClientHelper.setDefaultHeaders();
        String phoneInfo = String.format("{\"mobile\":\"%s\"}",phone);
        log.info("根据手机号获取微信用户id url {}, postData {}", url, phoneInfo);
        try {
            Response response = httpClientHelper.postResponse(url, phoneInfo);
            if (response == null || response.body() == null) {
                log.info("根据手机号获取微信用户id返回结果为空");
                return null;
            }
            String result = response.body().string();
            log.info("根据手机号获取微信用户id {}", result);
            return Optional.of(JSON.parseObject(result))
                    .map(p -> p.getString("userid"))
                    .orElse(null);
        } catch (Exception e) {
            log.error("根据手机号获取微信用户id异常 url {}, postData {}", url, phoneInfo, e);
        }
        return null;
    }


    /**
     * 根据邮箱获取userId
     * @param email
     * @return
     */
    public String getUserIdByEmail(String email) {
        String url = String.format(weixinHost, host, String.format(GET_USER_ID_BY_EMAIL, getToken()));
        httpClientHelper.setDefaultHeaders();
        String emailInfo = String.format("{\n" +
                "    \"email\":\"%s\",\n" +
                "    \"email_type\":2\n" +
                "}",email);
        log.info("get qiyeweixin userId by email url {}, postData {}", url, emailInfo);
        try {
            Response response = httpClientHelper.postResponse(url, emailInfo);
            if (response == null || response.body() == null) {
                log.info("get qiyeweixin userId by email result is null!");
                return null;
            }
            String result = response.body().string();
            log.info("get qiyeweixin userId by email result is {}", result);
            return Optional.of(JSON.parseObject(result))
                    .map(p -> p.getString("userid"))
                    .orElse(null);
        } catch (Exception e) {
            log.error("get qiyeweixin userId by email error, url {}, postData {}", url, emailInfo, e);
        }
        return null;
    }



    private String getUserInfo() {
        String urlinfo = String.format(GET_USER_INFO_BY_CODE, getToken(), code);
        if (isPrivateAuth()) {
            urlinfo = String.format(GET_USER_INFO_BY_CODE_NEW, getToken(), code);
        }
        String url = String.format(weixinHost, host, urlinfo);
        log.info("获取微信用户信息 url {}", url);
        httpClientHelper.setDefaultHeaders();
        try {
            Response response = httpClientHelper.getResponse(url);
            if (null != response.body() && response.code() == 200) {
                String result = Objects.requireNonNull(response.body()).string();
                log.info("微信接口返回值 url {} result {}", url, result);
                result = result.replaceFirst("UserId", "userid");
                return result;
            }
        } catch (Exception e) {
            log.error("企业微信获取UID异常 url {}", url, e);
        }
        return null;
    }

    private String getToken() {
        // 拼装token的缓存key
        String accessTokenKey = PlatformTypeEnum.WEIXIN_WORK.getType() + CommonConstants.SPLIT_UNDERLINE + CommonConstants.ACCESS_TOKEN + CommonConstants.SPLIT_UNDERLINE + corpid;
        if (cacheHelper.get(accessTokenKey) != null) {
            return cacheHelper.get(accessTokenKey);
        }
        String urlinfo = String.format(GET_TOKEN, corpid, corpsecret);
        String url = String.format(weixinHost, host, urlinfo);
        log.info("access token url {}", url);
        httpClientHelper.setDefaultHeaders();
        try {
            Response response = httpClientHelper.getResponse(url);
            log.info("access token result {}",JSONObject.toJSONString(response.body()));
            if (null != response.body() && response.code() == 200) {
                String result = Objects.requireNonNull(response.body()).string();
                String accessToken = JSON.parseObject(result).get("access_token").toString();
                cacheHelper.put(accessTokenKey, accessToken);
                return accessToken;
            }
        } catch (Exception e) {
            log.error("企业微信获取token异常 url {}", url, e);
        }
        return null;
    }

    /**
     * 获取当前部门下子部门列表信息， id若为空，则表示获取所有部门信息
     * @param id 父部门id
     * @return
     */
    public List<QiyeWeixinDepartment> getDepartments(Integer id) {

        String token = getToken();

        String urlInfo = Objects.isNull(id) ?
                String.format(GET_ALL_DEPARTMENT, token) :
                String.format(GET_CHILD_DEPARTMENT_BY_ID, token, id)
                ;

        String url = String.format(weixinHost, host, urlInfo);

        httpClientHelper.setDefaultHeaders();
        log.info("获取企业微信的部门列表， url = {}", url);

        try {
            Response response = httpClientHelper.getResponse(url);
            if (response == null || response.body() == null) {
                log.error("获取企业微信的部门列表异常，返回异常 url = {}", url);
                return Collections.emptyList();
            }

            String result = response.body().string();
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.getInteger("errcode") != 0) {
                log.error("获取企业微信的部门列表异常，请求结果出错 url = {}， 错误码 errcode = {} ", url, jsonObject.getInteger("errcode"));
                return Collections.emptyList();
            }

            log.info("获取企业微信的部门列表结果 result = {}", result);

            return JSONArray.parseArray(jsonObject.getString("department"), QiyeWeixinDepartment.class);

        } catch (Exception e) {
            log.error("获取企业微信的部门列表异常 url = {}, exception = {}", url, e);
        }
        return Collections.emptyList();
    }

    /**
     * 递归地获取部门下包含子部门的成员详细信息
     * @param departmentId 部门id
     * @return
     */
    public List<QiyeWeixinUser> getUsers(int departmentId, boolean isRecursion) {

        String token = getToken();
        String getUserUrl;
        if(isRecursion){
            getUserUrl = GET_USER_INFO_BY_DEPARTMENT_ID_IS_RECURSION;
        }else {
            getUserUrl = GET_USER_INFO_BY_DEPARTMENT_ID;
        }

        String urlInfo = String.format(getUserUrl, token, departmentId);

        String url = String.format(weixinHost, host, urlInfo);

        httpClientHelper.setDefaultHeaders();
        log.info("查询部门成员详情， url = {}, 部门id =  {}", url, departmentId);

        try {
            Response response = httpClientHelper.getResponse(url);
            if (response == null || response.body() == null) {
                log.error("查询部门成员详情异常，返回异常 url = {}, departmentId = {}", url, departmentId);
                return Collections.emptyList();
            }

            String result = response.body().string();
            JSONObject jsonObject = JSONObject.parseObject(result);
            if (jsonObject.getInteger("errcode") != 0) {
                log.error("查询部门成员详情异常，请求结果出错 url = {}，departmentId = {}, 错误码 errcode = {} ", url, departmentId,
                        jsonObject.getInteger("errcode"));
                return Collections.emptyList();
            }


            List<QiyeWeixinUser> userlist = JSONArray.parseArray(jsonObject.getString("userlist"), QiyeWeixinUser.class);
            log.info("查询部门成员详情成功，结果为:{}, 查询到的人数为 {}, url = {}, 部门id =  {}", JSON.toJSONString(userlist), userlist.size(), url, departmentId);
            return userlist;

        } catch (Exception e) {
            log.error("查询部门成员详情异常 url = {}, departmentId = {}, exception = {}", url, departmentId, e);
        }
        return Collections.emptyList();

    }


    /**
     * 发送微信消息
     * @param msg
     * @return
     */
    public boolean sendMessage(YfyMessage msg) {

        String token = getToken();
        String url = String.format(weixinHost, host, String.format(SEND_MESSAGE, token));

        httpClientHelper.setDefaultHeaders();
        Map<String, Object> msgtExtcardMap = new HashMap<>();
        msgtExtcardMap.put("title", msg.getTitle());
        msgtExtcardMap.put("description", msg.getContent());
        msgtExtcardMap.put( "url", msg.getH5Url());
        String textcardStr = JSONUtil.toJsonStr(msgtExtcardMap);
        String sendMsg = String.format("{\"touser\":\"%s\",\"msgtype\":\"textcard\",\"agentid\":%s,\"textcard\":%s}",
                                       msg.getReceivers(), agentId,  textcardStr);



        log.info("发送微信消息 url {}, postData {} msg {}", url, sendMsg, msg);
        try {
            Response response = httpClientHelper.postResponse(url, sendMsg);
            if (response == null || response.body() == null) {
                return false;
            }
            String result = response.body().string();
            log.info("发送微信消息返回值 {}", result);
            if (result.contains("ok")) {
                return true;
            }

        } catch (Exception e) {
            log.error("发送微信消息异常 url {}, postData {}", url, msg, e);
        }
        return false;
    }

    /**
     * 判断是否为手动授权
     * @return
     */
    private Boolean isPrivateAuth() {
        if (StringUtils.isNotBlank(authorizationMethod)
                && authorizationMethod.equals(CommonConstants.SNSAPI_PRIVATEINFO)) {
            return true;
        }
        return false;
    }
}