package com.fangcloud.thirdpartplatform.helper;

import com.fangcloud.thirdpartplatform.db.model.BaseEntity;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public class DBHelper {
    private DBHelper() {
    }

    /**
     * 返回settings中二进制表示的某一位
     *
     * @param settings 10010,传入的是10进制的18
     * @param index    1,表示取第"1"位,我们用0表示第一位
     * @return bitValue 1, 因为从右往左数第"1"位为1,所以返回1
     */
    public static int getBitmask(long settings, int index) {
        int bitValue = 0; // if index smaller than 0, we return 0 as default bit value
        if (index >= 0) {
            bitValue = (settings & (1L << index)) > 0 ? 1 : 0;
        }
        return bitValue;
    }

    /**
     * 设置settings中二进制表示的某一位的值
     *
     * @param settings 10010,传入的是10进制的18
     * @param index    1,表示设置第"1"位,我们用0表示第一位
     * @param value    0,表示设置为0
     * @return settings 10000,返回十进制的16
     */
    public static long setBitmask(long settings, int index, int value) {
        if (index >= 0) {
            long mask = 1 << index;
            if (value == 1) {
                settings = settings | mask;
            }
            else if (value == 0) {
                settings = settings & (~mask);
            }
        }
        return settings;
    }

    /**
     * 用boolean设置settings中某一位的值
     *
     * @param settings
     * @param index
     * @param value
     * @return
     */
    public static long setBooleanBitmask(long settings, int index, boolean value) {
        return setBitmask(settings, index, value ? 1 : 0);
    }

    /**
     * 得到settings中某一位的值,以boolean返回
     *
     * @param settings
     * @param index
     * @return
     */
    public static boolean getBooleanBitmask(long settings, int index) {
        return getBitmask(settings, index) == 1;
    }

    /**
     * 设置多个组合在一起的bitmask的值
     *
     * @param settings
     * @param indexes
     * @param value
     * @return
     */
    public static long setMultiBitmask(long settings, int[] indexes, long value) {
        for (int i = 0; i < indexes.length; i++) {
            int index = indexes[i];
            int bitValue = (int) ((value >> i) & 1);
            settings = setBitmask(settings, index, bitValue);
        }
        return settings;
    }

    /**
     * 得到组合bitmask实际的值
     *
     * @param settings
     * @param indexes
     * @return
     */
    public static long getMultiBitmask(long settings, int[] indexes) {
        long value = 0;
        for (int i = 0; i < indexes.length; i++) {
            int index = indexes[i];
            value += (1 << i) * getBitmask(settings, index);
        }
        return value;
    }

    /**
     * 将DB entity的list转换成id => entity的hashmap
     *
     * @param entities
     * @param <T>
     * @return
     */
    public static <T extends BaseEntity> Map<Long, T> indexEntitiesById(List<T> entities) {
        Map<Long, T> results = new HashMap<>();
        if (entities != null && !entities.isEmpty()) {
            results = entities.stream().collect(Collectors.toMap(T::getId, Function.identity()));
        }
        return results;
    }


    public static boolean sqlValidate(String str) {
        str = str.toLowerCase();
        String badStr = "select|update|and|or|delete|insert|truncate|char|into|substr|ascii|declare|exec|count|master|into|drop|execute|table";
        String[] badStrs = badStr.split("\\|");
        for (int i = 0; i < badStrs.length; i++) {
            if (str.indexOf(badStrs[i]) >= 0) {
                return true;
            }
        }
        return false;
    }
}
