package com.fangcloud.thirdpartplatform.helper;

import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class KiwiinstClientHelper {


    @Resource
    private HttpClientHelper httpClientHelper;

    static final String CREATE_WORKFLOW_URI = "/api/selfdevrest/WorkflowRestApiToOtherSystem/createWorkflow";
    static final String CREATE_WORKFLOW_TOKEN = "CA4CB750A59FBAAEAE7378BC4FFEB6CE";



    /**
     * 创建流程
     *
     * @param body
     * @param enterpriseId
     * @return
     */
    public String createWorkflow(String body, long enterpriseId) {
        Map<String, String> headers = new HashMap<>();
        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId + "");
        if (CollectionUtils.isEmpty(configMap)) {
            return null;
        }
        String oaHost = configMap.get(PlatformGlobalConfigKeyEnum.KIWIINST_OA_HOST.getKey());
        String accessCode = configMap.get(PlatformGlobalConfigKeyEnum.KIWIINST_OA_ACCESS_CODE.getKey());

        headers.put("accessCode", accessCode);
        httpClientHelper.setHeaders(headers);

        try {
            Response response = httpClientHelper.postResponse(oaHost + CREATE_WORKFLOW_URI, body);
            if (response.body() == null) {
                log.info("createWorkflow response is null !");
                return null;
            }
            String result = response.body().string();
            log.info("createWorkflow  result {}", result);
            return result;
        } catch (Exception e) {
            log.error("createWorkflow error !", e);
        }
        return null;
    }
}
