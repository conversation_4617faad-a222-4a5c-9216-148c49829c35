package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.common.exception.runtime.ExecuteException;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.constant.sync.DataTypeConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.runtime.Executor;
import com.fangcloud.thirdpartplatform.runtime.ExecutorContext;
import com.fangcloud.thirdpartplatform.utils.SyncStringUtils;
import com.fangcloud.thirdpartplatform.utils.XMLUtils;
import com.google.common.collect.Lists;
import com.jayway.jsonpath.JsonPath;
import com.ql.util.express.DefaultContext;
import com.ql.util.express.ExpressRunner;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.enums.DepartmentStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.sync.common.contants.SyncConstant.GB;

@Component
@Slf4j
public class APIHelper {
    private final static String OAUTH_RESULT = "oauth";
    private final static String API_RESULT = "api";
    private final static String SCRIPT = "script";
    private final static String JSON_PATH = "jsonPath";
    private final static String CODE_SCRIPT = "code_script";
    private final static String SPILT_KEY = "::";
    private final static String JSON_PATH_PREFIX = "$.";
    private final static String XML_PATH_PREFIX = "/";
    public final static String HTTP = "HTTP";
    public final static String HTTPS = "HTTPS";

    @Autowired
    private Executor executor;

    public final static String OAUTH_HEADER_RESULT = "oauth_header";

    public boolean isFetchParamFromOAuthResult (String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        if (value.startsWith(OAUTH_RESULT) && value.split(SPILT_KEY).length > 1) return true;
        return false;
    }

    public boolean isFetchParamFromAPIResult (String value) {
        if (StringUtils.isBlank(value)) {
            return false;
        }
        if (value.startsWith(API_RESULT) && value.split(SPILT_KEY).length > 1) return true;
        return false;
    }

    public boolean isFetchParamFromResult(String value) {
        if (isFetchParamFromAPIResult(value)) {
            return true;
        }
        if (isFetchParamFromOAuthResult(value)){
            return true;
        }
        return false;
    }

    public static void main(String[] args) {
        String script = "s.split(\"\\\\|\")";
        Object ss = new APIHelper().executeScript("s", "|4395|5029|", script);
        List convert = DataTypeConstants.convert(ss, List.class);
        List<String> strs = (List<String>)(List)convert;
        String a = "[\n" +
                "    {\n" +
                "        \"userID\":402638889,\n" +
                "        \"pObjectDataID\":\"4bb8885d-0b40-4662-93e4-7c540ef4a87e\",\n" +
                "        \"oIdDepartment\":1177934,\n" +
                "        \"startDate\":\"2022-08-31T00:00:00\",\n" +
                "        \"stopDate\":\"9999-12-31T00:00:00\",\n" +
                "        \"jobNumber\":\"V2022204\",\n" +
                "        \"entryDate\":\"2022-08-31T00:00:00\",\n" +
                "        \"lastWorkDate\":null,\n" +
                "        \"regularizationDate\":null,\n" +
                "        \"probation\":null,\n" +
                "        \"order\":null,\n" +
                "        \"employType\":0,\n" +
                "        \"serviceType\":0,\n" +
                "        \"serviceStatus\":0,\n" +
                "        \"approvalStatus\":4,\n" +
                "        \"employmentSource\":null,\n" +
                "        \"employmentForm\":null,\n" +
                "        \"isCharge\":\"0\",\n" +
                "        \"oIdJobPost\":null,\n" +
                "        \"oIdJobSequence\":null,\n" +
                "        \"oIdProfessionalLine\":null,\n" +
                "        \"oIdJobPosition\":null,\n" +
                "        \"oIdJobLevel\":null,\n" +
                "        \"oidJobGrade\":null,\n" +
                "        \"place\":null,\n" +
                "        \"employeeStatus\":\"3\",\n" +
                "        \"employmentType\":null,\n" +
                "        \"employmentChangeID\":\"779f2148-2bd9-4be6-af12-4560c23f5c79\",\n" +
                "        \"changedStatus\":null,\n" +
                "        \"pOIdEmpAdmin\":null,\n" +
                "        \"pOIdEmpReserve2\":null,\n" +
                "        \"businessTypeOID\":\"1\",\n" +
                "        \"changeTypeOID\":\"1\",\n" +
                "        \"entryStatus\":null,\n" +
                "        \"isCurrentRecord\":true,\n" +
                "        \"workYearBefore\":null,\n" +
                "        \"workYearGroupBefore\":null,\n" +
                "        \"workYearCompanyBefore\":0,\n" +
                "        \"workYearTotal\":null,\n" +
                "        \"workYearGroupTotal\":null,\n" +
                "        \"workYearCompanyTotal\":0.1,\n" +
                "        \"oIdOrganization\":*********,\n" +
                "        \"whereabouts\":null,\n" +
                "        \"blackStaffDesc\":null,\n" +
                "        \"blackListAddReason\":null,\n" +
                "        \"transitionTypeOID\":null,\n" +
                "        \"changeReason\":null,\n" +
                "        \"probationResult\":null,\n" +
                "        \"probationActualStopDate\":null,\n" +
                "        \"probationStartDate\":null,\n" +
                "        \"probationStopDate\":null,\n" +
                "        \"isHaveProbation\":\"0\",\n" +
                "        \"remarks\":null,\n" +
                "        \"addOrNotBlackList\":null,\n" +
                "        \"businessModifiedBy\":*********,\n" +
                "        \"businessModifiedTime\":\"2022-11-07T11:35:31\",\n" +
                "        \"traineeStartDate\":null,\n" +
                "        \"objectId\":\"72dc78a1-0d19-4272-ad58-d1cb3fdb60f1\",\n" +
                "        \"customProperties\":null,\n" +
                "        \"translateProperties\":null,\n" +
                "        \"createdBy\":*********,\n" +
                "        \"createdTime\":\"2022-11-07T11:35:31\",\n" +
                "        \"modifiedBy\":10000,\n" +
                "        \"modifiedTime\":\"2022-11-07T11:35:37\",\n" +
                "        \"stdIsDeleted\":false\n" +
                "    },\n" +
                "    {\n" +
                "        \"oIdDepartment\":222222\n" +
                "    }\n" +
                "]";
        Object extract = JSONPath.extract(a, "$[*].oIdDepartment");


        List<String> departmentIds = (List<String>)(List)extract;


        JSONArray jsonArray = (JSONArray) JSONArray.parse(a);
        jsonArray.getJSONObject(0).getString("");
        List<String> collect = strs.stream().filter(s -> s != null && !s.trim().equals(StringUtils.EMPTY)).collect(Collectors.toList());
        System.out.println();
    }

    public String fetchValueFromScript(String value, String jsonOrXml) {
        if (!isFetchParamFromResult(value)) {
            return value;
        }
        String[] split = value.split(SPILT_KEY);
        String script = split[1];
        if (script.startsWith(JSON_PATH_PREFIX)) {
            Object parse = JSON.parse(jsonOrXml);
            Object eval = JsonPath.read(parse.toString(), script);
            //如果结果为list,转换成list取下标为0的值
            if (eval instanceof List) {
                eval = ((List<?>) eval).get(0).toString();
            }
            return eval == null ? null : eval.toString();
        } else if (script.startsWith(XML_PATH_PREFIX)) {
            return XMLUtils.reade(jsonOrXml, script);
        } else {
            return value;
        }
    }

    public List<YfyDepartment> buildDept(JSONArray values, APIConfigValueBox apiConfigValueBox) {
        if (CollectionUtils.isEmpty(values) || apiConfigValueBox == null) {
            return null;
        }
        APIConfigValueBox.APIConfig apiConfig = apiConfigValueBox.getApiConfig();
        List<APIResultConfigDto> resultConfigs = apiConfig.getResultConfig();
        List<Map<String, Object>> mappingValues = mapping(values, resultConfigs);
        log.info("dept mappingValues:{}", JSON.toJSONString(mappingValues));
        DepConfigDto depConfigDto = apiConfigValueBox.getDepConfigDto();
        List<YfyDepartment> depts = buildDept(apiConfigValueBox.getDeptSpace().longValue(), mappingValues, depConfigDto);
        return depts;
    }

    public List<YfyDepartment> buildDept(Long deptSpace , List<Map<String, Object>> mappingValues, DepConfigDto depConfigDto) {
        List<YfyDepartment> depts = new ArrayList<>();
        YfyDepartment dept;
        for (Map<String, Object> value : mappingValues) {
            if (value == null) {
                continue;
            }
            dept = new YfyDepartment();
            Object idObject = value.get(SyncTaskConstants.DEPARTMENT_PARAMETER_ID);
            String id = Objects.isNull(idObject) ? null : String.valueOf(idObject);
            dept.setId(id);
            Object nameObject = value.get(SyncTaskConstants.DEPARTMENT_PARAMETER_NAME);
            String name = Objects.isNull(nameObject) ? null : String.valueOf(nameObject);
            dept.setName(SyncStringUtils.formatString(name));
            Object isDeleteObject = value.get(SyncTaskConstants.DEPARTMENT_PARAMETER_IS_DELETE);
            String status = Objects.isNull(isDeleteObject) ? DepartmentStatusEnum.SAVE.getCode() : "2";
            dept.setStatus(status);
            if (StringUtils.isNotBlank(status) && status.equals("2")) {
                if (dept.getName().length() > 27) {
                    dept.setName(SyncTaskConstants.DEPARTMENT_DISABLE_PREFIX + name.substring(0, 23) + "．．．");
                } else {
                    dept.setName(SyncTaskConstants.DEPARTMENT_DISABLE_PREFIX + dept.getName());
                }
            }
            Object directorIdObject = value.get(SyncTaskConstants.DEPARTMENT_PARAMETER_DIRECTOR_ID);
            String directorId = Objects.isNull(directorIdObject) ? null : String.valueOf(directorIdObject);
            dept.setDirectorId(directorId);

            Object parentIdObject = value.get(SyncTaskConstants.DEPARTMENT_PARAMETER_PARENT_ID);
            String parentId = Objects.isNull(parentIdObject) ? null : String.valueOf(parentIdObject);
            dept.setParentId(parentId);
            Object orderObject = value.get(SyncTaskConstants.DEPARTMENT_PARAMETER_ORDER);
            Long order = ObjectUtils.isEmpty(orderObject) ? 0L : Long.parseLong(orderObject.toString());
            dept.setOrder(order);

            // 校验数据库数据
            if (StringUtils.isEmpty(id) || StringUtils.isEmpty(name) ) {
                continue;
            }
            dept.setSpaceTotal(deptSpace);
            dept.setCollabAutoAccepted(depConfigDto.isCollabAutoAccepted());
            dept.setPublicFolder(depConfigDto.isPublicFolder());
            dept.setCreateTime(new Date());
            dept.setUpdateTime(new Date());
            depts.add(dept);
        }
        return depts;
    }

    public List<YfyUser> buildUser(JSONArray values, APIConfigValueBox apiConfigValueBox) {
        if (CollectionUtils.isEmpty(values) || apiConfigValueBox == null) {
            return null;
        }
        APIConfigValueBox.APIConfig apiConfig = apiConfigValueBox.getApiConfig();
        List<APIResultConfigDto> resultConfigs = apiConfig.getResultConfig();
        List<Map<String, Object>> mappingValues = mapping(values, resultConfigs);
        UserConfigDto userConfigDto = apiConfigValueBox.getUserConfigDto();
        List<YfyUser> users = buildUser(apiConfigValueBox.getUserSpace().longValue(), mappingValues, userConfigDto);
        return users;
    }

    public List<YfyUser> buildUser(Long userSpace, List<Map<String, Object>> mappingValues , UserConfigDto userConfigDto) {
        List<YfyUser> users = new ArrayList<>();
        YfyUser user;
        log.info("mappingValues {}", JSON.toJSONString(mappingValues));
        for (Map<String, Object> value : mappingValues) {
            if (CollectionUtils.isEmpty(value)) {
                continue;
            }
            user = new YfyUser();
            Object phoneObject = value.get(SyncTaskConstants.USER_PARAMETER_PHONE);
            String phone = Objects.isNull(phoneObject) ? null : String.valueOf(phoneObject);
            if (StringUtils.isNotBlank(phone) && phone.contains("+")) {
                if (phone.contains(SyncTaskConstants.USER_PHONE_CN)) {
                    phone = phone.substring(Math.max(0, phone.length() - 11));
                } else {
                    phone = null;
                }
            }
            user.setPhone(phone);
            Object departmentIdObject = value.get(SyncTaskConstants.USER_PARAMETER_DEPARTMENT_ID);
            String departmentId = Objects.isNull(departmentIdObject) ? null : String.valueOf(departmentIdObject);
            if (departmentId != null) {
                user.setDepartmentIds(Lists.newArrayList(departmentId));
            } else {
                Object departmentIdsObj= value.get(SyncTaskConstants.USER_PARAMETER_DEPARTMENT_IDS);
                if (departmentIdsObj != null) {
                    List<String> departmentIds = new ArrayList<>();
                    if(departmentIdsObj instanceof JSONArray){
                        for (Object o : (JSONArray) departmentIdsObj) {
                            departmentIds.add(String.valueOf(o));
                        }
                    }else {
                        departmentIds = (List<String>)(List)departmentIdsObj;
                    }

                    List<String> collect = departmentIds.stream().filter(s -> s != null && !s.trim().equals(StringUtils.EMPTY)).collect(Collectors.toList());
                    user.setDepartmentIds(collect);
                }
            }
            Object idObject = value.get(SyncTaskConstants.USER_PARAMETER_ID);
            String id = Objects.isNull(idObject) ? null : String.valueOf(idObject);
            user.setId(id);
            Object fullNameObject = value.get(SyncTaskConstants.USER_PARAMETER_FULL_NAME);
            String fullName = Objects.isNull(fullNameObject) ? null : String.valueOf(fullNameObject);
            user.setFullName(SyncStringUtils.formatString(fullName));
            if(StringUtils.isEmpty(id) || StringUtils.isEmpty(fullName)){
                continue;
            }
            Object emailObject = value.get(SyncTaskConstants.USER_PARAMETER_EMAIL);
            String email = Objects.isNull(emailObject) ? null : String.valueOf(emailObject);
            if(StringUtils.isNotEmpty(email)){
                user.setEmail(email);
            }else {
                String emailSuffix = userConfigDto.getEmailSuffix();
                if(StringUtils.isNotBlank(emailSuffix)){
                    user.setEmail(id + "@" +emailSuffix);
                } else{
                    log.info("email is null! user info {}", JSON.toJSONString(value));
                }
            }
            if (userConfigDto.isActivate()) {
                user.setStatus("1");
            } else {
                user.setStatus("3");
            }
            if (value.get(SyncTaskConstants.USER_STATUS) != null) {
                user.setStatus(value.get(SyncTaskConstants.USER_STATUS).toString());
            }
            Object orderObject = value.get(SyncTaskConstants.USER_PARAMETER_ORDER);
            Long order = ObjectUtils.isEmpty(orderObject) ? null : Long.parseLong(orderObject.toString());
            user.setOrder(order);
            user.setCreateTime(new Date());
            user.setSpaceTotal(userSpace * GB);
            user.setForced(true);

            Object isDisableObject = value.get(SyncTaskConstants.USER_IS_DISABLE);
            if(!Objects.isNull(isDisableObject)){
                user.setDisable(true);
                log.info("need disable user info :{}", JSON.toJSONString(user));
            }
            Object customPasswordObject = value.get(SyncTaskConstants.USER_CUSTOM_PASSWORD);
            if (!Objects.isNull(customPasswordObject)) {
                user.setCustomPassword(String.valueOf(customPasswordObject));
            }
            Object groupIdObject = value.get(SyncTaskConstants.USER_PARAMETER_GROUP_ID);
            String groupId = Objects.isNull(groupIdObject) ? null : String.valueOf(groupIdObject);
            if (groupId != null) {
                user.setGroupIds(Lists.newArrayList(groupId));
            } else {
                Object groupIdsObject= value.get(SyncTaskConstants.USER_PARAMETER_GROUP_IDS);
                if (groupIdsObject != null) {
                    List<String> groupIds = new ArrayList<>();
                    if(groupIdsObject instanceof JSONArray){
                        for (Object o : (JSONArray) groupIdsObject) {
                            groupIds.add(String.valueOf(o));
                        }
                    }else {
                        groupIds = (List<String>)(List)groupIdsObject;
                    }

                    List<String> collect = groupIds.stream().filter(s -> s != null && !s.trim().equals(StringUtils.EMPTY)).collect(Collectors.toList());
                    user.setGroupIds(collect);
                }
            }
            users.add(user);
        }
        return users;
    }

    private List<Map<String, Object>> mapping(JSONArray values, List<APIResultConfigDto> resultConfigs) {
        List<Map<String, Object>> mappingValues = new ArrayList<>();
        for (Object value : values) {
            Map<String, Object> mapping = null;
            if (value instanceof JSONObject) {
                JSONObject obj = (JSONObject) value;
                mapping = mapping(obj, resultConfigs);
            } else if (value instanceof List) {
                List<Object> obj = (List<Object>) value;
                Map<String, Object> valueMap = new HashMap<>();
                for (int i = 0; i < obj.size(); i++) {
                    valueMap.put(String.valueOf(i), obj.get(i));
                }
                mapping = mapping(valueMap, resultConfigs);
            }
            if (!CollectionUtils.isEmpty(mapping)) {
                mappingValues.add(mapping);
            }
        }
        return mappingValues;
    }

    public Map<String, Object> mapping(Map<String, Object> values, List<APIResultConfigDto> apiResultConfigs) {
        if (values == null || apiResultConfigs == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        for (APIResultConfigDto resultConfig : apiResultConfigs) {
            if (resultConfig == null) {
                continue;
            }
            String key = resultConfig.getName();
            if (StringUtils.isNotBlank(resultConfig.getMappingName())) {
                key = resultConfig.getMappingName();
            }

            //支持取多个参数名
            Object value = Arrays.stream(resultConfig.getName().split(","))
                    .map(values::get)
                    .filter(v -> v != null)
                    .findFirst()
                    .orElse(null);

            if (value == null){
                if (key.equals(SyncTaskConstants.USER_PARAMETER_EMAIL)){
                    result.put(key,value);
                    continue;
                }
            }
            String dataType = resultConfig.getDataType();

            Object convert = DataTypeConstants.convert(value, dataType);
            convert = getValueByFormat(key, dataType, convert, resultConfig.getFormat());
            if (StringUtils.isNotBlank(resultConfig.getRuleScript()) && !(key.equals(SyncTaskConstants.DEPARTMENT_PARAMETER_IS_TOP))) {
                // 规则过滤，如果满足则不处理该条数据，也就是过滤掉
                  if (checkRule(key, convert, resultConfig.getRuleScript())) {
                    result.clear();
                    break;
                }
            }
            result.put(key, convert);
             if (key.equals(SyncTaskConstants.DEPARTMENT_PARAMETER_IS_TOP)) {
                if (checkRule(key, convert, resultConfig.getRuleScript())) {
                    result.put(SyncTaskConstants.DEPARTMENT_PARAMETER_PARENT_ID, "");
                }
            }
        }
        return result;
    }

    public Object getValueByFormat(String key,String dataType, Object value, String format) {
        Object result = null;
        if (format != null && !format.trim().equals(StringUtils.EMPTY)) {
            if (format.startsWith(SCRIPT) && format.split(SPILT_KEY).length > 1) {
                result = executeScript(key, value, format.split(SPILT_KEY)[1]);
            } else if(format.startsWith(JSON_PATH) && format.split(SPILT_KEY).length > 1){
                result =  JSONPath.extract(value.toString(), format.split(SPILT_KEY)[1]);
            } else if (DataTypeConstants.getClassByDataType(dataType) == String.class) {
                result = String.format(format, value);
            } else if (DataTypeConstants.getClassByDataType(dataType) == Date.class) {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                result = sdf.format(value);
            } else if (format.startsWith(CODE_SCRIPT) && format.split(SPILT_KEY).length > 1){
                try {
                    result = executeContext(URLDecoder.decode(format.split(SPILT_KEY)[1], "UTF-8"), value);
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }
        } else {
            result = value;
        }
        return result;
    }

    public Boolean checkRuleFromScript(String script, Object value, String express) {
        String key;
        if (script != null && script.trim().startsWith(JSON_PATH_PREFIX)) {
            String[] split = script.split("\\.");
            key = split[split.length - 1];
        } else if (script != null && script.trim().startsWith(XML_PATH_PREFIX)) {
            String[] split = script.split(XML_PATH_PREFIX);
            key = split[split.length - 1];
        } else {
            key = script;
        }
        return checkRule(key, value, express);
    }


    public Boolean checkRule(String key, Object value, String express) {
        Object r = executeScript(key, value, express);
        return Boolean.valueOf(r.toString());
    }

    private Object executeScript(String key, Object value, String express) {
        try {
            ExpressRunner runner = new ExpressRunner();
            DefaultContext<String, Object> context = new DefaultContext<>();
            context.put(key, value);
            Object r = runner.execute(express, context, null, true, false);
            return r;
        } catch (Exception e) {
            throw new ParamException("规则解析异常。 key:" + key + "value:" + value + "express:" + express + "异常信息:" + e.getMessage());
        }
    }

    public Object executeContext(String code, Object params) {
        ExecutorContext executeContext = new ExecutorContext();
        executeContext.setCode(code);
        executeContext.setMethodName("execute");
        executeContext.setParams(Lists.newArrayList(params));
        executeContext.setDebug(true);
        Object obj = null;
        try {
            obj = executor.execute(executeContext);
        } catch (ExecuteException e) {
            e.printStackTrace();
        }
        return obj;
    }
}
