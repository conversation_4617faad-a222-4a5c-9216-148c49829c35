package com.fangcloud.thirdpartplatform.helper;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DateHelper {
    private static final SimpleDateFormat FULL_TIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 把以秒为单位的时间戳转化为"2016-04-11T14:41:51Z"格式
     *
     * @param time
     * @return
     */
    public static String transformTimestampToFullTime(long time) {
        return FULL_TIME_FORMAT.format(new Date(time * 1000L));
    }

    /**
     * 得到当前的unix时间戳,以秒为单位
     *
     * @return
     */
    public static long getCurrentTimeStamp() {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * 得到当前的unix时间戳,以ms为单位
     *
     * @return
     */
    public static long getCurrentTimeStampInMills() {
        return System.currentTimeMillis();
    }



    /**
     * 将以秒为单位的时间戳转化为"yyyy-MM-dd"格式
     *
     * @param time
     * @return
     */
    public static String transformTimeStampToDate(long time) {
        return DATE_FORMAT.format(new Date(time * 1000L));
    }

    /**
     * 将秒为单位的时间戳转化成天数
     *
     * @param time
     * @return
     */
    public static long transformTimeStampToDay(long time) {
        long day = time / 60 / 60 / 24;
        return day;
    }

}
