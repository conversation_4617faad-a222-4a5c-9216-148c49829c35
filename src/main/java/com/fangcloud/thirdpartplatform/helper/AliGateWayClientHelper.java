package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.cloudapi.sdk.client.ApacheHttpClient;
import com.alibaba.cloudapi.sdk.constant.SdkConstant;
import com.alibaba.cloudapi.sdk.enums.HttpMethod;
import com.alibaba.cloudapi.sdk.enums.ParamPosition;
import com.alibaba.cloudapi.sdk.model.ApiRequest;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;
import com.fangcloud.thirdpartplatform.entity.input.AliGateWayInitParams;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class AliGateWayClientHelper extends ApacheHttpClient {

    private String path;
    private HttpMethod method;
    private String pageNum;
    private String pageSize;

    public AliGateWayClientHelper(AliGateWayInitParams params){
        HttpClientBuilderParams httpsParam = new HttpClientBuilderParams();
        httpsParam.setAppKey(params.getAppKey());
        httpsParam.setAppSecret(params.getAppSecret());
        httpsParam.setScheme(params.getScheme());
        httpsParam.setHost(params.getHost());
        path = params.getPath();
        method = params.getMethod();
        pageNum = params.getPageNum();
        pageSize = params.getPageSize();
        super.init(httpsParam);
    }


    public String ResultData() {
        ApiRequest request = new ApiRequest(method , path);
        request.addParam("pageNum" , pageNum , ParamPosition.QUERY , true);
        request.addParam("pageSize" , pageSize , ParamPosition.QUERY , true);
        return (new String(sendSyncRequest(request).getBody(), SdkConstant.CLOUDAPI_ENCODING));
    }
}