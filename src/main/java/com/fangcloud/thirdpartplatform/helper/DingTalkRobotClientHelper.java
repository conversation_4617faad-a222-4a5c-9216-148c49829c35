package com.fangcloud.thirdpartplatform.helper;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.entity.DingMsg;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

@Component
@Slf4j
public class DingTalkRobotClientHelper {

    @Resource
    private CustomNacosConfig customNacosConfig;

    private String requestUrlTmp = "https://oapi.dingtalk.com/robot/send?access_token=%s";


    public String formatMsg(String type, String appName, String msgString) {
        return String.format("[%s] \nApp: %s \nDate: %s \nMsg %s",type, appName, new Date(), msgString);
    }

    public void sendTextMsg(DingMsg msg) {

        if (customNacosConfig.getCustomDingdingDebug()){
            return;
        }
        DingTalkClient client = new DefaultDingTalkClient(String.format(requestUrlTmp, customNacosConfig.getCustomDingDingMsgToken()));
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent(msg.getContent());
        request.setText(text);

        try {
            OapiRobotSendResponse response = client.execute(request);
        } catch (Exception e) {
            log.error("send ding msg error ", e);
        }
    }

    public void sendLinkMsg(DingMsg msg) {

        if (customNacosConfig.getCustomDingdingDebug()){
            return;
        }
        DingTalkClient client = new DefaultDingTalkClient(String.format(requestUrlTmp, customNacosConfig.getCustomDingDingMsgToken()));
        OapiRobotSendRequest request = new OapiRobotSendRequest();

        request.setMsgtype("link");
        OapiRobotSendRequest.Link link = new OapiRobotSendRequest.Link();
        link.setMessageUrl(msg.getMsgLink());
        link.setPicUrl(msg.getImgLink());
        link.setTitle(msg.getTitle());
        link.setText(msg.getContent());
        request.setLink(link);

        try {
            OapiRobotSendResponse response = client.execute(request);
        } catch (Exception e) {
            log.error("send ding msg error ", e);
        }
    }

    public void sendMarkdownMsg(DingMsg msg) {

        if (customNacosConfig.getCustomDingdingDebug()){
            return;
        }
        DingTalkClient client = new DefaultDingTalkClient(String.format(requestUrlTmp, customNacosConfig.getCustomDingDingMsgToken()));
        OapiRobotSendRequest request = new OapiRobotSendRequest();

        request.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle(msg.getTitle());
        markdown.setText(msg.getContent());
        request.setMarkdown(markdown);

        try {
            OapiRobotSendResponse response = client.execute(request);
        } catch (Exception e) {
            log.error("send ding msg error ", e);
        }
    }

}
