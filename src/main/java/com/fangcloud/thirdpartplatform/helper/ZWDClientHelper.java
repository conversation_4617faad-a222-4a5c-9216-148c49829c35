package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.entity.input.DingTalkInitParams;
import com.fangcloud.thirdpartplatform.entity.input.MessageParams;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.sync.common.entity.dto.YfyMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;


@Slf4j
public class ZWDClientHelper {


    private HttpClientHelper httpClientHelper;

    public static final String GET_ZWD_ALL_DEPT = "GET_ZWD_ALL_DEPT_KEY";
    public static final String GET_ZWD_ROOT_DEPT = "GET_ZWD_ROOT_DEPT_KEY";
    private static final String NEXT_LEVEL_DEPT_CODE_LIST_API = "/mozi/organization/pageSubOrganizationCodes";
    private static final String LIST_DEPT_INFO_API = "/mozi/organization/listOrganizationsByCodes";
    private static final String LIST_EMPLOYEE_ACCOUNT_IDS = "/mozi/employee/listEmployeeAccountIds";
    private static final String GET_ZWD_USER = "/rpc/oauth2/dingtalk_app_user.json";
    private static final String GET_ZWD_TOKEN = "/gettoken.json";
    private static final String SEND_ZWD_MESSAGE = "/message/workNotification";
    private static final String GET_ZWD_USER_LIST = "/mozi/organization/pageOrganizationEmployeePositions";
    private static final String GET_ZWD_SCOPES_V2 = "/auth/scopesV2";

    private static final String METHOD_POST = "POST";
    private static final String METHOD_GET = "GET";


    private String tenantId;
    private String accessKey;
    private String secretKey;
    private String host;
    private String enterpriseId;

    public ZWDClientHelper(DingTalkInitParams params){
        this.tenantId = params.getCorpId();
        this.accessKey = params.getAppId();
        this.secretKey = params.getAppSecret();
        this.host = params.getHost();
        this.enterpriseId = params.getEnterpriseParams().getEnterpriseId();
        httpClientHelper = SpringHelper.getBean(HttpClientHelper.class);
//        httpClientHelper= new HttpClientHelper();
    }

    /**
     * 获取全量用户列表
     * @return
     */
    public JSONArray getAllUserList() {

        // 获取顶级部门code
        String rootDeptCode = getScopesV2();

        // 获取首层的部门code列表,分页获取用户信息只能请求前10000条数据，不能使用顶级部门id
        String nextLevelDeptCode = getNextLevelDeptCode(rootDeptCode, 1);
        List<String> deptCodeList = (List<String>)JSONPath.extract(nextLevelDeptCode, "$.content.data");

        JSONArray allUserJSONArray = new JSONArray();
        // 获取全量用户信息
        for (String deptCode : deptCodeList) {
            allUserJSONArray.addAll(getAllUserListByDeptCode(deptCode));
        }
        return allUserJSONArray;
    }

    /**
     * 根据部门code分页获取全量用户详情
     * @param deptCode
     * @return
     */
    private JSONArray getAllUserListByDeptCode(String deptCode) {
        JSONArray allUserJSONArray = new JSONArray();
        long totalSize = 1;
        int pageNo = 0;
        long currentSize = 0;
        while (totalSize > currentSize ){
            pageNo++;

            JSONObject jSONObject = getUserInfoList(deptCode, pageNo);
            JSONObject jSONObjectContent = null;
            if(jSONObject.getBoolean("success")){
                jSONObjectContent = jSONObject.getJSONObject("content");
                totalSize = jSONObjectContent.getLong("totalSize");
            }
            if(totalSize == 0 ){
                break;
            }
            if(totalSize > 0){
                allUserJSONArray.addAll(jSONObjectContent.getJSONArray("data"));
            }
            currentSize = allUserJSONArray.size();
        }
        return allUserJSONArray;
    }

    /**
     * 获取全量部门列表
     * @return
     * @param cacheMap
     */
    public JSONArray getAllDeptList(Map<String, Object> cacheMap) {
        // 从缓存中获取所有部门code
        Set<String> allDeptCodeSet = (Set<String>) cacheMap.get(GET_ZWD_ALL_DEPT);
        JSONArray jsonArray = new JSONArray();
        List<String> list = new ArrayList<>();
        list.addAll(allDeptCodeSet);
        int size = list.size();
        // 分批获取部门详情，接口最大支持每批100条
        if( size<= 100){
            jsonArray = getDeptInfo(list);
        }else {
            int start = 0;
            int end = 100;
            while (end <= size){
                List<String> subList = list.subList(start, end);
                jsonArray.addAll(getDeptInfo(subList));
                if(end == size){
                    break;
                }
                start = end;
                end = end + 100;
                end = end > size ? size : end;
            }
        }
        return jsonArray;
    }

    /**
     * 根据code列表获取全量部门详情
     * @param list
     * @return
     */
    public JSONArray getDeptInfo(List<String> list) {

        List<Object> listObj = new ArrayList<>();
        for (String s : list) {
            listObj.add(s);
        }
        Map<String, List<Object>> map = new HashMap<>();
        map.put("tenantId", Arrays.asList(this.tenantId));
        map.put("organizationCodes", listObj);
        String params = buildZWDParams(map);

        Headers headers = buildZWDHeader(METHOD_POST, LIST_DEPT_INFO_API, params);

        String url = this.host + LIST_DEPT_INFO_API;
        try {
            log.info("zwd get listOrganizationsByCodes url :{}", url);
            Response response = httpClientHelper.postResponse(url, headers, params, httpClientHelper.getTextPlain());
            String result = Objects.requireNonNull(response.body()).string();
            log.info("zwd get listOrganizationsByCodes result :{}", result);
            return (JSONArray) JSONPath.extract(result, "$.content.data");
        } catch (IOException e) {
            log.info("zwd get listOrganizationsByCodes result error!");
        }
        return new JSONArray();
    }


    /**
     * 获取顶级部门code和全量部门code
     * @return
     */
    public Map<String, Object> getAllDeptCodeSet(){
        Map<String, Object> cacheMap = new HashMap<>();
        Set<String> allDeptCodeSet = new HashSet<>();

        // 获取根层需要进行过滤
        String rootDeptCode = getScopesV2();
        // 分层获取所有部门id
        getNextLevelDeptCodeList(rootDeptCode, allDeptCodeSet);
        cacheMap.put(GET_ZWD_ROOT_DEPT, rootDeptCode);
        cacheMap.put(GET_ZWD_ALL_DEPT, allDeptCodeSet);
        return cacheMap;
    }

    /**
     * 获取租户的通讯录范围
     * @return
     */
    private String getScopesV2() {
        Map<String, List<Object>> map = new HashMap<>();
        map.put("tenantId", Arrays.asList(this.tenantId));

        String params = buildZWDParams(map);

        Headers headers = buildZWDHeader(METHOD_GET, GET_ZWD_SCOPES_V2, params);

        String url = this.host + GET_ZWD_SCOPES_V2 + "?" + params;
        try {
            log.info("zwd scopesV2 url :{}", url);
            Response response = httpClientHelper.getResponse(url, headers);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("zwd scopesV2 result :{}", result);
            return (String) JSONPath.extract(result, "$.content.deptVisibleScopes[0]");
        } catch (IOException e) {
            log.info("zwd scopesV2 result error!");
        }
        return null;
    }


    /**
     * 递归获取全量部门信息
     * @param deptCode
     * @param allDeptCodeSet
     */
    private void getNextLevelDeptCodeList(String deptCode, Set<String> allDeptCodeSet) {

        int totalSize = 1;
        int pageNo = 0;
        int currentSize = 0;
        List <String> list = new ArrayList<>();

        while (totalSize > currentSize ){
            pageNo++;
            String apiResult = getNextLevelDeptCode(deptCode, pageNo);
            totalSize = (int) JSONPath.extract(apiResult, "$.content.totalSize");
            if(StringUtils.isEmpty(apiResult)){
                continue;
            }
            if(totalSize == 0 ){
                break;
            }
            if(totalSize > 0){
                list.addAll((List<String>)JSONPath.extract(apiResult, "$.content.data"));
            }
            currentSize = list.size();
        }

        allDeptCodeSet.addAll(list);

        for (String code : list) {
            getNextLevelDeptCodeList(code, allDeptCodeSet);
        }
    }


    public JSONObject getUserInfoList(String deptCode, int pageNo) {
        Map<String, List<Object>> map = new HashMap<>();
        map.put("returnTotalSize", Arrays.asList("true"));
        map.put("pageSize", Arrays.asList("100"));
        map.put("employeeStatus", Arrays.asList("A"));
        map.put("organizationCode", Arrays.asList(deptCode));
        map.put("pageNo", Arrays.asList(pageNo));
        map.put("tenantId", Arrays.asList(this.tenantId));

        String params = buildZWDParams(map);

        Headers headers = buildZWDHeader(METHOD_POST, GET_ZWD_USER_LIST, params);

        String url = this.host + GET_ZWD_USER_LIST;
        try {
            log.info("zwd get pageOrganizationEmployeePositions url :{}", url);
            Response response = httpClientHelper.postResponse(url, headers, params, httpClientHelper.getTextPlain());
            String result = Objects.requireNonNull(response.body()).string();
            log.info("zwd get pageOrganizationEmployeePositions result :{}", result);
            return JSONObject.parseObject(result);
        } catch (IOException e) {
            log.info("zwd get pageOrganizationEmployeePositions result error!");
        }
        return null;
    }

    public String getNextLevelDeptCode(String deptCode, int pageNo){
        Map<String, List<Object>> map = new HashMap<>();
        map.put("tenantId", Arrays.asList(this.tenantId));
        map.put("organizationCode", Arrays.asList(deptCode));
        map.put("status", Arrays.asList("A"));
        map.put("pageSize", Arrays.asList(100));
        map.put("pageNo", Arrays.asList(pageNo));
        map.put("returnTotalSize", Arrays.asList(true));

        String params = buildZWDParams(map);

        Headers headers = buildZWDHeader(METHOD_POST, NEXT_LEVEL_DEPT_CODE_LIST_API, params);

        String url = this.host + NEXT_LEVEL_DEPT_CODE_LIST_API;
        try {
            log.info("zwd get pageSubOrganizationCodes url :{}", url);
            Response response = httpClientHelper.postResponse(url, headers, params, httpClientHelper.getTextPlain());
            String result = Objects.requireNonNull(response.body()).string();
            log.info("zwd get pageSubOrganizationCodes result :{}", result);
            return result;
        } catch (IOException e) {
            log.info("zwd get pageSubOrganizationCodes result error!");
        }
        return null;
    }

    /**
     * 构建政务钉请求参数
     * @param params
     * @return
     */
    private String buildZWDParams(Map<String, List<Object>> params) {

        List<String> ret = new ArrayList<>();
        if (!params.isEmpty()) {
            params.forEach((k, v) -> {
                v.forEach(value -> {
                    ret.add(String.format("%s=%s", k, value));
                });
            });
        }
        Collections.sort(ret);
        String join = String.join("&", ret);
        return join;
    }

    public void sendOAMessage(YfyMessage yfyMessage, MessageParams messageParams) {

        String oaMessage = "{\"msgtype\": \"oa\",\"oa\": {\"head\": {\"bgcolor\": \"FFBBBBBB\",\"text\": \"%s\"},\"body\": {\"title\": \"%s\",\"content\": \"%s\"}}}";
        String format = String.format(oaMessage,messageParams.getMessagePushTitle(), yfyMessage.getTitle().replace(messageParams.getMessagePushTitle(), ""), yfyMessage.getContent());

        Map<String, List<Object>> map = new HashMap<>();
        map.put("receiverIds", Arrays.asList(yfyMessage.getReceivers()));
        map.put("tenantId", Arrays.asList(this.tenantId));
        map.put("bizMsgId", Arrays.asList(UUID.randomUUID().toString() + System.currentTimeMillis()));
        map.put("msg", Arrays.asList(format));

        String params = buildZWDParams(map);

        Headers headers = buildZWDHeader(METHOD_POST, SEND_ZWD_MESSAGE, params);

        String url = this.host + SEND_ZWD_MESSAGE;
        try {
            log.info("zwd workNotification url :{}", url);
            Response response = httpClientHelper.postResponse(url, headers, params, httpClientHelper.getTextPlain());
            String result = Objects.requireNonNull(response.body()).string();
            log.info("zwd workNotification result :{}", result);
        } catch (IOException e) {
            log.info("zwd workNotification result error!");
        }
    }

    /**
     * 换取政务钉钉发消息用户id
     * @param receivers
     * @return
     */
    public String getZwdAccountIdList(String receivers){

        Map<String, List<Object>> map = new HashMap<>();
        map.put("tenantId", Arrays.asList(this.tenantId));
        map.put("employeeCodes", Arrays.asList(receivers));
        String params = buildZWDParams(map);

        Headers headers = buildZWDHeader(METHOD_POST, LIST_EMPLOYEE_ACCOUNT_IDS, params);

        String url = this.host + LIST_EMPLOYEE_ACCOUNT_IDS;
        try {
            log.info("zwd get listEmployeeAccountIds url :{}", url);
            Response response = httpClientHelper.postResponse(url, headers, params, httpClientHelper.getTextPlain());
            String result = Objects.requireNonNull(response.body()).string();
            log.info("zwd get listEmployeeAccountIds result :{}", result);
            return JSONPath.extract(result, "$.content.data[0].accountId") +"";
        } catch (IOException e) {
            log.info("zwd get listEmployeeAccountIds result error!");
        }
        return null;
    }

    public JSONObject getUserInfoByCode(String code) {

        String accessToken = getAccessToken();
        Map<String, List<Object>> map = new HashMap<>();
        map.put("auth_code", Arrays.asList(code));
        map.put("access_token", Arrays.asList(accessToken));

        String params = buildZWDParams(map);

        Headers headers = buildZWDHeader(METHOD_POST, GET_ZWD_USER, params);

        String url = this.host + GET_ZWD_USER;
        try {
            log.info("zwd get dingtalk_app_user url :{}", url);
            Response response = httpClientHelper.postResponse(url, headers, params, httpClientHelper.getTextPlain());
            String result = Objects.requireNonNull(response.body()).string();
            log.info("zwd get dingtalk_app_user result :{}", result);
            return (JSONObject) JSONPath.extract(result, "$.content.data");
        } catch (IOException e) {
            log.info("zwd get dingtalk_app_user result error!");
        }
        return null;
    }

    public String getAccessToken() {
        Map<String, List<Object>> map = new HashMap<>();
        map.put("appkey", Arrays.asList(this.accessKey));
        map.put("appsecret", Arrays.asList(this.secretKey));

        String params = buildZWDParams(map);

        Headers headers = buildZWDHeader(METHOD_GET, GET_ZWD_TOKEN, params);

        String url = this.host + GET_ZWD_TOKEN + "?" + params;
        try {
            log.info("zwd gettoken url :{}", url);
            Response response = httpClientHelper.getResponse(url, headers);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("zwd gettoken result :{}", result);
            return (String) JSONPath.extract(result, "$.content.data.accessToken");
        } catch (IOException e) {
            log.info("zwd gettoken result error!");
        }
        return null;
    }

    /**
     *  构建政务钉鉴权信息
     * @param method
     * @param apiName
     * @param params
     * @return
     */
    private Headers buildZWDHeader(String method, String apiName, String params){

        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(this.enterpriseId);
        String ip = configMap.get(PlatformGlobalConfigKeyEnum.ZWD_IP.getKey());
        // 获取当前时间戳
        long timestamp = Instant.now().getEpochSecond();
        // 将时间戳转换为带时区的日期时间
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochSecond(timestamp), ZoneId.systemDefault());
        // 定义要使用的格式，与您的需求相对应
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.000+08:00");
        // 将日期时间格式化为指定的格式
        String formattedTime = zonedDateTime.format(formatter);
        // 增加28800秒
        timestamp += 28800;
        // 生成随机nonce
        Random rand = new Random();
        int randomNum = rand.nextInt(9000) + 1000;
        String nonce = timestamp * 1000 + String.valueOf(randomNum);

        String sign = buildSign(method, apiName, formattedTime, nonce, params);

        Headers headers = new Headers.Builder()
                .add("X-Hmac-Auth-Timestamp", formattedTime)
                .add("X-Hmac-Auth-Version", "1.0")
                .add("X-Hmac-Auth-Nonce", nonce)
                .add("apiKey", this.accessKey)
                .add("X-Hmac-Auth-Signature", sign)
                .add("X-Hmac-Auth-IP", ip)
                .add("X-Hmac-Auth-MAC", getMAC())
                .build();

        log.info("zwd apiName:{}, params:{}, headers:{}", apiName, params, JSON.toJSONString(headers));
        return headers;
    }


    private String buildSign(String method, String apiName, String timestamp, String nonce, String params) {
        String format = String.format("%s\n%s\n%s\n%s\n%s", method, timestamp, nonce, apiName, params);
        return hashHmacSha256(format ,this.secretKey); // 使用给定的密钥对数据进行HMAC-SHA256哈希运算，并返回字节数组形式的哈希值
    }

    public static String hashHmacSha256(String signature, String secretKey1) {
        byte[] bytes = signature.getBytes(StandardCharsets.UTF_8);

        try {
            Mac sha256HMAC = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKey = new SecretKeySpec(secretKey1.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            sha256HMAC.init(secretKey);
            byte[] hash = sha256HMAC.doFinal(bytes);
            String hashString = Base64.getEncoder().encodeToString(hash);
            return hashString;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getMAC(){
        try {
            InetAddress ip = InetAddress.getLocalHost();

            NetworkInterface network = NetworkInterface.getByInetAddress(ip);
            byte[] mac = network.getHardwareAddress();
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < mac.length; i++) {
                sb.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
            }
            return sb.toString();

        } catch (UnknownHostException e) {
            e.printStackTrace();
        } catch (SocketException e){
            e.printStackTrace();
        }
        return null;
    }


}
