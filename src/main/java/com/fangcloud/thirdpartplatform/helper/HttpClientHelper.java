package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSON;
import com.burgstaller.okhttp.AuthenticationCacheInterceptor;
import com.burgstaller.okhttp.CachingAuthenticatorDecorator;
import com.burgstaller.okhttp.digest.CachingAuthenticator;
import com.burgstaller.okhttp.digest.Credentials;
import com.burgstaller.okhttp.digest.DigestAuthenticator;
import com.fangcloud.thirdpartplatform.utils.SSLSocketClient;
import com.mashape.unirest.http.HttpResponse;
import com.mashape.unirest.http.Unirest;
import com.mashape.unirest.http.exceptions.UnirestException;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <p>Title: HttpClientHelper</p>
 * <p>Description: </p>
 * <p>Copyright: Copyright (c) 2019</p>
 * <p>Company: www.fangcloud.com</p>
 *
 * <AUTHOR>
 * date 2019-05-16
 * @version 1.0
 */
@Component
@Slf4j
public class HttpClientHelper {

    private static final MediaType TEXT_PLAIN = MediaType.parse("text/plain;charset=utf-8");

    private static final MediaType APPLICARION_JSON = MediaType.parse("application/json;charset=utf-8");

    private static final MediaType APPLICARION_FORM_DATA = MediaType.parse("application/x-www-form-urlencoded; charset=utf-8");

    private Headers headers;

    private OkHttpClient okHttpClient = new OkHttpClient().newBuilder()
                                                          .connectTimeout(120, TimeUnit.SECONDS)
                                                          .readTimeout(120, TimeUnit.SECONDS)
                                                          .writeTimeout(120, TimeUnit.SECONDS)
                                                          .sslSocketFactory(SSLSocketClient.getSSLSocketFactory(), SSLSocketClient.getX509TrustManager())
                                                          .hostnameVerifier(SSLSocketClient.getHostnameVerifier())
                                                          .build();

    public MediaType getApplicationFormData() {
        return APPLICARION_FORM_DATA;
    }

    public MediaType getApplicationJson() {
        return APPLICARION_JSON;
    }

    public MediaType getTextPlain() {
        return TEXT_PLAIN;
    }


    public Response getResponse(String url) throws IOException {
        Headers headers = getHeaders();
        if (null == headers) {
            headers = getDefaultHeaders();
        }

        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .build();
        return okHttpClient.newCall(request).execute();
    }

    public Response getResponse(String url, Headers headers) throws IOException {
        if (null == headers) {
            headers = getDefaultHeaders();
        }

        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .build();
        return okHttpClient.newCall(request).execute();
    }

    public Response postResponse(String url) throws IOException {
        Headers headers = getHeaders();
        if (null == headers) {
            headers = getDefaultHeaders();
        }

        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(null, ""))
                .headers(headers)
                .build();

        return okHttpClient.newCall(request).execute();
    }

    public Response postResponse(String url, String content) throws IOException {
        Headers headers = getHeaders();
        if (null == headers) {
            headers = getDefaultHeaders();
        }
        log.info("url:{}, header:{}, content:{}", url, headers.toString(), content);
        RequestBody requestBody = RequestBody.create(APPLICARION_JSON, content);
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(requestBody)
                .build();

        return okHttpClient.newCall(request).execute();
    }

    public Response postResponse(String url, String content, MediaType type) throws IOException {
        Headers headers = getHeaders();
        if (null == headers) {
            headers = getDefaultHeaders(type);
        }
        RequestBody requestBody = RequestBody.create(type, content);
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(requestBody)
                .build();

        return okHttpClient.newCall(request).execute();
    }

    public Response postResponse(String url, Headers headers, String content, MediaType type) throws IOException {

        log.info("postResponse url:{}, content:{}, header:{}", url, content, headers.toString());

        RequestBody requestBody = RequestBody.create(type, content);
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(requestBody)
                .build();
        return okHttpClient.newCall(request).execute();
    }

    public Headers getHeaders() {
        return this.headers;
    }

    public void setDefaultHeaders() {
        this.headers = getDefaultHeaders();
    }

    public void setHeaders(Map<String, String> headersMap) {

        Headers.Builder headers = new Headers.Builder();

        // 添加默认头
        headers.set("Content-Type", "application/json;charset=utf-8");

        if (null != headersMap) {
            headersMap.forEach((k, v) -> {
                headers.set(k, v);
            });
        }
        log.info("headersMap: {}", JSON.toJSONString(headersMap));

        this.headers = headers.build();
    }

    public Headers getDefaultHeaders() {
        return new Headers.Builder()
                .add("Content-Type", "application/json;charset=utf-8")
                .build();
    }

    public Headers getDefaultHeaders(MediaType type) {
        String mediaType;
        if (type == null) {
            mediaType = APPLICARION_JSON.toString();
        } else {
            mediaType = type.toString();
        }

        return new Headers.Builder()
                .add("Content-Type", mediaType)
                .build();
    }

    public void setDefaultFormHeaders() {
        Headers.Builder headers = new Headers.Builder();
        headers.set("Content-Type", "application/x-www-form-urlencoded");
        this.headers = headers.build();
    }


    public HttpResponse<String> unirestPost(Map<String, String> headers, String url, String content) {
       try {
           Unirest.setTimeouts(0, 0);
           com.mashape.unirest.http.HttpResponse<String> response =  Unirest.post(url)
                   .headers(headers)
                   .body(content)
                   .asString();
           return response;
       }catch (UnirestException e){
           return null;
       }
    }
    public HttpResponse<String> unirestGet(Map<String, String> headers, String url) {
        try {
        Unirest.setTimeouts(0, 0);
        com.mashape.unirest.http.HttpResponse<String> response =  Unirest.get(url)
                .headers(headers)
                .asString();
            return response;
        }catch (UnirestException e){
            return null;
        }
    }

    public Response postDigestAuth(String url, String content, String username, String password) throws IOException {

        Headers headers = getHeaders();
        if (null == headers) {
            headers = getDefaultHeaders();
        }
        RequestBody requestBody = RequestBody.create(APPLICARION_JSON, content);
        Request request = new Request.Builder()
                .url(url)
                .headers(headers)
                .post(requestBody)
                .build();


        Credentials credentials = new Credentials(username, password);

        OkHttpClient okHttpClient = new OkHttpClient().newBuilder()
                .connectTimeout(120, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(120, TimeUnit.SECONDS)
                .sslSocketFactory(SSLSocketClient.getSSLSocketFactory(), SSLSocketClient.getX509TrustManager())
                .hostnameVerifier(SSLSocketClient.getHostnameVerifier())
                .authenticator(new DigestAuthenticator(credentials))
                .build();
        return okHttpClient.newCall(request).execute();
    }

    public Response getDigestAuth(String url, String username, String password) throws IOException {

        final DigestAuthenticator authenticator = new DigestAuthenticator(new Credentials(username, password));

        final Map<String, CachingAuthenticator> authCache = new ConcurrentHashMap<>();
        final OkHttpClient client = new OkHttpClient.Builder()
                .authenticator(new CachingAuthenticatorDecorator(authenticator, authCache))
                .addInterceptor(new AuthenticationCacheInterceptor(authCache))
                .build();

        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        return client.newCall(request).execute();
    }

}
