package com.fangcloud.thirdpartplatform.helper;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.constant.ParamException;
import com.fangcloud.thirdpartplatform.entity.common.UserEnterpriseInfo;
import com.fangcloud.thirdpartplatform.entity.request.v2.CreateCommentBean;
import com.fangcloud.thirdpartplatform.entity.request.v2.CreateShareBean;
import com.fangcloud.thirdpartplatform.entity.request.v2.EditCollabBean;
import com.fangcloud.thirdpartplatform.entity.request.v2.InviteCollabBean;
import com.fangcloud.thirdpartplatform.entity.sync.*;
import com.fangcloud.thirdpartplatform.utils.AuthenticationUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Component
public class V2ClientHelper {

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private HttpClientHelper httpClientHelper;


    /**
     * 创建部门
     * @param createDepartmentBean
     * @param adminUserId
     * @return
     */
    public PublicDepartmentResult createDepartment(PublicDepartmentBean createDepartmentBean, long adminUserId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl4();
        url = String.format(url, adminUserId);
        String createDepartmentBeanStr = JSONObject.toJSONString(createDepartmentBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            log.info("createDepartment url : {}, : data {}", url, createDepartmentBeanStr);
            Response response = httpClientHelper.postResponse(url, createDepartmentBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error = response.body().string();
                }
                log.warn("call V2 createDepartment fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                PublicDepartmentResult publicDepartmentResult = new PublicDepartmentResult();
                publicDepartmentResult.setErrorMessage(errorMessage);
                return publicDepartmentResult;
            }
            String result = response.body().string();
            log.info("call V2 createDepartment result : {}", result);
            String departmentJson = Optional.of(JSON.parseObject(result))
                    .map(p -> p.getString("department"))
                    .orElse(null);
            if(StringUtils.isNotEmpty(departmentJson)){
                return JSON.parseObject(departmentJson, PublicDepartmentResult.class);
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("call V2 createDepartment error !", e);
        }
        return null;
    }


    /**
     * 修改部门信息
     * @param editDepartmentBean
     * @param departmentId
     * @param adminUserId
     * @return
     */
    public PublicDepartmentResult editDepartment(PublicDepartmentBean editDepartmentBean, long departmentId, long adminUserId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl5();
        url = String.format(url, adminUserId, departmentId);
        String editDepartmentBeanStr = JSONObject.toJSONString(editDepartmentBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            log.info("call V2 editDepartment url : {}, data : {}", url, editDepartmentBeanStr);
            Response response = httpClientHelper.postResponse(url, editDepartmentBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error= response.body().string();
                }
                log.warn("call V2 editDepartment fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                PublicDepartmentResult publicDepartmentResult = new PublicDepartmentResult();
                publicDepartmentResult.setErrorMessage(errorMessage);
                return publicDepartmentResult;
            }
            String result = response.body().string();
            log.info("call V2 editDepartment result : {}", result);
            String departmentJson = Optional.of(JSON.parseObject(result))
                    .map(p -> p.getString("department"))
                    .orElse(null);
            if(StringUtils.isNotEmpty(departmentJson)){
                return JSON.parseObject(departmentJson, PublicDepartmentResult.class);
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("call V2 createDepartment error !", e);
        }
        return null;
    }


    /**
     * 创建用户
     * @param syncPublicUserBean
     * @param adminUserId
     * @return
     */
    public CreateUserResult createUser(PublicUserBean syncPublicUserBean, long adminUserId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl7();
        url = String.format(url, adminUserId);
        String createUserBeanStr = JSONObject.toJSONString(syncPublicUserBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            log.info("call V2 createUser url : {}, data : {}", url, createUserBeanStr);
            Response response = httpClientHelper.postResponse(url, createUserBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error= response.body().string();
                }
                log.warn("call V2 createUser fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                CreateUserResult createUserResult = new CreateUserResult();
                createUserResult.setErrorMessage(errorMessage);
                return createUserResult;
            }
            String result = response.body().string();
            log.info("call V2 createUser result : {}", result);
            if(StringUtils.isNotEmpty(result)){
                return JSON.parseObject(result, CreateUserResult.class);
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("call V2 createDepartment error !", e);
        }
        return null;
    }


    /**
     * 修改用户信息
     * @param syncPublicUserBean
     * @param adminUserId
     * @param userId
     * @return
     */
    public UserResult editUser(PublicUserBean syncPublicUserBean, long adminUserId, long userId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl8();
        url = String.format(url, adminUserId, userId);
        String editUserBeanStr = JSONObject.toJSONString(syncPublicUserBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);

            log.info("call V2 editUser url : {}, data : {}", url, editUserBeanStr);
            Response response = httpClientHelper.postResponse(url, editUserBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error= response.body().string();
                }
                log.warn("call V2 editUser fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                UserResult userResult = new UserResult();
                userResult.setErrorMessage(errorMessage);
                return userResult;
            }
            String result = response.body().string();
            log.info("call V2 editUser result : {}", result);
            String userJson = Optional.of(JSON.parseObject(result))
                    .map(p -> p.getString("user"))
                    .orElse(null);
            if(StringUtils.isNotEmpty(userJson)){
                return JSON.parseObject(userJson, UserResult.class);
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("call V2 createDepartment error !", e);
        }
        return null;
    }

    /**
     * 删除用户
     * @param adminUserId
     * @param userId
     * @param deleteUserBean
     * @return
     */
    public boolean deleteUser(long adminUserId, long userId, DeleteUserBean deleteUserBean) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl10();
        url = String.format(url, adminUserId, userId);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            String deleteUserBeanStr = JSONObject.toJSONString(deleteUserBean);

            log.info("call V2 deleteUser url : {}, data : {}", url, deleteUserBeanStr);
            Response response = httpClientHelper.postResponse(url, deleteUserBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error= response.body().string();
                }
                log.warn("call V2 deleteUser fail ! error info :{}",error);
                return false;
            }
            String result = response.body().string();
            log.info("call V2 deleteUser result : {}", result);
            Boolean deleteResult = Optional.of(JSON.parseObject(result))
                    .map(p -> p.getBoolean("success"))
                    .orElse(false);
            return deleteResult;
        } catch (Exception e) {
            log.error("call V2 deleteUser error !", e);
        }
        return false;
    }

    /**
     * 删除部门
     * @param adminUserId
     * @param departmentId
     * @return
     */
    public boolean deleteDepartment(long adminUserId, long departmentId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl6();
        url = String.format(url, adminUserId, departmentId);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);

            log.info("call V2 deleteDepartment url : {}", url);
            Response response = httpClientHelper.postResponse(url);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error= response.body().string();
                }
                log.warn("call V2 deleteDepartment fail ! error info :{}",error);
                return false;
            }
            String result = response.body().string();
            log.info("call V2 deleteDepartment result : {}", result);
            boolean deleteResult = Optional.of(JSON.parseObject(result))
                    .map(p -> p.getBoolean("success"))
                    .orElse(null);
           return deleteResult;
        } catch (Exception e) {
            log.error("call V2 deleteDepartment error !", e);
        }
        return false;
    }


    /**
     * 编辑部门成员
     * @param editDepartmentUserBean
     * @param adminUserId
     * @param yfyDepartmentId
     * @return
     */
    public EditDepartmentResult editDepartmentUser(EditDepartmentUserBean editDepartmentUserBean, long adminUserId, long yfyDepartmentId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl9();
        url = String.format(url, adminUserId, yfyDepartmentId);
        String editDepartmentUserBeanStr = JSONObject.toJSONString(editDepartmentUserBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);

            log.info("call V2 editDepartmentUser url : {}, data : {}", url, editDepartmentUserBeanStr);
            Response response = httpClientHelper.postResponse(url, editDepartmentUserBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error= response.body().string();
                }
                log.warn("call V2 editDepartmentUser fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                EditDepartmentResult editDepartmentResult = new EditDepartmentResult();
                editDepartmentResult.setErrorMessage(errorMessage);
                return editDepartmentResult;
            }
            String result = response.body().string();
            log.info("call V2 editDepartmentUser result : {}", result);
            if(StringUtils.isNotEmpty(result)){
                return JSON.parseObject(result, EditDepartmentResult.class);
            }else {
                return null;
            }
        } catch (Exception e) {
            log.error("call V2 createDepartment error !", e);
        }
        return null;
    }


    public JSONObject getUserInfoByUserId(String userId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl13();
        String postData = String.format("{\"user_id\":\"%s\",\"fields\":[\"phone\"]}", userId);
        Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
        String result = HttpRequest.post(url).headerMap(headers, true).body(postData).execute().body().toString();
        log.info("getUserInfoByUserId result : {}, url {}, headers {}", result, url, headers);
        return (JSONObject) JSON.parseObject(result).get("user");
    }

    /**
     * 根据用户企业信息调用V2获取用户详细信息
     * @param userEnterpriseInfo
     * @return
     */
    public JSONObject getUserInfoFromV2(UserEnterpriseInfo userEnterpriseInfo) {

        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl1();
        String userEnterpriseInfoStr = JSONObject.toJSONString(userEnterpriseInfo);

        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            Response response = httpClientHelper.postResponse(url, userEnterpriseInfoStr);
            if (response.code() != 200 || response.body() == null) {
                log.error("调用V2获取失败 url {} body {}, response {}", url,userEnterpriseInfoStr, response.body().string());
                return null;
            }
            String result = response.body().string();
            log.info("调用V2获取用户信息 url {} result {}", url, result);
            return Optional.of(JSON.parseObject(result))
                    .map(p -> p.getJSONObject("user"))
                    .orElse(null);
        } catch (Exception e) {
            log.error("调用V2获取用户信息 异常 url {}", url, e);
        }
        return null;
    }


    /**
     * 同步用户
     * @param syncUserBean
     * @return
     */
    public SyncPlatformResult syncUser(SyncUserBean syncUserBean) {
        if (syncUserBean.getRequestId() == null) {
            syncUserBean.setRequestId(UUID.randomUUID().toString());
        }

        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl2();
        String syncUserBeanStr = JSONObject.toJSONString(syncUserBean);
        try {
            log.info("sync user from v2 url {} input info {}", url, syncUserBeanStr);
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            Response response = httpClientHelper.postResponse(url, syncUserBeanStr);
            String result = null;
            if(!Objects.isNull(response.body())){
                result = response.body().string();
            }
            if (response.code() != 200 ) {
                log.warn("sync user from v2 failed , result {}",result);
                return null;
            }
            log.info("sync user from v2 result {}", result);
            SyncPlatformResult syncPlatformResult = JSONObject.parseObject(result, SyncPlatformResult.class);
            return syncPlatformResult;
        } catch (Exception e) {
            log.error("sync user from v2 error ", e);
            return null;
        }
    }


    /**
     * 同步部门
     * @param syncDepartmentBean
     * @return
     */
    public SyncPlatformResult syncDept(SyncDepartmentBean syncDepartmentBean)  {
        if (syncDepartmentBean.getRequestId() == null) {
            syncDepartmentBean.setRequestId(UUID.randomUUID().toString());
        }
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl3();
        String syncUserBeanStr = JSONObject.toJSONString(syncDepartmentBean);
        try {
            log.info("sync department from v2 url {} input info {}", url, syncUserBeanStr);
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            Response response = httpClientHelper.postResponse(url, syncUserBeanStr);
            String result = null;
            if(!Objects.isNull(response.body())){
                result = response.body().string();
            }
            if (response.code() != 200 ) {
                log.warn("sync department from v2 failed , result {}",result);
                return null;
            }
            log.info("sync department from v2 result {}", result);
            SyncPlatformResult syncPlatformResult = JSONObject.parseObject(result, SyncPlatformResult.class);
            return syncPlatformResult;
        } catch (Exception e) {
            log.error("sync user from v2 error ", e);
            return null;
        }
    }


    /**
     * 将用户修改为未激活
     * @param syncActiveUsersBatchBean
     * @return
     */
    public SyncActiveUsersBatchOutput active_users_batch(SyncActiveUsersBatchBean syncActiveUsersBatchBean, long adminUserId) {

        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl11();
        url = String.format(url, adminUserId);
        String syncUserBeanStr = JSONObject.toJSONString(syncActiveUsersBatchBean);
        try {
            log.info("batch update user active from v2 url {} input info {}", url, syncUserBeanStr);
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            Response response = httpClientHelper.postResponse(url, syncUserBeanStr);
             String result = null;
            if(!Objects.isNull(response.body())){
                result = response.body().string();
            }
            if (response.code() != 200 ) {
                log.warn("batch update user active from v2 failed , result {}",result);
                return null;
            }
            log.info("batch update user active from v2 result {}", result);
            SyncActiveUsersBatchOutput syncActiveUsersBatchOutput = JSONObject.parseObject(result, SyncActiveUsersBatchOutput.class);
            return syncActiveUsersBatchOutput;
        } catch (Exception e) {
            log.error("batch update user active from v2 error ", e);
            return null;
        }
    }

    /**
     * 同步群组
     * @param syncGroupBean
     * @return
     */
    public SyncPlatformResult syncGroup(SyncGroupBean syncGroupBean)  {
        if (syncGroupBean.getRequestId() == null) {
            syncGroupBean.setRequestId(UUID.randomUUID().toString());
        }
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl12();
        String syncGroupBeanStr = JSONObject.toJSONString(syncGroupBean);
        try {
            log.info("sync group from v2 url {} input info {}", url, syncGroupBeanStr);
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            httpClientHelper.setHeaders(headers);
            Response response = httpClientHelper.postResponse(url, syncGroupBeanStr);
            String result = null;
            if(!Objects.isNull(response.body())){
                result = response.body().string();
            }
            if (response.code() != 200 ) {
                log.warn("sync group from v2 failed , result {}",result);
                return null;
            }
            log.info("sync group from v2 result {}", result);
            SyncPlatformResult syncPlatformResult = JSONObject.parseObject(result, SyncPlatformResult.class);
            return syncPlatformResult;
        } catch (Exception e) {
            log.error("sync group from v2 error ", e);
            return null;
        }
    }


    /**
     * 邀请协作
     * @param inviteCollabBean
     * @param operatorId
     * @return
     */
    public String inviteCollab(InviteCollabBean inviteCollabBean, long operatorId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl14();
        String inviteCollabBeanStr = JSONObject.toJSONString(inviteCollabBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            headers.put("X-Platform-User-Id", String.valueOf(operatorId));
            httpClientHelper.setHeaders(headers);
            log.info("inviteCollab url : {}, : data {}", url, inviteCollabBeanStr);
            Response response = httpClientHelper.postResponse(url, inviteCollabBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error = response.body().string();
                }
                log.warn("call V2 inviteCollab fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                throw new ParamException(errorMessage);
            }
            String result = response.body().string();

            log.info("call V2 inviteCollab result : {}", result);
            return result;
        } catch (Exception e) {
            log.info("call V2 inviteCollab error !", e);
            throw new ParamException(e.getMessage());
        }
    }

    /**
     * 修改协作
     * @param editCollabBean
     * @param operatorId
     * @return
     */
    public String editCollab(EditCollabBean editCollabBean, long operatorId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl15();
        String editCollabBeanStr = JSONObject.toJSONString(editCollabBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            headers.put("X-Platform-User-Id", String.valueOf(operatorId));
            httpClientHelper.setHeaders(headers);
            log.info("editCollab url : {}, : data {}", url, editCollabBeanStr);
            Response response = httpClientHelper.postResponse(url, editCollabBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error = response.body().string();
                }
                log.info("call V2 editCollab fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                throw new ParamException(errorMessage);
            }
            String result = response.body().string();
            log.info("call V2 editCollab result : {}", result);
            return result;
        } catch (Exception e) {
            log.info("call V2 editCollab error !", e);
            throw new ParamException(e.getMessage());
        }
    }

    /**
     * 修改协作
     * @param createCommentBean
     * @param operatorId
     * @return
     */
    public String createComment(CreateCommentBean createCommentBean, long operatorId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl16();
        String createCommentBeanStr = JSONObject.toJSONString(createCommentBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            headers.put("X-Platform-User-Id", String.valueOf(operatorId));
            httpClientHelper.setHeaders(headers);
            log.info("createComment url : {}, : data {}", url, createCommentBeanStr);
            Response response = httpClientHelper.postResponse(url, createCommentBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error = response.body().string();
                }
                log.info("call V2 createComment fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                throw new ParamException(errorMessage);
            }
            String result = response.body().string();
            log.info("call V2 createComment result : {}", result);
            return result;
        } catch (Exception e) {
            log.info("call V2 createComment error !", e);
            throw new ParamException(e.getMessage());
        }
    }

    /**
     * 创建分享
     * @param createShareBean
     * @param operatorId
     * @return
     */
    public String createShare(CreateShareBean createShareBean, long operatorId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl17();
        String createCommentBeanStr = JSONObject.toJSONString(createShareBean);
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            headers.put("X-Platform-User-Id", String.valueOf(operatorId));
            httpClientHelper.setHeaders(headers);
            log.info("createShare url : {}, : data {}", url, createCommentBeanStr);
            Response response = httpClientHelper.postResponse(url, createCommentBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error = response.body().string();
                }
                log.info("call V2 createShare fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                throw new ParamException(errorMessage);
            }
            String result = response.body().string();
            log.info("call V2 createShare result : {}", result);
            return result;
        } catch (Exception e) {
            log.info("call V2 createShare error !", e);
            throw new ParamException(e.getMessage());
        }
    }


    public String downloadFile(String fileId, String operatorId) {
        String url = customNacosConfig.getV2HostUrl() + customNacosConfig.getV2UriUrl20() 
        + "/" + fileId + "?no_redirect=true";
        try {
            Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getV2ServiceId(),
                    customNacosConfig.getV2Version(), customNacosConfig.getV2Secret());
            headers.put("X-Platform-User-Id", String.valueOf(operatorId));
            httpClientHelper.setHeaders(headers);
            Response response = httpClientHelper.getResponse(url);
            if (response.code() != 200 || response.body() == null) {
                String error = null;
                if(!Objects.isNull(response.body())){
                    error = response.body().string();
                }
                log.info("call V2 downloadfile fail ! error info :{}",error);
                String errorMessage = Optional.of(JSON.parseObject(error))
                        .map(p -> p.getString("message"))
                        .orElse(null);
                throw new ParamException(errorMessage);
            }
            String result = response.body().string();
            log.info("call V2 downloadfile result : {}", result);
            return result; 
        } catch (Exception e) {
            log.info("call V2 downloadfile error !", e);
            throw new ParamException(e.getMessage());
        }

    }



}
