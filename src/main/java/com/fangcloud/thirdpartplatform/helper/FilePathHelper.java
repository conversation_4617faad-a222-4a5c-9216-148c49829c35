package com.fangcloud.thirdpartplatform.helper;

import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
* <p>Title: FilePathHelper</p>
* <p>Description: </p>
* <p>Copyright: Copyright (c) 2019</p>
* <p>Company: www.fangcloud.com</p>
* <AUTHOR>
* Data 2019-05-16
* @version 1.0
*/
@Component
public class FilePathHelper {

    public InputStream getFileInputStream(String fileName) {
        ClassLoader classLoader = getClass().getClassLoader();

        InputStream fileStream = classLoader.getResourceAsStream(fileName);

        return fileStream;
    }

    public String getStringFromInputStream(String fileName) throws IOException {
        ClassLoader classLoader = getClass().getClassLoader();

        InputStream fileStream = classLoader.getResourceAsStream(fileName);

        ByteArrayOutputStream result = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = fileStream.read(buffer)) != -1) {
            result.write(buffer, 0, length);
        }

        return result.toString(StandardCharsets.UTF_8.name());
    }
}
