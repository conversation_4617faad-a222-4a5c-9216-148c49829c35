package com.fangcloud.thirdpartplatform.helper;

import com.fangcloud.thirdpartplatform.entity.FtpBaseInfo;
import com.fangcloud.thirdpartplatform.entity.FtpFilesInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.PrintCommandListener;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class FtpHelper {

    /**
     * 连接ftp
     */
    public FTPClient getFtpClient(FtpBaseInfo ftpBaseInfo) {
        String url = ftpBaseInfo.getIp();
        int port = ftpBaseInfo.getPort();
        String username = ftpBaseInfo.getUsername();
        String password = ftpBaseInfo.getPassword();

        FTPClient ftp = new FTPClient();
        ftp.setDataTimeout(0);
        ftp.setConnectTimeout(0);
        try {
            //ftp日志
            ftp.addProtocolCommandListener(
                    new PrintCommandListener(
                            new PrintWriter(new OutputStreamWriter(System.out, "UTF-8")), true));
            // 连接FTP服务器
            log.info("start getFtpClient,url:{},port:{},username:{}", url, port, username);

            ftp.connect(url, port);
            // 登录FTP
            ftp.login(username, password);
            int reply = ftp.getReplyCode();
            log.info("getFtpClient reply:{}", reply);
            if (!FTPReply.isPositiveCompletion(reply)) {
                log.error("initFtpClient error,reply:{}", reply);
                ftp.disconnect();
            }
            return ftp;
        } catch (IOException e) {
            log.error("getFtpClient error", e);
        }
        return null;
    }

    /**
     * 数据文件上传ftp
     */
    public void uploadToFtp(FtpBaseInfo ftpBaseInfo , String fileDir, String fileName, InputStream input)
            throws IOException {
        log.info("start ftp upload ,remotePath:{},fileName:{}", fileDir, fileName);
        FTPClient ftp = getFtpClient(ftpBaseInfo);
        mkdirAndModel(ftp, fileDir);
        try {
            log.info("ftpClient connected");
            boolean result = ftp.storeFile(fileName, input);
            log.info("ftp upload result:{}", result);
        } finally {
            input.close();
            ftp.disconnect();
        }
    }


    /**
     * 切换目录并设置传输模式
     */
    public void mkdirAndModel(FTPClient ftp, String dir) throws IOException {
        log.info("start mkdirAndModel,dir:{}", dir);
        ftp.makeDirectory(dir);
        changeDirAndModel(ftp, dir);
        log.info("end mkdirAndModel,dir:{}", dir);
    }

    /**
     * 切换目录并设置传输模式
     */
    public void changeDirAndModel(FTPClient ftp, String dir) throws IOException {
        ftp.changeWorkingDirectory(dir);
        //设置PassiveMode传输
        ftp.enterLocalPassiveMode();
        //设置以二进制流的方式传输
        ftp.setFileType(FTP.BINARY_FILE_TYPE);
        ftp.setControlEncoding("utf8");
    }

    /**
     * 删除目录及所有子项
     * holdLimit 保留文件个数
     */
    public void deleteFromFtp(FtpBaseInfo ftpBaseInfo, String dir, int holdLimit) throws IOException {
        FTPClient ftp = getFtpClient(ftpBaseInfo);
        changeDirAndModel(ftp, dir);
        FTPFile[] files = ftp.listFiles();
        int i = 0;
        for (FTPFile file : files) {
            if (file.isFile() && files.length - i > holdLimit) {
                ftp.deleteFile(file.getName());
            }
            i++;
        }
        FTPFile[] dirs = ftp.listDirectories();
        for (FTPFile subDir : dirs) {
            deleteFromFtp(ftpBaseInfo, dir + "/" + subDir.getName(), 0);
        }
    }

    /**
     * 获取文件列表
     * @param dir
     * @return
     * @throws IOException
     */
    public List<FtpFilesInfo> getFileListFromFtp(FtpBaseInfo ftpBaseInfo, String dir) throws IOException {
        FTPClient ftp = getFtpClient(ftpBaseInfo);
        changeDirAndModel(ftp, dir);
        FTPFile[] files = ftp.listFiles();
        List<FtpFilesInfo> ftpFilesInfos = new ArrayList<>();
        for(int i = 0; i< files.length; i++) {
            FtpFilesInfo ftpFilesInfo = FtpFilesInfo.builder()
                    .fileName(files[i].getName())
                    .file(files[i])
                    .build();
            ftpFilesInfos.add(ftpFilesInfo);
        }
        ftp.disconnect();
        return  ftpFilesInfos;
    }


    /**
     * 获取文件内容
     * @param ftpBaseInfo
     * @param dir
     * @param fileName
     * @return
     * @throws IOException
     */
    public InputStream getFileInfoFromFtp(FtpBaseInfo ftpBaseInfo, String dir, String fileName) throws IOException {
        FTPClient ftpClient = getFtpClient(ftpBaseInfo);
        changeDirAndModel(ftpClient, dir);
        InputStream inputStream = ftpClient.retrieveFileStream(fileName);
        ftpClient.disconnect();

        return inputStream;
    }

}
