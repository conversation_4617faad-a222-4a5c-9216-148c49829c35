package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.entity.input.EnterpriseParams;
import com.fangcloud.thirdpartplatform.utils.AuthenticationUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Component
@Slf4j
public class OpenClientHelper {

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private HttpClientHelper httpClientHelper;

    private static final String OPEN_PLATFORM_USER_URL_ALL = "%s/api/v2/admin/user/all?platform_id=%d&page_id=%s&page_capacity=%s";

    /**
     * 获取用户的免密登陆url
     *
     * @param enterpriseParams
     * @return
     */
    public String getLoginUrlFromOpenApi(EnterpriseParams enterpriseParams) {
        String url = customNacosConfig.getOpenHostUrl() + customNacosConfig.getOpenUriGetLoginUrl();
        String params = String.format("client_id=%s&enterprise_id=%s&platform_id=%s&type=%s&identifier=%s",
                enterpriseParams.getClientId(), enterpriseParams.getEnterpriseId(),
                enterpriseParams.getPlatformId(), enterpriseParams.getType(),
                enterpriseParams.getIdentifier());
        url = url + "?" + params;
        Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getOpenServiceId(),
                customNacosConfig.getOpenVersion(), customNacosConfig.getOpenSecret());
        httpClientHelper.setHeaders(headers);

        try {
            Response response = httpClientHelper.getResponse(url);
            if (response.body() == null) {
                log.warn("调用开放平台获取登录信息为空 url {} header {}", url, headers);
                return null;
            }
            String result = response.body().string();
            log.info("调用开放平台获取登录信息 url {} 返回值 result {}", url, result);

            if (JSON.parseObject(result).getString("login_url") != null) {
                return JSON.parseObject(result).getString("login_url");
            }
        } catch (Exception e) {
            log.error("调用开放平台获取登录信息 异常 url {}", url, e);
        }
        return null;
    }

    public JSONArray getOneUserInfo(String accountToken, int platformId) {
        int pageId = 0;
        int pageSize = 1;
        Map<String, String> headersMap = new HashMap<>();
        headersMap.put("Authorization", "Bearer " + accountToken);
        String requestUrl = String.format(OPEN_PLATFORM_USER_URL_ALL, customNacosConfig.getOpenHostUrl(), platformId, pageId, pageSize);
        httpClientHelper.setHeaders(headersMap);
        try {
            log.info("调用open接口 url {}, headers {}", requestUrl, httpClientHelper.getHeaders().toString());

            Response response = httpClientHelper.getResponse(requestUrl);
            if (null == response.body()) {
                // 重试一次
                response = httpClientHelper.getResponse(requestUrl);
            }

            if (null == response.body()) {
                return null;
            }
            String result = Objects.requireNonNull(response.body()).string();
            log.info("调用open接口返回 数据 {}", result);
            JSONObject jsonObject = JSON.parseObject(result);
            if (jsonObject.get("users") != null) {
                return (JSONArray) jsonObject.get("users");
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }
}
