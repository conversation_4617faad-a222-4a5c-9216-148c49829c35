package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.entity.request.ark.WorkflowApprovalBean;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
public class ArkClientHelper {

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private HttpClientHelper httpClientHelper;

    public static String HEADER_KEY_USER_ID = "User-id";


    /**
     * 执行工作流审批
     * @param workflowApprovalBean
     * @param userId
     * @return
     */
    public boolean workflowApproval(WorkflowApprovalBean workflowApprovalBean, long userId) {
        String url = customNacosConfig.getArkHostUrl() + customNacosConfig.getArkUriUrl1();
        String approvalBeanStr = JSONObject.toJSONString(workflowApprovalBean);
        try {

            httpClientHelper.setHeaders(buildWorkflowHeader(userId));
            log.info("approvalBeanStr url : {}, : data {}", url, approvalBeanStr);

            Response response = httpClientHelper.postResponse(url, approvalBeanStr);
            if (response.code() != 200 || response.body() == null) {
                String result = null;
                if(!Objects.isNull(response.body())){
                    result = response.body().string();
                }
                log.info("call ark workflowApproval fail ! result info :{}",result);
                return false;
            }
            String result = response.body().string();
            log.info("call ark workflowApproval result : {}", result);
            return Optional.of(JSON.parseObject(result))
                    .map(p -> p.getBoolean("data"))
                    .orElse(false);
        } catch (Exception e) {
            log.error("call ark workflowApproval error !", e);
        }
        return false;
    }


    /**
     * 构建工作流header
     * @param userId
     * @return
     */
    private Map<String, String> buildWorkflowHeader(long userId) {

        Map<String, String> data = new HashMap<>();
        data.put(HEADER_KEY_USER_ID, String.valueOf(userId));

        return data;
    }


}
