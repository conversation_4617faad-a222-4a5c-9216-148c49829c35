package com.fangcloud.thirdpartplatform.helper;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Component
public class CacheHelper {

    private Cache<String, String> cache = CacheBuilder.newBuilder()
                                                      .maximumSize(1000)
                                                      .expireAfterWrite(60, TimeUnit.MINUTES)
                                                      .concurrencyLevel(10)
                                                      .recordStats()
                                                      .build();

    public void put(String key, String value) {
        cache.put(key, value);
    }

    public String get(String key) {
        return cache.getIfPresent(key);
    }

    public static Cache<String, String> getExpireTimeCache(long duration, TimeUnit unit) {
        return CacheBuilder.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(duration, unit)
                .concurrencyLevel(10)
                .recordStats()
                .build();

    }

    private Cache<String, Object> caches = CacheBuilder.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(1, TimeUnit.DAYS)
            .concurrencyLevel(10)
            .recordStats()
            .build();

    public void putObject(String key, Object value) {
        caches.put(key, value);
    }

    public Object getObject(String key) {
        return caches.getIfPresent(key);
    }
}
