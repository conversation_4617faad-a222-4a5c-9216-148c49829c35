package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.sync.DataTypeConstants;
import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.dao.PlatformDepartmentMapper;
import com.fangcloud.thirdpartplatform.db.dao.PlatformUserMapper;
import com.fangcloud.thirdpartplatform.db.model.PlatformDepartment;
import com.fangcloud.thirdpartplatform.db.model.PlatformUser;
import com.fangcloud.thirdpartplatform.entity.dto.*;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.enums.DepartmentStatusEnum;
import com.sync.common.enums.UserStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @author: MKX
 * creat: 2022-05-24
 **/
@Component
@Slf4j
public class PushHelper {

    @Resource
    private APIHelper apiHelper;

    @Resource
    private PlatformDepartmentMapper platformDepartmentMapper;

    @Resource
    private RedisStringManager redisStringManager;

    public List<YfyDepartment> buildDept(JSONArray values, PushConfigValueBox pushConfigValueBox) {
        if (CollectionUtils.isEmpty(values) || pushConfigValueBox == null) {
            return null;
        }
        checkSzgdDelOpera(values,pushConfigValueBox,"DEPT");
        List<PushParamConfigDto> paramConfigs = pushConfigValueBox.getParamConfig();
        List<Map<String, Object>> mappingValues = mapping(values, paramConfigs, pushConfigValueBox);
        DepConfigDto depConfigDto = pushConfigValueBox.getDepConfigDto();
        List<YfyDepartment> depts = apiHelper.buildDept(pushConfigValueBox.getDeptSpace().longValue(), mappingValues, depConfigDto);
        return depts;
    }

    public List<YfyUser> buildUser(JSONArray values, PushConfigValueBox pushConfigValueBox) {
        if (CollectionUtils.isEmpty(values) || pushConfigValueBox == null) {
            return null;
        }
        checkSzgdDelOpera(values,pushConfigValueBox,"USER");
        List<PushParamConfigDto> paramConfigs = pushConfigValueBox.getParamConfig();
        List<Map<String, Object>> mappingValues = mapping(values, paramConfigs, pushConfigValueBox);
        UserConfigDto userConfigDto = pushConfigValueBox.getUserConfigDto();
        List<YfyUser> users = apiHelper.buildUser(pushConfigValueBox.getUserSpace().longValue(), mappingValues, userConfigDto);
        return users;
    }

    private List<Map<String, Object>> mapping(JSONArray values, List<PushParamConfigDto> paramConfigs, PushConfigValueBox pushConfigValueBox) {
        List<Map<String, Object>> mappingValues = new ArrayList<>();
        for (Object value : values) {
            JSONObject obj = (JSONObject) value;
            mappingValues.add(mapping(obj, paramConfigs, pushConfigValueBox));
        }
        return mappingValues;
    }

    public Map<String, Object> mapping(Map<String, Object> values, List<PushParamConfigDto> paramConfigs, PushConfigValueBox pushConfigValueBox) {
        if (values == null || paramConfigs == null) {
            return null;
        }
        Map<String, Object> result = new HashMap<>();
        for (PushParamConfigDto paramConfigDto : paramConfigs) {
            if (paramConfigDto == null) {
                continue;
            }
            String key = paramConfigDto.getName();
            if (StringUtils.isNotBlank(paramConfigDto.getMappingName())) {
                key = paramConfigDto.getMappingName();
            }
            Object value = values.get(paramConfigDto.getName());
            //对value进行判空,如果value为空就跳出循环
            if (value == null) {
                continue;
            }
            String dataType = paramConfigDto.getDataType();
            Object convert = apiHelper.getValueByFormat(key, dataType, value, paramConfigDto.getFormat());
            convert = DataTypeConstants.convert(convert, dataType);
            if (StringUtils.isNotBlank(paramConfigDto.getRuleScript()) && !key.equals(SyncTaskConstants.DEPARTMENT_PARAMETER_IS_TOP) && !key.equals(SyncTaskConstants.USER_IS_DISABLE) && !key.equals(SyncTaskConstants.DEPARTMENT_PARAMETER_IS_DELETE)) {
                // 规则过滤，如果满足则不处理该条数据，也就是过滤掉
                if (apiHelper.checkRule(key, convert, paramConfigDto.getRuleScript())) break;
            }
            if (key.equals(SyncTaskConstants.DEPARTMENT_PARAMETER_IS_TOP)) {
                if (apiHelper.checkRule(key, convert, paramConfigDto.getRuleScript())) {
                    result.put(SyncTaskConstants.DEPARTMENT_PARAMETER_PARENT_ID, "");
                }
            }
            boolean status = key.equals(SyncTaskConstants.USER_IS_DISABLE) || key.equals(SyncTaskConstants.DEPARTMENT_PARAMETER_IS_DELETE);
            if (pushConfigValueBox.getOperationType().equals(CommonConstants.OPERATION_TYP_SAVE_OR_UPDATE)) {
                if (status) {
                    if(convert != null){
                        if (apiHelper.checkRule(key, convert, paramConfigDto.getRuleScript())) {
                            //满足过滤规则就删除
                            if (pushConfigValueBox.getDepConfigDto() != null) {
                                convert = DepartmentStatusEnum.DELETE.getCode();
                            }
                            if (pushConfigValueBox.getUserConfigDto() != null) {
                                convert = UserStatusEnum.DELETE.getCode();
                            }
                        } else {
                            //不满足过滤规则就同步,因为默认已经是同步状态了所以不需要在去做其他处理,直接跳过
                            continue;
                        }
                    }
                }
            }
            if (pushConfigValueBox.getOperationType().equals(CommonConstants.OPERATION_TYP_DELETE)) {
                if (status) {
                    if (pushConfigValueBox.getDepConfigDto() != null) {
                        convert = DepartmentStatusEnum.DELETE.getCode();
                    }
                    if (pushConfigValueBox.getUserConfigDto() != null) {
                        convert = UserStatusEnum.DELETE.getCode();
                    }
                }
            }
            result.put(key, convert);
        }
        return result;
    }

    private void checkSzgdDelOpera(JSONArray values, PushConfigValueBox pushConfigValueBox,String type){
        //szgd查询需要删除的数据
        PushResultConfigDto responseType = pushConfigValueBox.getResultConfig().stream().filter(s -> "Response_Type".equals(s.getName())).collect(Collectors.toList()).get(0);
        if (responseType!=null&&responseType.getResultValue().equals("SZGD")&&CommonConstants.OPERATION_TYP_DELETE.equals(pushConfigValueBox.getOperationType())){
            //操作是删除时，通过baseOrgId从数据库获取当前部门信息进行组装
            JSONObject object = (JSONObject)values.get(0);
            if (type.equals("DEPT")){
                PlatformDepartment platformDepartment = platformDepartmentMapper.getByPlatformIdAndDepartmentId(2L,object.getString("baseOrgId"));
                if (platformDepartment!=null){
                    object.put("baseOrgName",platformDepartment.getName());
                }
            }else {
                String taskUserKey ="SYNC_USER_ID_"+object.getString("uid");
                String user = redisStringManager.get(taskUserKey);
                if (user!=null){
                    redisStringManager.delete(taskUserKey);
                    JSONObject userJson = JSONObject.parseObject(user);
                    values.remove(0);
                    values.add(userJson);
                }
            }


        }
    }
}
