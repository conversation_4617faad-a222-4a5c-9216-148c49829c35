package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSON;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.fangcloud.thirdpartplatform.utils.Base64Utils;
import com.sync.common.entity.dto.YfyMessage;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class ThirdClientHelper {


    @Resource
    private HttpClientHelper httpClientHelper;


    public void sendTextMessage(YfyMessage yfyMessage, String enterpriseId) {

        Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId);

        String sendMessageUrl = configMap.get(PlatformGlobalConfigKeyEnum.ZWD_SEND_MESSAGE_URL);

        String token = Base64Utils.encode(yfyMessage.getReceivers() + "zwsp" + System.currentTimeMillis()/1000);

        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("receivers", yfyMessage.getReceivers());
        dataMap.put("title", yfyMessage.getTitle());
        dataMap.put("content", yfyMessage.getContent());

        Map<String, String> headers = new HashMap<>();
        headers.put("token", token);
        httpClientHelper.setHeaders(headers);
        try {
            log.info("send text message url:{},header:{}, body:{}", sendMessageUrl, JSON.toJSONString(headers), JSON.toJSONString(dataMap));
            Response response = httpClientHelper.postResponse(sendMessageUrl, JSON.toJSONString(dataMap));
            if (response == null || response.body() == null) {
                log.info("send text message result is null!");
            }else {
                log.info("send text message result is :{}!", response.body().string());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
}
