package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.ccwork.AESEncryptUtil;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.entity.CcworkEvent;
import com.fangcloud.thirdpartplatform.entity.dto.ThirdPartyValueBox;
import com.fangcloud.thirdpartplatform.entity.input.CcWorkInitParam;
import com.fangcloud.thirdpartplatform.entity.output.CcWorkDepartment;
import com.fangcloud.thirdpartplatform.entity.output.CcWorkUser;
import com.sync.common.entity.dto.YfyMessage;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.enums.UserStatusEnum;
import com.sync.common.enums.YfyMessageTypeEnum;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.net.URL;
import java.util.*;

import static com.sync.common.contants.SyncConstant.GB;

@Slf4j
public class CcWorkClientHelper {

    private static final String GET_TOKEN = "%s:8888/v1/getapptoken?corpid=%s&appid=%s&appkey=%s";

    private static final String DEPARTMENT_RELATION = "%s:8888/v1/dept/relation?access_token=%s&did=0&page=%s";
    private static final String USER_LIST = "%s:8888/v1/user/list?access_token=%s&did=%s";
    private static final String SEND_LINK_MESSAGE = "%s/message/custom/send?appid=%s&secret=%s&trans_id=%s";

    /**
     * 免密登录
     */
    private static final String SSO_CHECK_SIGN = "%s:8888/v1/sso/check_sign";

    private static final String DEPT_RELATION_PTH = "datas";

    private static final String DEPT_PAGE_COUNT_PTH = "pageCount";
    private static final String ACCOUNT_PATH = "$.account";
    private static final String NAME_PATH = "$.name";
    private static final String PHONE_PATH = "$.mobile";
    private static final String DEPT_PATH = "$.deptData";

    private static final String TICKET_PATH = "$.ticket";

    private static final String TOKEN_PATH = "$.access_token";
    private static final String ERRCODE_PATH = "$.errcode";

    private static final String CID_PATH = "$.cid";
    private static final String MESSAGE_CONTENT = "[该功能不支持推推客户端,请在云盘中打开]";

    private String corpId;

    private String msgSignature;

    private String timeStamp;

    private String nonce;

    private String encrypt;

    private HttpClientHelper httpClientHelper;

    private CustomNacosConfig customNacosConfig;


    public CcWorkClientHelper(CcWorkInitParam ccWorkInitParam) {
        corpId = ccWorkInitParam.getCorpId();
        msgSignature = ccWorkInitParam.getMsgSignature();
        timeStamp = ccWorkInitParam.getTimeStamp();
        nonce = ccWorkInitParam.getNonce();
        encrypt = ccWorkInitParam.getEncrypt();
        httpClientHelper = SpringHelper.getBean(HttpClientHelper.class);
        customNacosConfig = SpringHelper.getBean(CustomNacosConfig.class);
    }


    public String getAccessToken() {
        String url = String.format(GET_TOKEN, customNacosConfig.getCustomTuiHost(), corpId, customNacosConfig.getCustomTuiSsoAppId(), customNacosConfig.getCustomTuiSecretKey());
        try {
            log.info("getAccessToken url {}", url);
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body()).string();
            log.info("getAccessToken result {}", result);
            String accessToken = (String) JSONPath.extract(result, TOKEN_PATH);
            return accessToken;
        } catch (Exception e) {
            log.error("getAccessToken error url {}", url, e);
        }
        return null;
    }

    public List<String> getDepartmentIds() {
        List<String> ids = new ArrayList<>();
        List<CcWorkDepartment> departmentRelation = getDepartmentRelation();
        departmentRelation.forEach(ccWorkDepartment -> {
            ids.add(ccWorkDepartment.getId());
        });
        return ids;
    }

    public List<CcWorkDepartment> getDepartmentRelation() {
        String accessToken = getAccessToken();
        List<CcWorkDepartment> departments = new ArrayList<>();
        int page = 1;
        while (true) {
            String url = String.format(DEPARTMENT_RELATION, customNacosConfig.getCustomTuiHost(), accessToken, page);
            try {
                log.info("getDepartmentRelation url {}", url);
                page++;
                Response response = httpClientHelper.getResponse(url);
                String result = Objects.requireNonNull(response.body().string());
                log.info("getDepartmentRelation result {}", result);
                JSONObject jsonObject = JSONObject.parseObject(result);
                List<CcWorkDepartment> ccWorkDepartments = JSONArray.parseArray(jsonObject.getString(DEPT_RELATION_PTH), CcWorkDepartment.class);
                if (CollectionUtils.isEmpty(ccWorkDepartments)) {
                    break;
                }
                departments.addAll(ccWorkDepartments);
                String pageCount = jsonObject.getString(DEPT_PAGE_COUNT_PTH);
                if (page > Integer.parseInt(pageCount)) {
                    break;
                }
            } catch (Exception e) {
                log.error("getDepartmentRelation error url {}", url, e);
                break;
            }
        }
        return departments;
    }

    public List<CcWorkUser> getUserList(String departmentId) {
        String accessToken = getAccessToken();
        String url = String.format(USER_LIST, customNacosConfig.getCustomTuiHost(), accessToken, departmentId);
        try {
            Response response = httpClientHelper.getResponse(url);
            String result = Objects.requireNonNull(response.body().string());
            log.info("getUserList result {}", result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            List<CcWorkUser> ccWorkUsers = JSONArray.parseArray(jsonObject.getString(DEPT_RELATION_PTH), CcWorkUser.class);
            if (CollectionUtils.isEmpty(ccWorkUsers)) {
                return null;
            }
            ccWorkUsers.forEach(s -> s.setDepartmentId(departmentId));
            return ccWorkUsers;
        } catch (Exception e) {
            log.error("getUserList error url {}", url, e);
        }
        return null;
    }

    private List<YfyUser> buildUsers(String departmentId, JSONArray userList, PlatformSyncConfig platformSyncConfig) {
        String valueBox = platformSyncConfig.getValueBox();
        ThirdPartyValueBox thirdPartyValueBox = JSON.parseObject(valueBox, ThirdPartyValueBox.class);
        List<YfyUser> yfyUsers = new ArrayList<>();
        for (int i = 0; i < userList.size(); i++) {
            JSONObject jsonObject = userList.getJSONObject(i);
            String account = jsonObject.getString("account");
            String email = jsonObject.getString("email");
            String emailSuffix = thirdPartyValueBox.getUserConfigDto().getEmailSuffix();
            if (StringUtils.isNotBlank(emailSuffix)) {
                email = account + emailSuffix;
            }
            List<String> departmentIds = new ArrayList<>();
            departmentIds.add(departmentId);
            YfyUser yfyUser = YfyUser.builder()
                    .id(account)
                    .fullName(jsonObject.getString("name"))
                    .email(email)
                    .spaceTotal(thirdPartyValueBox.getUserSpace() * GB)
                    .phone(thirdPartyValueBox.getUserConfigDto().isSyncPhone() ? jsonObject.getString("mobile") : null)
                    .departmentIds(departmentIds)
                    .createTime(new Date())
                    .status(thirdPartyValueBox.getUserConfigDto().isActivate() ? UserStatusEnum.SAVE.getCode() : "3")
                    .build();
            log.info("ccwork buildYfyUser:{} , ccworkDept:{}", yfyUser, jsonObject.toJSONString());
            yfyUsers.add(yfyUser);
        }
        return yfyUsers;
    }

    public YfyUser getUserInfoByTicket() {
        encrypt = encrypt.replace(" ", "+");
        String str = AESEncryptUtil.decrypt(customNacosConfig.getCustomTuiSecretKey(), "", msgSignature, timeStamp, nonce, encrypt);
        String ticket = (String) JSONPath.extract(str, TICKET_PATH);
        YfyUser yfyUser = getAccountByTicket(nonce, ticket);
        return yfyUser;
    }

    public CcworkEvent getEventObject() {
        encrypt = encrypt.replace(" ", "+");
        String str = AESEncryptUtil.decrypt(customNacosConfig.getCustomTuiSecretKey(), "", msgSignature, timeStamp, nonce, encrypt);
        log.info("事件回调解析数据为： {}", str);
        CcworkEvent event = JSONObject.parseObject(str, CcworkEvent.class);
        return event;
    }

    private YfyUser getAccountByTicket(String nonce, String ticket) {
        String url = String.format(SSO_CHECK_SIGN, customNacosConfig.getCustomTuiHost());
        try {
            String postDataByTicket = getPostDataByTicket(nonce, ticket);
            log.info("get accountByTicket url {}, postData {}", url, postDataByTicket);
            Response response = httpClientHelper.postResponse(url, postDataByTicket);
            String result = Objects.requireNonNull(response.body().string());
            log.info("get accountByTicket result {}", result);
            String errCode = (String) JSONPath.extract(result, ERRCODE_PATH);
            if (!"0".equals(errCode)) {
                return null;
            }
            String account = (String) JSONPath.extract(result, ACCOUNT_PATH);
            YfyUser yfyUser = YfyUser.builder()
                    .id(account)
                    .fullName((String) JSONPath.extract(result, NAME_PATH))
                    .phone((String) JSONPath.extract(result, PHONE_PATH))
                    .tuituiTenantsID((String) JSONPath.extract(result, CID_PATH))
                    .createTime(new Date())
                    .status("1")
                    .build();
            JSONArray deptDatas = (JSONArray) JSONPath.extract(result, DEPT_PATH);
            if (!deptDatas.isEmpty()) {
                List<String> list = new ArrayList<>();
                for (int i = 0; i < deptDatas.size(); i++) {
                    JSONObject jsonObject = deptDatas.getJSONObject(i);
                    String did = jsonObject.getString("did");
                    list.add(did);
                }
                yfyUser.setDepartmentIds(list);
            }
            log.info("get accountByTicket yfyUser {}", yfyUser);
            return yfyUser;
        } catch (Exception e) {
            log.error("get accountByTicket error url {}", url, e);
        }
        return null;
    }


    private String getPostDataByTicket(String nonce, String ticket) {
        return "{\"nonce\":\"" + nonce + "\",\"ticket\":\"" + ticket + "\"}";
    }

    public boolean sendLinkMessage(YfyMessage yfyMessage) {

        String h5Url = yfyMessage.getH5Url();
        h5Url = getRealUrl(h5Url, "target=new&open_window=local");
        String content = yfyMessage.getContent();
        if (!isSendLinkMessage(yfyMessage)) {
            //云盘h5消息不支持的类型,给出错误提示
            content += MESSAGE_CONTENT;
        }
        yfyMessage.setContent(content);
        yfyMessage.setH5Url(h5Url);
        return sendMessage(getSendLinkMessagePostData(yfyMessage));
    }

    private String getRedirectHost(String webUrl) {
        try {
            URL url = new URL(webUrl);
            String protocol = url.getProtocol();
            String redirectHost = url.getHost();
            return protocol + "://" + redirectHost;
        } catch (Exception e) {
            log.error("getRedirectHost error url {}", webUrl, e);
        }
        return null;
    }

    private boolean sendMessage(String postData) {
        String transId = UUID.randomUUID().toString();
        String url = String.format(SEND_LINK_MESSAGE, customNacosConfig.getCustomTuiMessageHost(), customNacosConfig.getCustomTuiMessageAppId(), customNacosConfig.getCustomTuiMessageSecret(), transId);
        log.info("sendLinkMessage url {}, postData {}", url, postData);
        try {
            Response response = httpClientHelper.postResponse(url, postData);
            String result = Objects.requireNonNull(response.body().string());
            log.info("sendLinkMessage result {}", result);
            Integer errCode = (Integer) JSONPath.extract(result, ERRCODE_PATH);
            if (errCode == 0) {
                return true;
            }
        } catch (Exception e) {
            log.error("sendLinkMessage error url {}", url, e);
        }
        return false;
    }

    private String getSendLinkMessagePostData(YfyMessage yfyMessage) {
        return String.format("{\"tousers\":[\"%s\"],\"msgtype\":\"%s\",\"link\":{\"url\":\"%s\",\"title\":\"%s\",\"content\":\"%s\",\"image\":\"\"}}",
                yfyMessage.getReceivers(), "link", yfyMessage.getH5Url(), yfyMessage.getTitle(), yfyMessage.getContent());
    }


    private Boolean isSendLinkMessage(YfyMessage yfyMessage) {
        if (YfyMessageTypeEnum.COMMENT.getType().equals(yfyMessage.getType())
                || YfyMessageTypeEnum.SHARELINK.getType().equals(yfyMessage.getType())
                || YfyMessageTypeEnum.COLLAB.getType().equals(yfyMessage.getType())
                || YfyMessageTypeEnum.FOLLOW.getType().equals(yfyMessage.getType())) {
            return true;
        }
        return false;
    }

    private String getRealUrl(String h5Url, String format) {
        if (h5Url.contains("?")) {
            h5Url = h5Url + "&" + format;
        } else {
            h5Url = h5Url + "?" + format;
        }
        return h5Url;
    }

    private String getMessageContent(String content, String title) {
        return content + ":" + title;
    }
}
