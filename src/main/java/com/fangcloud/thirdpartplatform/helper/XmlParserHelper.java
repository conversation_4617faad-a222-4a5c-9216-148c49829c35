package com.fangcloud.thirdpartplatform.helper;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.xml.bind.JAXB;
import java.io.ByteArrayInputStream;
import java.io.InputStream;

/**
* <p>Title: XmlParserHelper</p>
* <p>Description: </p>
* <p>Copyright: Copyright (c) 2019</p>
* <p>Company: www.fangcloud.com</p>
* <AUTHOR>
* Data 2019-05-23
* @version 1.0
*/
@Slf4j
public class XmlParserHelper {


    public static <T> T parserToObject(String xmlString, Class<T> t) {

        String xml = StringUtils.trimWhitespace(xmlString);

        if (null == xml) {
            return null;
        }

        try (InputStream inputStream = new ByteArrayInputStream(xml.getBytes())) {

            return JAXB.unmarshal(inputStream, t);
        } catch (Exception e) {
            log.error("转换xml异常 ", e);
        }

        return null;
    }
}
