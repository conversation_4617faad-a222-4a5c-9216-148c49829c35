package com.fangcloud.thirdpartplatform.helper.ai;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.net.URLEncoder;
import java.util.concurrent.CompletableFuture;

@Slf4j
public class SseEmitterUtil {

    public static void sendError(SseEmitter sseEmitter,String msg){
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(500L);
                sseEmitter.send("systemError:" + msg);
                sseEmitter.complete();
            } catch (Exception e) {
                log.error("发送sse消息异常", e);
            }
        });

    }

    public static void send(SseEmitter sseEmitter,String msg){
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(100L);
                sseEmitter.send( msg);
            } catch (Exception e) {
                log.error("发送sse消息异常", e);
            }
        });

    }

    public static void sendTest(SseEmitter sseEmitter,String msg){
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(500L);
                int length = msg.length();
                int startIndex = 0;
                int chunkSize = 3;

                while (startIndex < length) {
                    int endIndex = Math.min(startIndex + chunkSize, length);
                    String chunk = msg.substring(startIndex, endIndex);
                    String encode = URLEncoder.encode(chunk, "UTF-8");
                    String info = encode.replaceAll("\\+", "%20");
                    sseEmitter.send( info);
                    startIndex += chunkSize;
                    Thread.sleep(50L);
                }
                sseEmitter.complete();
            } catch (Exception e) {
                log.error("发送sse消息异常", e);
            }
        });

    }

    public static void sendClose(SseEmitter sseEmitter,String msg){
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(500L);
                sseEmitter.send( msg);
                sseEmitter.complete();
            } catch (Exception e) {
                log.error("发送sse消息异常", e);
            }
        });

    }
}
