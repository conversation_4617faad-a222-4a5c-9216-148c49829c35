package com.fangcloud.thirdpartplatform.helper.ai;

import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Objects;
import java.util.function.Consumer;

/**
 * 描述：OpenAIEventSourceListener
 *
 * <AUTHOR>
 * @date 2023-02-22
 */
@Slf4j
@RequiredArgsConstructor
public class AiPubDocYunEventSourceListener extends EventSourceListener {

    final SseEmitter sseEmitter;


    String last = "";
    @Setter
    Consumer<String> onComplate = s -> {

    };


    /**
     * {@inheritDoc}
     */
    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.info("打开连接");

    }

    /**
     * {@inheritDoc}
     */
    @SneakyThrows
    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {

        if (data != null) {
            sseEmitter.send(data);

        }
    }

    @SneakyThrows
    @Override
    public void onClosed(EventSource eventSource) {
        log.info("链接关闭");
        SseHelper.complete(sseEmitter);
    }


    @SneakyThrows
    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        log.error("OpenAI  sse连接异常data：{}，异常：{}", t);

        if (Objects.isNull(response)) {
            return;
        }
        ResponseBody body = response.body();
        if (Objects.nonNull(body)) {
            log.error("OpenAI  sse连接异常data：{}，异常：{}", body.string(), t);
        } else {
            log.error("OpenAI  sse连接异常data：{}，异常：{}", response, t);
        }
        eventSource.cancel();
        SseHelper.complete(sseEmitter);
    }
}
