package com.fangcloud.thirdpartplatform.helper.ai;

import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.ResponseBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;

/**
 * 描述：AiSseEventSourceListener
 *
 * <AUTHOR>
 * @date 2023-02-22
 */
@Slf4j
@RequiredArgsConstructor
public class AiSseEventSourceListener extends EventSourceListener {

    private final StringBuilder contentCollector = new StringBuilder();
    private final CompletableFuture<String> resultFuture = new CompletableFuture<>();


    /**
     * {@inheritDoc}
     */
    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.info("打开连接");

    }

    /**
     * {@inheritDoc}
     */
    @SneakyThrows
    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        log.info(" 收到SSE事件 - ID: {}, Type: {}, Data: {}", id, type, data);
        if (data != null) {
            String content = SseContentParser.extractContent(data);
            if (!content.isEmpty())  {
                contentCollector.append(content);
            }

        }
    }

    @SneakyThrows
    @Override
    public void onClosed(EventSource eventSource) {
        try {
            resultFuture.complete(contentCollector.toString());
        } finally {
            eventSource.cancel();
        }
    }


    @SneakyThrows
    @Override
    public void onFailure(EventSource eventSource, Throwable t, Response response) {
        log.error("SSE流异常, 状态码:{}, 错误信息:{}",
                response != null ? response.code() : "unknown",
                t.getMessage());
        if (response != null && response.body() != null) {
            log.error("错误响应:{}", response.body().string());
        }
        eventSource.cancel();
        resultFuture.completeExceptionally(t);
    }

    public CompletableFuture<String> getResultFuture() {
        return resultFuture;
    }
}
