package com.fangcloud.thirdpartplatform.helper.ai;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;

public class SseContentParser {
    private static final ObjectMapper mapper = new ObjectMapper();
    private static final ObjectReader READER = mapper.readerFor(JsonNode.class);

    /**
     * 精确解析SSE事件中的content
     * @param eventData 原始数据（如：data:{"choices":[{"delta":{"content":"xxx"}}]})
     * @return 提取的content（可能为空字符串）
     */
    public static String extractContent(String eventData) {
        try {
            // 1. 去除SSE前缀
            String jsonStr = eventData.startsWith("data:")  ?
                    eventData.substring(5).trim()  :
                    eventData;

            // 2. 完整解析JSON
            JsonNode root = READER.readValue(jsonStr);

            // 3. 按数据结构精准提取
            JsonNode choices = root.path("choices");
            if (choices.isArray()  && choices.size()  > 0) {
                JsonNode delta = choices.get(0).path("delta");
                if (delta.has("content"))  {
                    return delta.path("content").asText();
                }
            }
        } catch (Exception e) {
            // 4. 错误处理（打印日志或抛出运行时异常）
            System.err.println("SSE 数据解析失败: " + eventData);
            e.printStackTrace();
        }
        return "";
    }
}
