package com.fangcloud.thirdpartplatform.helper.ai;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;

import java.net.Proxy;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * open ai 客户端
 *
 * <AUTHOR>
 */

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatGPTStream {

    private String apiKey;
    private List<String> apiKeyList;

    private OkHttpClient okHttpClient;
    /**
     * 连接超时
     */
    @Builder.Default
    private long timeout = 90;

    /**
     * 网络代理
     */
    @Builder.Default
    private Proxy proxy = Proxy.NO_PROXY;
    /**
     * 反向代理
     */
    @Builder.Default
    private String apiHost = "https://v2master.fangcloud.net/aiapi/assistant/chat/chatStream";

    /**
     * 初始化
     */
    public ChatGPTStream init() {
        OkHttpClient.Builder client = new OkHttpClient.Builder();
        client.connectTimeout(timeout, TimeUnit.SECONDS);
        client.writeTimeout(timeout, TimeUnit.SECONDS);
        client.readTimeout(timeout, TimeUnit.SECONDS);
        if (Objects.nonNull(proxy)) {
            client.proxy(proxy);
        }

        okHttpClient = client.build();

        return this;
    }

    public void streamChatCompletion(String message,
                                     EventSourceListener eventSourceListener, String userId) {

        try {
            EventSource.Factory factory = EventSources.createFactory(okHttpClient);

            log.info("chatGPTStream message:{}", message);

            Request request = new Request.Builder()
                    .url(apiHost)
                    .header("User-Id", userId)
                    .post(RequestBody.create(MediaType.parse("application/json"),
                            message))
                    .build();
            factory.newEventSource(request, eventSourceListener);

        } catch (Exception e) {
            log.error("请求出错：{}", e);
        }
    }







}
