package com.fangcloud.thirdpartplatform.helper;

import com.fangcloud.thirdpartplatform.constant.sync.SyncTaskConstants;
import com.fangcloud.thirdpartplatform.db.model.PlatformSyncConfig;
import com.fangcloud.thirdpartplatform.repository.cache.RedisStringManager;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class SyncTaskHelper {
    @Resource
    private RedisStringManager redisStringManager;

    public boolean checkMultiTask(PlatformSyncConfig platformSyncConfig) {
        String taskBinding = getTaskBinding(platformSyncConfig);
        if (StringUtils.isNotBlank(taskBinding)) {
            List<String> taskList = Lists.newArrayList(taskBinding.split(","));
            if (taskList.contains(platformSyncConfig.getId().toString())) {
                return true;
            }
        }
        return false;
    }


    public boolean checkMainTask(PlatformSyncConfig platformSyncConfig) {
        if (checkMultiTask(platformSyncConfig)) {
            String taskBinding = getTaskBinding(platformSyncConfig);
            String mainTaskId = taskBinding.split(",")[0];
            if (StringUtils.isNotBlank(mainTaskId)) {
                return mainTaskId.equals(platformSyncConfig.getId().toString());
            }
        }
        return false;
    }

    public String getTaskBinding(PlatformSyncConfig platformSyncConfig) {
        String taskBinding = redisStringManager.get(SyncTaskConstants.TASK_BINDING +
                platformSyncConfig.getEnterpriseId() + platformSyncConfig.getSyncType());
        if (StringUtils.isNotBlank(taskBinding)) {
            return taskBinding;
        }
        return null;
    }

}
