package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOHeaders;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTORequest;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.fangcloud.thirdpartplatform.constant.CommonConstants;
import com.fangcloud.thirdpartplatform.constant.PlatformTypeEnum;
import com.fangcloud.thirdpartplatform.constant.sync.PlatformGlobalConfigKeyEnum;
import com.fangcloud.thirdpartplatform.entity.input.DingTalkInitParams;
import com.fangcloud.thirdpartplatform.service.impl.GlobalConfigServiceImpl;
import com.google.common.collect.Lists;
import com.sync.common.entity.dto.DingTalkDepartment;
import com.sync.common.entity.dto.DingTalkUserInfo;
import com.sync.common.entity.dto.YfyMessage;
import com.sync.common.entity.dto.YfyUser;
import com.sync.common.exceptions.SyncException;
import com.sync.common.service.DingTalkServiceImpl;
import com.sync.common.utils.StreamUtils;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.URL;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;


@Slf4j
public class DingTalkClientHelper {

    @Autowired
    private DingTalkServiceImpl dingTalkService;

    private static final String GET_USER_BY_CODE = "/sns/getuserinfo_bycode";
    private static final String GET_USERID_BY_UNIONID = "/user/getUseridByUnionid";
    private static final String GET_ACCESS_TOKEN = "/gettoken";
    private static final String GET_USER_INFO = "/user/get";
    private static final String GET_USER_INFO_V2 = "/topapi/v2/user/get";
    private static final String GET_DEPARTMENT_INFO_V2 = "/topapi/v2/department/get";
    private static final String SEND_MESSAGE = "/topapi/message/corpconversation/asyncsend_v2";
    private static final String GET_DEPARTMENTS_BY_PARENT_ID = "/topapi/v2/department/listsub";
    private static final String GET_USERS_BY_DEPARTMENT_ID = "/topapi/user/listsimple";
    private static final String GET_USER_DETAILS_BY_DEPARTMENT_ID = "/topapi/v2/user/list";
    private static final String GET_SUB_DEPARTMENT_ID_LIST = "/topapi/v2/department/listsubid";
    private static final String GET_MESSAGE_MASS_SEND = "/topapi/message/mass/send";
    private static final String GET_USER_ID_BY_PHONE = "/topapi/v2/user/getbymobile";
    private static final String OPEN_BY_WORK_PLATFORM_URL = "dingtalk://dingtalkclient/action/openapp?corpid=%s&container_type=work_platform&app_id=0_%s&redirect_type=jump&params=&redirect_url=%s";


    private CacheHelper cacheHelper;

    private String appId;
    private String appSecret;
    private String corpId;
    private String corpSecret;
    private String host;
    private String agentId;
    private String robotCode;
    private String code;

    private String authCode;
    /**
     * 订阅号消息unionId;
     */
    private String unionId;
    public DingTalkClientHelper(DingTalkInitParams params){
        appId = params.getAppId();
        appSecret = params.getAppSecret();
        corpId = params.getCorpId();
        corpSecret = params.getCorpSecret();
        host = StringUtils.isEmpty(params.getHost()) ? "https://oapi.dingtalk.com" : params.getHost();
        agentId = params.getAgentId();
        robotCode = params.getRobotCode();
        code = params.getCode();
        authCode = params.getAuthCode();
        unionId = params.getUnionId();
        cacheHelper = SpringHelper.getBean(CacheHelper.class);
    }


    public String getUserIdByCode() {
        DingTalkUserInfo user = this.getUserByCode();
        if (user != null && user.getUnionid() != null) {
            return this.getUserIdByUnionId(user.getUnionid());
        } else {
            throw new SyncException("根据code获取钉钉用户信息出错");
        }
    }

    public String getUserIdByAuthCode() {
        try {
            Client client = createClient();
            GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
                    .setClientId(appId)
                    .setClientSecret(appSecret)
                    .setCode(authCode)
                    .setGrantType("authorization_code");
            GetUserTokenResponse getUserTokenResponse = client.getUserToken(getUserTokenRequest);
            String accessToken = getUserTokenResponse.getBody().getAccessToken();
            return getUserinfo(accessToken);
        }catch (Exception e) {
            log.error("getUserIdByAuthCode error",e);
        }
        return null;
    }


    public String getUserinfo(String accessToken) throws Exception {
        com.aliyun.dingtalkcontact_1_0.Client client = createClientGetUserInfo();
        GetUserHeaders getUserHeaders = new GetUserHeaders();
        getUserHeaders.xAcsDingtalkAccessToken = accessToken;
        //获取用户个人信息，如需获取当前授权人的信息，unionId参数必须传me
        GetUserResponseBody me = client.getUserWithOptions("me", getUserHeaders, new RuntimeOptions()).getBody();
        String unionId = me.getUnionId();
        return getUserIdByUnionId(unionId);
    }

    public YfyUser getUserInfoByUserId(String userId) {
        if(StringUtils.isEmpty(userId)){
            return null;
        }
        String url = host + GET_USER_INFO;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiUserGetRequest req = new OapiUserGetRequest();
        req.setUserid(userId);
        req.setHttpMethod("GET");

        try {
            OapiUserGetResponse rsp = (OapiUserGetResponse)client.execute(req, this.getAccessToken());
            if (rsp != null) {
                return YfyUser.builder()
                        .id(rsp.getUserid())
                        .fullName(rsp.getName())
                        .createTime(new Date())
                        .phone(rsp.getMobile())
                        .email(rsp.getEmail())
                        .status("1")
                        //用errorCode暂存钉钉工号
                        .errorCode(rsp.getJobnumber())
                        .build();
            }
        } catch (Exception var7) {
            log.error("获取钉钉用户详细信息出错", var7);
        }

        return null;
    }

    public OapiV2UserGetResponse.UserGetResponse getUserInfoByUserIdV2(String userId) {
        String url = host + GET_USER_INFO_V2;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiV2UserGetRequest req = new OapiV2UserGetRequest();
        req.setUserid(userId);
        req.setLanguage("zh_CN");
        OapiV2UserGetResponse rsp = null;

        try {
            rsp = (OapiV2UserGetResponse)client.execute(req, getAccessToken());
            log.info("获取钉钉用户信息详情出错：用户id是 {}, 结果是 {}", userId, rsp.getBody());
        } catch (ApiException var7) {
            log.error("获取钉钉用户信息详情出错：用户id是 {}，错误是 {} ", userId, var7.toString());
        }

        log.info(rsp.getBody());
        return rsp.getResult();
    }

    public DingTalkUserInfo getUserByCode() {
        String url = host + GET_USER_BY_CODE;
        DefaultDingTalkClient client = new DefaultDingTalkClient(url);
        OapiSnsGetuserinfoBycodeRequest req = new OapiSnsGetuserinfoBycodeRequest();
        req.setTmpAuthCode(code);

        try {
            OapiSnsGetuserinfoBycodeResponse response = (OapiSnsGetuserinfoBycodeResponse)client.execute(req, appId, appSecret);
            log.info("getUserByCode:钉钉返回response：{}", JSONObject.toJSON(response));
            if (response.isSuccess()) {
                OapiSnsGetuserinfoBycodeResponse.UserInfo userInfo = response.getUserInfo();
                return DingTalkUserInfo.buildByUserInfo(userInfo);
            }
        } catch (ApiException var8) {
            log.error("获取钉钉user出错：", var8);
        }

        return new DingTalkUserInfo();
    }

    public String getUserIdByUnionId(String unionid) {
        String url = host + GET_USERID_BY_UNIONID;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiUserGetUseridByUnionidRequest request = new OapiUserGetUseridByUnionidRequest();
        request.setUnionid(unionid);
        request.setHttpMethod("GET");
        String accessToken = this.getAccessToken();

        try {
            OapiUserGetUseridByUnionidResponse response = (OapiUserGetUseridByUnionidResponse)client.execute(request, accessToken);
            log.info("getUserIdByUnionid:钉钉返回response：{}", JSONObject.toJSON(response));
            return response.getUserid();
        } catch (ApiException var8) {
            log.error("获取钉钉userId出错：", var8);
            return null;
        }
    }

    public boolean sendLinkMessage(YfyMessage yfyMessage) {
        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
        request.setUseridList(yfyMessage.getReceivers());
        request.setAgentId(Long.parseLong(agentId));
        request.setToAllUser(false);
        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        msg.setMsgtype("link");
        msg.setLink(new OapiMessageCorpconversationAsyncsendV2Request.Link());
        msg.getLink().setTitle(yfyMessage.getTitle());
        msg.getLink().setText(yfyMessage.getContent());
        msg.getLink().setMessageUrl(yfyMessage.getH5Url());
        msg.getLink().setPicUrl(yfyMessage.getPicUrl());
        request.setMsg(msg);
        return this.sendMessage(request);
    }

    public boolean isSendRobotMessage() {
        if (Strings.isNotBlank(robotCode)) {
            return true;
        }
        return false;
    }

    public boolean sendRobotLinkMessage(YfyMessage yfyMessage) {
        try {
        com.aliyun.dingtalkrobot_1_0.Client client = createClientSendRobotMessage();
        BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
        batchSendOTORequest.setRobotCode(robotCode);
        batchSendOTORequest.setUserIds(Arrays.asList(yfyMessage.getReceivers()));
        batchSendOTORequest.setMsgKey("sampleLink");
        String messageUrl = String.format(OPEN_BY_WORK_PLATFORM_URL, corpId, agentId, URL.encode(yfyMessage.getH5Url()));
        batchSendOTORequest.setMsgParam(String.format("{\"text\":\"%s\",\"title\":\"%s\",\"picUrl\":\"%s\",\"messageUrl\":\"%s\"}",
                yfyMessage.getContent(), yfyMessage.getTitle(), yfyMessage.getPicUrl(), messageUrl));
        BatchSendOTOHeaders batchSendOTOHeaders = new BatchSendOTOHeaders();
        batchSendOTOHeaders.xAcsDingtalkAccessToken = getAccessToken();
        client.batchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
        log.info("钉钉link消息返回发送成功{}", JSONObject.toJSONString(batchSendOTORequest));
        return true;
        } catch (Exception e) {
            log.error("钉钉link消息失败返回发送结果{}", JSONObject.toJSONString(e));
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 根据手机号获取userId
     * @param phone
     * @return
     */
    public String getUserIdByPhone(String phone)
    {
        DingTalkClient client = new DefaultDingTalkClient(host + GET_USER_ID_BY_PHONE);
        OapiV2UserGetbymobileRequest req = new OapiV2UserGetbymobileRequest();
        req.setMobile(phone);
        OapiV2UserGetbymobileResponse rsp = null;
        String userId = null;
        try {
            rsp = client.execute(req, getAccessToken());
            log.info("获取钉钉用户id详情：用户phone是 {}, 结果是 {}", phone, JSONObject.toJSONString(rsp.getResult()));
            userId = rsp.getResult().getUserid();
        } catch (ApiException e) {
            log.error("获取钉钉用户id出错：用户phone是 {}，错误是 {} ", phone, e.toString());
        }
        return userId;
    }



    public boolean sendMessage(OapiMessageCorpconversationAsyncsendV2Request request) {
        String url = host + SEND_MESSAGE;
        DefaultDingTalkClient client = new DefaultDingTalkClient(url);

        try {
            OapiMessageCorpconversationAsyncsendV2Response response = (OapiMessageCorpconversationAsyncsendV2Response)client.execute(request, getAccessToken());
            if (response.getErrcode() == 0L) {
                log.info("钉钉link消息返回发送结果{}", JSONObject.toJSONString(response));
                return true;
            }

            log.error("钉钉link消息返回发送结果{}", JSONObject.toJSONString(response));
        } catch (ApiException var5) {
            var5.printStackTrace();
        }

        return false;
    }

    /**
     * 卡片消息
     * @return
     */
    public boolean sendUserActionCardMsg(YfyMessage yfyMessage) {
        String url = host + GET_MESSAGE_MASS_SEND;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiMessageMassSendRequest request = new OapiMessageMassSendRequest();
        request.setUnionid(unionId);
        request.setIsToAll(false);
        request.setMsgType("action_card");
        request.setUuid(UUID.randomUUID().toString());
        request.setUseridList(yfyMessage.getReceivers());
        OapiMessageMassSendRequest.MessageBody msgBody = new OapiMessageMassSendRequest.MessageBody();
        msgBody.setActionCard(new OapiMessageMassSendRequest.ActionCard());
        msgBody.getActionCard().setTitle(yfyMessage.getTitle());
        msgBody.getActionCard().setMarkdown(yfyMessage.getContent());
        msgBody.getActionCard().setSingleTitle(yfyMessage.getTitle());
        msgBody.getActionCard().setSingleUrl(yfyMessage.getWebUrl());
        request.setMsgBody(msgBody);

        try {
            OapiMessageMassSendResponse response = client.execute(request, getAccessToken());
            if (response.getErrcode() == 0L) {
                log.info("send msg 成功 agentId {} response {} msg {}", agentId, response.getBody(), yfyMessage.getContent());
                return true;
            } else {
                log.info("send msg 失败 agentId {} error {} msg {}", agentId, response.getBody(), yfyMessage.getContent());
            }
        } catch (Exception e) {
            log.error("获取dingtalk send msg 异常 appKey {}, appSecret {}, agentId {}, msg {}", appId, appSecret, agentId, yfyMessage.getContent());
        }
        return false;
    }

    public List<DingTalkDepartment> getDepartmentsByParentId(long parentId) {
        String url = host + GET_DEPARTMENTS_BY_PARENT_ID;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
        req.setDeptId(parentId);
        req.setLanguage("zh_CN");

        try {
            OapiV2DepartmentListsubResponse rsp = (OapiV2DepartmentListsubResponse)client.execute(req, getAccessToken());
            List<OapiV2DepartmentListsubResponse.DeptBaseResponse> deptBaseResponses = rsp.getResult();
            List<DingTalkDepartment> departmentList = StreamUtils.map(deptBaseResponses, (deptBaseResponse) -> {
                return new DingTalkDepartment(deptBaseResponse.getDeptId(), deptBaseResponse.getName(), deptBaseResponse.getParentId());
            });
            log.info("通过父部门id获取子部门列表, 父部门id为 {}, 子部门结果为 {}", parentId, rsp.getBody());
            return departmentList;
        } catch (Exception var9) {
            log.error("获取钉钉部门信息出错：部门id是 {}，错误是 {} ", parentId, var9.toString());
            return null;
        }
    }

    public List<Long> getSubDepartmentIds(long departmentId) {
        String url = host + GET_SUB_DEPARTMENT_ID_LIST;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiV2DepartmentListsubidRequest req = new OapiV2DepartmentListsubidRequest();
        req.setDeptId(departmentId);
        OapiV2DepartmentListsubidResponse rsp = null;

        try {
            rsp = (OapiV2DepartmentListsubidResponse)client.execute(req, getAccessToken());
            OapiV2DepartmentListsubidResponse.DeptListSubIdResponse subIdResponse = rsp.getResult();
            log.info("获取钉钉子部门ID列表：部门id是 {}, 结果是 {}", departmentId, rsp.getBody());
            return subIdResponse.getDeptIdList();
        } catch (ApiException var8) {
            log.error("获取钉钉子部门ID列表出错：部门id是 {}，错误是 {} ", departmentId, var8.toString());
            return null;
        }
    }
    public OapiUserListsimpleResponse.PageResult getUserInfoByDepartmentId(long departmentId, long nextCursor) {
        String url = host + GET_USERS_BY_DEPARTMENT_ID;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiUserListsimpleRequest req = new OapiUserListsimpleRequest();
        req.setDeptId(departmentId);
        req.setCursor(nextCursor);
        req.setSize(100L);
        req.setContainAccessLimit(true);

        try {
            OapiUserListsimpleResponse rsp = (OapiUserListsimpleResponse)client.execute(req, getAccessToken());
            log.info("获取钉钉部门用户信息：部门id是 {}, 结果是 {}", departmentId, rsp.getBody());
            return rsp.getResult();
        } catch (Exception var9) {
            log.error("获取钉钉部门用户信息出错：部门id是 {}，错误是 {} ", departmentId, var9.toString());
            return null;
        }
    }

    public com.dingtalk.api.response.OapiV2UserListResponse.PageResult getUserDetailsByDepartmentId(long departmentId, long nextCursor) {

        String url = host + GET_USER_DETAILS_BY_DEPARTMENT_ID;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiV2UserListRequest req = new OapiV2UserListRequest();
        req.setDeptId(departmentId);
        req.setCursor(nextCursor);
        req.setSize(100L);
        req.setContainAccessLimit(true);
        OapiV2UserListResponse rsp = null;

        try {
            rsp = (OapiV2UserListResponse)client.execute(req, getAccessToken());
            log.info("获取钉钉部门用户详细信息：部门id是 {}, 结果是 {}", departmentId, rsp.getBody());
            return rsp.getResult();
        } catch (ApiException var10) {
            log.error("获取钉钉部门用户信息详情出错：部门id是 {}，错误是 {} ", departmentId, var10.toString());
            return null;
        }
    }

    private String getAccessToken() {
        // 拼装token的缓存key
        String accessTokenKey = PlatformTypeEnum.DING_TALK.getType() + CommonConstants.SPLIT_UNDERLINE + CommonConstants.ACCESS_TOKEN + CommonConstants.SPLIT_UNDERLINE + appId;
        if (cacheHelper.get(accessTokenKey) != null) {
            return cacheHelper.get(accessTokenKey);
        }

        String url = host + GET_ACCESS_TOKEN;
        DefaultDingTalkClient client = new DefaultDingTalkClient(url);
        OapiGettokenRequest request = new OapiGettokenRequest();
        request.setAppkey(appId);
        request.setAppsecret(appSecret);
        request.setHttpMethod("GET");

        try {
            OapiGettokenResponse response = (OapiGettokenResponse)client.execute(request);
            log.info("getAccessToken:钉钉返回response：{}", JSONObject.toJSON(response));
            String accessToken = response.getAccessToken();
            cacheHelper.put(accessTokenKey, accessToken);
            return accessToken;
        } catch (ApiException var6) {
            log.error("获取钉钉AccessToken出错：{}", var6.toString());
            return null;
        }
    }

    /**
     * 获取全部钉钉用户
     */
    public List<OapiV2UserListResponse.ListUserResponse> getAllDingTalkUsers(String enterpriseId) {

        List<OapiV2UserListResponse.ListUserResponse> userResponseList = new ArrayList<>();

        getUsersAndSubDeptUsers(1L, userResponseList, enterpriseId);

        return userResponseList;
    }

    /**
     * 根据部门id获取钉钉部门的全部人员
     * @param departmentId 获取全部人员时这个值设为根部门id值
     * @param userResponseList 用户结果集合，调用该方法需要自己传一个这个集合
     */
    public void getUsersAndSubDeptUsers(
            Long departmentId, List<OapiV2UserListResponse.ListUserResponse> userResponseList, String enterpriseId) {

        queryUsersByDeptId(departmentId, 0L, userResponseList);

        List<Long> subDeptIds = getSubDepartmentIds(departmentId);
        if (!CollectionUtils.isEmpty(subDeptIds)) {
            Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId + "");
            String dingTalkFilterDeptIds = configMap.get(PlatformGlobalConfigKeyEnum.DING_TALK_FILTER_DEPT_IDS.getKey());
            if (dingTalkFilterDeptIds != null) {
                subDeptIds.removeIf(subDeptId -> Lists.newArrayList(dingTalkFilterDeptIds.split(",")).contains(String.valueOf(subDeptId)));
            }
            subDeptIds.forEach(subDeptId -> getUsersAndSubDeptUsers(subDeptId, userResponseList, enterpriseId));
        }

    }

    // 分页多次遍历获取某一部门ID的用户
    private void queryUsersByDeptId(
            Long deptId, Long nextCursor, List<OapiV2UserListResponse.ListUserResponse> userResponseList) {

        OapiV2UserListResponse.PageResult pageResult = getUserDetailsByDepartmentId(deptId, nextCursor);
        if (!CollectionUtils.isEmpty(pageResult.getList())) {
            userResponseList.addAll(pageResult.getList());
        }
        if (pageResult.getHasMore()){
            queryUsersByDeptId(deptId, pageResult.getNextCursor(), userResponseList);
        }
    }

    /**
     * 获取全部钉钉部门
     */
    public List<DingTalkDepartment> getAllDingTalkDepartments(String enterpriseId) {
        // 没有加根部门
        List<DingTalkDepartment> dingTalkDepartments = new ArrayList<>();

        getAllDingTalkSubDepartments(1, dingTalkDepartments, enterpriseId);
        return dingTalkDepartments;
    }

    /**
     * 获取某部门下的全部子部门
     * @param parentId
     * @param dingTalkDepartments
     */
    public void getAllDingTalkSubDepartments(long parentId, List<DingTalkDepartment> dingTalkDepartments, String enterpriseId) {

        List<DingTalkDepartment> departmentList = getDepartmentsByParentId(parentId);

        if (!CollectionUtils.isEmpty(departmentList)) {
            Map<String, String> configMap = GlobalConfigServiceImpl.getGlobalConfigMap(enterpriseId + "");
            String dingTalkFilterDeptIds = configMap.get(PlatformGlobalConfigKeyEnum.DING_TALK_FILTER_DEPT_IDS.getKey());
            if (dingTalkFilterDeptIds != null) {
                departmentList.removeIf(department -> Lists.newArrayList(dingTalkFilterDeptIds.split(",")).contains(String.valueOf(department.getDeptId())));
            }
            dingTalkDepartments.addAll(departmentList);
            departmentList.forEach(dingTalkDepartment -> {
                getAllDingTalkSubDepartments(dingTalkDepartment.getDeptId(), dingTalkDepartments, enterpriseId);
            });

        }
    }

    /**
     * 根据部门id查询部门详情
     */
    public OapiV2DepartmentGetResponse.DeptGetResponse getDingTalkDepartmentById(long id) {

        OapiV2DepartmentGetResponse.DeptGetResponse departmentDetails = getDepartmentDetailsById(id);

        return departmentDetails;
    }

    public OapiV2DepartmentGetResponse.DeptGetResponse getDepartmentDetailsById(long id) {
        String url = host + GET_DEPARTMENT_INFO_V2;
        DingTalkClient client = new DefaultDingTalkClient(url);
        OapiV2DepartmentGetRequest req = new OapiV2DepartmentGetRequest();
        req.setDeptId(id);
        req.setLanguage("zh_CN");

        try {
            OapiV2DepartmentGetResponse rsp = (OapiV2DepartmentGetResponse)client.execute(req, getAccessToken());
            OapiV2DepartmentGetResponse.DeptGetResponse result = rsp.getResult();
            log.info("通过部门id获取部门详细信息, 部门id为 {}, 部门结果为 {}", id, rsp.getBody());
            return result;
        } catch (Exception var8) {
            log.error("获取钉钉部门详细信息出错：部门id是 {}，错误是 {} ", id, var8.toString());
            return null;
        }
    }


    public com.aliyun.dingtalkoauth2_1_0.Client createClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkoauth2_1_0.Client(config);
    }


    public com.aliyun.dingtalkcontact_1_0.Client createClientGetUserInfo() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkcontact_1_0.Client(config);
    }

    public static com.aliyun.dingtalkrobot_1_0.Client createClientSendRobotMessage() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkrobot_1_0.Client(config);
    }
}
