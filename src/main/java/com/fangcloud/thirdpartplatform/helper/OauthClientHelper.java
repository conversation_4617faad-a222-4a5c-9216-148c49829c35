package com.fangcloud.thirdpartplatform.helper;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONPath;
import com.fangcloud.thirdpartplatform.config.CustomNacosConfig;
import com.fangcloud.thirdpartplatform.utils.AuthenticationUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
@Slf4j
public class OauthClientHelper {

    @Resource
    private CustomNacosConfig customNacosConfig;

    @Resource
    private HttpClientHelper httpClientHelper;


    /**
     * 根据企业id获取client列表
     *
     * @param enterpriseId
     * @return
     */
    public JSONArray getClientList(long enterpriseId) {
        String params = customNacosConfig.getOauthHostUrl() + customNacosConfig.getOauthGetClients();
        String url = String.format(params,enterpriseId);
        Map<String, String> headers = AuthenticationUtils.getHeaders(customNacosConfig.getOauthServiceId(),
                customNacosConfig.getOauthVersion(), customNacosConfig.getOauthSecret());
        httpClientHelper.setHeaders(headers);

        try {
            Response response = httpClientHelper.getResponse(url);
            if (response.body() == null) {
                log.warn("get oauth clients url {} header {}", url, headers);
                return null;
            }
            String result = response.body().string();
            log.info("get oauth clients url {}  result {}", url, result);
            return (JSONArray) JSONPath.extract(result, "$.client_list");
        } catch (Exception e) {
            log.error("get oauth clients error url {}", url, e);
        }
        return null;
    }
}
