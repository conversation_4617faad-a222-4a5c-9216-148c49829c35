package com.fangcloud.thirdpartplatform.codeScript.nanchang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.db.dao.DepartmentMapper;
import com.fangcloud.thirdpartplatform.db.dao.DepartmentsUsersMapper;
import com.fangcloud.thirdpartplatform.db.model.Department;
import com.fangcloud.thirdpartplatform.db.model.DepartmentsUsers;
import com.fangcloud.thirdpartplatform.helper.HttpClientHelper;
import com.fangcloud.thirdpartplatform.utils.Md5Utils;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 * @version 01
 * @classname HmacSignUtil
 * @description 签名生成的工具类
 * @date 2021/1/27 13:41
 * @since 2021
 */
public class CodeScriptNanchangInside {


    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    private static final Logger LOGGER = Logger.getLogger(CodeScriptNanchangInside.class.getName());

    private final static String url = "http://*************:10005/platform/thirdparty/invoke";

    @Resource
    private HttpClientHelper httpClientHelper;

    /**
     * 登录
     * @param obj
     * @
     */
    public Object execute(Object obj) throws Exception {
        LOGGER.info("CodeScriptNanchangInside start!");

        Object data = null;
        try {


            JSONObject param = new JSONObject();
            param.put("type", "thirdparty_invoke");

            JSONObject jsonObject = (JSONObject) obj;
            JSONArray userInfoList = jsonObject.getJSONArray("user_info_list");
            for (Object o : userInfoList) {
                JSONObject userInfo = (JSONObject) o;
                Long userId = userInfo.getLong("user_id");
                List<DepartmentsUsers> departmentsUsers = departmentsUsersMapper.queryByUserId(userId);
                Map<String, Long> deptNamePathMap = new HashMap<>();
                for (DepartmentsUsers deptUser : departmentsUsers) {
                    Department dept = departmentMapper.queryById(deptUser.getDepartmentId());
                    deptNamePathMap.put(buildNamePath(dept), dept.getSpaceTotal());
                }
                ((JSONObject) o).put("dept_name_path_map", deptNamePathMap);

            }

            jsonObject.put("enterpriseId", 115);
            jsonObject.put("platformId", 2);
            jsonObject.put("thirdpartyInvokeType", "SYNC_INNER_USER_OUTSIDE");

            param.put("data", jsonObject);
//
            Map<String, String> header = new HashMap<>();
//
            long time = System.currentTimeMillis();
//
            header.put("PLATFORM-SIGN", Md5Utils.getMD5OfStr(time + "YFY"));
            header.put("PLATFORM-TIME", time + "");
//
            httpClientHelper.setHeaders(header);

            LOGGER.info("CodeScriptNanchangInside url" + url);
            LOGGER.info("CodeScriptNanchangInside param" + JSON.toJSONString(param));
            LOGGER.info("CodeScriptNanchangInside header" + JSON.toJSONString(header));
//
            Response response = httpClientHelper.postResponse(url, JSON.toJSONString(param));
            String result = Objects.requireNonNull(response.body().string());
            LOGGER.info("sync user batch result " + result);
            JSONObject jsonObjectResult = JSONObject.parseObject(result);
            data = jsonObjectResult.get("data");
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("sync user batch error" + e.getMessage());
        }

        LOGGER.info("CodeScriptNanchangInside end!");

        return data;
    }

    private String buildNamePath(Department dept) {
        List<Integer> parentIds = parsePath(dept.getPathIds());

        StringBuilder pathBuilder = new StringBuilder();
        for (int pid : parentIds) {
            pathBuilder.append(departmentMapper.queryById(pid).getName()).append("/");
        }
        pathBuilder.append(dept.getName());
        String[] split = pathBuilder.toString().split("/", 2);
        return split[1];
    }

    /**
     * 通用路径解析方法
     */
    private List<Integer> parsePath(String path) {
        List<Integer> ids = new ArrayList<>();
        String[] segments = path.split("/");
        for (String seg : segments) {
            if (!seg.isEmpty() && !seg.equals("/")) {
                ids.add(Integer.parseInt(seg));
            }
        }
        return ids;
    }

    public static void main(String[] args) {

    }


}