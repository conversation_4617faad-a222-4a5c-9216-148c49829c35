//暂时注释
/*
package com.fangcloud.thirdpartplatform.codeScript.nanchang;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fangcloud.thirdpartplatform.db.dao.*;
import com.fangcloud.thirdpartplatform.db.model.*;
import com.fangcloud.thirdpartplatform.entity.sync.*;
import com.fangcloud.thirdpartplatform.helper.V2ClientHelper;
import com.fangcloud.thirdpartplatform.utils.ByteconverUtils;
import com.sync.common.utils.SyncStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;

*/
/**
 * <AUTHOR>
 * @version 01
 * @classname HmacSignUtil
 * @description 签名生成的工具类
 * @date 2021/1/27 13:41
 * @since 2021
 *//*

public class CodeScriptNanchangOutside {

    private static final Logger LOGGER = Logger.getLogger(CodeScriptNanchangOutside.class.getName());

    @Autowired
    private PlatformUserMapper platformUserMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private V2ClientHelper v2ClientHelper;

    @Autowired
    private EnterpriseMapper enterpriseMapper;

    @Autowired
    private DepartmentsUsersMapper departmentsUsersMapper;

    @Autowired
    private DepartmentMapper departmentMapper;

    */
/**
     * 登录
     * @param obj
     * @
     *//*

    public Object execute(Object obj) throws Exception {
        LOGGER.info("CodeScriptNanchangOutside start!");

        Map<Long, String> syncResult = new HashMap<>();
        try {
            JSONObject jsonObject = (JSONObject) obj;

            JSONArray userInfoList = jsonObject.getJSONArray("user_info_list");
            Integer platformId = jsonObject.getInteger("platformId");
            Integer enterpriseId = jsonObject.getInteger("enterpriseId");

            // 1. 获取企业信息
            Enterprise enterprise = enterpriseMapper.queryById(enterpriseId);
            if(Objects.isNull(enterprise)){
                LOGGER.info("enterprise id :" + enterpriseId + ", info is null !");
            }else {
                for (Object o : userInfoList) {
                    JSONObject userInfo = (JSONObject) o;
                    LOGGER.info("userInfo is :" + JSON.toJSONString(userInfo));
                    String userTicket = userInfo.getString("user_ticket");
                    Long userId = userInfo.getLong("user_id");
                    Boolean active = userInfo.getBoolean("active");
                    Map<String, Long> insideDeptNamePathMap = (Map) userInfo.get("dept_name_path_map");
                    PlatformUser platformUser = platformUserMapper.queryByPlatformIdUserTicket(platformId, userTicket);
                    if(Objects.isNull(platformUser)){
                        syncResult.put(userId, "未在外网匹配到账号");
                    }else {
                        User user = userMapper.queryById(platformUser.getUserId());

                        if(user.getId() == enterprise.getAdminUserId()){ // 外网云盘超级管理员不能同步
                            syncResult.put(userId, "外网超级管理员状态不能同步");
                        }else {
                            SyncActiveUsersBatchBean syncActiveUsersBatchBean = null;
                            if(user.isActive()){
                                if(!active){ // 内网用户未激活，外网用户激活，需要将外网用户设置为未激活
                                    syncActiveUsersBatchBean = new SyncActiveUsersBatchBean();
                                    syncActiveUsersBatchBean.setUserIds(Arrays.asList(user.getId()));
                                    syncActiveUsersBatchBean.setStatus("disable");
                                }
                            }else {
                                if(active){ // 内网用户激活，外网用户未激活，需要将外网用户设置为激活
                                    syncActiveUsersBatchBean = new SyncActiveUsersBatchBean();
                                    syncActiveUsersBatchBean.setUserIds(Arrays.asList(user.getId()));
                                    syncActiveUsersBatchBean.setStatus("enable");
                                }
                            }

                            if(!Objects.isNull(syncActiveUsersBatchBean)){
                                SyncActiveUsersBatchOutput syncActiveUsersBatchOutput = v2ClientHelper.active_users_batch(syncActiveUsersBatchBean, enterprise.getAdminUserId());

                                if(Objects.isNull(syncActiveUsersBatchOutput) || !Objects.isNull(syncActiveUsersBatchOutput.getFailUserIds())){
                                    syncResult.put(userId, "外网账号状态修改失败");
                                }

                            }
                        }
                    }
                    if (insideDeptNamePathMap != null && insideDeptNamePathMap.size() > 0) {
                        List<DepartmentsUsers> outsideDepartmentsUsers = departmentsUsersMapper.queryByUserId(platformUser.getUserId());
                        List<Long> outsideDepartmentList = outsideDepartmentsUsers*.departmentId as List<Long>
                        Map<String, Long> outsideDeptNamePathMap = buildNamePath(outsideDepartmentList);
                        LOGGER.info("outsideDeptNamePathMap :" + outsideDeptNamePathMap);
                        insideDeptNamePathMap.each { deptNamePath, spaceTotal ->
                            if (outsideDeptNamePathMap.get(deptNamePath) == null) {
                                PublicDepartmentResult departmentResult = ensureDepartmentExists(deptNamePath, enterprise, spaceTotal);
                                if (departmentResult != null) {
                                    deptAddUser(platformUser.getUserId(), departmentResult.getId(), enterprise);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            LOGGER.info("sync user batch error" + e.getMessage());
        }

        LOGGER.info("CodeScriptNanchangOutside end!");

        return syncResult;
    }

    private PublicDepartmentResult ensureDepartmentExists (String deptNamePath, Enterprise enterprise, Long spaceTotal){

        String[] layers = cleanAndSplitPath(deptNamePath);
        if (layers.length == 0) {
            return null;
        }

        StringBuilder currentPath = new StringBuilder();
        Department topDepartment = departmentMapper.queryRootDepartment(enterprise.getId());
        long currentParentId = topDepartment.getId();

        for (String layer : layers) {
            if (currentPath.length() == 0) {
                currentPath.append(layer);
            } else {
                currentPath.append("/").append(layer);
            }
            String currentFullPath = currentPath.toString();

            long newParentId = isDepartmentExist(layer, currentParentId, enterprise.getId());
            if (newParentId != currentParentId) {
                //部门已存在
                currentParentId = newParentId;

                if (deptNamePath.equals(currentFullPath)) {
                    PublicDepartmentResult departmentResult = new PublicDepartmentResult();
                    departmentResult.setId(currentParentId);
                    return departmentResult;
                }
            } else {
                //新建部门
                PublicDepartmentResult departmentResult = createDepartment(currentParentId, layer, spaceTotal, enterprise);
                if (departmentResult.getErrorMessage() == null) {
                    // 更新父ID为新建部门的ID
                    currentParentId = departmentResult.getId();

                    if (deptNamePath.equals(currentFullPath)) {
                        return departmentResult;
                    }
                }
            }
        }
        return null;
    }


    */
/**
     * 构建部门名称路径
     *//*

    private Map<String, Long> buildNamePath (List <Long> departmentList) {
        Map<String, Long> deptNamePathMap = new HashMap<>();
        List<Department> departments = departmentMapper.queryByIds(departmentList);
        for (Department department : departments) {
            List<Long> parentIds = parsePath(department.getPathIds());

            StringBuilder pathBuilder = new StringBuilder();
            for (long pid : parentIds) {
                pathBuilder.append(departmentMapper.queryById(pid).getName()).append("/");
            }
            pathBuilder.append(department.getName());
            String[] split = pathBuilder.toString().split("/", 2);
            deptNamePathMap.put(split[1], department.getId());
        }
        return deptNamePathMap;
    }

    private List<Long> parsePath(String path) {
        List<Long> ids = new ArrayList<>();
        String[] segments = path.split("/");
        for (String seg : segments) {
            if (!seg.isEmpty() && !seg.equals("/")) {
                ids.add(Long.parseLong(seg));
            }
        }
        return ids;
    }


    private String[] cleanAndSplitPath (String path){
        String cleaned = path.replaceAll('^/+|/+$', ""); // 使用单引号包裹正则
        return cleaned.split("/+");
    }


    private PublicDepartmentResult createDepartment ( long currentParentId, String layer,
                                                      Long spaceTotal, Enterprise enterprise){
        PublicDepartmentBean syncDepartmentBean = new PublicDepartmentBean();
        // 2.1 设置部门名称
        syncDepartmentBean.setDepartmentName(SyncStringUtils.formatString(layer));

        // 2.2 设置父部门id
        syncDepartmentBean.setParentId(currentParentId);

        // 2.3 设置部门空间，默认设置100G
        syncDepartmentBean.setSpaceTotal(ByteconverUtils.getConver(spaceTotal));

        // 2.4 设置是否创建公共资料库，默认为true
        syncDepartmentBean.setCreateCommonFolder(true);
        // 2.5 设置部门是否自动接受协作，默认为true
        syncDepartmentBean.setCollabAutoAccepted(true);

        PublicDepartmentResult syncPublicDepartmentResult = v2ClientHelper.createDepartment(syncDepartmentBean, enterprise.getAdminUserId());

        return syncPublicDepartmentResult;
    }

    private void deptAddUser (Long userId, Long departmentId, Enterprise enterprise){
        //添加用户至新建部门
        EditDepartmentUserBean editDepartmentUserBean = new EditDepartmentUserBean();
        List<Long> addUserIds = new ArrayList<>();
        addUserIds.add(userId);
        editDepartmentUserBean.setAddUserIds(addUserIds);
        EditDepartmentResult editDepartmentResult = v2ClientHelper.editDepartmentUser(editDepartmentUserBean, enterprise.getAdminUserId(), departmentId);
    }

    private long isDepartmentExist(String name, long parentId, long enterpriseId) {
        List<Department> departments = departmentMapper.queryByName(enterpriseId, name);
        for (Department department : departments) {
            if (parentId == department.getParentId()) {
                return department.getId();
            }
        }
        return parentId;
    }

    public static void main(String[] args) {
        Map<Long, String> syncResult = new HashMap<>();
        syncResult.put(111L, "1123");
        syncResult.put(1121L, "11233w");
        System.out.println(JSON.toJSONString(syncResult));

        String result = "{\"success_user_ids\":[679],\"fail_user_ids\":null,\"status\":\"enable\",\"success\":true}";
        SyncActiveUsersBatchOutput syncActiveUsersBatchOutput = JSONObject.parseObject(result, SyncActiveUsersBatchOutput.class);
        if(Objects.isNull(syncActiveUsersBatchOutput) || !CollectionUtils.isEmpty(syncActiveUsersBatchOutput.getFailUserIds())) {
//            syncResult.put(userId, "外网账号状态修改失败");
            System.out.println();
        }
    }


}*/
