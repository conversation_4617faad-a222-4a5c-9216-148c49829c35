#!/bin/bash

productId=$1
env=$2
serverRoot="/usr/local/services"
linkRoot="${serverRoot}/platform"
linkTestRoot="${serverRoot}/platform-test"
localRoot=$PWD

usage() {
  echo "Usage: `basename $0` productId test/prod" 1>&2; exit 1;
}

if [ ! $1 ];then
  usage
fi


if [ "${env}" == "test" ];then
  chmod 777 ./platform
  cp ./platform ./platform-test
  sed -i "s/platform/platform-test/g" ./platform-test
  \cp ./platform-test /etc/init.d/platform-test
  systemctl daemon-reload


  #创建软链接    ln -s source target
  if [ ! -h "${linkTestRoot}" ];then
    ln -s $localRoot $linkTestRoot
    echo "create ln success"
  else
    rm -rf $linkTestRoot
    ln -s $localRoot $linkTestRoot
    echo "recreate ln success"
  fi


  echo "env test application.yml build success"

  pidTest=`netstat -nlp | grep java | grep :8081 | awk '{print $7}' | grep [0-9]* -o`
  if [ ${pidTest} ];then
    kill -9 $pidTest
    echo "kill third-part-platform pid ${pidTest} success "
  fi

  #启动进程
  /etc/init.d/platform-test restart
else

  chmod 777 ./platform
  \cp ./platform /etc/init.d/
  systemctl daemon-reload
  chkconfig  platform on
  #创建软链接    ln -s source target
  if [ ! -h "${linkRoot}" ];then
    ln -s $localRoot $linkRoot
    echo "create ln success"
  else
    rm -rf $linkRoot
    ln -s $localRoot $linkRoot
    echo "recreate ln success"
  fi

  pidProd=`netstat -nlp | grep java | grep :8089 | awk '{print $7}' | grep [0-9]* -o`
  if [ ${pidProd} ];then
    kill -9 $pidProd
    echo "kill platform pid ${pidProd} success "
  fi

  #启动进程
  /etc/init.d/platform restart

  #添加crontab
  cron_job='*/5 * * * * /etc/init.d/platform status >> /etc/init.d/nohup.txt'
  ( crontab -l | grep -v "platform"; echo "$cron_job" ) | crontab -

  #crontab -l
fi
