#!/bin/sh
# chkconfig: 2345 87 88
# Description: sync-app service

. /etc/init.d/functions

export HOME
source /etc/profile
PATH=$PATH:/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
HYBRID_APP_HOME=/usr/local/services/platform/
SEARCH_FLAT=platform/
cd ${HYBRID_APP_HOME}
function start() {
 /bin/bash ${HYBRID_APP_HOME}/start.sh -s
 PID=`ps -ef | grep java | grep "${SEARCH_FLAT}" | grep -v grep | awk '{print $2}'`
 APP=`ps -ef | grep java | grep "${SEARCH_FLAT}" | grep -v grep | awk '{print $16}'`
 if [ ! -z "$PID" ];then
     echo $PID > /var/run/platform.pid
     action "$(date '+%Y-%m-%d %H:%M:%S') Starting (APP:$APP) Server (PID:$PID)" /bin/true
 else
     action "$(date '+%Y-%m-%d %H:%M:%S') Starting (APP:$APP) Server" /bin/false
 fi
}

function stop() {
##找出对应进程并关闭
 PID=`ps -ef | grep java | grep "${SEARCH_FLAT}" | grep -v grep | awk '{print $2}'`
 APP=`ps -ef | grep java | grep "${SEARCH_FLAT}" | grep -v grep | awk '{print $16}'`
    if [ ! -z "$PID" ]; then
       kill -9 $PID
       if [ $? -eq 0 ];then
           action "$(date '+%Y-%m-%d %H:%M:%S') Stoping (APP:$APP) Server" /bin/true
       else
           action "$(date '+%Y-%m-%d %H:%M:%S') Stoping (APP:$APP) Server" /bin/false
       fi
    fi
}

function status() {
 PID=`ps -ef | grep java | grep "${SEARCH_FLAT}" | grep -v grep | awk '{print $2}'`
 APP=`ps -ef | grep java | grep "${SEARCH_FLAT}" | grep -v grep | awk '{print $16}'`
 if [ $PID ];then
  echo -e "$(date '+%Y-%m-%d %H:%M:%S') (APP:$APP) \033[32mRunning\033[0m (PID:$PID)..."
 else
  echo -e "$(date '+%Y-%m-%d %H:%M:%S') (APP:$APP) \033[31m Not running\033[0m..."
        #cd /etc/init.d
        $0 restart
 fi
}

case "$1" in
  start)
  start
     ;;
  stop)
  stop
     ;;
  restart)
     $0 stop
     $0 start
     ;;
  status)
  status
  ;;
  *)
     echo "Usage: service platform {start|stop|restart|status}"
     exit 1
esac

exit 0