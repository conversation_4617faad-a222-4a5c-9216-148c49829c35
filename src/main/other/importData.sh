#!/bin/bash

# 配置信息
ENTERPRISE_ID=115  # 企业ID
PLATFORM_ID=2      # 平台ID
SERVICE_ID="2"  # 服务ID
VERSION="1"        # 版本号
SECRET="855bc28260168be0b7e9892398437b45ee2118eb"         # 密钥
API_BASE_URL="http://v2-svc"  # API基础URL

# 声明全局变量
declare -a data_array
declare -i total_items
declare -i batch_size=30

# 生成UUID
generate_uuid() {
    uuidgen | tr -d '-'
}

generate_email() {
    local id=$1
    if [[ "$id" == *"@"* ]]; then
        echo "$id"
    else
        echo "${id}@csg.cn"
    fi
}

# 获取MD5
get_md5() {
    echo -n "$1" | md5sum | awk '{print $1}'
}

# 获取认证头部
get_auth_headers() {
    # 获取当前时间戳（秒）
    local timestamp=$(date +%s)

    # 生成token
    local token=$(get_md5 "${SERVICE_ID}${SECRET}${timestamp}${VERSION}")

    # 输出认证头部
    echo "Internal-Serviceid: ${SERVICE_ID}"
    echo "Internal-Timestamp: ${timestamp}"
    echo "Internal-Token: ${token}"
    echo "Internal-Version: ${VERSION}"
}

# 获取API地址
get_api_url() {
    local dataType=$1
    if [ "$dataType" = "org" ]; then
        echo "${API_BASE_URL}/internal_api/sync_account/sync_depts"
    else
        echo "${API_BASE_URL}/internal_api/sync_account/sync_users"
    fi
}

# 函数：处理数据批次
process_batch() {
    local dataType=$1
    local start=$2
    local end=$3
    local requestId=$(generate_uuid)

    # 构建JSON数据
    if [ "$dataType" = "org" ]; then
        # 构建SyncDepartmentBean格式的JSON
        local jsonData="{\"departments\":["
        for ((i=start; i<end; i++)); do
            if [ $i -gt $start ]; then
                jsonData+=","
            fi
            jsonData+="${data_array[$i]}"
        done
        jsonData+="],\"enterprise_id\":${ENTERPRISE_ID},\"platform_id\":${PLATFORM_ID},\"request_id\":\"${requestId}\"}"
    else
        # 构建SyncUserBean格式的JSON
        local jsonData="{\"users\":["
        for ((i=start; i<end; i++)); do
            if [ $i -gt $start ]; then
                jsonData+=","
            fi
            jsonData+="${data_array[$i]}"
        done
        jsonData+="],\"enterprise_id\":${ENTERPRISE_ID},\"platform_id\":${PLATFORM_ID},\"request_id\":\"${requestId}\"}"
    fi

    # 获取认证头部
    local authHeaders=$(get_auth_headers)

    # 获取API地址
    local apiUrl=$(get_api_url "$dataType")

    # 调用接口
    echo "Processing batch of ${dataType} data..."
    echo "Request ID: ${requestId}"

    # 构建curl命令
    local curlCmd="curl -X POST \"${apiUrl}\""
    curlCmd+=" -H \"Content-Type: application/json\""

    # 添加认证头部
    while IFS= read -r header; do
        curlCmd+=" -H \"${header}\""
    done <<< "$authHeaders"

    # 添加请求体
    curlCmd+=" -d '${jsonData}'"

    # 执行请求
    eval "$curlCmd"

    echo "Batch processed successfully"
}

# 处理CSV文件
process_csv() {
    local dataType=$1
    local filePath=$2

    # 检查文件是否存在
    if [ ! -f "$filePath" ]; then
        echo "Error: File $filePath does not exist"
        exit 1
    fi

    # 检查文件是否为空
    if [ ! -s "$filePath" ]; then
        echo "Error: CSV file is empty or format is incorrect"
        exit 1
    fi

    # 读取表头
    header=$(head -n 1 "$filePath")
    if [ -z "$header" ]; then
        echo "Error: CSV file is empty or format is incorrect"
        exit 1
    fi

    # 将表头转换为小写并创建列名到索引的映射
    IFS=',' read -ra headers <<< "$header"
    declare -A headerMap
    for i in "${!headers[@]}"; do
        headerMap[$(echo "${headers[$i]}" | tr '[:upper:]' '[:lower:]' | tr -d ' ')]=$i
    done

    # 清空数据数组
    data_array=()

    # 处理数据行
    while IFS=',' read -r line; do
        if [ -z "$line" ]; then
            continue
        fi
        IFS=',' read -ra values <<< "$line"

        if [ "$dataType" = "org" ]; then
            # 检查必要的列是否存在
            if [ -z "${headerMap[id]}" ] || [ -z "${headerMap[name]}" ] || [ -z "${headerMap[parentid]}" ]; then
                echo "Error: CSV file missing required columns (id, name, or parentId)"
                exit 1
            fi

            id="${values[${headerMap[id]}]}"
            name="${values[${headerMap[name]}]}"
            parentId="${values[${headerMap[parentid]}]}"

            # 构建YfyDepartment格式的JSON对象
            jsonObj="{\"id\":\"$id\",\"name\":\"$name\",\"parent_id\":\"$parentId\",\"space_total\":4096,\"status\":\"1\"}"
            data_array+=("$jsonObj")
        else
            # 检查必要的列是否存在
            if [ -z "${headerMap[id]}" ] || [ -z "${headerMap[real_name]}" ] || [ -z "${headerMap[department_id]}" ] || [ -z "${headerMap[user_status]}" ]; then
                echo "Error: CSV file missing required columns (id, real_name, department_id, or user_status)"
                exit 1
            fi

            id="${values[${headerMap[id]}]}"
            name="${values[${headerMap[real_name]}]}"
            departmentId="${values[${headerMap[department_id]}]}"
            userStatus="${values[${headerMap[user_status]}]}"

            # 检查用户状态
            if [ "$userStatus" == "1" ]; then
                email=$(generate_email "$id")
                # 构建YfyUser格式的JSON对象
                if [ -z "$departmentId" ]; then
                    jsonObj="{\"id\":\"$id\",\"full_name\":\"$name\",\"department_ids\":[],\"space_total\":10737418240,\"status\":\"3\",\"email\":\"$email\"}"
                else
                    jsonObj="{\"id\":\"$id\",\"full_name\":\"$name\",\"department_ids\":[\"$departmentId\"],\"space_total\":10737418240,\"status\":\"3\",\"email\":\"$email\"}"
                fi
                data_array+=("$jsonObj")
            fi
        fi
    done < <(tail -n +2 "$filePath")

    # 获取总数据量
    total_items=${#data_array[@]}

    # 检查数据是否为空
    if [ $total_items -eq 0 ]; then
        echo "Error: No valid data found in CSV file"
        exit 1
    fi

    # 批量处理数据
    for ((i=0; i<total_items; i+=batch_size)); do
        local end=$((i + batch_size))
        if [ $end -gt $total_items ]; then
            end=$total_items
        fi
        process_batch "$dataType" $i $end
    done
}

# 主函数
main() {
    # 检查参数
    if [ $# -ne 2 ]; then
        echo "Usage: $0 <dataType> <filePath>"
        echo "dataType should be 'org' or 'user'"
        exit 1
    fi

    local dataType=$1
    local filePath=$2

    # 验证数据类型
    if [ "$dataType" != "org" ] && [ "$dataType" != "user" ]; then
        echo "Error: Invalid dataType. Must be 'org' or 'user'"
        exit 1
    fi

    # 处理CSV文件
    process_csv "$dataType" "$filePath"

    echo "Import completed successfully"
}

# 执行主函数
main "$@"