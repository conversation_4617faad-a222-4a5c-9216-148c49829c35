package com.fangcloud.thirdpartplatform.container.config;

import com.fangcloud.thirdpartplatform.common.enums.ExecutorType;
import com.fangcloud.thirdpartplatform.runtime.Executor;
import com.fangcloud.thirdpartplatform.runtime.ExecutorFactory;
import com.fangcloud.thirdpartplatform.runtime.processor.manager.BeanDefinitionsProcessorManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

@Configuration
public class BeanConfig {

    @DependsOn({"springBeanDefinitionsProcessorManager"})
    @Bean(initMethod = "init", destroyMethod = "destroy")
    public ExecutorFactory faaSContainer(BeanDefinitionsProcessorManager beanDefinitionsProcessorManager) {
        ExecutorFactory instance = ExecutorFactory.getInstance();
        instance.setBeanDefinitionsProcessorManager(beanDefinitionsProcessorManager);
        return instance;
    }

    @Bean
    public Executor executor(ExecutorFactory executorFactory) {
        return executorFactory.getExecutor(ExecutorType.GROOVY);
    }
}
