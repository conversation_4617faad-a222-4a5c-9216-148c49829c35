package com.fangcloud.thirdpartplatform.container.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.METHOD})
public @interface DataSourceResource {

    /**
     * 数据库名称
     * @return
     */
    String dataSource() default "";
}
