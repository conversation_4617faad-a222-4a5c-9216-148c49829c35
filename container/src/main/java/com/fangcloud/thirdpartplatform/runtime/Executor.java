package com.fangcloud.thirdpartplatform.runtime;

import com.fangcloud.thirdpartplatform.common.exception.runtime.CompileException;
import com.fangcloud.thirdpartplatform.common.exception.runtime.ExecuteException;

import java.util.Map;

/**
 * 代码执行器
 * <AUTHOR>
 */

public interface Executor {

    /**
     * 代码编译
     * @param
     */
    Object compile(ExecutorContext executeContext) throws CompileException;

    /**
     * 代码执行
     * @param executeContext
     * @return
     */
    Object execute(ExecutorContext executeContext) throws ExecuteException;

    /**
     * 获取执行器名称
     * 根据该名称
     * @return
     */
    String type();
}
