import com.fangcloud.thirdpartplatform.common.enums.ExecutorType;
import com.fangcloud.thirdpartplatform.common.enums.context.Environment;
import com.fangcloud.thirdpartplatform.common.exception.runtime.CompileException;
import com.fangcloud.thirdpartplatform.common.exception.runtime.ExecuteException;
import com.fangcloud.thirdpartplatform.runtime.Executor;
import com.fangcloud.thirdpartplatform.runtime.ExecutorContext;
import com.fangcloud.thirdpartplatform.runtime.ExecutorFactory;
import com.fangcloud.thirdpartplatform.runtime.FaaSContainer;
import com.fangcloud.thirdpartplatform.runtime.context.FaaSContextHolder;

import java.util.ArrayList;

public class GroovyTest {
    public static void main(String[] args) throws ExecuteException, CompileException {
        String code ="public class Test {\n" +
                "    private int age = 11;\n" +
                "\n" +
                "    public String test(String name) {\n" +
                "        return \"test, my name is \" + name + \" and age is \" + age;\n" +
                "    }\n" +
                "}";
        // 容器初始化
        FaaSContainer.getInstance().start();
        // 根据执行器类型获取执行器
        Executor executor = ExecutorFactory.getInstance().getExecutor(ExecutorType.GROOVY);
        // 构建执行上下文
        ExecutorContext executeContext = new ExecutorContext();
        // 设置代码
        executeContext.setCode(code);
        // 设置调用方法
        executeContext.setMethodName("test");
        executeContext.setServiceName("test");
        // 代码编译，并初始化bean(对象)
        FaaSContextHolder.put(() -> Environment.ONLINE);
        executor.compile(executeContext);
        // 设置方法入参
        ArrayList<Object> params = new ArrayList<>();
        params.add("zhang san");
        executeContext.setParams(params);
        // 方法执行，并获取执行结果
        Object execute = executor.execute(executeContext);
        System.out.println(execute);
        // 容器关闭
        FaaSContainer.getInstance().close();
    }
}
